# UQ Analysis Configuration for GPT-OSS:20b NLI methods
# This config runs all GPT-OSS variants of NLI-based UQ methods

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: only process first N records per task
  test_mode: false
  test_limit: 5

  # LLM model being analyzed
  llm_model: "qwen3-32b"

  # Batch processing
  batch_size: 100

  # Progress reporting
  progress_report_interval: 10

# Tasks to analyze - All four main tasks
tasks:
  sentiment_analysis:
    enabled: true
    dataset_sources:
      - "twitter_sentiment"
    output_collection: "UQ_result_sentiment_analysis"
    response_field: "parsed_answer"

  explorative_coding:
    enabled: true
    dataset_sources:
      - "pytorch_commits"
    output_collection: "UQ_result_explorative_coding"
    response_field: "raw_answer"

  counterfactual_qa:
    enabled: true
    dataset_sources:
      - "counterfactual_data"
    output_collection: "UQ_result_counterfactual_qa"
    response_field: "parsed_answer"

  topic_labeling:
    enabled: true
    dataset_sources:
      - "topic_model_labeling"
    output_collection: "UQ_result_topic_labeling"
    response_field: "parsed_answer"

# UQ Methods Configuration - GPT-OSS variants only
uq_methods:
  # Enable GPT-OSS variant methods
  enabled_methods:
    - "EigValLaplacianNLIUQ_GPT_OSS_20B"
    - "EccentricityNLIEntailUQ_GPT_OSS_20B"
    - "SemanticEntropyNLIUQ_GPT_OSS_20B"
    - "NumSetsUQ_GPT_OSS_20B"
    - "LUQUQ_GPT_OSS_20B"
    - "LUQSentenceUQ_GPT_OSS_20B"
    - "KernelLanguageEntropyUQ_GPT_OSS_20B"

  # Method-specific parameters
  method_params:
    SemanticEntropyNLIUQ_GPT_OSS_20B:
      entailment_threshold: 0.5
      strict_entailment: true
    EccentricityNLIEntailUQ_GPT_OSS_20B:
      thres: 0.9
      affinity: "entail"
    EigValLaplacianNLIUQ_GPT_OSS_20B:
      affinity: "entail"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/uq_analysis_gpt_oss.log"

# Resume Configuration
resume:
  enabled: true
  check_existing: true
  skip_completed: true
