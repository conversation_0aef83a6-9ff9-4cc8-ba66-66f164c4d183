# YAML config for small test harness (test_uq_methods.py)
uq_test:
  mongo:
    host: localhost
    port: 27017
    db: LLM-UQ
    source_collection: response_collections
    test_results_collection: UQ_results_test

  filters:
    dataset_source: null
    prompt_variant: null

  methods:
    - eigval_jaccard
    - ecc_jaccard
    - eigval_nli
    - ecc_nli
    - se
    - numsets
    - embed_qwen
    - embed_e5

  limit_groups: 5
  output_json: uq_test_results.json

