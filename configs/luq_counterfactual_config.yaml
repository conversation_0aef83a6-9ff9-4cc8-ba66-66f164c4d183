# LUQ Counterfactual Analysis Configuration
# 专门用于LUQ方法的counterfactual数据分析

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: process limited records for testing
  test_mode: false
  test_limit: 10

  # LLM model being analyzed
  llm_model: "qwen3-32b"

  # Batch processing
  batch_size: 50

  # Progress reporting
  progress_report_interval: 10

# Tasks to analyze - 只启用counterfactual分析
tasks:
  counterfactual_qa:
    enabled: true
    dataset_sources:
      - "counterfactual_data"
    output_collection: "UQ_result_LUQ_counterfactual"  # 新的collection名称
    response_field: "parsed_answer"

# UQ Methods Configuration - 只启用LUQ方法
uq_methods:
  enabled_methods:
    - "LUQUQ"  # 只使用LUQ方法
  
  # LUQ method parameters
  method_params:
    LUQUQ:
      model_name: "microsoft/deberta-large-mnli"
      verbose: true

# Resume Configuration
resume:
  enabled: true
  checkpoint_file: "luq_counterfactual_checkpoint.json"
  save_interval: 20

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/luq_counterfactual_analysis.log"
