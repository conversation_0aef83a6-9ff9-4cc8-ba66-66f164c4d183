# LUQSENTENCE Counterfactual Analysis Configuration
# 专门用于LUQSENTENCE方法的counterfactual数据分析

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: process limited records for testing
  test_mode: false
  test_limit: 10

  # LLM model being analyzed
  llm_model: "qwen3-32b"

  # Batch processing
  batch_size: 20

  # Progress reporting
  progress_report_interval: 5

# Tasks to analyze - 只启用counterfactual分析
tasks:
  counterfactual_qa:
    enabled: true
    dataset_sources:
      - "counterfactual_data"
    output_collection: "UQ_result_LUQSENTENCE_counterfactual"  # 新的collection名称
    response_field: "parsed_answer"

# UQ Methods Configuration - 只启用LUQSENTENCE
uq_methods:
  # 只启用LUQSENTENCE方法
  enabled_methods:
    - "LUQSENTENCEUQ"
  
  # Method-specific parameters
  method_params:
    LUQSENTENCEUQ:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 1.0  # 匹配较短文本中的所有句子
      matching_mode: "bottom"  # 最相似匹配
      verbose: false

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/luqsentence_counterfactual.log"

# Resume Configuration
resume:
  enabled: true
  checkpoint_interval: 10  # Save progress every 10 groups
  resume_file: "checkpoints/luqsentence_counterfactual_resume.json"
