# LUQSENTENCE Counterfactual Test Configuration
# 用于小规模测试LUQSENTENCE方法

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: only process first N records per task
  test_mode: true
  test_limit: 5  # 只处理前5个问题进行测试

  # LLM model being analyzed
  llm_model: "qwen3-32b"

  # Batch processing
  batch_size: 5

  # Progress reporting
  progress_report_interval: 1

# Tasks to analyze - 只启用counterfactual分析
tasks:
  counterfactual_qa:
    enabled: true
    dataset_sources:
      - "counterfactual_data"
    output_collection: "UQ_result_LUQSENTENCE_counterfactual_test"  # 测试collection
    response_field: "parsed_answer"

# UQ Methods Configuration - 只启用LUQSENTENCE
uq_methods:
  # 只启用LUQSENTENCE方法
  enabled_methods:
    - "LUQSENTENCEUQ"
  
  # Method-specific parameters
  method_params:
    LUQSENTENCEUQ:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 1.0  # 匹配较短文本中的所有句子
      matching_mode: "bottom"  # 最相似匹配
      verbose: true  # 测试时启用详细输出

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/luqsentence_counterfactual_test.log"

# Resume Configuration
resume:
  enabled: false  # 测试时不启用断点续传
