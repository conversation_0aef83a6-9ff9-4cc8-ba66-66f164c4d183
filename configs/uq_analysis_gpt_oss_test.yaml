# YAML config for GPT-OSS:20b NLI UQ analysis - Small Scale Test
# This config runs a limited test of GPT-OSS variants for validation
uq_analysis:
  mongo:
    host: localhost
    port: 27017
    db: LLM-UQ
    source_collection: response_collections_grouped
    results_collection: UQ_results
    matrix_collection: uq_matrix
    embedding_cache_collection: embeddings_cache

  filters:
    dataset_source: counterfactual_data   # Focus on counterfactual dataset
    prompt_variant: null                  # All prompt variants

  # Start with a subset of GPT-OSS methods for testing
  methods:
    - numsets_gpt_oss              # NumSets with GPT-OSS:20b (fast)
    - se_gpt_oss                   # Semantic Entropy with GPT-OSS:20b (medium)

  limit_groups: 2                  # Limit to 2 groups for testing

  # Additional settings for testing
  skip_embedding: true             # Skip embedding methods
  verbose: true                    # Enable verbose output for debugging
