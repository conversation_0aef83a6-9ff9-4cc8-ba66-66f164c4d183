# YAML config for main UQ analysis (analyze_uq_from_mongo.py)
uq_analysis:
  mongo:
    host: localhost
    port: 27017
    db: LLM-UQ
    source_collection: response_collections
    results_collection: UQ_results
    matrix_collection: uq_matrix
    embedding_cache_collection: embeddings_cache

  filters:
    dataset_source: null   # e.g., twitter_sentiment
    prompt_variant: null   # e.g., sampled

  methods:
    - eigval_jaccard
    - ecc_jaccard
    - eigval_nli
    - ecc_nli
    - se
    - numsets
    - embed_qwen
    - embed_e5

  limit_groups: 0  # 0 means no limit

