# UQ Analysis Test Configuration
# This is the test version that only processes first 10 records per task

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: only process first N records per task
  test_mode: true
  test_limit: 3  # 对于counterfactual，只处理前3个问题进行测试

  # LLM model being analyzed
  llm_model: "qwen3-32b"

  # Batch processing
  batch_size: 10

  # Progress reporting
  progress_report_interval: 5

# Tasks to analyze
tasks:
  sentiment_analysis:
    enabled: false  # 暂时禁用，专注于counterfactual测试
    dataset_sources:
      - "twitter_sentiment"
    output_collection: "UQ_result_sentiment_analysis_test"
    response_field: "parsed_answer"  # Use parsed_answer for sentiment analysis

  explorative_coding:
    enabled: false  # 暂时禁用，专注于counterfactual测试
    dataset_sources:
      - "pytorch_commits"
    output_collection: "UQ_result_explorative_coding_test"
    response_field: "raw_answer"  # Use raw_answer for explorative coding

  counterfactual_qa:
    enabled: true
    dataset_sources:
      - "counterfactual_data"
    output_collection: "UQ_result_counterfactual_qa_test"
    response_field: "parsed_answer"  # Use parsed_answer for counterfactual responses

# UQ Methods Configuration - 选择适合counterfactual长文本分析的方法
uq_methods:
  # 启用多个UQ方法进行counterfactual分析
  enabled_methods:
    - "SemanticEntropyNLIUQ"      # 语义熵 - 适合长文本
    - "NumSetsUQ"                 # 语义集合数量 - 适合长文本
    - "EmbeddingQwenUQ"           # Qwen嵌入相似性 - 适合长文本
    - "EmbeddingE5UQ"             # E5嵌入相似性 - 适合长文本
  
  # Method-specific parameters
  method_params:
    SemanticEntropyNLIUQ:
      entailment_threshold: 0.5
      strict_entailment: true
    EccentricityJaccardUQ:
      thres: 0.9
    EccentricityNLIEntailUQ:
      thres: 0.9
      affinity: "entail"
    EigValLaplacianNLIUQ:
      affinity: "entail"
    LofreeCPUQ:
      lambda1: 1.0
      lambda2: 1.0

# Logging Configuration
logging:
  level: "INFO"  # 使用INFO级别查看LUQUQ进度
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/uq_analysis_test.log"

# Resume Configuration
resume:
  enabled: true
  check_existing: true
  skip_completed: true
