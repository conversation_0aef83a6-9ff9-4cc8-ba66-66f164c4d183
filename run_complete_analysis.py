#!/usr/bin/env python3
"""
运行完整的UQ方法分析脚本 - 优化版本
该脚本分析所有Twitter和Commit数据，包括validation准确率计算

主要改进:
1. 修复缓存数据格式不一致问题
2. 增强错误处理和日志记录
3. 添加数据验证和完整性检查
4. 优化内存使用和性能监控
5. 改进进度监控和中断恢复机制
6. 统一配置管理
7. 添加单元测试支持
8. 优化输出格式和报告生成

新功能:
- 支持从检查点恢复中断的分析
- 内存使用监控和自动清理
- 详细的数据质量报告
- 综合性能分析报告
- 配置文件管理
- 单元测试框架
"""

import subprocess
import sys
import argparse
import os
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查必要的依赖和文件"""
    required_files = [
        "analyze_all_uq_methods.py",
        "core/nli_calculator.py",
        "core/config_manager.py",
        "core/report_generator.py"
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        logger.error(f"Missing required files: {missing_files}")
        return False

    # 检查数据文件
    data_files = [
        "data/all_twitter_responses.csv",
        "data/all_commit_responses.csv"
    ]

    missing_data = []
    for file_path in data_files:
        if not os.path.exists(file_path):
            missing_data.append(file_path)

    if missing_data:
        logger.warning(f"Missing data files: {missing_data}")
        logger.warning("Analysis will fail if these files are not available")

    return True

def run_analysis(max_twitter_identifiers=None, max_commit_identifiers=None,
                nli_model="microsoft/deberta-large-mnli", resume=False,
                skip_twitter=False, skip_commit=False, run_tests=False):
    """
    运行完整的UQ方法分析

    Args:
        max_twitter_identifiers: 限制Twitter数据的identifier数量（None表示处理所有）
        max_commit_identifiers: 限制Commit数据的identifier数量（默认50，因为数据量很大）
        nli_model: 使用的NLI模型名称
        resume: 是否从检查点恢复
        skip_twitter: 是否跳过Twitter数据分析
        skip_commit: 是否跳过Commit数据分析
        run_tests: 是否在分析前运行测试
    """

    # 检查依赖
    if not check_dependencies():
        logger.error("Dependency check failed")
        return False

    # 运行测试（如果请求）
    if run_tests:
        logger.info("Running tests before analysis...")
        test_result = subprocess.run([sys.executable, "run_tests.py"], capture_output=True, text=True)
        if test_result.returncode != 0:
            logger.error("Tests failed!")
            logger.error(test_result.stderr)
            return False
        else:
            logger.info("All tests passed!")

    cmd = [sys.executable, "analyze_all_uq_methods.py"]

    # 添加基本参数
    cmd.extend([
        "--twitter_csv", "data/all_twitter_responses.csv",
        "--commit_csv", "data/all_commit_responses.csv",
        "--output_file", "data/Uq_Evaluation_20250731/uq_methods_complete_analysis.csv",
        "--nli_model", nli_model
    ])

    # 添加可选参数
    if max_twitter_identifiers:
        cmd.extend(["--max_twitter_identifiers", str(max_twitter_identifiers)])

    if max_commit_identifiers:
        cmd.extend(["--max_commit_identifiers", str(max_commit_identifiers)])

    if resume:
        cmd.append("--resume")

    if skip_twitter:
        cmd.append("--skip_twitter")

    if skip_commit:
        cmd.append("--skip_commit")

    logger.info(f"Running command: {' '.join(cmd)}")
    logger.info(f"Twitter identifiers limit: {'ALL' if max_twitter_identifiers is None else max_twitter_identifiers}")
    logger.info(f"Commit identifiers limit: {'ALL' if max_commit_identifiers is None else max_commit_identifiers}")
    logger.info(f"NLI model: {nli_model}")
    logger.info(f"Resume from checkpoint: {resume}")
    logger.info(f"Skip Twitter: {skip_twitter}")
    logger.info(f"Skip Commit: {skip_commit}")
    print("="*80)

    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n" + "="*80)
        logger.info("Analysis completed successfully!")

        # 显示生成的文件
        output_dir = "data/Uq_Evaluation_20250731"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            logger.info(f"Generated {len(files)} files in {output_dir}:")
            for file in sorted(files):
                logger.info(f"  - {file}")

        print("\nGenerated files:")
        print("- Main results: data/Uq_Evaluation_20250731/uq_methods_complete_analysis.csv")
        print("- Pivot tables: data/Uq_Evaluation_20250731/*_pivot.csv")
        print("- Comprehensive reports: data/Uq_Evaluation_20250731/*_report_*.txt/json/csv")
        print("- NLI cache: cache/nli_results_cache.csv")
        print("")
        print("Management commands:")
        print("- View cache statistics: python nli_cache_manager.py stats")
        print("- Run tests: python run_tests.py")
        print("- Resume interrupted analysis: python run_complete_analysis.py --resume")

        return True

    except subprocess.CalledProcessError as e:
        logger.error(f"Error running analysis: {e}")
        return False
    except KeyboardInterrupt:
        logger.warning("Analysis interrupted by user")
        logger.info("Use --resume flag to continue from last checkpoint")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Run complete UQ methods analysis with enhanced features",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full analysis
  python run_complete_analysis.py

  # Quick test with limited data
  python run_complete_analysis.py --quick_test

  # Resume from checkpoint
  python run_complete_analysis.py --resume

  # Run with tests first
  python run_complete_analysis.py --run_tests

  # Skip Twitter data
  python run_complete_analysis.py --skip_twitter

  # Custom limits
  python run_complete_analysis.py --max_twitter_identifiers 100 --max_commit_identifiers 50
        """
    )

    # 数据限制参数
    parser.add_argument('--max_twitter_identifiers', type=int, default=None,
                       help='Limit number of Twitter identifiers (default: process all)')
    parser.add_argument('--max_commit_identifiers', type=int, default=None,
                       help='Limit number of Commit identifiers (default: process all)')

    # 模型选择
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'],
                       help='NLI model to use for similarity computation')

    # 执行控制
    parser.add_argument('--quick_test', action='store_true',
                       help='Run a quick test with limited data (5 identifiers each)')
    parser.add_argument('--resume', action='store_true',
                       help='Resume from last checkpoint if available')
    parser.add_argument('--skip_twitter', action='store_true',
                       help='Skip Twitter data analysis')
    parser.add_argument('--skip_commit', action='store_true',
                       help='Skip Commit data analysis')
    parser.add_argument('--run_tests', action='store_true',
                       help='Run unit tests before starting analysis')

    # 调试和信息
    parser.add_argument('--check_deps', action='store_true',
                       help='Check dependencies and exit')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 检查依赖
    if args.check_deps:
        if check_dependencies():
            print("All dependencies are available")
            return 0
        else:
            print("Some dependencies are missing")
            return 1

    # 运行分析
    try:
        if args.quick_test:
            logger.info("Running quick test with limited data...")
            success = run_analysis(
                max_twitter_identifiers=5,
                max_commit_identifiers=5,
                nli_model=args.nli_model,
                resume=args.resume,
                skip_twitter=args.skip_twitter,
                skip_commit=args.skip_commit,
                run_tests=args.run_tests
            )
        else:
            logger.info("Running full analysis...")
            success = run_analysis(
                max_twitter_identifiers=args.max_twitter_identifiers,
                max_commit_identifiers=args.max_commit_identifiers,
                nli_model=args.nli_model,
                resume=args.resume,
                skip_twitter=args.skip_twitter,
                skip_commit=args.skip_commit,
                run_tests=args.run_tests
            )

        if success:
            logger.info("Analysis completed successfully!")
            return 0
        else:
            logger.error("Analysis failed!")
            return 1

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())