#!/usr/bin/env python3
"""
DeepSeek错峰优惠时段调度脚本
支持北京时间00:30-08:30执行，自动关闭，断点续连
"""

import os
import sys
import time
import signal
import logging
import schedule
import subprocess
from datetime import datetime, timezone, timedelta
from typing import Optional
import threading
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deepseek_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeepSeekScheduler:
    """DeepSeek定时调度器"""
    
    def __init__(self):
        self.process = None
        self.is_running = False
        self.should_stop = False
        self.start_time = "00:30"
        self.end_time = "08:30"
        self.status_file = "scheduler_status.json"
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("DeepSeek Scheduler initialized")
        
    def _signal_handler(self, signum, frame):
        """处理系统信号"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.should_stop = True
        self._stop_llm_process()
        
    def _get_beijing_time(self) -> datetime:
        """获取北京时间"""
        beijing_tz = timezone(timedelta(hours=8))
        return datetime.now(beijing_tz)
        
    def _is_discount_time(self) -> bool:
        """检查是否在优惠时段（北京时间00:30-08:30）"""
        current_time = self._get_beijing_time()
        current_hour_min = current_time.strftime("%H:%M")
        
        # 处理跨日情况：00:30-08:30
        if self.start_time <= self.end_time:
            # 正常情况，不跨日
            return self.start_time <= current_hour_min <= self.end_time
        else:
            # 跨日情况：23:30-08:30
            return current_hour_min >= self.start_time or current_hour_min <= self.end_time
            
    def _should_start_now(self) -> bool:
        """检查是否应该立即启动"""
        current_time = self._get_beijing_time()
        current_hour_min = current_time.strftime("%H:%M")
        
        # 在00:30启动，允许1分钟误差
        start_parts = self.start_time.split(":")
        start_hour, start_min = int(start_parts[0]), int(start_parts[1])
        
        current_parts = current_hour_min.split(":")
        current_hour, current_min = int(current_parts[0]), int(current_parts[1])
        
        # 计算分钟差
        current_total_min = current_hour * 60 + current_min
        start_total_min = start_hour * 60 + start_min
        
        # 处理跨日
        if start_hour == 0:  # 00:30的情况
            if current_hour >= 12:  # 如果当前是下午/晚上，说明是前一天
                current_total_min -= 24 * 60
        
        diff = current_total_min - start_total_min
        return 0 <= diff <= 1  # 允许1分钟延迟启动
        
    def _should_stop_now(self) -> bool:
        """检查是否应该立即停止"""
        current_time = self._get_beijing_time()
        current_hour_min = current_time.strftime("%H:%M")
        
        # 在08:30停止，允许前后1分钟误差
        end_parts = self.end_time.split(":")
        end_hour, end_min = int(end_parts[0]), int(end_parts[1])
        
        current_parts = current_hour_min.split(":")
        current_hour, current_min = int(current_parts[0]), int(current_parts[1])
        
        # 计算分钟差
        current_total_min = current_hour * 60 + current_min
        end_total_min = end_hour * 60 + end_min
        
        diff = current_total_min - end_total_min
        return -1 <= diff <= 1
        
    def _save_status(self, status: dict):
        """保存调度状态"""
        try:
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save status: {e}")
            
    def _load_status(self) -> dict:
        """加载调度状态"""
        try:
            with open(self.status_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
        except Exception as e:
            logger.error(f"Failed to load status: {e}")
            return {}
            
    def _start_llm_process(self):
        """启动LLM响应生成进程"""
        if self.process and self.process.poll() is None:
            logger.warning("LLM process already running")
            return
            
        try:
            logger.info("Starting LLM response generation process...")
            
            # 默认启用断点续连（不需要额外参数）
            cmd = [sys.executable, "llm_response_generator.py"]
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.is_running = True
            
            # 保存状态
            self._save_status({
                "started_at": self._get_beijing_time().isoformat(),
                "pid": self.process.pid,
                "status": "running"
            })
            
            logger.info(f"LLM process started with PID: {self.process.pid}")
            
            # 启动输出监控线程
            threading.Thread(target=self._monitor_process_output, daemon=True).start()
            
        except Exception as e:
            logger.error(f"Failed to start LLM process: {e}")
            self.is_running = False
            
    def _monitor_process_output(self):
        """监控进程输出"""
        if not self.process:
            return
            
        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    # 转发输出到日志，去掉多余换行
                    logger.info(f"LLM: {line.rstrip()}")
                    
                # 检查是否需要停止
                if self.should_stop:
                    break
                    
        except Exception as e:
            logger.error(f"Error monitoring process output: {e}")
            
    def _stop_llm_process(self):
        """停止LLM响应生成进程"""
        if not self.process or self.process.poll() is not None:
            logger.info("No LLM process to stop")
            self.is_running = False
            return
            
        try:
            logger.info(f"Stopping LLM process (PID: {self.process.pid})...")
            
            # 尝试优雅关闭
            self.process.terminate()
            
            # 等待进程结束，最多等30秒
            try:
                self.process.wait(timeout=30)
                logger.info("LLM process stopped gracefully")
            except subprocess.TimeoutExpired:
                logger.warning("LLM process didn't stop gracefully, forcing kill...")
                self.process.kill()
                self.process.wait()
                logger.info("LLM process killed")
                
        except Exception as e:
            logger.error(f"Error stopping LLM process: {e}")
        finally:
            self.process = None
            self.is_running = False
            
            # 更新状态
            self._save_status({
                "stopped_at": self._get_beijing_time().isoformat(),
                "status": "stopped"
            })
            
    def _check_and_start(self):
        """检查并启动任务"""
        if self.should_stop:
            return
            
        beijing_time = self._get_beijing_time()
        logger.info(f"Checking schedule at Beijing time: {beijing_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if not self._is_discount_time():
            logger.info("Not in discount time window (00:30-08:30 Beijing time)")
            if self.is_running:
                logger.info("Stopping LLM process - outside discount window")
                self._stop_llm_process()
            return
            
        if self._should_stop_now():
            logger.info("Reached end time (08:30), stopping LLM process")
            self._stop_llm_process()
            return
            
        if not self.is_running:
            if self._should_start_now() or self._is_discount_time():
                logger.info("Starting LLM process - in discount window")
                self._start_llm_process()
            else:
                logger.info("In discount window but not start time yet")
        else:
            logger.info("LLM process already running")
            
    def run_scheduler(self):
        """运行调度器"""
        logger.info("Starting DeepSeek scheduler...")
        logger.info(f"Discount window: {self.start_time}-{self.end_time} Beijing time")
        
        # 每分钟检查一次
        schedule.every().minute.do(self._check_and_start)
        
        # 立即检查一次
        self._check_and_start()
        
        try:
            while not self.should_stop:
                schedule.run_pending()
                time.sleep(30)  # 每30秒检查一次调度
                
                # 检查进程状态
                if self.is_running and self.process and self.process.poll() is not None:
                    logger.warning("LLM process ended unexpectedly")
                    self.is_running = False
                    
                    # 如果在优惠时段内且进程意外结束，重启
                    if self._is_discount_time() and not self._should_stop_now():
                        logger.info("Restarting LLM process...")
                        self._start_llm_process()
                        
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            logger.info("Shutting down scheduler...")
            self._stop_llm_process()
            logger.info("Scheduler stopped")
            
    def run_once(self):
        """运行一次检查（用于测试）"""
        beijing_time = self._get_beijing_time()
        logger.info(f"Current Beijing time: {beijing_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Is discount time: {self._is_discount_time()}")
        logger.info(f"Should start now: {self._should_start_now()}")
        logger.info(f"Should stop now: {self._should_stop_now()}")
        
        if self._is_discount_time() and not self._should_stop_now():
            logger.info("Would start LLM process now")
            self._start_llm_process()
            
            # 等待用户中断或到达停止时间
            try:
                while not self.should_stop and not self._should_stop_now():
                    time.sleep(60)
                    if self.process and self.process.poll() is not None:
                        logger.info("LLM process completed")
                        break
            except KeyboardInterrupt:
                logger.info("Manual interrupt")
            finally:
                self._stop_llm_process()
        else:
            logger.info("Not in valid time window, skipping execution")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="DeepSeek错峰优惠时段调度器")
    parser.add_argument("--run-once", action="store_true", 
                       help="只运行一次（用于测试）")
    parser.add_argument("--check-time", action="store_true",
                       help="只检查时间状态")
    
    args = parser.parse_args()
    
    scheduler = DeepSeekScheduler()
    
    if args.check_time:
        beijing_time = scheduler._get_beijing_time()
        print(f"当前北京时间: {beijing_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"是否在优惠时段 (00:30-08:30): {scheduler._is_discount_time()}")
        print(f"是否应该启动: {scheduler._should_start_now()}")
        print(f"是否应该停止: {scheduler._should_stop_now()}")
        return
    
    if args.run_once:
        scheduler.run_once()
    else:
        scheduler.run_scheduler()

if __name__ == "__main__":
    main()
