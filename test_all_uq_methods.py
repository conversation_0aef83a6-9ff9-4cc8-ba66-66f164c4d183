#!/usr/bin/env python3
"""
Test all UQ methods on counterfactual responses
"""

import sys
import logging
import time
from typing import List, Dict, Any
from pymongo import MongoClient

sys.path.append('.')

# Import all UQ methods
from uq_methods.implementations.luq import LUQUQ
from uq_methods.implementations.semantic_entropy import SemanticEntropyNLIUQ
from uq_methods.implementations.num_sets import NumSetsUQ
from uq_methods.implementations.embedding_qwen import EmbeddingQwenUQ
from uq_methods.implementations.embedding_e5 import EmbeddingE5UQ
from uq_methods.implementations.eigval_laplacian import EigValLaplacianNLIUQ
from uq_methods.implementations.eccentricity import EccentricityNLIEntailUQ

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def get_counterfactual_sample() -> List[str]:
    """获取counterfactual样本"""
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        collection = db["response_collections"]
        
        pipeline = [
            {"$match": {"task_name": "counterfactual_qa", "dataset_source": "counterfactual_data"}},
            {"$group": {
                "_id": {"category": "$category", "row_index": "$row_index", "prompt_seed": "$prompt_seed"},
                "responses": {"$push": "$parsed_answer"},
                "count": {"$sum": 1}
            }},
            {"$match": {"count": {"$gte": 3}}},  # 至少3个响应
            {"$limit": 1}
        ]
        
        result = list(collection.aggregate(pipeline))
        client.close()
        
        if result:
            group = result[0]
            responses = group["responses"][:3]  # 取前3个响应
            category = group["_id"]["category"]
            row_index = group["_id"]["row_index"]
            
            log.info(f"获取样本: {category}, 问题{row_index}, {len(responses)}个响应")
            return responses
        else:
            log.error("未找到counterfactual样本")
            return []
            
    except Exception as e:
        log.error(f"获取样本失败: {e}")
        return []


def test_uq_method(method_name: str, method_class, responses: List[str]) -> Dict[str, Any]:
    """测试单个UQ方法"""
    log.info(f"\n🧪 测试 {method_name}")
    log.info("=" * 50)
    
    try:
        start_time = time.time()
        
        # 初始化方法
        if method_name == "LUQUQ":
            uq_method = method_class(verbose=False)
        elif method_name in ["SemanticEntropyNLIUQ", "NumSetsUQ", "EigValLaplacianNLIUQ", "EccentricityNLIEntailUQ"]:
            uq_method = method_class(verbose=False)
        else:
            uq_method = method_class()
        
        # 计算不确定性
        result = uq_method.compute_uncertainty(responses)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if 'error' in result:
            log.error(f"❌ {method_name} 失败: {result['error']}")
            return {
                'method': method_name,
                'status': 'failed',
                'error': result['error'],
                'processing_time': processing_time
            }
        else:
            uncertainty_score = result.get('uncertainty_score', 'N/A')
            log.info(f"✅ {method_name} 成功")
            log.info(f"   不确定性分数: {uncertainty_score}")
            log.info(f"   处理时间: {processing_time:.2f}秒")
            
            return {
                'method': method_name,
                'status': 'success',
                'uncertainty_score': uncertainty_score,
                'processing_time': processing_time,
                'full_result': result
            }
            
    except Exception as e:
        log.error(f"❌ {method_name} 异常: {str(e)}")
        return {
            'method': method_name,
            'status': 'error',
            'error': str(e),
            'processing_time': 0
        }


def main():
    """主函数"""
    log.info("🧪 测试所有UQ方法在Counterfactual数据上的表现")
    log.info("=" * 80)
    
    # 获取测试数据
    responses = get_counterfactual_sample()
    if not responses:
        log.error("❌ 无法获取测试样本")
        return
    
    log.info(f"📊 测试数据: {len(responses)} 个响应")
    for i, resp in enumerate(responses):
        log.info(f"   响应 {i+1}: {len(resp)} 字符")
    
    # 定义要测试的UQ方法
    uq_methods = [
        ("LUQUQ", LUQUQ),
        ("SemanticEntropyNLIUQ", SemanticEntropyNLIUQ),
        ("NumSetsUQ", NumSetsUQ),
        ("EmbeddingQwenUQ", EmbeddingQwenUQ),
        ("EmbeddingE5UQ", EmbeddingE5UQ),
        ("EigValLaplacianNLIUQ", EigValLaplacianNLIUQ),
        ("EccentricityNLIEntailUQ", EccentricityNLIEntailUQ),
    ]
    
    # 测试所有方法
    results = []
    total_start_time = time.time()
    
    for method_name, method_class in uq_methods:
        result = test_uq_method(method_name, method_class, responses)
        results.append(result)
    
    total_time = time.time() - total_start_time
    
    # 汇总结果
    log.info("\n" + "=" * 80)
    log.info("📊 UQ方法测试结果汇总")
    log.info("=" * 80)
    
    successful_methods = [r for r in results if r['status'] == 'success']
    failed_methods = [r for r in results if r['status'] in ['failed', 'error']]
    
    log.info(f"✅ 成功: {len(successful_methods)}/{len(results)} 个方法")
    log.info(f"❌ 失败: {len(failed_methods)}/{len(results)} 个方法")
    log.info(f"⏱️  总耗时: {total_time:.2f}秒")
    
    # 详细结果表格
    print(f"\n{'方法名':<25} {'状态':<8} {'不确定性分数':<15} {'耗时(秒)':<10}")
    print("-" * 70)
    
    for result in results:
        method = result['method']
        status = result['status']
        
        if status == 'success':
            score = f"{result['uncertainty_score']:.4f}"
            time_str = f"{result['processing_time']:.2f}"
            status_str = "✅成功"
        else:
            score = "N/A"
            time_str = f"{result['processing_time']:.2f}"
            status_str = "❌失败"
        
        print(f"{method:<25} {status_str:<8} {score:<15} {time_str:<10}")
    
    # 显示失败原因
    if failed_methods:
        log.info(f"\n❌ 失败方法详情:")
        for result in failed_methods:
            log.info(f"   {result['method']}: {result.get('error', '未知错误')}")
    
    # 不确定性分数排序
    if successful_methods:
        log.info(f"\n📈 不确定性分数排序 (从高到低):")
        sorted_methods = sorted(successful_methods, 
                              key=lambda x: x['uncertainty_score'], reverse=True)
        for i, result in enumerate(sorted_methods, 1):
            score = result['uncertainty_score']
            method = result['method']
            log.info(f"   {i}. {method}: {score:.4f}")
    
    log.info(f"\n🎉 所有UQ方法测试完成!")


if __name__ == "__main__":
    main()
