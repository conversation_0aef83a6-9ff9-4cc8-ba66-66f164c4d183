# LLM响应生成器 - 进度管理功能

## 功能概述

本系统现在支持进度检查和断点续传功能，确保在程序中断后可以继续处理，避免重复计算。

## 主要功能

### 1. 自动进度检查
- 程序启动时自动检查现有进度
- 跳过已完成的项目和尝试
- 从上次中断的地方继续处理

### 2. 进度监控工具
- 查看整体处理进度
- 检查特定运行的详细信息
- 监控特定数据集的完成情况

### 3. 灵活的启动选项
- 支持命令行参数控制
- 可选择是否恢复现有进度
- 可调整每个变体的尝试次数

## 使用方法

### 基本运行
```bash
# 正常运行（自动恢复进度）
python llm_response_generator.py

# 指定尝试次数
python llm_response_generator.py --attempts 10

# 不恢复进度，创建新运行
python llm_response_generator.py --no-resume

# 只检查进度，不运行生成
python llm_response_generator.py --check-progress
```

### 进度检查工具
```bash
# 检查整体进度
python progress_checker.py overall

# 检查特定运行
python progress_checker.py run <run_id>

# 检查特定数据集
python progress_checker.py dataset twitter_sentiment
python progress_checker.py dataset pytorch_commits
```

## 进度管理机制

### 1. 运行ID (Run ID)
- 每个完整的处理会话都有唯一的运行ID
- 用于标识和跟踪特定的处理批次
- 支持多个运行并行或串行执行

### 2. 任务ID (Task ID)
- 格式: `task_{dataset_source}_{item_id}_{prompt_variant}`
- 唯一标识每个数据项和prompt变体的组合
- 用于检查特定任务的完成状态

### 3. 尝试编号 (Attempt Number)
- 每个任务可以有多次尝试
- 从1开始编号
- 支持从上次中断的尝试继续

## 数据库结构

### 进度跟踪字段
- `run_id`: 运行标识符
- `task_id`: 任务标识符
- `task_attempt_prompt`: 当前尝试编号
- `task_attempt_total`: 总尝试计数
- `execution_timestamp`: 执行时间戳

### 查询示例
```javascript
// 查找特定运行的所有记录
db.response.find({"run_id": "your-run-id"})

// 统计每个任务的尝试次数
db.response.aggregate([
  {"$match": {"run_id": "your-run-id"}},
  {"$group": {
    "_id": "$task_id",
    "attempts": {"$sum": 1}
  }}
])
```

## 错误处理和恢复

### 1. 网络中断
- 程序会记录已完成的尝试
- 重启后从上次中断的地方继续
- 不会重复处理已完成的部分

### 2. API限制
- 如果遇到API限制，程序会等待后重试
- 已完成的尝试会被保存
- 不会丢失已处理的数据

### 3. 程序崩溃
- 所有已完成的尝试都会保存到MongoDB
- 重启后自动检测并跳过已完成的部分
- 支持手动指定运行ID继续处理

## 监控和调试

### 1. 日志文件
- 详细的操作日志保存在 `llm_response_generator.log`
- 包含进度检查、跳过项目、错误处理等信息
- 便于调试和监控

### 2. 进度统计
- 实时显示跳过和处理的项目数量
- 显示每个任务的尝试进度
- 提供完成率统计

### 3. 性能优化
- 避免重复计算提高效率
- 支持大规模数据处理
- 内存使用优化

## 注意事项

1. **MongoDB连接**: 确保MongoDB服务正在运行
2. **API密钥**: 确保设置了正确的API密钥
3. **数据文件**: 确保CSV数据文件存在
4. **磁盘空间**: 确保有足够的存储空间
5. **网络连接**: 确保网络连接稳定

## 故障排除

### 常见问题
1. **进度检查失败**: 检查MongoDB连接
2. **重复处理**: 检查run_id是否正确
3. **内存不足**: 减少并发处理数量
4. **API限制**: 增加重试间隔时间

### 调试命令
```bash
# 检查MongoDB连接
python -c "from pymongo import MongoClient; print(MongoClient('mongodb://localhost:27017/').server_info())"

# 检查日志文件
tail -f llm_response_generator.log

# 检查数据库状态
python progress_checker.py overall
```


