#!/usr/bin/env python3
"""
NLI缓存管理工具
用于查看、管理和使用结构化的NLI缓存CSV文件
"""

import pandas as pd
import os
import argparse
from typing import List, Dict, Optional
import hashlib

NLI_CACHE_DIR = "cache"
NLI_CSV_CACHE_FILE = os.path.join(NLI_CACHE_DIR, "nli_results_cache.csv")

def get_text_hash(text: str) -> str:
    """生成文本的哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def load_nli_cache() -> pd.DataFrame:
    """加载NLI缓存DataFrame"""
    if os.path.exists(NLI_CSV_CACHE_FILE):
        return pd.read_csv(NLI_CSV_CACHE_FILE)
    else:
        print(f"NLI cache file not found: {NLI_CSV_CACHE_FILE}")
        return pd.DataFrame()

def show_cache_stats():
    """显示缓存统计信息"""
    df = load_nli_cache()
    if df.empty:
        print("No cache data available.")
        return
    
    print(f"=== NLI Cache Statistics ===")
    print(f"Total entries: {len(df)}")
    print(f"Unique text1 entries: {df['text1_hash'].nunique()}")
    print(f"Unique text2 entries: {df['text2_hash'].nunique()}")
    print(f"Models used: {', '.join(df['model_name'].unique())}")
    print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    print(f"\nEntries by model:")
    print(df['model_name'].value_counts())
    
    print(f"\nSimilarity score statistics:")
    print(df['similarity_score'].describe())

def search_cache(text1: str = None, text2: str = None, model_name: str = None, 
                similarity_range: tuple = None) -> pd.DataFrame:
    """搜索缓存中的条目"""
    df = load_nli_cache()
    if df.empty:
        return df
    
    # 按条件过滤
    if text1:
        text1_hash = get_text_hash(text1)
        df = df[df['text1_hash'] == text1_hash]
    
    if text2:
        text2_hash = get_text_hash(text2)
        df = df[df['text2_hash'] == text2_hash]
    
    if model_name:
        df = df[df['model_name'] == model_name]
    
    if similarity_range:
        min_sim, max_sim = similarity_range
        df = df[(df['similarity_score'] >= min_sim) & (df['similarity_score'] <= max_sim)]
    
    return df

def get_similarity_for_texts(text1: str, text2: str, model_name: str) -> Optional[float]:
    """获取两个文本的相似度分数（如果在缓存中）"""
    text1_hash = get_text_hash(text1)
    text2_hash = get_text_hash(text2)
    
    df = load_nli_cache()
    if df.empty:
        return None
    
    # 查找精确匹配
    match = df[
        (df['text1_hash'] == text1_hash) & 
        (df['text2_hash'] == text2_hash) & 
        (df['model_name'] == model_name)
    ]
    
    if not match.empty:
        return match.iloc[0]['similarity_score']
    
    # 查找反向匹配
    reverse_match = df[
        (df['text1_hash'] == text2_hash) & 
        (df['text2_hash'] == text1_hash) & 
        (df['model_name'] == model_name)
    ]
    
    if not reverse_match.empty:
        return reverse_match.iloc[0]['similarity_score']
    
    return None

def export_cache_subset(output_file: str, model_name: str = None, 
                       min_similarity: float = None, max_similarity: float = None):
    """导出缓存的子集"""
    df = load_nli_cache()
    if df.empty:
        print("No cache data to export.")
        return
    
    # 应用过滤条件
    if model_name:
        df = df[df['model_name'] == model_name]
    
    if min_similarity is not None:
        df = df[df['similarity_score'] >= min_similarity]
    
    if max_similarity is not None:
        df = df[df['similarity_score'] <= max_similarity]
    
    # 导出
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"Exported {len(df)} entries to {output_file}")

def merge_cache_files(cache_files: List[str], output_file: str):
    """合并多个缓存文件"""
    all_dfs = []
    
    for file in cache_files:
        if os.path.exists(file):
            df = pd.read_csv(file)
            all_dfs.append(df)
            print(f"Loaded {len(df)} entries from {file}")
        else:
            print(f"Warning: File not found: {file}")
    
    if not all_dfs:
        print("No valid cache files found.")
        return
    
    # 合并并去重
    merged_df = pd.concat(all_dfs, ignore_index=True)
    print(f"Total entries before deduplication: {len(merged_df)}")
    
    # 基于text1_hash, text2_hash, model_name去重
    merged_df = merged_df.drop_duplicates(
        subset=['text1_hash', 'text2_hash', 'model_name'],
        keep='last'  # 保留最新的条目
    )
    
    print(f"Total entries after deduplication: {len(merged_df)}")
    
    # 保存
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"Merged cache saved to {output_file}")

def cleanup_cache(keep_latest_n: int = None, remove_duplicates: bool = True):
    """清理缓存文件"""
    df = load_nli_cache()
    if df.empty:
        print("No cache data to clean.")
        return
    
    original_count = len(df)
    
    # 去重
    if remove_duplicates:
        df = df.drop_duplicates(
            subset=['text1_hash', 'text2_hash', 'model_name'],
            keep='last'
        )
        print(f"Removed {original_count - len(df)} duplicate entries")
    
    # 保留最新的N个条目
    if keep_latest_n and len(df) > keep_latest_n:
        df = df.sort_values('timestamp').tail(keep_latest_n)
        print(f"Kept only the latest {keep_latest_n} entries")
    
    # 保存清理后的缓存
    backup_file = NLI_CSV_CACHE_FILE + '.backup'
    if os.path.exists(NLI_CSV_CACHE_FILE):
        os.rename(NLI_CSV_CACHE_FILE, backup_file)
        print(f"Backup created: {backup_file}")
    
    df.to_csv(NLI_CSV_CACHE_FILE, index=False, encoding='utf-8-sig')
    print(f"Cleaned cache saved with {len(df)} entries")

def main():
    parser = argparse.ArgumentParser(description="NLI Cache Manager")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # 统计信息
    subparsers.add_parser('stats', help='Show cache statistics')
    
    # 搜索
    search_parser = subparsers.add_parser('search', help='Search cache entries')
    search_parser.add_argument('--model', type=str, help='Filter by model name')
    search_parser.add_argument('--min-sim', type=float, help='Minimum similarity score')
    search_parser.add_argument('--max-sim', type=float, help='Maximum similarity score')
    search_parser.add_argument('--limit', type=int, default=10, help='Limit results')
    
    # 导出
    export_parser = subparsers.add_parser('export', help='Export cache subset')
    export_parser.add_argument('output_file', help='Output CSV file')
    export_parser.add_argument('--model', type=str, help='Filter by model name')
    export_parser.add_argument('--min-sim', type=float, help='Minimum similarity score')
    export_parser.add_argument('--max-sim', type=float, help='Maximum similarity score')
    
    # 合并
    merge_parser = subparsers.add_parser('merge', help='Merge multiple cache files')
    merge_parser.add_argument('cache_files', nargs='+', help='Cache files to merge')
    merge_parser.add_argument('--output', required=True, help='Output merged file')
    
    # 清理
    cleanup_parser = subparsers.add_parser('cleanup', help='Cleanup cache')
    cleanup_parser.add_argument('--keep-latest', type=int, help='Keep only N latest entries')
    cleanup_parser.add_argument('--no-dedup', action='store_true', help='Skip duplicate removal')
    
    # 查询相似度
    query_parser = subparsers.add_parser('query', help='Query similarity for two texts')
    query_parser.add_argument('text1', help='First text')
    query_parser.add_argument('text2', help='Second text')
    query_parser.add_argument('model', help='Model name')
    
    args = parser.parse_args()
    
    if args.command == 'stats':
        show_cache_stats()
    
    elif args.command == 'search':
        similarity_range = None
        if args.min_sim is not None or args.max_sim is not None:
            similarity_range = (
                args.min_sim if args.min_sim is not None else 0.0,
                args.max_sim if args.max_sim is not None else 1.0
            )
        
        results = search_cache(
            model_name=args.model,
            similarity_range=similarity_range
        )
        
        if not results.empty:
            print(f"Found {len(results)} matching entries:")
            print(results.head(args.limit).to_string(index=False))
        else:
            print("No matching entries found.")
    
    elif args.command == 'export':
        export_cache_subset(
            args.output_file,
            model_name=args.model,
            min_similarity=args.min_sim,
            max_similarity=args.max_sim
        )
    
    elif args.command == 'merge':
        merge_cache_files(args.cache_files, args.output)
    
    elif args.command == 'cleanup':
        cleanup_cache(
            keep_latest_n=args.keep_latest,
            remove_duplicates=not args.no_dedup
        )
    
    elif args.command == 'query':
        similarity = get_similarity_for_texts(args.text1, args.text2, args.model)
        if similarity is not None:
            print(f"Cached similarity: {similarity:.4f}")
        else:
            print("Similarity not found in cache.")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()