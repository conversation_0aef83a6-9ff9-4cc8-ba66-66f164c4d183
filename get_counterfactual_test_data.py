#!/usr/bin/env python3
"""
获取counterfactual测试数据的脚本
如果MongoDB不可用，提供备用的测试数据
"""

import sys
import os
import logging
from typing import List, Tuple

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def get_counterfactual_test_sentences() -> List[Tuple[str, str]]:
    """
    获取counterfactual测试句子对
    首先尝试从MongoDB获取，失败则使用预定义的测试数据
    """
    
    # 方法1: 尝试从MongoDB获取
    try:
        from test_luqsentence_counterfactual import get_counterfactual_samples
        
        print("🔍 尝试从MongoDB获取counterfactual数据...")
        samples = get_counterfactual_samples(limit=2)
        
        if samples:
            sentence_pairs = []
            for sample in samples:
                responses = sample["responses"][:2]
                category = sample["category"]
                
                if len(responses) >= 2:
                    # 从每个响应中提取第一句话
                    sentences = []
                    for response in responses:
                        first_sentence = response.split('.')[0].strip()
                        if len(first_sentence) > 20:
                            sentences.append(first_sentence)
                    
                    if len(sentences) >= 2:
                        sentence_pairs.append((sentences[0], sentences[1]))
                        print(f"✅ 从MongoDB获取句子对 (类别: {category})")
            
            if sentence_pairs:
                return sentence_pairs
                
    except Exception as e:
        log.warning(f"无法从MongoDB获取数据: {e}")
    
    # 方法2: 使用预定义的测试数据
    print("📝 使用预定义的测试数据...")
    
    predefined_pairs = [
        (
            "The adoption of Europe-wide quarantine measures from 1350 would have profoundly altered demographic patterns across the continent",
            "Quarantine measures implemented in medieval Europe had significant impact on population distribution and social structures"
        ),
        (
            "The Dominican Order's preference for Byzantine artistic styles around 1300 would have changed the trajectory of Renaissance art",
            "Religious orders in the late medieval period had minimal influence on artistic development and cultural movements"
        ),
        (
            "Economic policies during the Black Death period focused primarily on trade restrictions and border controls",
            "Medieval European governments implemented comprehensive public health measures including isolation protocols"
        ),
        (
            "The rise of merchant guilds in Italian city-states created new forms of political organization",
            "Political power in medieval Italy remained concentrated exclusively in traditional feudal structures"
        ),
        (
            "Technological innovations in agriculture during the 14th century improved crop yields significantly",
            "Agricultural productivity declined throughout the late medieval period due to climate change"
        )
    ]
    
    print(f"✅ 准备了 {len(predefined_pairs)} 个预定义测试句子对")
    for i, (sent1, sent2) in enumerate(predefined_pairs, 1):
        print(f"   {i}. 句子1: {sent1[:80]}...")
        print(f"      句子2: {sent2[:80]}...")
    
    return predefined_pairs


def analyze_sentence_relationships(sentence_pairs: List[Tuple[str, str]]):
    """分析句子对的预期关系"""
    print(f"\n📊 句子对关系分析:")
    print(f"{'='*60}")
    
    expected_relationships = [
        "ENTAILMENT - 两句都描述隔离措施的影响",
        "CONTRADICTION - 一句说有影响，一句说影响很小",
        "NEUTRAL - 两句描述不同但相关的历史概念",
        "CONTRADICTION - 一句说权力分散，一句说权力集中",
        "CONTRADICTION - 一句说产量提高，一句说产量下降"
    ]
    
    for i, ((sent1, sent2), expected) in enumerate(zip(sentence_pairs, expected_relationships), 1):
        print(f"\n🧪 测试对 {i}:")
        print(f"   句子1: {sent1}")
        print(f"   句子2: {sent2}")
        print(f"   预期关系: {expected}")


def main():
    """主函数"""
    print("📊 获取counterfactual测试数据")
    print("="*50)
    
    # 获取测试数据
    sentence_pairs = get_counterfactual_test_sentences()
    
    if not sentence_pairs:
        print("❌ 无法获取任何测试数据")
        return
    
    print(f"\n✅ 成功获取 {len(sentence_pairs)} 个句子对")
    
    # 分析句子关系
    analyze_sentence_relationships(sentence_pairs)
    
    print(f"\n💡 使用建议:")
    print(f"   1. 这些句子对涵盖了不同的NLI关系类型")
    print(f"   2. 可以用来测试Ollama模型的NLI能力")
    print(f"   3. 与DeBERTa模型对比，评估一致性")
    
    return sentence_pairs


if __name__ == "__main__":
    main()
