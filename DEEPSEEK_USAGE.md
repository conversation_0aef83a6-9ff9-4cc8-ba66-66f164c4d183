# DeepSeek错峰任务调度使用指南

## 📋 概览

本系统支持在DeepSeek优惠时段（北京时间00:30-08:30）自动执行LLM不确定性分析任务，包含：

- **情感分析**: 200个样本 × 5个prompt × 6次重复 = 6,000次调用
- **代码探索**: 200个样本 × 5个prompt × 6次重复 = 6,000次调用  
- **反事实问答**: 10个样本 × 5个prompt × 6次重复 = 300次调用
- **总计**: 12,300次API调用

## 🚀 快速开始

### 1. 环境准备

```bash
# 设置API密钥
export DEEPSEEK_API_KEY="your-api-key-here"

# 安装依赖
pip install schedule

# 检查环境
python start_deepseek_task.py check
```

### 2. 查看任务概览

```bash
python start_deepseek_task.py summary
```

### 3. 测试模式（推荐先运行）

```bash
python start_deepseek_task.py test
```

## ⏰ 调度器使用

### 自动调度（推荐）

```bash
# 启动调度器，将在00:30-08:30自动执行任务
python start_deepseek_task.py schedule
```

调度器特性：
- ✅ 自动在北京时间00:30启动任务
- ✅ 自动在08:30停止任务  
- ✅ 支持断点续连
- ✅ 进程异常时自动重启
- ✅ 优雅停止和状态保存

### 立即执行

```bash
# 忽略时间限制，立即执行
python start_deepseek_task.py run
```

### 检查调度状态

```bash
python start_deepseek_task.py status
```

## 🔧 高级用法

### 直接使用调度器

```bash
# 启动调度器
python deepseek_scheduler.py

# 只运行一次（测试用）
python deepseek_scheduler.py --run-once

# 检查时间状态
python deepseek_scheduler.py --check-time
```

### 直接使用生成器

```bash
# 恢复执行（断点续连）
python llm_response_generator.py --resume

# 测试模式
python llm_response_generator.py --test-mode

# 检查进度
python llm_response_generator.py --check-progress

# 全新开始
python llm_response_generator.py --no-resume
```

## 📊 监控和日志

### 日志文件

- `deepseek_scheduler.log`: 调度器日志
- `llm_response_generator.log`: 任务执行日志
- `scheduler_status.json`: 调度器状态文件

### 进度监控

任务支持断点续连，会自动记录：
- 已处理的数据项
- 每个prompt的完成次数
- 运行ID和时间戳

### MongoDB存储

所有生成的响应保存到MongoDB：
- Database: `LLM-UQ`
- Collection: `response_collections`
- 测试Collection: `test_response`

## ⚡ 最佳实践

### 1. 错峰时段运行

```bash
# 推荐：在优惠时段前启动调度器
python start_deepseek_task.py schedule
```

### 2. 分阶段执行

```bash
# 1. 先测试
python start_deepseek_task.py test

# 2. 检查环境
python start_deepseek_task.py check

# 3. 启动调度器
python start_deepseek_task.py schedule
```

### 3. 监控执行

```bash
# 实时查看日志
tail -f deepseek_scheduler.log

# 检查状态
python start_deepseek_task.py status
```

### 4. 手动控制

- **停止**: 按`Ctrl+C`优雅停止
- **强制停止**: `kill -TERM <pid>`
- **紧急停止**: `kill -KILL <pid>`

## 🛠 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   export DEEPSEEK_API_KEY="sk-your-key-here"
   ```

2. **时区问题**
   - 系统使用北京时间UTC+8
   - 调度器会自动处理时区转换

3. **进程卡死**
   - 调度器会自动重启异常进程
   - 手动停止：`Ctrl+C`

4. **数据不足**
   - 检查数据文件是否存在
   - 确认数据格式正确

### 日志分析

```bash
# 查看调度器状态
grep "Beijing time" deepseek_scheduler.log | tail -10

# 查看任务进度  
grep "处理.*项目" llm_response_generator.log | tail -10

# 查看API调用
grep "LLM API call" llm_response_generator.log | tail -10
```

## 📈 性能预期

- **处理速度**: 每分钟约20-40次API调用
- **总耗时**: 约5-10小时（在8小时优惠窗口内）
- **成功率**: 支持自动重试，预期95%+成功率
- **存储**: 每次调用约2-5KB数据

## 🔒 安全注意

- ✅ API密钥通过环境变量设置
- ✅ 日志不包含敏感信息
- ✅ 支持优雅停止，避免数据丢失
- ✅ 自动状态保存和恢复
