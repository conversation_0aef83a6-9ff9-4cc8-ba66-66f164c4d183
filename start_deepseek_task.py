#!/usr/bin/env python3
"""
DeepSeek任务启动脚本
支持立即执行、调度执行、状态检查等功能
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime, timezone, timedelta

def get_beijing_time():
    """获取北京时间"""
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz)

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查API密钥
    api_key = os.getenv('DEEPSEEK_API_KEY')
    # api_key = "***********************************"
    if not api_key:
        print("❌ DEEPSEEK_API_KEY环境变量未设置")
        return False
    else:
        print(f"✅ API密钥已设置: {api_key[:10]}...{api_key[-4:]}")
    
    # 检查必要文件
    required_files = [
        'llm_response_generator.py',
        'config.yaml',
        'deepseek_scheduler.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file} 存在")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 检查数据文件
    data_files = [
        'sampled_semeval.csv',
        'sampled_commits.csv',
        'data/conterfactual/counterfactual_prompts.csv'
    ]
    
    print("\n📊 数据文件检查:")
    for file in data_files:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    lines = sum(1 for _ in f)
                print(f"✅ {file}: {lines} 行")
            except:
                print(f"⚠️  {file}: 无法读取行数")
        else:
            print(f"❌ {file}: 不存在")
    
    print(f"\n🕐 当前北京时间: {get_beijing_time().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return True

def run_immediate():
    """立即运行任务"""
    print("🚀 立即启动DeepSeek任务...")
    
    try:
        cmd = [sys.executable, "llm_response_generator.py"]
        subprocess.run(cmd, check=True)
        print("✅ 任务执行完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 任务执行失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  用户中断任务")
        return False
    
    return True

def run_test_mode():
    """运行测试模式"""
    print("🧪 启动测试模式...")
    
    try:
        cmd = [sys.executable, "llm_response_generator.py", "--test-mode"]
        subprocess.run(cmd, check=True)
        print("✅ 测试模式完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试模式失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
        return False
    
    return True

def run_scheduler():
    """运行调度器"""
    print("⏰ 启动DeepSeek错峰调度器...")
    print("📅 优惠时段: 北京时间 00:30-08:30")
    print("🔄 支持断点续连和自动重启")
    print("⏹️  按 Ctrl+C 停止调度器")
    
    try:
        cmd = [sys.executable, "deepseek_scheduler.py"]
        subprocess.run(cmd, check=True)
        print("✅ 调度器正常退出")
    except subprocess.CalledProcessError as e:
        print(f"❌ 调度器执行失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  用户停止调度器")
        return False
    
    return True

def check_schedule_status():
    """检查调度状态"""
    print("📊 检查调度状态...")
    
    try:
        cmd = [sys.executable, "deepseek_scheduler.py", "--check-time"]
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 状态检查失败: {e}")
        return False
    
    return True

def show_task_summary():
    """显示任务概览"""
    print("📋 任务配置概览:")
    print("="*50)
    
    tasks = [
        {
            "name": "情感分析 (Sentiment Analysis)",
            "dataset": "Twitter情感数据",
            "samples": "200个样本",
            "prompts": "5个prompt模板",
            "attempts": "每个prompt重复6次",
            "total": "200 × 5 × 6 = 6,000次调用"
        },
        {
            "name": "代码探索 (Explorative Coding)",
            "dataset": "PyTorch提交数据",
            "samples": "200个样本",
            "prompts": "5个prompt模板", 
            "attempts": "每个prompt重复6次",
            "total": "200 × 5 × 6 = 6,000次调用"
        },
        {
            "name": "反事实问答 (Counterfactual QA)",
            "dataset": "历史反事实问题",
            "samples": "10个样本",
            "prompts": "5个prompt模板",
            "attempts": "每个prompt重复6次",
            "total": "10 × 5 × 6 = 300次调用"
        }
    ]
    
    total_calls = 0
    for i, task in enumerate(tasks, 1):
        print(f"\n{i}. {task['name']}")
        print(f"   📊 数据集: {task['dataset']}")
        print(f"   🔢 样本数: {task['samples']}")
        print(f"   📝 模板数: {task['prompts']}")
        print(f"   🔁 重复次数: {task['attempts']}")
        print(f"   📈 预计调用: {task['total']}")
        
        # 提取数字计算总数
        import re
        numbers = re.findall(r'(\d+) × (\d+) × (\d+) = (\d+)', task['total'])
        if numbers:
            total_calls += int(numbers[0][3])
    
    print("\n" + "="*50)
    print(f"📊 总计: {total_calls:,} 次API调用")
    print(f"🕐 预计时间: {total_calls * 2 / 60:.0f}-{total_calls * 4 / 60:.0f} 分钟")
    print(f"💰 优惠时段: 每日 00:30-08:30 (北京时间)")
    print("="*50)

def main():
    parser = argparse.ArgumentParser(description="DeepSeek任务启动器")
    parser.add_argument("action", choices=[
        "check", "run", "test", "schedule", "status", "summary"
    ], help="操作类型")
    
    args = parser.parse_args()
    
    print("🤖 DeepSeek任务启动器")
    print("="*50)
    
    if args.action == "check":
        success = check_environment()
        if success:
            print("\n✅ 环境检查通过，可以开始执行任务")
        else:
            print("\n❌ 环境检查失败，请修复问题后重试")
            sys.exit(1)
            
    elif args.action == "summary":
        show_task_summary()
        
    elif args.action == "status":
        check_schedule_status()
        
    elif args.action == "test":
        if not check_environment():
            sys.exit(1)
        run_test_mode()
        
    elif args.action == "run":
        if not check_environment():
            sys.exit(1)
        run_immediate()
        
    elif args.action == "schedule":
        if not check_environment():
            sys.exit(1)
        run_scheduler()

if __name__ == "__main__":
    main()
