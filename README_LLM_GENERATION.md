# LLM响应生成器

这个工具用于从SemEval2017和PyTorch commits数据集中采样数据，调用LLM生成响应，并将完整结果保存到MongoDB。

## 功能特性

- **模块化设计**: 数据采样和LLM响应生成分离，支持独立运行
- **数据采样**: 从两个数据源各采样500条数据
- **多Prompt策略**: 支持only_answer、answer_then_reason、reasoning_then_answer三种策略
- **完整响应保存**: 保存LLM的完整响应和logprobs数据
- **结构化存储**: 按照标准数据表结构保存到MongoDB
- **CSV输出**: 生成采样数据的CSV文件供后续使用

## 文件结构

```
├── data_sampler.py              # 数据采样模块
├── llm_response_generator.py    # LLM响应生成模块
├── run_llm_generation.py        # 运行脚本（支持分离运行）
├── sampled_semeval.csv          # SemEval采样数据（生成）
├── sampled_commits.csv          # PyTorch commits采样数据（生成）
└── README_LLM_GENERATION.md     # 使用说明
```

## 模块说明

### 1. 数据采样模块 (`data_sampler.py`)
- **功能**: 从原始数据源采样数据并保存为CSV
- **输入**: 原始数据文件
- **输出**: `sampled_semeval.csv`, `sampled_commits.csv`
- **独立运行**: `python data_sampler.py`

### 2. LLM响应生成模块 (`llm_response_generator.py`)
- **功能**: 从CSV文件读取数据，调用LLM生成响应
- **输入**: 采样数据CSV文件
- **输出**: MongoDB LLM-UQ.response集合
- **独立运行**: `python llm_response_generator.py`

## 数据表结构

保存到MongoDB的数据包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| run_id | String | 唯一运行ID |
| task_id | String | 任务标识符 |
| dataset_source | String | 数据集来源 |
| task_category | String | 任务类别 |
| input_text | String | 输入文本 |
| reference_answer | String | 参考答案 |
| model_identifier | String | 模型标识符 |
| prompt_variant | String | Prompt策略 |
| prompt_template | String | 原始prompt模板 |
| final_prompt | String | 最终发送的prompt |
| generation_config | JSON | 生成参数 |
| task_attempt_prompt | Integer | 当前尝试序号 |
| task_attempt_total | Integer | 总尝试序号 |
| raw_response | String | 原始响应 |
| response_logprobs | JSON | Token概率数据 |
| raw_answer | String | 解析的答案 |
| parsed_answer | String | 结构化答案 |
| parsed_reason | String | 解析的推理 |
| execution_timestamp | Datetime | 执行时间戳 |

## 使用方法

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install openai pymongo pandas
```

### 2. 设置API密钥

```bash
export DASHSCOPE_API_KEY='your-api-key'
```

### 3. 运行方式

#### 方式1: 完整流程（推荐）
```bash
python run_llm_generation.py
```

#### 方式2: 仅数据采样
```bash
python run_llm_generation.py --sample
```

#### 方式3: 仅LLM响应生成
```bash
python run_llm_generation.py --generate
```

#### 方式4: 自定义采样数量
```bash
python run_llm_generation.py --semeval-size 300 --commits-size 400
```

#### 方式5: 独立运行模块
```bash
# 仅运行数据采样
python data_sampler.py

# 仅运行LLM响应生成
python llm_response_generator.py
```

## 数据源说明

### SemEval2017数据集
- **文件**: `data/SemEval2017-task4-test.subtask-A.english.csv`
- **格式**: CSV，包含id、label、text字段
- **任务**: 情感分析
- **采样**: 500条记录

### PyTorch Commits数据集
- **目录**: `data/pytorch_commits/`
- **格式**: JSON文件，每个文件包含一个commit信息
- **任务**: 代码分析
- **采样**: 500条记录

## Prompt模板

### Twitter情感分析模板
- `prompts/twitter_sentiment.txt`: 只返回答案
- `prompts/twitter_sentiment_reason.txt`: 答案+推理
- `prompts/twitter_sentiment_reason_first.txt`: 推理+答案

### Commits模块分析模板
- `prompts/commit_only_answer.txt`: 只返回模块名
- `prompts/commit_answer_then_reason.txt`: 模块名+推理
- `prompts/commit_reasoning_then_answer.txt`: 推理+模块名

## 输出文件

### CSV文件
- `sampled_semeval.csv`: SemEval采样数据
- `sampled_commits.csv`: PyTorch commits采样数据

### MongoDB
- **数据库**: LLM-UQ
- **集合**: response
- **内容**: 完整的LLM响应数据

## 工作流程

### 完整流程
1. **数据采样**: 从原始数据源采样并保存为CSV
2. **LLM调用**: 从CSV读取数据，调用LLM生成响应
3. **数据保存**: 将完整响应保存到MongoDB

### 分离流程
1. **步骤1**: 运行数据采样，生成CSV文件
2. **步骤2**: 运行LLM响应生成，从CSV读取数据

## 注意事项

1. **API调用**: 确保API密钥有效且有足够的配额
2. **MongoDB**: 确保MongoDB服务正在运行
3. **数据文件**: 确保数据源文件存在且可访问
4. **网络连接**: 确保能够访问DashScope API
5. **CSV依赖**: LLM响应生成需要先运行数据采样

## 错误处理

程序包含以下错误处理机制：

- API调用异常处理
- 文件读取异常处理
- MongoDB连接异常处理
- 数据解析异常处理
- CSV文件依赖检查

## 自定义配置

可以通过修改代码中的参数来自定义：

- 采样数量
- 模型名称
- 生成参数
- MongoDB连接配置
- Prompt模板路径

## 扩展性

代码设计为模块化结构，可以轻松扩展：

- 添加新的数据源
- 添加新的prompt策略
- 添加新的任务类型
- 修改数据表结构
- 分离更多功能模块
