[Unit]
Description=Network Monitor and Auto-Reconnect Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
ExecStart=/home/<USER>/repo/llm-uncertainty-1/network_monitor.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log

[Install]
WantedBy=multi-user.target
