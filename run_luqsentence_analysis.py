#!/usr/bin/env python3
"""
LUQSENTENCE Analysis Script
专门用于运行LUQSENTENCE方法对counterfactual数据的UQ分析
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(__file__))

# Create necessary directories
os.makedirs("logs", exist_ok=True)
os.makedirs("checkpoints", exist_ok=True)

from run_uq_analysis import UQAnalysisRunner

def setup_logging():
    """设置日志配置"""
    log_file = f"logs/luqsentence_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return log_file

def print_banner():
    """打印启动横幅"""
    print("=" * 80)
    print("🧠 LUQSENTENCE Counterfactual Analysis")
    print("=" * 80)
    print("📊 Sentence-Level LUQ Uncertainty Quantification")
    print("🎯 Target: Counterfactual QA responses")
    print("🔬 Method: LUQSENTENCE with complete sentence matching")
    print("=" * 80)

def print_config_info(config_path: str, test_mode: bool):
    """打印配置信息"""
    print(f"\n📋 Configuration:")
    print(f"   Config file: {config_path}")
    print(f"   Mode: {'TEST' if test_mode else 'FULL'}")
    print(f"   Method: LUQSENTENCE")
    print(f"   Target collection: UQ_result_LUQSENTENCE_counterfactual{'_test' if test_mode else ''}")

def check_prerequisites():
    """检查运行前提条件"""
    print(f"\n🔍 Checking prerequisites...")
    
    # Check MongoDB connection
    try:
        from pymongo import MongoClient
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        collection = db["response_collections"]
        
        # Count counterfactual responses
        count = collection.count_documents({
            "task_name": "counterfactual_qa", 
            "dataset_source": "counterfactual_data"
        })
        
        print(f"   ✅ MongoDB connection: OK")
        print(f"   ✅ Counterfactual responses: {count} found")
        
        if count == 0:
            print(f"   ⚠️  Warning: No counterfactual responses found!")
            return False
            
        client.close()
        
    except Exception as e:
        print(f"   ❌ MongoDB connection failed: {e}")
        return False
    
    # Check LUQSENTENCE method
    try:
        from uq_methods.implementations.luq_sentence import LUQSENTENCEUQ
        print(f"   ✅ LUQSENTENCE method: Available")
    except Exception as e:
        print(f"   ❌ LUQSENTENCE method not found: {e}")
        return False
    
    return True

def estimate_runtime(test_mode: bool):
    """估算运行时间"""
    if test_mode:
        print(f"\n⏱️  Estimated runtime: 5-10 minutes (test mode)")
    else:
        print(f"\n⏱️  Estimated runtime: 2-4 hours (full analysis)")
        print(f"   💡 Tip: Use tmux for long-running analysis")

def print_tmux_instructions():
    """打印tmux使用说明"""
    print(f"\n🖥️  Tmux Usage Instructions:")
    print(f"   1. Start new session: tmux new-session -d -s luqsentence")
    print(f"   2. Attach to session: tmux attach -t luqsentence")
    print(f"   3. Run analysis: python run_luqsentence_analysis.py")
    print(f"   4. Detach session: Ctrl+B, then D")
    print(f"   5. Check progress: tmux attach -t luqsentence")
    print(f"   6. Kill session: tmux kill-session -t luqsentence")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Run LUQSENTENCE analysis on counterfactual data")
    parser.add_argument("--test", "-t", action="store_true",
                       help="Run in test mode (limited data)")
    parser.add_argument("--config", "-c", 
                       help="Custom config file path")
    parser.add_argument("--check-only", action="store_true",
                       help="Only check prerequisites, don't run analysis")
    
    args = parser.parse_args()
    
    # Setup logging
    log_file = setup_logging()
    log = logging.getLogger(__name__)
    
    # Print banner
    print_banner()
    
    # Determine config file
    if args.config:
        config_path = args.config
    elif args.test:
        config_path = "configs/luqsentence_counterfactual_test.yaml"
    else:
        config_path = "configs/luqsentence_counterfactual_config.yaml"
    
    # Print config info
    print_config_info(config_path, args.test)
    
    # Check prerequisites
    if not check_prerequisites():
        print(f"\n❌ Prerequisites check failed. Please fix the issues above.")
        return 1
    
    if args.check_only:
        print(f"\n✅ Prerequisites check passed!")
        return 0
    
    # Estimate runtime
    estimate_runtime(args.test)
    
    # Print tmux instructions if not in test mode
    if not args.test:
        print_tmux_instructions()
    
    # Confirm before starting
    if not args.test:
        print(f"\n⚠️  This will run a full LUQSENTENCE analysis on all counterfactual data.")
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Analysis cancelled.")
            return 0
    
    print(f"\n🚀 Starting LUQSENTENCE analysis...")
    print(f"   Log file: {log_file}")
    
    try:
        # Run analysis
        runner = UQAnalysisRunner(config_path)
        runner.run_analysis()
        
        print(f"\n🎉 LUQSENTENCE analysis completed successfully!")
        print(f"   Results saved to MongoDB collection")
        print(f"   Log file: {log_file}")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Analysis interrupted by user")
        log.info("Analysis interrupted by user")
        return 1
        
    except Exception as e:
        print(f"\n❌ Analysis failed: {str(e)}")
        log.error(f"Analysis failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
