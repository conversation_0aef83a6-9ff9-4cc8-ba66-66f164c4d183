#!/bin/bash

# 网络监控脚本测试脚本

echo "=== 网络监控脚本测试 ==="

# 测试1: 检查网络连接
echo "测试1: 检查网络连接"
source network_monitor.sh
if check_network; then
    echo "✓ 网络连接正常"
else
    echo "✗ 网络连接异常"
fi

# 测试2: 检查WiFi接口
echo ""
echo "测试2: 检查WiFi接口"
WIFI_INTERFACE=$(ip link show | grep -E "wlx|wlan|wifi" | awk -F: '{print $2}' | tr -d ' ' | head -1)
if [ -n "$WIFI_INTERFACE" ]; then
    echo "✓ 找到WiFi接口: $WIFI_INTERFACE"
else
    echo "✗ 未找到WiFi接口"
fi

# 测试3: 检查NetworkManager状态
echo ""
echo "测试3: 检查NetworkManager状态"
if command -v nmcli > /dev/null 2>&1; then
    echo "✓ NetworkManager可用"
    echo "WiFi连接状态:"
    nmcli device status | grep wifi
else
    echo "✗ NetworkManager不可用"
fi

# 测试4: 检查natapp文件
echo ""
echo "测试4: 检查natapp文件"
if [ -f "/etc/natapp/natapp" ]; then
    echo "✓ natapp文件存在"
    ls -la /etc/natapp/natapp
else
    echo "✗ natapp文件不存在"
fi

# 测试5: 检查natapp进程
echo ""
echo "测试5: 检查natapp进程"
if pgrep -f "natapp" > /dev/null; then
    echo "✓ natapp进程正在运行"
    ps aux | grep natapp | grep -v grep
else
    echo "✗ natapp进程未运行"
fi

# 测试6: 检查ping功能
echo ""
echo "测试6: 检查ping功能"
if ping -c 1 -W 1 8.8.8.8 > /dev/null 2>&1; then
    echo "✓ 普通ping测试成功"
else
    echo "✗ 普通ping测试失败"
fi

if [ -n "$WIFI_INTERFACE" ]; then
    if ping -c 1 -W 1 -I "$WIFI_INTERFACE" 8.8.8.8 > /dev/null 2>&1; then
        echo "✓ 指定接口ping测试成功"
    else
        echo "✗ 指定接口ping测试失败"
    fi
fi

echo ""
echo "=== 测试完成 ==="
