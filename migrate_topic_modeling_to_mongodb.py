#!/usr/bin/env python3
"""
Migrate topic modeling results to MongoDB in the same format as llm_response_generation
"""

import json
import pandas as pd
from pymongo import MongoClient
from datetime import datetime
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def load_topic_modeling_data():
    """Load topic modeling input data and results"""
    base_path = "/home/<USER>/repo/llm-uncertainty-1/data/topic_modeling"
    
    # Load input data
    input_df = pd.read_csv(os.path.join(base_path, "topic-model-topwords-label.csv"))
    log.info(f"Loaded {len(input_df)} input rows")
    
    # Load results
    results_path = os.path.join(base_path, "results")
    result_files = [f for f in os.listdir(results_path) if f.endswith('.json') and not f.endswith('_summary.json')]
    
    results_data = {}
    for file in result_files:
        # Extract model name from filename: labels_20250813_114326_gpt-oss_20b.json
        raw_model_name = file.replace('labels_20250813_114326_', '').replace('.json', '')

        # Map filename model names to correct format
        model_name_mapping = {
            'gpt-oss_20b': 'gpt-oss:20b',
            'phi4_latest': 'phi4:latest'
        }
        model_name = model_name_mapping.get(raw_model_name, raw_model_name)

        with open(os.path.join(results_path, file), 'r') as f:
            results_data[model_name] = json.load(f)

        log.info(f"Loaded {len(results_data[model_name])} results for model {model_name}")
    
    return input_df, results_data


def create_mongodb_documents(input_df, results_data):
    """Convert topic modeling data to MongoDB format matching llm_response_generation structure"""
    documents = []
    
    # Group results by row_index and model
    grouped_results = {}
    for model_name, results in results_data.items():
        grouped_results[model_name] = {}
        for result in results:
            row_idx = result['row_index']
            if row_idx not in grouped_results[model_name]:
                grouped_results[model_name][row_idx] = []
            grouped_results[model_name][row_idx].append(result['label'])
    
    # Create documents for each row and model combination
    for idx, row in input_df.iterrows():
        for model_name in grouped_results.keys():
            if idx in grouped_results[model_name]:
                responses = grouped_results[model_name][idx]
                
                # Create a document for each response (to match the structure)
                for i, response in enumerate(responses):
                    # Map response index to proper prompt_seed format
                    prompt_seed = f"topic_{i+1:02d}" if i < 11 else f"topic_{(i % 11) + 1:02d}"

                    doc = {
                        "task_name": "topic_labeling",  # 修正任务名称
                        "dataset_source": "topic_model_labeling",
                        "llm_model": model_name,
                        "prompt_seed": prompt_seed,  # 使用正确的prompt_seed格式
                        "input_text": row['key_terms'],  # 直接使用key_terms作为input_text
                        "raw_answer": response,
                        "parsed_answer": response,  # For topic modeling, raw and parsed are the same
                        "row_index": int(idx),
                        "paper_name": row['paper_name'],
                        "doi": row['doi'],
                        "table_number": str(row['table_number']),  # Keep as string to handle 'Appendix' etc.
                        "topic_number": int(row['topic_number']),
                        "key_terms": row['key_terms'],
                        "original_label": row['original_label'],
                        "created_at": datetime.now(),
                        "data_source": "topic_modeling_migration"
                    }
                    documents.append(doc)
    
    log.info(f"Created {len(documents)} MongoDB documents")
    return documents


def insert_to_mongodb(documents):
    """Insert documents to MongoDB"""
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        collection = db["topic_modeling_responses"]  # 独立的collection
        
        # Check if topic_modeling data already exists
        existing_count = collection.count_documents({"task_name": "topic_modeling"})
        if existing_count > 0:
            log.warning(f"Found {existing_count} existing topic_modeling documents")
            response = input("Do you want to delete existing data and re-insert? (y/N): ")
            if response.lower() in ['y', 'yes']:
                result = collection.delete_many({"task_name": "topic_modeling"})
                log.info(f"Deleted {result.deleted_count} existing documents")
            else:
                log.info("Skipping insertion to avoid duplicates")
                client.close()
                return
        
        # Insert new documents
        result = collection.insert_many(documents)
        log.info(f"Inserted {len(result.inserted_ids)} documents to MongoDB")
        
        # Verify insertion
        total_count = collection.count_documents({"task_name": "topic_modeling"})
        log.info(f"Total topic_modeling documents in database: {total_count}")
        
        # Show sample grouping
        pipeline = [
            {"$match": {"task_name": "topic_modeling"}},
            {"$group": {
                "_id": {
                    "row_index": "$row_index",
                    "llm_model": "$llm_model"
                },
                "count": {"$sum": 1}
            }},
            {"$group": {
                "_id": "$_id.llm_model",
                "groups": {"$sum": 1},
                "avg_responses_per_group": {"$avg": "$count"}
            }}
        ]
        
        stats = list(collection.aggregate(pipeline))
        log.info("Data grouping statistics:")
        for stat in stats:
            log.info(f"  Model {stat['_id']}: {stat['groups']} groups, avg {stat['avg_responses_per_group']:.1f} responses per group")
        
        client.close()
        
    except Exception as e:
        log.error(f"Error inserting to MongoDB: {e}")
        raise


def main():
    """Main function"""
    log.info("🔄 Starting topic modeling data migration to MongoDB")
    
    # Load data
    input_df, results_data = load_topic_modeling_data()
    
    # Convert to MongoDB format
    documents = create_mongodb_documents(input_df, results_data)
    
    # Insert to MongoDB
    insert_to_mongodb(documents)
    
    log.info("✅ Topic modeling data migration completed!")
    log.info("📊 Data is now ready for UQ analysis")
    
    # Show next steps
    print("\n" + "="*60)
    print("📋 Next Steps for UQ Analysis:")
    print("="*60)
    print("1. Update configs/uq_analysis_config.yaml to include topic_modeling task")
    print("2. Run: python run_uq_analysis.py --config configs/uq_analysis_config.yaml")
    print("3. Results will be saved to UQ_result_topic_modeling collection")
    print("="*60)


if __name__ == "__main__":
    main()
