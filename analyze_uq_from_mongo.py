import os
import sys
import json
import argparse
import hashlib
from datetime import datetime, timezone
from typing import List, Dict, Any, Tuple, Optional

from pymongo import MongoClient
from tqdm import tqdm
import numpy as np

# Optional YAML config
try:
    import yaml  # type: ignore
except Exception:
    yaml = None  # Will error if used without dependency


def _apply_yaml_config(args):
    if not getattr(args, "config", None):
        return args
    if yaml is None:
        raise RuntimeError("PyYAML is required to use --config. Install via `pip install pyyaml`. ")
    with open(args.config, "r") as f:
        cfg = yaml.safe_load(f) or {}
    section = cfg.get("uq_analysis") or cfg  # allow either root or namespaced
    mongo = section.get("mongo", {})
    filters = section.get("filters", {})
    methods = section.get("methods")
    args.mongo_host = mongo.get("host", args.mongo_host)
    args.mongo_port = int(mongo.get("port", args.mongo_port))
    args.mongo_db = mongo.get("db", args.mongo_db)
    args.source_collection = mongo.get("source_collection", args.source_collection)
    args.results_collection = mongo.get("results_collection", args.results_collection)
    args.matrix_collection = mongo.get("matrix_collection", args.matrix_collection)
    args.embedding_cache_collection = mongo.get("embedding_cache_collection", args.embedding_cache_collection)
    args.dataset_source = filters.get("dataset_source", args.dataset_source)
    args.prompt_variant = filters.get("prompt_variant", args.prompt_variant)
    if isinstance(methods, list):
        args.methods = ",".join(methods)
    if "limit_groups" in section:
        args.limit_groups = int(section.get("limit_groups", args.limit_groups))
    return args

# UQ methods (exclude DegMat as requested)
from uq_methods.implementations.eig_val_laplacian_jaccard import (
    EigValLaplacianJaccardUQ,
)
from uq_methods.implementations.eccentricity_jaccard import (
    EccentricityJaccardUQ,
)
from uq_methods.implementations.eig_val_laplacian_nli_entail import (
    EigValLaplacianNLIUQ,
)
from uq_methods.implementations.eccentricity_nli_entail import (
    EccentricityNLIEntailUQ,
)
from uq_methods.implementations.num_sets import NumSetsUQ
from uq_methods.base import SemanticEntropyUQ

# GPT-OSS:20b NLI variants
from uq_methods.implementations.nli_gpt_oss_variants import (
    EigValLaplacianNLIUQ_GPT_OSS_20B,
    EccentricityNLIEntailUQ_GPT_OSS_20B,
    SemanticEntropyNLIUQ_GPT_OSS_20B,
    NumSetsUQ_GPT_OSS_20B,
    LUQUQ_GPT_OSS_20B,
    LUQSentenceUQ_GPT_OSS_20B,
    KernelLanguageEntropyUQ_GPT_OSS_20B
)


def md5_text(text: str) -> str:
    return hashlib.md5((text or "").encode("utf-8")).hexdigest()


def md5_list_str(items: List[str]) -> str:
    # stable hashing: JSON dump of list
    return hashlib.md5(json.dumps(items, ensure_ascii=False, sort_keys=False).encode("utf-8")).hexdigest()


def parse_message_id(task_id: Optional[str], dataset_source: str) -> Optional[str]:
    """Try to extract message id from task_id like:
    task_{dataset_source}_{item_id}_prompt_{num}_{prompt_variant}
    Return None if not found.
    """
    if not task_id or not dataset_source:
        return None
    prefix = f"task_{dataset_source}_"
    if not task_id.startswith(prefix):
        return None
    rest = task_id[len(prefix):]
    # rest expected: "{item_id}_prompt_{num}_{variant}"
    try:
        i = rest.index("_prompt_")
        return rest[:i]
    except ValueError:
        return None


def ensure_indexes(col_results, col_matrix, col_embed):
    try:
        col_results.create_index(
            [
                ("group_key.task_type", 1),
                ("group_key.dataset_source", 1),
                ("group_key.prompt_variant", 1),
                ("group_key.prompt_seed", 1),
                ("group_key.prompt_index", 1),
                ("group_key.message_id", 1),
                ("method.method_name", 1),
                ("meta.responses_hash", 1),
            ],
            name="uq_results_group_method_hash",
        )
    except Exception:
        pass
    try:
        col_matrix.create_index(
            [
                ("group_key.task_type", 1),
                ("group_key.dataset_source", 1),
                ("group_key.prompt_variant", 1),
                ("group_key.prompt_seed", 1),
                ("group_key.prompt_index", 1),
                ("group_key.message_id", 1),
            ],
            name="uq_matrix_group",
        )
    except Exception:
        pass
    try:
        col_embed.create_index(
            [("text_hash", 1), ("model_name", 1)], unique=True, name="embed_text_model"
        )
    except Exception:
        pass


# ---------------- Embedding methods are now moved into uq_methods/implementations ----------------
from uq_methods.implementations.embedding_qwen import EmbeddingQwenUQ
from uq_methods.implementations.embedding_e5 import EmbeddingE5UQ

def cosine_similarity_matrix(X: np.ndarray) -> np.ndarray:
    # X: n x d
    if X.size == 0:
        return np.eye(0)
    norms = np.linalg.norm(X, axis=1, keepdims=True) + 1e-12
    Xn = X / norms
    return Xn @ Xn.T


# ---------------- UQ runner ----------------

class UQRunner:
    def __init__(self, args):
        self.args = args
        self.mongo = MongoClient(f"mongodb://{args.mongo_host}:{args.mongo_port}/")
        self.db = self.mongo[args.mongo_db]
        self.col_src = self.db[args.source_collection]
        self.col_res = self.db[args.results_collection]
        self.col_mat = self.db[args.matrix_collection]
        self.col_embed = self.db[args.embedding_cache_collection]
        ensure_indexes(self.col_res, self.col_mat, self.col_embed)
        # Prepare methods
        self.methods = self._build_methods(args)

    def _build_methods(self, args) -> List[Tuple[str, Any]]:
        m: List[Tuple[str, Any]] = []
        # Jaccard family
        if "eigval_jaccard" in args.methods:
            m.append(("EigValLaplacian_Jaccard", EigValLaplacianJaccardUQ()))
        if "ecc_jaccard" in args.methods:
            m.append(("Eccentricity_Jaccard", EccentricityJaccardUQ()))
        # NLI entail family
        if "eigval_nli" in args.methods:
            m.append(("EigValLaplacian_NLI_Entail", EigValLaplacianNLIUQ()))
        if "ecc_nli" in args.methods:
            m.append(("Eccentricity_NLI_Entail", EccentricityNLIEntailUQ()))
        # GPT-OSS:20b NLI variants
        if "eigval_nli_gpt_oss" in args.methods:
            m.append(("EigValLaplacian_NLI_GPT-OSS-20B", EigValLaplacianNLIUQ_GPT_OSS_20B()))
        if "ecc_nli_gpt_oss" in args.methods:
            m.append(("Eccentricity_NLI_GPT-OSS-20B", EccentricityNLIEntailUQ_GPT_OSS_20B()))
        if "se_gpt_oss" in args.methods:
            m.append(("SemanticEntropy_NLI_GPT-OSS-20B", SemanticEntropyNLIUQ_GPT_OSS_20B()))
        if "numsets_gpt_oss" in args.methods:
            m.append(("NumSets_NLI_GPT-OSS-20B", NumSetsUQ_GPT_OSS_20B()))
        if "luq_gpt_oss" in args.methods:
            m.append(("LUQ_NLI_GPT-OSS-20B", LUQUQ_GPT_OSS_20B()))
        if "luq_sentence_gpt_oss" in args.methods:
            m.append(("LUQSentence_NLI_GPT-OSS-20B", LUQSentenceUQ_GPT_OSS_20B()))
        if "kle_gpt_oss" in args.methods:
            m.append(("KernelLanguageEntropy_NLI_GPT-OSS-20B", KernelLanguageEntropyUQ_GPT_OSS_20B()))
        # Semantic Entropy
        if "se" in args.methods:
            m.append(("SemanticEntropy", SemanticEntropyUQ()))
        # NumSets
        if "numsets" in args.methods:
            m.append(("NumSets", NumSetsUQ()))
        # Embeddings
        if not args.skip_embedding:
            if "embed_qwen" in args.methods:
                m.append(("Embedding_Qwen", None))
            if "embed_nv" in args.methods or "embed_e5" in args.methods:
                m.append(("E5", None))
        # Placeholders for KLE, LUQ, LofreeCP
        if "kle" in args.methods:
            m.append(("KLE", None))
        if "luq" in args.methods:
            m.append(("LUQ", None))
        if "lofreecp" in args.methods:
            m.append(("LofreeCP", None))
        return m

    def _group_cursor(self):
        match: Dict[str, Any] = {}
        if self.args.dataset_source:
            match["dataset_source"] = self.args.dataset_source
        if self.args.prompt_variant:
            match["prompt_variant"] = self.args.prompt_variant
        pipeline = [
            {"$match": match} if match else {"$match": {}},
            {
                "$group": {
                    "_id": {
                        "task_type": "$task_name",  # as requested
                        "dataset_source": "$dataset_source",
                        "prompt_variant": "$prompt_variant",
                        "prompt_seed": "$prompt_seed",
                        "prompt_index": "$prompt_index",
                    },
                    "count": {"$sum": 1},
                }
            },
            {"$sort": {"_id.dataset_source": 1, "_id.task_type": 1}},
        ]
        if self.args.limit_groups and self.args.limit_groups > 0:
            pipeline.append({"$limit": int(self.args.limit_groups)})
        return self.col_src.aggregate(pipeline, allowDiskUse=True)

    def _fetch_group_docs(self, gid: Dict[str, Any]) -> List[Dict[str, Any]]:
        q = {
            "task_name": gid["task_type"],
            "dataset_source": gid["dataset_source"],
            "prompt_variant": gid["prompt_variant"],
            "prompt_seed": gid["prompt_seed"],
            "prompt_index": gid["prompt_index"],
        }
        return list(self.col_src.find(q))

    def _build_group_key(self, docs: List[Dict[str, Any]]) -> Dict[str, Any]:
        d0 = docs[0]
        task_type = d0.get("task_name")
        dataset_source = d0.get("dataset_source")
        prompt_variant = d0.get("prompt_variant")
        prompt_seed = d0.get("prompt_seed")
        prompt_index = d0.get("prompt_index")
        task_id = d0.get("task_id")
        message_id = parse_message_id(task_id, dataset_source) or str(prompt_seed)
        input_text = d0.get("input_text")
        return {
            "task_type": task_type,
            "dataset_source": dataset_source,
            "prompt_variant": prompt_variant,
            "prompt_seed": prompt_seed,
            "prompt_index": prompt_index,
            "message_id": message_id,
            "input_text": input_text,
        }

    def _infer_reference_text(self, docs: Optional[List[Dict[str, Any]]]):
        # Try to infer reference (ground truth) from docs; falls back to None
        try:
            if docs:
                refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
                if refs:
                    from collections import Counter
                    return Counter(refs).most_common(1)[0][0]
        except Exception:
            pass
        return None

    def _save_result(self, group_key: Dict[str, Any], method_name: str, uq_value: Optional[float], metrics: Dict[str, Any], responses_hash: str, n_responses: int):
        doc = {
            "group_key": group_key,
            "method": {
                "method_name": method_name,
                "model_category": None,  # left blank as requested
                "method_params": {},
            },
            "outputs": {
                "uq_value": uq_value,
                "metrics": metrics or {},
            },
            "meta": {
                "n_responses": n_responses,
                "responses_hash": responses_hash,
            },
            "timestamps": {"created_at": datetime.now(timezone.utc)},
        }
        self.col_res.insert_one(doc)

    def _run_methods(self, group_key: Dict[str, Any], responses: List[str], responses_hash: str, docs: Optional[List[Dict[str, Any]]] = None):
        for (name, inst) in self.methods:
            try:
                reference_text = self._infer_reference_text(docs)
                if name == "Embedding_Qwen":
                    # Use method class directly to compute from responses
                    uq = EmbeddingQwenUQ()
                    uq.set_reference_text(reference_text)
                    result = uq.compute_uncertainty(responses)
                    self._save_result(group_key, name, result.get("uncertainty_score"), result, responses_hash, len(responses))
                elif name == "E5":
                    uq = EmbeddingE5UQ()
                    uq.set_reference_text(reference_text)
                    result = uq.compute_uncertainty(responses)
                    self._save_result(group_key, name, result.get("uncertainty_score"), result, responses_hash, len(responses))
                elif name in ("KLE", "LUQ", "LofreeCP"):
                    self._save_result(group_key, name, None, {"note": "not implemented"}, responses_hash, len(responses))
                else:
                    result = inst.compute_uncertainty(responses)
                    uq_value = result.get("uncertainty_score") or result.get("uncertainty") or result.get("score")
                    metrics = {k: v for k, v in result.items() if k not in ("uncertainty_score", "uncertainty", "score")}
                    self._save_result(group_key, name, uq_value, metrics, responses_hash, len(responses))
            except Exception as e:
                self._save_result(group_key, name, None, {"error": str(e)}, responses_hash, len(responses))

    def _build_and_save_matrix(self, group_key: Dict[str, Any], responses_hash: str):
        # Gather all methods for this group_key + responses_hash (latest batch just inserted)
        q = {"group_key": group_key, "meta.responses_hash": responses_hash}
        rows = list(self.col_res.find(q))
        scores = {r["method"]["method_name"]: r["outputs"].get("uq_value") for r in rows}
        doc = {
            "group_key": group_key,
            "scores": scores,
            "meta": {
                "n_methods": len(scores),
                "responses_hash": responses_hash,
            },
            "timestamps": {"created_at": datetime.now(timezone.utc)},
        }
        self.col_mat.insert_one(doc)

    def run(self):
        groups = list(self._group_cursor())
        if not groups:
            print("No groups found in source collection.")
            return
        print(f"Found {len(groups)} groups. Running UQ methods: {self.args.methods}")
        for g in tqdm(groups, desc="Groups"):
            gid = g["_id"]
            docs = self._fetch_group_docs(gid)
            # Collect parsed answers
            responses = [d.get("parsed_answer") for d in docs if d.get("parsed_answer")]
            if len(responses) < 2:
                continue
            group_key = self._build_group_key(docs)
            responses_hash = md5_list_str(responses)
            # Run methods and save results
            self._run_methods(group_key, responses, responses_hash, docs)
            # Build matrix for this group
            self._build_and_save_matrix(group_key, responses_hash)


def parse_args():
    p = argparse.ArgumentParser(description="Analyze UQ from MongoDB responses and save results.")
    p.add_argument("--mongo-host", default="localhost")
    p.add_argument("--mongo-port", type=int, default=27017)
    p.add_argument("--mongo-db", default="LLM-UQ")
    p.add_argument("--source-collection", default="response_collections", help="Mongo source collection with LLM responses")
    p.add_argument("--results-collection", default="UQ_results")
    p.add_argument("--matrix-collection", default="uq_matrix")
    p.add_argument("--embedding-cache-collection", default="embeddings_cache")
    p.add_argument(
        "--methods",
        default="eigval_jaccard,ecc_jaccard,eigval_nli,ecc_nli,se,numsets",
        help="Comma-separated methods: eigval_jaccard,ecc_jaccard,eigval_nli,ecc_nli,se,numsets,embed_qwen,embed_e5,kle,luq,lofreecp",
    )
    p.add_argument("--skip-embedding", action="store_true", help="Skip embedding-based methods (placeholders)")
    p.add_argument("--dataset-source", default=None)
    p.add_argument("--prompt-variant", default=None)
    p.add_argument("--limit-groups", type=int, default=0)
    p.add_argument("--config", default=None, help="Path to YAML config that can override CLI args")
    args = p.parse_args()
    _apply_yaml_config(args)
    args.methods = [m.strip() for m in args.methods.split(",") if m.strip()]
    return args


if __name__ == "__main__":
    args = parse_args()
    runner = UQRunner(args)
    runner.run()

