#!/usr/bin/env python3
"""
UQ Analysis Main Script
Analyzes qwen3-32b results using various UQ methods and stores results in MongoDB.
"""

import os
import sys
import yaml
import argparse
import logging
from typing import Dict, Any, List
from pymongo import MongoClient

# Add project root to path
sys.path.append(os.path.dirname(__file__))

# Create necessary directories
os.makedirs("logs", exist_ok=True)
os.makedirs("uq_analysis", exist_ok=True)

from uq_analysis.method_loader import UQMethodLoader
from uq_analysis.data_processor import DataProcessor
from uq_analysis.resume_manager import ResumeManager
from uq_analysis.progress_manager import ProgressManager, setup_logging

log = logging.getLogger(__name__)


class UQAnalysisRunner:
    """Main UQ analysis runner."""
    
    def __init__(self, config_path: str):
        """
        Initialize the UQ analysis runner.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self.load_config(config_path)
        self.method_loader = UQMethodLoader()
        self.data_processor = DataProcessor(self.config["mongodb"])
        self.resume_manager = ResumeManager(self.config["resume"])
        self.progress_manager = ProgressManager(self.config["analysis"])
        
        # Setup logging
        setup_logging(self.config["logging"])
        
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            log.info(f"Loaded configuration from {config_path}")
            return config
        except Exception as e:
            print(f"Failed to load config from {config_path}: {str(e)}")
            sys.exit(1)
    
    def initialize(self):
        """Initialize all components."""
        log.info("Initializing UQ analysis system...")
        
        # Discover UQ methods
        discovered_methods = self.method_loader.discover_methods()
        log.info(f"Discovered {len(discovered_methods)} UQ methods")
        
        # Connect to MongoDB
        self.data_processor.connect_mongodb()
        
        log.info("Initialization complete")
    
    def run_analysis(self):
        """Run the complete UQ analysis."""
        try:
            self.initialize()
            
            # Get enabled tasks
            enabled_tasks = [(name, config) for name, config in self.config["tasks"].items() 
                           if config.get("enabled", True)]
            
            if not enabled_tasks:
                log.warning("No enabled tasks found")
                return
            
            self.progress_manager.start_analysis(len(enabled_tasks))
            
            # Process each task
            for task_name, task_config in enabled_tasks:
                self.process_task(task_name, task_config)
            
            self.progress_manager.finish_analysis()
            log.info("UQ analysis completed successfully")
            
        except Exception as e:
            log.error(f"Analysis failed: {str(e)}")
            raise
        finally:
            self.cleanup()
    
    def process_task(self, task_name: str, task_config: Dict[str, Any]):
        """
        Process a single task.
        
        Args:
            task_name: Name of the task
            task_config: Task configuration
        """
        log.info(f"Processing task: {task_name}")
        
        # Get task parameters
        dataset_sources = task_config["dataset_sources"]
        output_collection_name = task_config["output_collection"]
        response_field = task_config["response_field"]
        
        # Load UQ methods
        enabled_methods = self.config["uq_methods"]["enabled_methods"]
        method_params = self.config["uq_methods"]["method_params"]
        uq_methods = self.method_loader.load_enabled_methods(enabled_methods, method_params)
        
        if not uq_methods:
            log.error(f"No UQ methods loaded for task {task_name}")
            return
        
        # Process each dataset source
        for dataset_source in dataset_sources:
            self.process_task_dataset(task_name, dataset_source, response_field, 
                                    output_collection_name, uq_methods)
    
    def process_task_dataset(self, task_name: str, dataset_source: str, 
                           response_field: str, output_collection_name: str,
                           uq_methods: Dict[str, Any]):
        """
        Process a task for a specific dataset source.
        
        Args:
            task_name: Name of the task
            dataset_source: Dataset source name
            response_field: Field to extract responses from
            output_collection_name: Name of output collection
            uq_methods: Dictionary of UQ method instances
        """
        log.info(f"Processing {task_name}/{dataset_source}")
        
        # Get output collection
        output_collection = self.data_processor.db[output_collection_name]
        
        # Create index for efficient querying
        try:
            output_collection.create_index([
                ("group_key.task_name", 1),
                ("group_key.dataset_source", 1),
                ("group_key.prompt_seed", 1)
            ], name="uq_analysis_index", background=True)
        except Exception:
            pass  # Index might already exist
        
        # Get groups to process
        limit = self.config["analysis"]["test_limit"] if self.config["analysis"]["test_mode"] else None
        groups = self.data_processor.get_task_groups(task_name, dataset_source, limit)
        
        if not groups:
            log.warning(f"No groups found for {task_name}/{dataset_source}")
            return
        
        # Load completed work for resume functionality
        method_names = list(uq_methods.keys())
        self.resume_manager.load_completed_work(output_collection, task_name, method_names)
        
        # Filter pending work
        pending_work = self.resume_manager.filter_pending_work(
            groups, method_names, output_collection_name)
        
        if not pending_work:
            log.info(f"All work completed for {task_name}/{dataset_source}")
            return
        
        # Start task progress tracking
        total_groups = len(pending_work)
        total_methods = len(method_names)
        self.progress_manager.start_task(f"{task_name}/{dataset_source}", 
                                       total_groups, total_methods)
        
        # Process groups
        for group_idx, (group, pending_methods) in enumerate(pending_work):
            group_key = self.data_processor.build_group_key(group["_id"])

            # Only compute UQ for pending methods
            pending_uq_methods = {name: uq_methods[name] for name in pending_methods}

            if not pending_uq_methods:
                # All methods already completed for this group
                skipped = len(method_names)
                successful = 0
                failed = 0

                for method_name in method_names:
                    self.progress_manager.log_skip(task_name, group_key, method_name)
            else:
                # Compute UQ for pending methods
                try:
                    # Extract llm_model from group data instead of config
                    llm_model = group["_id"].get("llm_model", self.config["analysis"]["llm_model"])
                    result = self.data_processor.compute_uq_for_group(
                        group, response_field, pending_uq_methods, llm_model)

                    if result:
                        # Save result to database
                        self.data_processor.save_or_update_result(result, output_collection)

                        # Count successful and failed computations
                        successful = result["metadata"]["successful_methods"]
                        failed = result["metadata"]["failed_methods"]
                        skipped = len(method_names) - len(pending_methods)

                        # Log individual method results
                        for method_name, method_result in result["uq_results"].items():
                            if method_result["status"] == "success":
                                uq_value = method_result["uq_value"]
                                self.progress_manager.log_success(
                                    task_name, group_key, method_name, uq_value)
                            else:
                                error = method_result.get("error", "Unknown error")
                                self.progress_manager.log_error(
                                    task_name, group_key, method_name, error)

                        # Mark work as completed in resume manager
                        for method_name in pending_methods:
                            self.resume_manager.mark_work_completed(
                                output_collection_name, group_key, method_name)
                    else:
                        # No result (not enough responses)
                        failed = len(pending_methods)
                        successful = 0
                        skipped = len(method_names) - len(pending_methods)

                        for method_name in pending_methods:
                            self.progress_manager.log_error(
                                task_name, group_key, method_name, "Not enough responses")

                except Exception as e:
                    # Error processing group
                    failed = len(pending_methods)
                    successful = 0
                    skipped = len(method_names) - len(pending_methods)

                    for method_name in pending_methods:
                        self.progress_manager.log_error(
                            task_name, group_key, method_name, str(e))

            # Update progress
            self.progress_manager.update_group_progress(
                f"{task_name}/{dataset_source}", group_idx, successful, failed, skipped)
        
        # Finish task
        self.progress_manager.finish_task(f"{task_name}/{dataset_source}")
    
    def cleanup(self):
        """Cleanup resources."""
        try:
            self.data_processor.disconnect_mongodb()
        except Exception as e:
            log.error(f"Error during cleanup: {str(e)}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run UQ analysis on qwen3-32b results")
    parser.add_argument("--config", "-c", 
                       default="configs/uq_analysis_config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--test", "-t", action="store_true",
                       help="Use test configuration")
    
    args = parser.parse_args()
    
    # Use test config if requested
    if args.test:
        config_path = "configs/uq_analysis_test.yaml"
    else:
        config_path = args.config
    
    # Run analysis
    runner = UQAnalysisRunner(config_path)
    runner.run_analysis()


if __name__ == "__main__":
    main()
