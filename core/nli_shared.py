"""
Shared access to a singleton CachedNLICalculator per model.
支持传统的Transformer模型、Ollama模型和vLLM模型
"""
from typing import Dict, Union
from core.nli_calculator import CachedNLICalculator, OllamaNLICalculator, VLLMNLICalculator

_nli_calculators: Dict[str, Union[CachedNLICalculator, OllamaNLICalculator, VLLMNLICalculator]] = {}

def get_nli_calculator(model_name: str, use_ollama: bool = False, use_vllm: bool = False,
                      vllm_host: str = "http://localhost:8000",
                      api_key: str = "token-abc123") -> Union[CachedNLICalculator, OllamaNLICalculator, VLLMNLICalculator]:
    """
    获取NLI计算器实例

    Args:
        model_name: 模型名称
        use_ollama: 是否使用Ollama模型
        use_vllm: 是否使用vLLM模型
        vllm_host: vLLM服务地址
        api_key: vLLM API密钥

    Returns:
        NLI计算器实例
    """
    if use_ollama and use_vllm:
        raise ValueError("Cannot use both Ollama and vLLM at the same time")

    cache_key = f"{'ollama_' if use_ollama else 'vllm_' if use_vllm else ''}{model_name}"

    if cache_key not in _nli_calculators:
        if use_ollama:
            _nli_calculators[cache_key] = OllamaNLICalculator(model_name, verbose=False)
        elif use_vllm:
            _nli_calculators[cache_key] = VLLMNLICalculator(model_name, verbose=False,
                                                          vllm_host=vllm_host, api_key=api_key)
        else:
            _nli_calculators[cache_key] = CachedNLICalculator(model_name, verbose=False)

    return _nli_calculators[cache_key]

def get_ollama_nli_calculator(model_name: str) -> OllamaNLICalculator:
    """获取Ollama NLI计算器的便捷方法"""
    return get_nli_calculator(model_name, use_ollama=True)

def get_vllm_nli_calculator(model_name: str, vllm_host: str = "http://localhost:8000",
                           api_key: str = "token-abc123") -> VLLMNLICalculator:
    """获取vLLM NLI计算器的便捷方法"""
    return get_nli_calculator(model_name, use_vllm=True, vllm_host=vllm_host, api_key=api_key)

