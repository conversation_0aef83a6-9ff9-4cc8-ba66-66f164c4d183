from pymongo import MongoC<PERSON>
from datetime import datetime, timezone
import os
import asyncio
from typing import List, Dict, Any, Optional
from openai import AsyncOpenAI


class MongoDBOptimizedClient:
    """Optimized Qwen client with MongoDB-based progress tracking and resume"""
    
    def __init__(
        self,
        api_key: str = None,
        model: str = "qwen-plus",
        base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1",
        mongo_config: Dict[str, Any] = None,
        max_concurrent: int = 50  # Increased for 500 query batches
    ):
        self.client = AsyncOpenAI(
            api_key=api_key or os.getenv("DASHSCOPE_API_KEY"),
            base_url=base_url
        )
        self.model = model
        self.semaphore = asyncio.Semaphore(max_concurrent)
        # Remove rate limiting for unlimited speed mode
        # self.rate_limiter = asyncio.Semaphore(qps_limit)
        
        # MongoDB setup
        self.mongo_config = mongo_config or {
            "host": "localhost",
            "port": 27017,
            "database": "commit_analysis",
            "collection": "responses"
        }
        self.mongo_client = MongoClient(f"mongodb://{self.mongo_config['host']}:{self.mongo_config['port']}/")
        self.db = self.mongo_client[self.mongo_config['database']]
        self.collection = self.db[self.mongo_config['collection']]
        
        # Default parameters for speed
        self.default_params = {
            "temperature": 0.7,
            "max_tokens": 256,
            "top_p": 0.9
        }
    
    def get_progress_for_commit(self, commit_sha: str, prompt_type: str) -> Dict[str, Any]:
        """Check progress for a specific commit and prompt type"""
        query = {
            "commit_sha": commit_sha,
            "prompt_type": prompt_type
        }
        
        existing_responses = list(self.collection.find(query, {"query_index": 1, "_id": 0}))
        existing_indices = {r["query_index"] for r in existing_responses}
        
        max_index = max(existing_indices) if existing_indices else 0
        completed_count = len(existing_indices)
        
        return {
            "commit_sha": commit_sha,
            "prompt_type": prompt_type,
            "completed_count": completed_count,
            "max_index": max_index,
            "completed_indices": sorted(existing_indices),
            "is_complete": completed_count >= 30,
            "remaining_count": max(0, 30 - completed_count),
            "next_start_index": max_index + 1
        }
    
    def get_all_progress(self, commit_sha: str) -> Dict[str, Any]:
        """Get progress for all prompt types for a commit"""
        prompt_types = ["single_word", "module_reasoning", "reasoning_module"]
        
        progress = {}
        for prompt_type in prompt_types:
            progress[prompt_type] = self.get_progress_for_commit(commit_sha, prompt_type)
        
        # Calculate overall progress
        total_completed = sum(p["completed_count"] for p in progress.values())
        total_required = 30 * len(prompt_types)
        
        return {
            "commit_sha": commit_sha,
            "prompt_types": progress,
            "total_completed": total_completed,
            "total_required": total_required,
            "overall_progress": f"{total_completed}/{total_required} ({(total_completed/total_required*100):.1f}%)"
        }
    
    def should_skip_commit_prompt_type(self, commit_sha: str, prompt_type: str) -> bool:
        """Check if we should skip this commit+prompt_type combination"""
        progress = self.get_progress_for_commit(commit_sha, prompt_type)
        return progress["is_complete"]
    
    def get_resume_info(self, commit_sha: str, prompt_type: str) -> Dict[str, Any]:
        """Get resume information for starting from the right index (1-30)"""
        progress = self.get_progress_for_commit(commit_sha, prompt_type)
        
        if progress["is_complete"]:
            return {"skip": True, "reason": "Already complete"}
        
        return {
            "skip": False,
            "start_index": progress["next_start_index"],  # 1-based indexing
            "remaining_count": progress["remaining_count"],
            "completed_indices": progress["completed_indices"]
        }
    
    async def generate_single_with_check(
        self,
        commit_data: Dict[str, Any],
        prompt_text: str,
        prompt_type: str,
        query_index: int,
        **kwargs
    ) -> Optional[str]:
        """Generate response only if not already processed"""
        
        # Check if already processed
        progress = self.get_progress_for_commit(commit_data["sha"], prompt_type)
        
        if query_index in progress["completed_indices"]:
            print(f"✅ Skipping {commit_data['sha'][:8]}_{prompt_type}_{query_index} - already processed")
            return None
        
        # Rate-limited API call - REMOVED for unlimited speed
        async with self.semaphore:
            # Removed rate limiter and sleep for maximum speed
            try:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user", "content": prompt_text}],
                    **{**self.default_params, **kwargs}
                )
                
                result = response.choices[0].message.content
                
                # Save to MongoDB
                self.save_response(
                    commit_data=commit_data,
                    prompt_type=prompt_type,
                    prompt_text=prompt_text,
                    response_text=result,
                    query_index=query_index,
                    model_name=self.model
                )
                
                print(f"🚀 Processed {commit_data['sha'][:8]}_{prompt_type}_{query_index}")
                return result
                
            except Exception as e:
                print(f"❌ Error processing {commit_data['sha'][:8]}_{prompt_type}_{query_index}: {e}")
                return None
    
    def save_response(
        self,
        commit_data: Dict[str, Any],
        prompt_type: str,
        prompt_text: str,
        response_text: str,
        query_index: int,
        model_name: str
    ):
        """Save response to MongoDB"""
        document = {
            "commit_sha": commit_data.get('sha'),
            "repo_name": commit_data.get('repo_name'),
            "author": commit_data.get('author'),
            "date": commit_data.get('date'),
            "message": commit_data.get('message'),
            "prompt_type": prompt_type,
            "prompt_text": prompt_text,
            "response_text": response_text,
            "model_name": model_name,
            "query_index": query_index,
            "timestamp": datetime.now(timezone.utc)
        }
        
        self.collection.insert_one(document)
    
    async def batch_generate_for_commit(
        self,
        commit_data: Dict[str, Any],
        prompts: List[str],
        prompt_type: str) -> List[str]:
        """Generate responses for a commit, starting from the right index"""
        
        # Get resume information
        resume_info = self.get_resume_info(commit_data["sha"], prompt_type)
        
        if resume_info["skip"]:
            print(f"🎯 Skipping complete commit+prompt_type: {commit_data['sha'][:8]}_{prompt_type}")
            return []
        
        start_index = resume_info["start_index"]
        remaining_prompts = prompts[start_index:]
        
        if not remaining_prompts:
            print(f"🎯 No remaining prompts for {commit_data['sha'][:8]}_{prompt_type}")
            return []
        
        print(f"🚀 Resuming {commit_data['sha'][:8]}_{prompt_type} from index {start_index}")
        print(f"📊 Remaining: {len(remaining_prompts)} prompts")
        print(f"📈 Current: {start_index}/30 ({(start_index/30*100):.1f}%)")
        
        results = []
        total_required = 30
        
        for i, prompt_text in enumerate(remaining_prompts):
            query_index = start_index + i + 1  # Convert to 1-based indexing (1-30)
            
            result = await self.generate_single_with_check(
                commit_data=commit_data,
                prompt_text=prompt_text,
                prompt_type=prompt_type,
                query_index=query_index
            )
            
            if result:
                results.append(result)
            
            # Real-time progress
            total_processed = start_index + i + 1
            progress_pct = (total_processed / total_required) * 100
            
            print(f"⚡ Speed mode: {total_processed}/30 ({progress_pct:.1f}%)")
        
        # Final progress summary
        final_pct = (start_index + len(results)) / total_required * 100
        print(f"✅ Completed {commit_data['sha'][:8]}_{prompt_type}: "
              f"{start_index + len(results)}/30 ({final_pct:.1f}%)")
        
        return results
    
    def print_progress_summary(self, commit_sha: str):
        """Print detailed progress summary"""
        progress = self.get_all_progress(commit_sha)
        
        print(f"\n📊 Progress Summary for {commit_sha[:8]}:")
        print(f"Overall: {progress['overall_progress']}")
        
        for prompt_type, data in progress["prompt_types"].items():
            status = "✅ COMPLETE" if data["is_complete"] else f"🔄 {data['remaining_count']} remaining"
            print(f"  {prompt_type}: {data['completed_count']}/30 - {status}")
    
    def get_unprocessed_commits(self, commits: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter commits that still need processing"""
        unprocessed = []
        
        for commit in commits:
            progress = self.get_all_progress(commit["sha"])
            if progress["total_completed"] < 90:  # 3 prompt types × 30 queries
                unprocessed.append(commit)
        
        return unprocessed
    
    async def close(self):
        """Close connections"""
        self.mongo_client.close()
        await self.client.close()