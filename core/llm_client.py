from openai import AsyncOpenAI
from typing import List, Dict, Any, Optional, AsyncIterator
import os
from dataclasses import dataclass


@dataclass
class LLMResponse:
    content: str
    usage: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    finish_reason: Optional[str] = None


class QwenClient:
    """Qwen client for LLM interactions with streaming support"""
    
    def __init__(
        self,
        api_key: str = None,
        model: str = "qwen3-32b",
        base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1",  # 阿里云API地址
        nli_model: str = "qwen3-latest",  # NLI推理专用模型
        **default_params
    ):
        self.client = AsyncOpenAI(
            api_key=api_key or os.getenv("QWEN_API_KEY", "dummy_key"),
            base_url=base_url
        )
        self.model = model
        self.nli_model = nli_model
        self.default_params = {
            "temperature": 0.7,
            "max_tokens": 1024,
            **default_params
        }
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        n: int = 1,
        stream: bool = False,
        **kwargs
    ) -> List[str]:
        """生成多个响应用于UQ分析，支持流式输出"""
        model = model or self.model
        params = {**self.default_params, **kwargs}
        responses = []
        
        for i in range(n):
            if stream:
                # 流式输出收集完整响应
                response_content = ""
                async for chunk in self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    stream=True,
                    **params
                ):
                    if chunk.choices[0].delta.content:
                        response_content += chunk.choices[0].delta.content
                responses.append(response_content)
            else:
                # 非流式输出
                response = await self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    stream=False,
                    **params
                )
                responses.append(response.choices[0].message.content)
        
        return responses
    
    async def generate_single_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        model: str = None,
        stream: bool = False
    ) -> str:
        """生成单个响应"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        responses = await self.generate_response(
            messages=messages,
            model=model or self.model,
            n=1,
            stream=stream
        )
        return responses[0]
    
    async def generate_nli_reasoning(
        self,
        premise: str,
        hypothesis: str,
        **kwargs
    ) -> Dict[str, Any]:
        """执行NLI推理"""
        prompt = f"""Given the premise: "{premise}"
Given the hypothesis: "{hypothesis}"

Determine the relationship between the premise and hypothesis.
Please respond with exactly one of: ENTAILMENT, CONTRADICTION, NEUTRAL

Also provide a brief explanation of your reasoning in 1-2 sentences."""
        
        messages = [
            {"role": "system", "content": "You are an expert in natural language inference (NLI). Analyze the relationship between statements carefully and respond with the correct classification followed by your reasoning."},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.client.chat.completions.create(
            model=self.nli_model,
            messages=messages,
            stream=False,
            temperature=0.1,
            max_tokens=256
        )
        
        content = response.choices[0].message.content.strip()
        lines = content.split('\n')
        label_line = lines[0].upper()
        explanation = '\n'.join(lines[1:]).strip() if len(lines) > 1 else content
        
        if "ENTAILMENT" in label_line:
            label = "ENTAILMENT"
        elif "CONTRADICTION" in label_line:
            label = "CONTRADICTION"
        elif "NEUTRAL" in label_line:
            label = "NEUTRAL"
        else:
            label = "UNKNOWN"
        
        return {
            "label": label,
            "confidence": 0.8,  # 可以基于响应解析置信度
            "explanation": explanation,
            "model": self.nli_model,
            "premise": premise,
            "hypothesis": hypothesis
        }
    
    async def generate_streaming_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None
    ) -> AsyncIterator[str]:
        """流式输出生成器"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        async for chunk in self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=True,
            **self.default_params
        ):
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
    
    async def close(self):
        """关闭客户端连接"""
        await self.client.close()


class Qwen25Client(QwenClient):
    """Qwen2.5 client for LLM interactions with non-streaming support by default."""
    def __init__(self, api_key: str = None, model: str = "qwen2.5-32b-instruct", base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1", nli_model: str = "qwen2.5-nli", **default_params):
        super().__init__(api_key=api_key, model=model, base_url=base_url, nli_model=nli_model, **default_params)
    async def generate_single_response(self, prompt: str, system_prompt: Optional[str] = None, model: str = None, stream: bool = False) -> str:
        # Always use non-streaming for Qwen2.5
        return await super().generate_single_response(prompt, system_prompt, model, stream=False)