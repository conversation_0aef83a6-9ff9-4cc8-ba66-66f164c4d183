from openai import OpenAI
from typing import List, Dict, Any, Optional, Iterator
import os
from dataclasses import dataclass


@dataclass
class LLMResponse:
    content: str
    usage: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    finish_reason: Optional[str] = None


class QwenClientSync:
    """Synchronous Qwen client for LLM interactions"""
    
    def __init__(
        self,
        api_key: str = None,
        model: str = "qwen3-32b",
        base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1",
        nli_model: str = "qwen3-latest",
        **default_params
    ):
        self.client = OpenAI(
            api_key=api_key or os.getenv("QWEN_API_KEY", "dummy_key"),
            base_url=base_url
        )
        self.model = model
        self.nli_model = nli_model
        self.default_params = {
            "temperature": 0.7,
            "max_tokens": 1024,
            **default_params
        }
    
    def generate_response(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        n: int = 1,
        stream: bool = True,
        **kwargs
    ) -> List[str]:
        """生成多个响应用于UQ分析"""
        model = model or self.model
        params = {**self.default_params, **kwargs}
        responses = []
        
        for i in range(n):
            # Always use streaming mode to avoid enable_thinking issues
            response_content = ""
            stream_response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                stream=True,
                **params
            )
            for chunk in stream_response:
                if chunk.choices[0].delta.content:
                    response_content += chunk.choices[0].delta.content
            responses.append(response_content)
        
        return responses
    
    def generate_single_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        model: str = None,
        stream: bool = True
    ) -> str:
        """生成单个响应"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        responses = self.generate_response(
            messages=messages,
            model=model or self.model,
            n=1,
            stream=True  # Force streaming mode
        )
        return responses[0]
    
    def generate_nli_reasoning(
        self,
        premise: str,
        hypothesis: str,
        **kwargs
    ) -> Dict[str, Any]:
        """执行NLI推理"""
        prompt = f"""Given the premise: "{premise}"
Given the hypothesis: "{hypothesis}"

Determine the relationship between the premise and hypothesis.
Please respond with exactly one of: ENTAILMENT, CONTRADICTION, NEUTRAL

Also provide a brief explanation of your reasoning in 1-2 sentences."""
        
        messages = [
            {"role": "system", "content": "You are an expert in natural language inference (NLI). Analyze the relationship between statements carefully and respond with the correct classification followed by your reasoning."},
            {"role": "user", "content": prompt}
        ]
        
        # Use streaming mode to avoid enable_thinking issues
        response_content = ""
        stream_response = self.client.chat.completions.create(
            model=self.nli_model,
            messages=messages,
            stream=True,
            temperature=0.1,
            max_tokens=256
        )
        
        for chunk in stream_response:
            if chunk.choices[0].delta.content:
                response_content += chunk.choices[0].delta.content
        
        content = response_content.strip()
        lines = content.split('\n')
        label_line = lines[0].upper()
        explanation = '\n'.join(lines[1:]).strip() if len(lines) > 1 else content
        
        if "ENTAILMENT" in label_line:
            label = "ENTAILMENT"
        elif "CONTRADICTION" in label_line:
            label = "CONTRADICTION"
        elif "NEUTRAL" in label_line:
            label = "NEUTRAL"
        else:
            label = "UNKNOWN"
        
        return {
            "label": label,
            "confidence": 0.8,
            "explanation": explanation,
            "model": self.nli_model,
            "premise": premise,
            "hypothesis": hypothesis
        }
    
    def generate_streaming_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None
    ) -> Iterator[str]:
        """同步流式输出生成器"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        stream_response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=True,
            **self.default_params
        )
        
        for chunk in stream_response:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
    
    def close(self):
        """关闭客户端连接"""
        # 同步客户端不需要显式关闭
        pass


class Qwen25ClientSync(QwenClientSync):
    """Synchronous Qwen2.5 client for LLM interactions with non-streaming support by default."""
    def __init__(
        self, 
        api_key: str = None, 
        model: str = "qwen2.5-32b-instruct", 
        base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1", 
        nli_model: str = "qwen2.5-nli", 
        **default_params
    ):
        super().__init__(api_key=api_key, model=model, base_url=base_url, nli_model=nli_model, **default_params)
    
    def generate_single_response(self, prompt: str, system_prompt: Optional[str] = None, model: str = None, stream: bool = True) -> str:
        # Always use streaming mode to avoid enable_thinking issues
        return super().generate_single_response(prompt, system_prompt, model, stream=True)