"""
报告生成模块
提供详细的分析结果报告和统计信息
"""

import pandas as pd
import numpy as np
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ReportGenerator:
    """分析结果报告生成器"""
    
    def __init__(self, output_dir: str = "data/Uq_Evaluation_20250731"):
        """
        初始化报告生成器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def generate_comprehensive_report(self, results_df: pd.DataFrame, 
                                    validation_report: Optional[Dict] = None,
                                    cache_stats: Optional[Dict] = None) -> Dict[str, str]:
        """
        生成综合分析报告
        
        Args:
            results_df: 分析结果DataFrame
            validation_report: 数据验证报告
            cache_stats: 缓存统计信息
            
        Returns:
            生成的报告文件路径字典
        """
        report_files = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 1. 生成执行摘要
            summary_file = os.path.join(self.output_dir, f"analysis_summary_{timestamp}.txt")
            self._generate_executive_summary(results_df, summary_file, validation_report, cache_stats)
            report_files['summary'] = summary_file
            
            # 2. 生成详细统计报告
            stats_file = os.path.join(self.output_dir, f"detailed_statistics_{timestamp}.json")
            self._generate_detailed_statistics(results_df, stats_file)
            report_files['statistics'] = stats_file
            
            # 3. 生成方法比较报告
            comparison_file = os.path.join(self.output_dir, f"method_comparison_{timestamp}.csv")
            self._generate_method_comparison(results_df, comparison_file)
            report_files['comparison'] = comparison_file
            
            # 4. 生成数据质量报告
            quality_file = os.path.join(self.output_dir, f"data_quality_report_{timestamp}.txt")
            self._generate_data_quality_report(results_df, quality_file, validation_report)
            report_files['quality'] = quality_file
            
            # 5. 生成性能报告
            performance_file = os.path.join(self.output_dir, f"performance_report_{timestamp}.txt")
            self._generate_performance_report(results_df, performance_file, cache_stats)
            report_files['performance'] = performance_file
            
            logger.info(f"Generated comprehensive report with {len(report_files)} files")
            return report_files
            
        except Exception as e:
            logger.error(f"Failed to generate comprehensive report: {e}")
            return {}
    
    def _generate_executive_summary(self, results_df: pd.DataFrame, output_file: str,
                                  validation_report: Optional[Dict] = None,
                                  cache_stats: Optional[Dict] = None):
        """生成执行摘要"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("UQ方法分析 - 执行摘要\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 基本统计
            f.write("基本统计信息:\n")
            f.write("-" * 40 + "\n")
            f.write(f"总结果数量: {len(results_df)}\n")
            f.write(f"数据类型: {', '.join(results_df['data_type'].unique())}\n")
            f.write(f"UQ方法数量: {len(results_df['uq_method'].unique())}\n")
            f.write(f"样本大小范围: {results_df['sample_size'].min()} - {results_df['sample_size'].max()}\n\n")
            
            # 数据验证信息
            if validation_report:
                f.write("数据验证结果:\n")
                f.write("-" * 40 + "\n")
                f.write(f"原始数据行数: {validation_report.get('original_rows', 'N/A')}\n")
                f.write(f"清理后行数: {validation_report.get('cleaned_rows', 'N/A')}\n")
                f.write(f"丢弃行数: {validation_report.get('dropped_rows', 'N/A')}\n")
                if validation_report.get('issues'):
                    f.write("数据问题:\n")
                    for issue in validation_report['issues']:
                        f.write(f"  - {issue}\n")
                f.write("\n")
            
            # 方法性能概览
            f.write("方法性能概览:\n")
            f.write("-" * 40 + "\n")
            
            # 按方法统计不确定性分数
            method_stats = results_df.groupby('uq_method')['uncertainty_score'].agg([
                'count', 'mean', 'std', 'min', 'max'
            ]).round(4)
            
            for method, stats in method_stats.iterrows():
                f.write(f"{method}:\n")
                f.write(f"  样本数: {stats['count']}\n")
                f.write(f"  平均不确定性: {stats['mean']:.4f}\n")
                f.write(f"  标准差: {stats['std']:.4f}\n")
                f.write(f"  范围: {stats['min']:.4f} - {stats['max']:.4f}\n\n")
            
            # Twitter验证准确率（如果有）
            if 'validation_accuracy' in results_df.columns:
                twitter_data = results_df[results_df['data_type'] == 'twitter']
                if not twitter_data.empty:
                    f.write("Twitter验证准确率:\n")
                    f.write("-" * 40 + "\n")
                    val_stats = twitter_data.groupby('uq_method')['validation_accuracy'].agg([
                        'count', 'mean', 'std'
                    ]).round(4)
                    
                    for method, stats in val_stats.iterrows():
                        f.write(f"{method}: {stats['mean']:.4f} ± {stats['std']:.4f} (n={stats['count']})\n")
                    f.write("\n")
            
            # 缓存统计
            if cache_stats:
                f.write("缓存统计:\n")
                f.write("-" * 40 + "\n")
                f.write(f"NLI缓存条目: {cache_stats.get('nli_entries', 'N/A')}\n")
                f.write(f"缓存命中率: {cache_stats.get('hit_rate', 'N/A')}\n\n")
            
            # 建议和结论
            f.write("主要发现和建议:\n")
            f.write("-" * 40 + "\n")
            
            # 找出表现最好的方法
            if 'validation_accuracy' in results_df.columns:
                twitter_data = results_df[results_df['data_type'] == 'twitter']
                if not twitter_data.empty:
                    best_method = twitter_data.groupby('uq_method')['validation_accuracy'].mean().idxmax()
                    best_accuracy = twitter_data.groupby('uq_method')['validation_accuracy'].mean().max()
                    f.write(f"1. 验证准确率最高的方法: {best_method} ({best_accuracy:.4f})\n")
            
            # 找出不确定性分数最稳定的方法
            method_stability = results_df.groupby('uq_method')['uncertainty_score'].std()
            most_stable = method_stability.idxmin()
            f.write(f"2. 不确定性分数最稳定的方法: {most_stable} (std={method_stability.min():.4f})\n")
            
            # 数据类型比较
            if len(results_df['data_type'].unique()) > 1:
                f.write("3. 不同数据类型表现差异明显，建议针对性优化\n")
            
            f.write("4. 建议进一步分析异常值和错误案例\n")
            f.write("5. 考虑增加更多样本大小进行测试\n")
    
    def _generate_detailed_statistics(self, results_df: pd.DataFrame, output_file: str):
        """生成详细统计信息"""
        stats = {}
        
        # 基本统计
        stats['basic'] = {
            'total_results': len(results_df),
            'data_types': results_df['data_type'].value_counts().to_dict(),
            'uq_methods': results_df['uq_method'].value_counts().to_dict(),
            'sample_sizes': results_df['sample_size'].value_counts().to_dict(),
            'prompt_types': results_df['prompt_type'].value_counts().to_dict()
        }
        
        # 不确定性分数统计
        stats['uncertainty_scores'] = {}
        for method in results_df['uq_method'].unique():
            method_data = results_df[results_df['uq_method'] == method]['uncertainty_score']
            stats['uncertainty_scores'][method] = {
                'count': int(method_data.count()),
                'mean': float(method_data.mean()),
                'std': float(method_data.std()),
                'min': float(method_data.min()),
                'max': float(method_data.max()),
                'median': float(method_data.median()),
                'q25': float(method_data.quantile(0.25)),
                'q75': float(method_data.quantile(0.75))
            }
        
        # 相似度分数统计
        if 'mean_similarity' in results_df.columns:
            stats['similarity_scores'] = {}
            for method in results_df['uq_method'].unique():
                method_data = results_df[results_df['uq_method'] == method]['mean_similarity']
                if method_data.notna().any():
                    stats['similarity_scores'][method] = {
                        'count': int(method_data.count()),
                        'mean': float(method_data.mean()),
                        'std': float(method_data.std()),
                        'min': float(method_data.min()),
                        'max': float(method_data.max())
                    }
        
        # 验证准确率统计（Twitter数据）
        if 'validation_accuracy' in results_df.columns:
            twitter_data = results_df[results_df['data_type'] == 'twitter']
            if not twitter_data.empty:
                stats['validation_accuracy'] = {}
                for method in twitter_data['uq_method'].unique():
                    method_data = twitter_data[twitter_data['uq_method'] == method]['validation_accuracy']
                    stats['validation_accuracy'][method] = {
                        'count': int(method_data.count()),
                        'mean': float(method_data.mean()),
                        'std': float(method_data.std()),
                        'min': float(method_data.min()),
                        'max': float(method_data.max())
                    }
        
        # 错误统计
        error_results = results_df[results_df['method_details'].str.contains('ERROR', na=False)]
        if not error_results.empty:
            stats['errors'] = {
                'total_errors': len(error_results),
                'error_by_method': error_results['uq_method'].value_counts().to_dict(),
                'error_by_data_type': error_results['data_type'].value_counts().to_dict()
            }
        
        # 保存统计信息
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Detailed statistics saved to {output_file}")
    
    def _generate_method_comparison(self, results_df: pd.DataFrame, output_file: str):
        """生成方法比较报告"""
        comparison_data = []
        
        for data_type in results_df['data_type'].unique():
            type_data = results_df[results_df['data_type'] == data_type]
            
            for sample_size in type_data['sample_size'].unique():
                size_data = type_data[type_data['sample_size'] == sample_size]
                
                for method in size_data['uq_method'].unique():
                    method_data = size_data[size_data['uq_method'] == method]
                    
                    comparison_row = {
                        'data_type': data_type,
                        'sample_size': sample_size,
                        'uq_method': method,
                        'result_count': len(method_data),
                        'mean_uncertainty': method_data['uncertainty_score'].mean(),
                        'std_uncertainty': method_data['uncertainty_score'].std(),
                        'mean_similarity': method_data['mean_similarity'].mean() if 'mean_similarity' in method_data.columns else None,
                        'error_count': len(method_data[method_data['method_details'].str.contains('ERROR', na=False)])
                    }
                    
                    # 添加验证准确率（如果有）
                    if 'validation_accuracy' in method_data.columns and data_type == 'twitter':
                        comparison_row['mean_validation_accuracy'] = method_data['validation_accuracy'].mean()
                        comparison_row['std_validation_accuracy'] = method_data['validation_accuracy'].std()
                    
                    comparison_data.append(comparison_row)
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"Method comparison saved to {output_file}")
    
    def _generate_data_quality_report(self, results_df: pd.DataFrame, output_file: str,
                                    validation_report: Optional[Dict] = None):
        """生成数据质量报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("数据质量报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 数据完整性检查
            f.write("数据完整性检查:\n")
            f.write("-" * 30 + "\n")
            
            missing_data = results_df.isnull().sum()
            for column, missing_count in missing_data.items():
                if missing_count > 0:
                    percentage = (missing_count / len(results_df)) * 100
                    f.write(f"{column}: {missing_count} 缺失值 ({percentage:.2f}%)\n")
            
            if missing_data.sum() == 0:
                f.write("所有列都没有缺失值\n")
            
            f.write("\n")
            
            # 异常值检测
            f.write("异常值检测:\n")
            f.write("-" * 30 + "\n")
            
            # 检查不确定性分数的异常值
            uncertainty_scores = results_df['uncertainty_score'].dropna()
            if len(uncertainty_scores) > 0:
                q1 = uncertainty_scores.quantile(0.25)
                q3 = uncertainty_scores.quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                outliers = uncertainty_scores[(uncertainty_scores < lower_bound) | 
                                            (uncertainty_scores > upper_bound)]
                f.write(f"不确定性分数异常值: {len(outliers)} 个 ({len(outliers)/len(uncertainty_scores)*100:.2f}%)\n")
                
                if len(outliers) > 0:
                    f.write(f"  范围: {outliers.min():.4f} - {outliers.max():.4f}\n")
            
            # 错误分析
            error_results = results_df[results_df['method_details'].str.contains('ERROR', na=False)]
            if not error_results.empty:
                f.write(f"\n错误结果: {len(error_results)} 个 ({len(error_results)/len(results_df)*100:.2f}%)\n")
                error_by_method = error_results['uq_method'].value_counts()
                for method, count in error_by_method.items():
                    f.write(f"  {method}: {count} 个错误\n")
            
            # 数据验证报告
            if validation_report:
                f.write("\n原始数据验证:\n")
                f.write("-" * 30 + "\n")
                f.write(f"原始行数: {validation_report.get('original_rows', 'N/A')}\n")
                f.write(f"清理后行数: {validation_report.get('cleaned_rows', 'N/A')}\n")
                f.write(f"数据保留率: {validation_report.get('cleaned_rows', 0) / validation_report.get('original_rows', 1) * 100:.2f}%\n")
                
                if validation_report.get('issues'):
                    f.write("\n数据问题列表:\n")
                    for i, issue in enumerate(validation_report['issues'], 1):
                        f.write(f"{i}. {issue}\n")
    
    def _generate_performance_report(self, results_df: pd.DataFrame, output_file: str,
                                   cache_stats: Optional[Dict] = None):
        """生成性能报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("性能分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 处理效率统计
            f.write("处理效率统计:\n")
            f.write("-" * 30 + "\n")
            f.write(f"总处理结果数: {len(results_df)}\n")
            
            # 按方法统计处理量
            method_counts = results_df['uq_method'].value_counts()
            f.write("\n各方法处理量:\n")
            for method, count in method_counts.items():
                f.write(f"  {method}: {count} 个结果\n")
            
            # 错误率统计
            error_results = results_df[results_df['method_details'].str.contains('ERROR', na=False)]
            if not error_results.empty:
                error_rate = len(error_results) / len(results_df) * 100
                f.write(f"\n总体错误率: {error_rate:.2f}%\n")
                
                method_error_rates = {}
                for method in results_df['uq_method'].unique():
                    method_results = results_df[results_df['uq_method'] == method]
                    method_errors = method_results[method_results['method_details'].str.contains('ERROR', na=False)]
                    error_rate = len(method_errors) / len(method_results) * 100
                    method_error_rates[method] = error_rate
                
                f.write("\n各方法错误率:\n")
                for method, rate in sorted(method_error_rates.items(), key=lambda x: x[1]):
                    f.write(f"  {method}: {rate:.2f}%\n")
            
            # 缓存性能
            if cache_stats:
                f.write("\n缓存性能:\n")
                f.write("-" * 30 + "\n")
                for key, value in cache_stats.items():
                    f.write(f"{key}: {value}\n")
            
            # 数据类型处理效率
            f.write("\n数据类型处理分布:\n")
            f.write("-" * 30 + "\n")
            type_counts = results_df['data_type'].value_counts()
            for data_type, count in type_counts.items():
                percentage = count / len(results_df) * 100
                f.write(f"{data_type}: {count} 个结果 ({percentage:.1f}%)\n")
            
            # 样本大小分布
            f.write("\n样本大小分布:\n")
            f.write("-" * 30 + "\n")
            size_counts = results_df['sample_size'].value_counts().sort_index()
            for size, count in size_counts.items():
                percentage = count / len(results_df) * 100
                f.write(f"样本大小 {size}: {count} 个结果 ({percentage:.1f}%)\n")
        
        logger.info(f"Performance report saved to {output_file}")


def generate_pivot_tables(results_df: pd.DataFrame, output_dir: str) -> Dict[str, str]:
    """
    生成数据透视表
    
    Args:
        results_df: 结果DataFrame
        output_dir: 输出目录
        
    Returns:
        生成的文件路径字典
    """
    pivot_files = {}
    
    try:
        for data_type in results_df['data_type'].unique():
            type_df = results_df[results_df['data_type'] == data_type]
            
            # 不确定性分数透视表
            uncertainty_pivot = type_df.pivot_table(
                index=['prompt_type', 'sample_size'], 
                columns='uq_method', 
                values='uncertainty_score',
                aggfunc='mean'
            ).round(4)
            
            uncertainty_file = os.path.join(output_dir, f'{data_type}_uncertainty_pivot.csv')
            uncertainty_pivot.to_csv(uncertainty_file, encoding='utf-8-sig')
            pivot_files[f'{data_type}_uncertainty'] = uncertainty_file
            
            # 相似度分数透视表
            if 'mean_similarity' in type_df.columns:
                similarity_pivot = type_df.pivot_table(
                    index=['prompt_type', 'sample_size'], 
                    columns='uq_method', 
                    values='mean_similarity',
                    aggfunc='mean'
                ).round(4)
                
                similarity_file = os.path.join(output_dir, f'{data_type}_similarity_pivot.csv')
                similarity_pivot.to_csv(similarity_file, encoding='utf-8-sig')
                pivot_files[f'{data_type}_similarity'] = similarity_file
            
            # 验证准确率透视表（仅Twitter）
            if data_type == 'twitter' and 'validation_accuracy' in type_df.columns:
                validation_pivot = type_df.pivot_table(
                    index=['prompt_type', 'sample_size'], 
                    columns='uq_method', 
                    values='validation_accuracy',
                    aggfunc='mean'
                ).round(4)
                
                validation_file = os.path.join(output_dir, f'{data_type}_validation_accuracy_pivot.csv')
                validation_pivot.to_csv(validation_file, encoding='utf-8-sig')
                pivot_files[f'{data_type}_validation'] = validation_file
        
        logger.info(f"Generated {len(pivot_files)} pivot tables")
        return pivot_files
        
    except Exception as e:
        logger.error(f"Failed to generate pivot tables: {e}")
        return {}
