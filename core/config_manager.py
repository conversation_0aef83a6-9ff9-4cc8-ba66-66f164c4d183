"""
配置管理模块
提供统一的配置加载和管理功能
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/analysis_config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                logger.info(f"Loaded configuration from {self.config_file}")
            else:
                logger.warning(f"Configuration file {self.config_file} not found, using defaults")
                self.config = self._get_default_config()
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data': {
                'twitter_csv': 'data/all_twitter_responses.csv',
                'commit_csv': 'data/all_commit_responses.csv',
                'output_dir': 'data/Uq_Evaluation_20250731',
                'cache_dir': 'cache',
                'sample_sizes': {
                    'twitter': [10, 15, 20],
                    'commit': [10, 15, 20, 25, 30]
                }
            },
            'nli': {
                'default_model': 'microsoft/deberta-large-mnli',
                'max_length': 256,
                'cache': {
                    'enable_csv_cache': True,
                    'cache_save_interval': 5
                }
            },
            'performance': {
                'memory_monitoring': True,
                'memory_threshold_mb': 4000,
                'batch_processing': {
                    'intermediate_save_interval': 20
                }
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(levelname)s - %(message)s'
            },
            'error_handling': {
                'continue_on_error': True,
                'save_error_results': True
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'data.twitter_csv' 格式
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, output_file: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            output_file: 输出文件路径，默认使用原配置文件
        """
        output_file = output_file or self.config_file
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"Saved configuration to {output_file}")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据相关配置"""
        return self.get('data', {})
    
    def get_nli_config(self) -> Dict[str, Any]:
        """获取NLI相关配置"""
        return self.get('nli', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能相关配置"""
        return self.get('performance', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志相关配置"""
        return self.get('logging', {})
    
    def get_error_handling_config(self) -> Dict[str, Any]:
        """获取错误处理相关配置"""
        return self.get('error_handling', {})
    
    def get_sample_sizes(self, data_type: str) -> list:
        """
        获取指定数据类型的样本大小配置
        
        Args:
            data_type: 数据类型 ('twitter' 或 'commit')
            
        Returns:
            样本大小列表
        """
        return self.get(f'data.sample_sizes.{data_type}', [10, 15, 20])
    
    def get_memory_threshold(self) -> int:
        """获取内存阈值（MB）"""
        return self.get('performance.memory_threshold_mb', 4000)
    
    def get_cache_save_interval(self) -> int:
        """获取缓存保存间隔"""
        return self.get('nli.cache.cache_save_interval', 5)
    
    def get_intermediate_save_interval(self) -> int:
        """获取中间结果保存间隔"""
        return self.get('performance.batch_processing.intermediate_save_interval', 20)
    
    def is_memory_monitoring_enabled(self) -> bool:
        """检查是否启用内存监控"""
        return self.get('performance.memory_monitoring', True)
    
    def should_continue_on_error(self) -> bool:
        """检查是否在错误时继续处理"""
        return self.get('error_handling.continue_on_error', True)
    
    def should_save_error_results(self) -> bool:
        """检查是否保存错误结果"""
        return self.get('error_handling.save_error_results', True)
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            配置是否有效
        """
        try:
            # 检查必需的配置项
            required_keys = [
                'data.twitter_csv',
                'data.commit_csv',
                'data.output_dir',
                'nli.default_model'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    logger.error(f"Missing required configuration: {key}")
                    return False
            
            # 检查文件路径
            data_files = [
                self.get('data.twitter_csv'),
                self.get('data.commit_csv')
            ]
            
            for file_path in data_files:
                if not os.path.exists(file_path):
                    logger.warning(f"Data file not found: {file_path}")
            
            # 检查样本大小配置
            for data_type in ['twitter', 'commit']:
                sample_sizes = self.get_sample_sizes(data_type)
                if not isinstance(sample_sizes, list) or not sample_sizes:
                    logger.error(f"Invalid sample_sizes for {data_type}")
                    return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

# 全局配置管理器实例
_config_manager = None

def get_config_manager(config_file: str = "config/analysis_config.yaml") -> ConfigManager:
    """
    获取全局配置管理器实例（单例模式）
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        ConfigManager实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    return _config_manager

def reload_config(config_file: str = "config/analysis_config.yaml"):
    """
    重新加载配置
    
    Args:
        config_file: 配置文件路径
    """
    global _config_manager
    _config_manager = ConfigManager(config_file)
