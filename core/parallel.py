import asyncio
from typing import List, Callable, Any
import aiofiles

class ParallelProcessor:
    """优化并行处理prompt和UQ分析"""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
    
    async def process_batch(
        self,
        items: List[Any],
        process_func: Callable,
        batch_size: int = 5
    ) -> List[Any]:
        """批量并行处理"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_with_semaphore(item):
            async with semaphore:
                return await process_func(item)
        
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i+batch_size]
            batch_results = await asyncio.gather(
                *[process_with_semaphore(item) for item in batch],
                return_exceptions=True
            )
            results.extend(batch_results)
        
        return results