"""
Shared text similarity cache utilities for symmetric pairwise similarities.
Provides <PERSON><PERSON><PERSON> and generic cached pairwise store.
"""
from typing import Dict, <PERSON><PERSON>, Callable
import hashlib

Pa<PERSON>ey = Tuple[str, str]


def _pair_hash(a: str, b: str, tag: str = "") -> str:
    # order-invariant pair key
    h1 = hashlib.md5((a or "").encode("utf-8")).hexdigest()
    h2 = hashlib.md5((b or "").encode("utf-8")).hexdigest()
    if h1 <= h2:
        base = f"{h1}|{h2}|{tag}"
    else:
        base = f"{h2}|{h1}|{tag}"
    return hashlib.md5(base.encode("utf-8")).hexdigest()


class PairwiseSimilarityCache:
    def __init__(self):
        self._store: Dict[str, float] = {}

    def get_or_compute(self, a: str, b: str, tag: str, compute_fn: Callable[[str, str], float]) -> float:
        key = _pair_hash(a, b, tag)
        if key in self._store:
            return self._store[key]
        val = compute_fn(a, b)
        self._store[key] = float(val)
        return self._store[key]


# Global singleton for general pairwise similarities
PAIRWISE_CACHE = PairwiseSimilarityCache()


def jaccard_similarity_cached(a: str, b: str) -> float:
    from collections import Counter

    def _compute(x: str, y: str) -> float:
        s1 = set((x or "").lower().split())
        s2 = set((y or "").lower().split())
        inter = len(s1 & s2)
        union = len(s1 | s2)
        return inter / union if union > 0 else 0.0

    return PAIRWISE_CACHE.get_or_compute(a, b, tag="jaccard", compute_fn=_compute)

