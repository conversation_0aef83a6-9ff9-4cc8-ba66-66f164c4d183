import json
import os
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
import hashlib


class CheckpointManager:
    """Manages experiment checkpoints for resuming interrupted experiments"""
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        
    def create_checkpoint(
        self,
        experiment_id: str,
        completed_prompts: Set[str],
        config: Dict[str, Any],
        dataset_name: str,
        uq_method_name: str
    ) -> str:
        """
        Create a checkpoint for the current experiment state
        
        Args:
            experiment_id: Unique identifier for the experiment
            completed_prompts: Set of completed prompt identifiers
            config: Full experiment configuration
            dataset_name: Name of the dataset being processed
            uq_method_name: Name of the UQ method being applied
            
        Returns:
            Path to the created checkpoint file
        """
        checkpoint_data = {
            'experiment_id': experiment_id,
            'dataset_name': dataset_name,
            'uq_method_name': uq_method_name,
            'completed_prompts': list(completed_prompts),
            'config': config,
            'timestamp': time.time(),
            'checkpoint_version': '1.0'
        }
        
        # Create checkpoint filename
        checkpoint_hash = hashlib.md5(
            f"{experiment_id}_{dataset_name}_{uq_method_name}".encode()
        ).hexdigest()[:8]
        
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_hash}_{int(time.time())}.json"
        
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
            
        # Clean up old checkpoints for the same experiment
        self._cleanup_old_checkpoints(experiment_id, dataset_name, uq_method_name)
        
        return str(checkpoint_file)
    
    def resume_experiment(
        self,
        experiment_id: str,
        dataset_name: str,
        uq_method_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Resume an experiment from the latest checkpoint
        
        Args:
            experiment_id: Unique identifier for the experiment
            dataset_name: Name of the dataset
            uq_method_name: Name of the UQ method
            
        Returns:
            Checkpoint data if found, None otherwise
        """
        checkpoints = self._find_checkpoints(experiment_id, dataset_name, uq_method_name)
        
        if not checkpoints:
            return None
            
        # Get the most recent checkpoint
        latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
        
        try:
            with open(latest_checkpoint, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
                
            # Convert list back to set for completed_prompts
            checkpoint_data['completed_prompts'] = set(checkpoint_data.get('completed_prompts', []))
            
            return checkpoint_data
            
        except (json.JSONDecodeError, FileNotFoundError):
            return None
    
    def get_experiment_status(self, experiment_id: str) -> Dict[str, Any]:
        """Get status of all checkpoints for an experiment"""
        checkpoints = list(self.checkpoint_dir.glob("*.json"))
        experiment_checkpoints = []
        
        for checkpoint_file in checkpoints:
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                    
                if checkpoint_data.get('experiment_id') == experiment_id:
                    experiment_checkpoints.append({
                        'file': str(checkpoint_file),
                        'dataset': checkpoint_data.get('dataset_name'),
                        'uq_method': checkpoint_data.get('uq_method_name'),
                        'completed_count': len(checkpoint_data.get('completed_prompts', [])),
                        'timestamp': checkpoint_data.get('timestamp'),
                        'config': checkpoint_data.get('config')
                    })
            except (json.JSONDecodeError, FileNotFoundError):
                continue
        
        return {
            'experiment_id': experiment_id,
            'checkpoints': experiment_checkpoints,
            'total_checkpoints': len(experiment_checkpoints)
        }
    
    def list_all_experiments(self) -> List[Dict[str, Any]]:
        """List all experiments with available checkpoints"""
        checkpoints = list(self.checkpoint_dir.glob("*.json"))
        experiments = {}
        
        for checkpoint_file in checkpoints:
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                    
                experiment_id = checkpoint_data.get('experiment_id')
                if experiment_id:
                    if experiment_id not in experiments:
                        experiments[experiment_id] = {
                            'experiment_id': experiment_id,
                            'datasets': set(),
                            'uq_methods': set(),
                            'last_activity': 0,
                            'checkpoints': []
                        }
                    
                    experiments[experiment_id]['datasets'].add(
                        checkpoint_data.get('dataset_name')
                    )
                    experiments[experiment_id]['uq_methods'].add(
                        checkpoint_data.get('uq_method_name')
                    )
                    experiments[experiment_id]['last_activity'] = max(
                        experiments[experiment_id]['last_activity'],
                        checkpoint_data.get('timestamp', 0)
                    )
                    experiments[experiment_id]['checkpoints'].append(str(checkpoint_file))
                    
            except (json.JSONDecodeError, FileNotFoundError):
                continue
        
        # Convert sets to lists for JSON serialization
        for exp in experiments.values():
            exp['datasets'] = list(exp['datasets'])
            exp['uq_methods'] = list(exp['uq_methods'])
            
        return list(experiments.values())
    
    def delete_checkpoint(self, checkpoint_file: str) -> bool:
        """Delete a specific checkpoint"""
        try:
            checkpoint_path = Path(checkpoint_file)
            if checkpoint_path.exists():
                checkpoint_path.unlink()
                return True
        except Exception:
            pass
        return False
    
    def cleanup_experiment(self, experiment_id: str) -> int:
        """Clean up all checkpoints for an experiment"""
        checkpoints = list(self.checkpoint_dir.glob("*.json"))
        deleted_count = 0
        
        for checkpoint_file in checkpoints:
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                    
                if checkpoint_data.get('experiment_id') == experiment_id:
                    checkpoint_file.unlink()
                    deleted_count += 1
                    
            except (json.JSONDecodeError, FileNotFoundError):
                continue
        
        return deleted_count
    
    def _find_checkpoints(
        self,
        experiment_id: str,
        dataset_name: str,
        uq_method_name: str
    ) -> List[Path]:
        """Find all checkpoints for a specific experiment combination"""
        checkpoints = []
        
        for checkpoint_file in self.checkpoint_dir.glob("*.json"):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                    
                if (
                    checkpoint_data.get('experiment_id') == experiment_id and
                    checkpoint_data.get('dataset_name') == dataset_name and
                    checkpoint_data.get('uq_method_name') == uq_method_name
                ):
                    checkpoints.append(checkpoint_file)
                    
            except (json.JSONDecodeError, FileNotFoundError):
                continue
        
        return checkpoints
    
    def _cleanup_old_checkpoints(
        self,
        experiment_id: str,
        dataset_name: str,
        uq_method_name: str,
        keep_last: int = 3
    ):
        """Keep only the last N checkpoints for a specific experiment combination"""
        checkpoints = self._find_checkpoints(experiment_id, dataset_name, uq_method_name)
        
        if len(checkpoints) > keep_last:
            # Sort by modification time (oldest first)
            checkpoints.sort(key=lambda x: x.stat().st_mtime)
            
            # Delete old checkpoints
            for checkpoint_file in checkpoints[:-keep_last]:
                try:
                    checkpoint_file.unlink()
                except Exception:
                    pass


class CheckpointError(Exception):
    """Custom exception for checkpoint-related errors"""
    pass