"""
Shared embedding encoder with in-memory + disk cache.
Provides a singleton CachedEmbeddingEncoder per model name.
Caches are saved under ./cache/embeddings_{model_md5}.pkl
"""
from typing import Dict, List
import hashlib
import numpy as np
from sentence_transformers import SentenceTransformer
import os
import pickle


class CachedEmbeddingEncoder:
    def __init__(self, model_name: str):
        self.model_name = model_name
        self._model: SentenceTransformer = SentenceTransformer(model_name)
        # cache key: md5(text) + normalize flag
        self._cache: Dict[str, np.ndarray] = {}
        # disk cache path per model
        os.makedirs("cache", exist_ok=True)
        self._cache_path = os.path.join(
            "cache", f"embeddings_{hashlib.md5(model_name.encode('utf-8')).hexdigest()}.pkl"
        )
        self._load_cache()

    @staticmethod
    def _text_hash(text: str) -> str:
        return hashlib.md5((text or "").encode("utf-8")).hexdigest()

    def _cache_key(self, text: str, normalize: bool) -> str:
        return f"{self._text_hash(text)}::{self.model_name}::norm={int(bool(normalize))}"

    def _load_cache(self) -> None:
        if os.path.exists(self._cache_path):
            try:
                with open(self._cache_path, 'rb') as f:
                    data = pickle.load(f)
                # only accept dict[str, np.ndarray]
                if isinstance(data, dict):
                    self._cache = {k: np.array(v, dtype=np.float32) for k, v in data.items()}
            except Exception:
                # ignore corrupted cache
                pass

    def _save_cache(self) -> None:
        try:
            with open(self._cache_path, 'wb') as f:
                pickle.dump(self._cache, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception:
            pass

    def encode_one(self, text: str, normalize: bool = True) -> np.ndarray:
        key = self._cache_key(text, normalize)
        if key in self._cache:
            return self._cache[key]
        emb = self._model.encode([text], convert_to_tensor=False, normalize_embeddings=normalize)
        vec = np.array(emb[0], dtype=np.float32)
        self._cache[key] = vec
        # persist immediately
        self._save_cache()
        return vec

    def encode_many(self, texts: List[str], normalize: bool = True) -> List[np.ndarray]:
        return [self.encode_one(t, normalize=normalize) for t in texts]


# Singleton registry per model name
_encoders: Dict[str, CachedEmbeddingEncoder] = {}

def get_embedding_encoder(model_name: str) -> CachedEmbeddingEncoder:
    if model_name not in _encoders:
        _encoders[model_name] = CachedEmbeddingEncoder(model_name)
    return _encoders[model_name]

