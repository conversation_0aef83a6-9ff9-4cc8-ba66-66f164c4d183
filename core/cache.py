import hashlib
import json
import pickle
from pathlib import Path
from typing import Any, Optional

class PromptCache:
    """缓存重复prompt的结果，供不同UQ方法使用"""
    
    def __init__(self, cache_dir: str = "./cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, prompt: str, model: str, **params) -> str:
        """基于prompt内容、模型和参数生成唯一key"""
        key_data = {
            "prompt": prompt,
            "model": model,
            "params": sorted(params.items())
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, prompt: str, model: str, **params) -> Optional[list]:
        """获取缓存的响应"""
        key = self._get_cache_key(prompt, model, **params)
        cache_file = self.cache_dir / f"{key}.pkl"
        
        if cache_file.exists():
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        return None
    
    def save(self, prompt: str, model: str, responses: list, **params):
        """保存响应到缓存"""
        key = self._get_cache_key(prompt, model, **params)
        cache_file = self.cache_dir / f"{key}.pkl"
        
        with open(cache_file, 'wb') as f:
            pickle.dump(responses, f)
    
    def clear(self):
        """清空缓存"""
        for file in self.cache_dir.glob("*.pkl"):
            file.unlink()