from typing import Dict, Any, Optional, List
from rich.console import Console
from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn, MofNCompleteColumn, TaskID
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
import time


class ProgressTracker:
    def __init__(self, show_visual: bool = True):
        self.console = Console()
        self.show_visual = show_visual
        self.progress = None
        self.live = None
        self.tasks: Dict[str, TaskID] = {}
        self.experiment_info = {}
        
        if self.show_visual:
            self._setup_progress()
    
    def _setup_progress(self):
        """Setup rich progress visualization"""
        self.progress = Progress(
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=None),
            MofNCompleteColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeRemainingColumn(),
            console=self.console
        )
        
        self.layout = Layout()
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )
        
    def start_experiment(self, experiment_name: str, config: Dict[str, Any]):
        """Initialize experiment tracking"""
        self.experiment_info = {
            'name': experiment_name,
            'config': config,
            'start_time': time.time()
        }
        
        if self.show_visual:
            self.console.print(Panel(
                f"[bold green]开始实验: {experiment_name}[/bold green]\n"
                f"模型: {config.get('openai', {}).get('model', 'unknown')}\n"
                f"数据集: {len(config.get('datasets', []))} 个",
                title="实验信息",
                border_style="green"
            ))
    
    def add_dataset_task(self, dataset_name: str, total_samples: int) -> str:
        """Add a dataset processing task"""
        task_id = f"dataset_{dataset_name}"
        
        if self.show_visual and self.progress:
            self.tasks[task_id] = self.progress.add_task(
                f"📊 {dataset_name}",
                total=total_samples
            )
        
        return task_id
    
    def add_uq_method_task(self, uq_method_name: str, total_prompts: int) -> str:
        """Add a UQ method processing task"""
        task_id = f"uq_{uq_method_name}"
        
        if self.show_visual and self.progress:
            self.tasks[task_id] = self.progress.add_task(
                f"🔬 {uq_method_name}",
                total=total_prompts
            )
        
        return task_id
    
    def update_progress(self, task_id: str, advance: int = 1):
        """Update progress for a specific task"""
        if self.show_visual and self.progress and task_id in self.tasks:
            self.progress.advance(self.tasks[task_id], advance)
    
    def update_status(self, task_id: str, status: str):
        """Update task status message"""
        if self.show_visual and self.progress and task_id in self.tasks:
            self.progress.update(self.tasks[task_id], description=status)
    
    def log_metric(self, metric_name: str, value: Any, dataset: str = None, uq_method: str = None):
        """Log a metric with optional context"""
        context = []
        if dataset:
            context.append(f"数据集: {dataset}")
        if uq_method:
            context.append(f"方法: {uq_method}")
        
        context_str = " | ".join(context) if context else ""
        self.console.print(f"📈 [cyan]{metric_name}:[/cyan] {value} {context_str}")
    
    def log_warning(self, message: str):
        """Log a warning message"""
        self.console.print(f"⚠️  [yellow]警告:[/yellow] {message}")
    
    def log_error(self, message: str):
        """Log an error message"""
        self.console.print(f"❌ [red]错误:[/red] {message}")
    
    def log_success(self, message: str):
        """Log a success message"""
        self.console.print(f"✅ [green]成功:[/green] {message}")
    
    def display_summary(self, results: Dict[str, Any]):
        """Display experiment summary"""
        if not self.show_visual:
            return
            
        duration = time.time() - self.experiment_info.get('start_time', 0)
        
        table = Table(title="实验完成摘要")
        table.add_column("指标", style="cyan", no_wrap=True)
        table.add_column("值", style="magenta")
        
        table.add_row("实验名称", self.experiment_info.get('name', 'unknown'))
        table.add_row("总耗时", f"{duration:.2f}秒")
        table.add_row("完成时间", time.strftime("%Y-%m-%d %H:%M:%S"))
        
        for key, value in results.items():
            if isinstance(value, (int, float)):
                table.add_row(key, str(value))
            else:
                table.add_row(key, str(value))
        
        self.console.print(table)
    
    def __enter__(self):
        """Context manager entry"""
        if self.show_visual and self.progress:
            self.progress.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.show_visual and self.progress:
            self.progress.stop()


class SimpleProgressTracker:
    """Simple progress tracker without visual elements"""
    
    def __init__(self):
        self.stats = {
            'datasets_processed': 0,
            'uq_methods_processed': 0,
            'total_prompts': 0,
            'successful_responses': 0,
            'failed_responses': 0,
            'start_time': time.time()
        }
    
    def start_experiment(self, experiment_name: str, config: Dict[str, Any]):
        """Initialize experiment tracking"""
        print(f"开始实验: {experiment_name}")
        print(f"配置: {config}")
    
    def add_dataset_task(self, dataset_name: str, total_samples: int) -> str:
        """Add dataset processing task"""
        print(f"处理数据集: {dataset_name} ({total_samples} 样本)")
        return f"dataset_{dataset_name}"
    
    def add_uq_method_task(self, uq_method_name: str, total_prompts: int) -> str:
        """Add UQ method processing task"""
        print(f"应用UQ方法: {uq_method_name} ({total_prompts} prompts)")
        return f"uq_{uq_method_name}"
    
    def update_progress(self, task_id: str, advance: int = 1):
        """Update progress"""
        if 'uq_' in task_id:
            self.stats['total_prompts'] += advance
    
    def update_status(self, task_id: str, status: str):
        """Update status"""
        print(f"状态更新: {task_id} - {status}")
    
    def log_metric(self, metric_name: str, value: Any, dataset: str = None, uq_method: str = None):
        """Log metric"""
        print(f"指标: {metric_name} = {value}")
    
    def log_warning(self, message: str):
        """Log warning"""
        print(f"警告: {message}")
    
    def log_error(self, message: str):
        """Log error"""
        print(f"错误: {message}")
    
    def log_success(self, message: str):
        """Log success"""
        print(f"成功: {message}")
    
    def display_summary(self, results: Dict[str, Any]):
        """Display summary"""
        duration = time.time() - self.stats['start_time']
        print(f"\n实验完成!")
        print(f"耗时: {duration:.2f}秒")
        print(f"结果: {results}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass