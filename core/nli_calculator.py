"""
NLI计算模块 - 统一的自然语言推理计算和缓存管理

该模块提供：
1. 统一的NLI模型加载和计算
2. 完整的三分数计算 (entailment, neutral, contradiction)
3. 缓存管理
4. 向后兼容性
5. Ollama模型支持
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from typing import List, Tuple, NamedTuple, Optional, Dict, Any
import hashlib
import time
import logging
import os
import pandas as pd
import sqlite3
import json
import requests
import re

logger = logging.getLogger(__name__)

class NLIResult(NamedTuple):
    """NLI计算结果，包含三个分数和原始logits"""
    entailment: float
    neutral: float
    contradiction: float
    logits: Optional[List[float]] = None


class NLICalculator:
    """统一的NLI计算器"""
    
    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False):
        """
        初始化NLI计算器
        
        Args:
            model_name: NLI模型名称
            verbose: 是否输出详细信息
        """
        self.model_name = model_name
        self.verbose = verbose
        
        # 初始化设备和模型
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        if self.verbose:
            logger.info(f"Loading NLI model: {model_name} on {self.device}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name).to(self.device)
        self.model.eval()
        
        if self.verbose:
            logger.info(f"NLI model loaded successfully")
    
    def compute_nli_scores(self, text1: str, text2: str) -> NLIResult:
        """
        计算两个文本之间的完整NLI分数
        
        Args:
            text1: 前提文本
            text2: 假设文本
            
        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        try:
            inputs = self.tokenizer(
                text1, text2, 
                return_tensors="pt", 
                truncation=True, 
                max_length=256
            ).to(self.device)
            
            with torch.no_grad():
                logits = self.model(**inputs).logits
                probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
                raw_logits = logits.cpu().numpy()[0]

            # Label mapping: 0=contradiction, 1=neutral, 2=entailment
            contradiction_score = float(probs[0])
            neutral_score = float(probs[1])
            entailment_score = float(probs[2])

            return NLIResult(
                entailment=entailment_score,
                neutral=neutral_score,
                contradiction=contradiction_score,
                logits=raw_logits.tolist()
            )
            
        except Exception as e:
            logger.warning(f"Error computing NLI scores for model {self.model_name}: {str(e)}")
            # 返回均匀分布作为默认值
            return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33, logits=None)
    
    def compute_entailment_score(self, text1: str, text2: str) -> float:
        """
        计算entailment分数（向后兼容）
        
        Args:
            text1: 前提文本
            text2: 假设文本
            
        Returns:
            float: entailment分数
        """
        nli_result = self.compute_nli_scores(text1, text2)
        return nli_result.entailment
    
    def compute_similarity_matrix(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """
        计算响应列表的相似度矩阵
        
        Args:
            responses: 响应文本列表
            use_score: 使用的分数类型 ("entailment", "neutral", "contradiction")
            
        Returns:
            numpy.ndarray: 相似度矩阵
        """
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # 计算双向分数并取平均
                    nli_ij = self.compute_nli_scores(responses[i], responses[j])
                    nli_ji = self.compute_nli_scores(responses[j], responses[i])
                    
                    # 根据指定的分数类型获取值
                    if use_score == "entailment":
                        score_ij = nli_ij.entailment
                        score_ji = nli_ji.entailment
                    elif use_score == "neutral":
                        score_ij = nli_ij.neutral
                        score_ji = nli_ji.neutral
                    elif use_score == "contradiction":
                        score_ij = nli_ij.contradiction
                        score_ji = nli_ji.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")
                    
                    W[i, j] = (score_ij + score_ji) / 2
        
        # 确保矩阵对称
        W = (W + W.T) / 2
        return W
    
    @staticmethod
    def get_text_hash(text: str) -> str:
        """生成文本的哈希值"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def get_cache_key(self, text1: str, text2: str) -> str:
        """生成缓存键 - 使用两个文本按顺序拼接的hash"""
        # 将两个文本按顺序拼接，然后计算hash
        combined_text = f"{text1}|||{text2}|||{self.model_name}"
        return self.get_text_hash(combined_text)



class CachedNLICalculator(NLICalculator):
    """带缓存的NLI计算器 - 支持CSV文件持久化缓存"""

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False, use_sqlite: bool = True):
        super().__init__(model_name, verbose)

        # 缓存配置
        self.cache_dir = "cache"
        self.use_sqlite = use_sqlite

        if use_sqlite:
            self.db_file = os.path.join(self.cache_dir, "nli_cache.db")
        else:
            self.csv_cache_file = os.path.join(self.cache_dir, "nli_results_cache.csv")

        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)

        # 初始化缓存后端
        if use_sqlite:
            self._init_sqlite_cache()
        else:
            # CSV缓存不需要特殊初始化
            pass
        
    def _load_csv_cache_for_key(self, cache_key: str) -> dict:
        """为特定key从CSV文件加载缓存条目"""
        if not os.path.exists(self.csv_cache_file):
            return None

        try:
            # 读取CSV文件，使用cache_key作为索引
            df = pd.read_csv(self.csv_cache_file)

            # 如果有cache_key列，直接查找
            if 'cache_key' in df.columns:
                matching_rows = df[df['cache_key'] == cache_key]

                if len(matching_rows) > 0:
                    row = matching_rows.iloc[0]  # 取第一个匹配的行
                    return {
                        'text1': row['text1'],
                        'text2': row['text2'],
                        'model_name': row['model_name'],
                        'entailment': float(row['entailment']),
                        'neutral': float(row['neutral']),
                        'contradiction': float(row['contradiction']),
                        'cache_key': row['cache_key'],
                        'timestamp': row.get('timestamp', '')
                    }
            else:
                # 向后兼容：尝试使用旧的hash方式查找
                if self.verbose:
                    logger.debug("CSV file doesn't have cache_key column, using legacy lookup")

        except Exception as e:
            if self.verbose:
                logger.warning(f"Error loading cache for key {cache_key}: {e}")

        return None

    def _init_sqlite_cache(self):
        """初始化SQLite缓存数据库"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 检查是否存在旧版本的表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='nli_cache'")
            table_exists = cursor.fetchone() is not None

            if table_exists:
                # 检查表结构是否为旧版本（包含text1, text2字段）
                cursor.execute("PRAGMA table_info(nli_cache)")
                columns = [column[1] for column in cursor.fetchall()]
                has_text_columns = 'text1' in columns and 'text2' in columns

                if has_text_columns:
                    if self.verbose:
                        logger.info("Found legacy NLI cache table, performing migration...")
                    self._migrate_legacy_cache(conn)
                else:
                    if self.verbose:
                        logger.info("Found optimized NLI cache table, no migration needed")
            else:
                # 创建新的优化缓存表
                self._create_optimized_cache_table(conn)

            conn.close()

            if self.verbose:
                logger.info(f"Initialized SQLite NLI cache: {self.db_file}")

        except Exception as e:
            logger.error(f"Failed to initialize SQLite cache: {e}")

    def _create_optimized_cache_table(self, conn):
        """创建优化的缓存表结构"""
        cursor = conn.cursor()

        # 创建优化的缓存表，不存储原始文本
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS nli_cache (
                cache_key TEXT PRIMARY KEY,
                model_name TEXT NOT NULL,
                entailment REAL NOT NULL,
                neutral REAL NOT NULL,
                contradiction REAL NOT NULL,
                timestamp TEXT NOT NULL
            )
        ''')

        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_name ON nli_cache(model_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON nli_cache(timestamp)')

        conn.commit()

        if self.verbose:
            logger.info("Created optimized NLI cache table structure")

    def _migrate_legacy_cache(self, conn):
        """迁移旧版本的缓存数据到新结构"""
        cursor = conn.cursor()

        try:
            # 创建新表（只保留必要字段）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS nli_cache_new (
                    cache_key TEXT PRIMARY KEY,
                    model_name TEXT NOT NULL,
                    entailment REAL NOT NULL,
                    neutral REAL NOT NULL,
                    contradiction REAL NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')

            # 迁移数据（只保留NLI结果，丢弃原始文本）
            cursor.execute("SELECT cache_key, model_name, entailment, neutral, contradiction, timestamp FROM nli_cache")
            old_records = cursor.fetchall()

            migrated_count = 0
            for record in old_records:
                cache_key, model_name, entailment, neutral, contradiction, timestamp = record

                # 插入到新表
                cursor.execute('''
                    INSERT OR REPLACE INTO nli_cache_new
                    (cache_key, model_name, entailment, neutral, contradiction, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (cache_key, model_name, entailment, neutral, contradiction, timestamp))

                migrated_count += 1

            # 删除旧表，重命名新表
            cursor.execute("DROP TABLE nli_cache")
            cursor.execute("ALTER TABLE nli_cache_new RENAME TO nli_cache")

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_name ON nli_cache(model_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON nli_cache(timestamp)')

            conn.commit()

            if self.verbose:
                logger.info(f"Successfully migrated {migrated_count} cache entries to optimized format")

        except Exception as e:
            # 回滚操作
            conn.rollback()
            cursor.execute("DROP TABLE IF EXISTS nli_cache_new")
            conn.commit()
            logger.error(f"Failed to migrate legacy cache: {e}")
            raise

    def _load_sqlite_cache_for_key(self, cache_key: str) -> dict:
        """从SQLite数据库加载缓存条目（优化版本）"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT model_name, entailment, neutral, contradiction, timestamp
                FROM nli_cache WHERE cache_key = ?
            ''', (cache_key,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return {
                    'model_name': row[0],
                    'entailment': float(row[1]),
                    'neutral': float(row[2]),
                    'contradiction': float(row[3]),
                    'cache_key': cache_key,
                    'timestamp': row[4]
                }

        except Exception as e:
            if self.verbose:
                logger.warning(f"Error loading SQLite cache for key {cache_key}: {e}")

        return None

    def _save_to_sqlite_cache(self, cache_key: str, data: dict):
        """保存条目到SQLite数据库（优化版本，不存储原始文本）"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 使用INSERT OR REPLACE确保不会有重复条目
            cursor.execute('''
                INSERT OR REPLACE INTO nli_cache
                (cache_key, model_name, entailment, neutral, contradiction, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                cache_key,
                data['model_name'],
                data['entailment'],
                data['neutral'],
                data['contradiction'],
                data['timestamp']
            ))

            conn.commit()
            conn.close()

            if self.verbose:
                logger.debug(f"Saved optimized NLI cache entry to SQLite: {cache_key}")

        except Exception as e:
            logger.warning(f"Failed to save to SQLite cache: {e}")

    def _append_to_csv_cache(self, cache_key: str, data: dict):
        """将新的缓存条目追加到CSV文件"""
        try:
            # 准备新行数据，包含cache_key作为主索引
            new_row = {
                'cache_key': cache_key,
                'text1': data['text1'],
                'text2': data['text2'],
                'model_name': data['model_name'],
                'entailment': data['entailment'],
                'neutral': data['neutral'],
                'contradiction': data['contradiction'],
                'timestamp': data['timestamp']
            }

            # 如果文件不存在，创建新文件
            if not os.path.exists(self.csv_cache_file):
                df = pd.DataFrame([new_row])
                df.to_csv(self.csv_cache_file, index=False, encoding='utf-8-sig')
                if self.verbose:
                    logger.info(f"Created new NLI CSV cache file: {self.csv_cache_file}")
            else:
                # 检查文件是否有正确的列结构
                try:
                    existing_df = pd.read_csv(self.csv_cache_file, nrows=1)
                    if 'cache_key' not in existing_df.columns:
                        # 需要重建文件结构
                        self._rebuild_csv_with_cache_key()
                except:
                    pass

                # 追加到现有文件
                df = pd.DataFrame([new_row])
                df.to_csv(self.csv_cache_file, mode='a', header=False, index=False, encoding='utf-8-sig')

            if self.verbose:
                logger.debug(f"Appended new NLI cache entry to {self.csv_cache_file}")

        except Exception as e:
            logger.warning(f"Failed to append to NLI CSV cache: {e}")

    def _rebuild_csv_with_cache_key(self):
        """重建CSV文件，添加cache_key列作为索引"""
        try:
            if not os.path.exists(self.csv_cache_file):
                return

            # 读取现有数据
            df = pd.read_csv(self.csv_cache_file)

            # 为每行生成cache_key
            cache_keys = []
            for _, row in df.iterrows():
                combined_text = f"{row['text1']}|||{row['text2']}|||{row['model_name']}"
                cache_key = self.get_text_hash(combined_text)
                cache_keys.append(cache_key)

            # 添加cache_key列到第一列
            df.insert(0, 'cache_key', cache_keys)

            # 重新保存文件
            df.to_csv(self.csv_cache_file, index=False, encoding='utf-8-sig')

            if self.verbose:
                logger.info(f"Rebuilt CSV cache file with cache_key index: {len(df)} entries")

        except Exception as e:
            logger.warning(f"Failed to rebuild CSV cache file: {e}")

    def compute_nli_scores_cached(self, text1: str, text2: str) -> NLIResult:
        """
        带缓存的NLI分数计算 - 只使用本地文件缓存

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        cache_key = self.get_cache_key(text1, text2)

        # 1. 从缓存读取（SQLite或CSV）
        if self.use_sqlite:
            cached_data = self._load_sqlite_cache_for_key(cache_key)
            cache_type = "SQLite"
        else:
            cached_data = self._load_csv_cache_for_key(cache_key)
            cache_type = "CSV"

        if cached_data:
            # 尝试获取logits，如果没有则为None（向后兼容）
            logits = cached_data.get('logits', None)
            if logits and isinstance(logits, str):
                # 如果logits是字符串，尝试解析
                try:
                    import json
                    logits = json.loads(logits)
                except:
                    logits = None

            nli_result = NLIResult(
                entailment=cached_data['entailment'],
                neutral=cached_data['neutral'],
                contradiction=cached_data['contradiction'],
                logits=logits
            )
            if self.verbose:
                logger.debug(f"Loaded NLI result from {cache_type} cache")
            return nli_result

        # 2. 计算新的NLI分数
        if self.verbose:
            logger.info(f"Computing new NLI scores for model {self.model_name}")
        nli_result = self.compute_nli_scores(text1, text2)

        # 3. 立即保存到CSV文件
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        cache_data = {
            'text1': text1,
            'text2': text2,
            'model_name': self.model_name,
            'entailment': nli_result.entailment,
            'neutral': nli_result.neutral,
            'contradiction': nli_result.contradiction,
            'logits': nli_result.logits,
            'timestamp': timestamp
        }

        # 4. 立即保存到缓存（SQLite或CSV）
        if self.use_sqlite:
            self._save_to_sqlite_cache(cache_key, cache_data)
        else:
            self._append_to_csv_cache(cache_key, cache_data)

        return nli_result
    
    def compute_similarity_matrix_cached(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """计算响应列表的缓存相似度矩阵"""
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    # 使用缓存计算双向分数并取平均
                    nli_ij = self.compute_nli_scores_cached(responses[i], responses[j])
                    nli_ji = self.compute_nli_scores_cached(responses[j], responses[i])
                    
                    # 根据指定的分数类型获取值
                    if use_score == "entailment":
                        score_ij = nli_ij.entailment
                        score_ji = nli_ji.entailment
                    elif use_score == "neutral":
                        score_ij = nli_ij.neutral
                        score_ji = nli_ji.neutral
                    elif use_score == "contradiction":
                        score_ij = nli_ij.contradiction
                        score_ji = nli_ji.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")
                    
                    W[i, j] = (score_ij + score_ji) / 2
        
        # 确保矩阵对称
        W = (W + W.T) / 2
        return W
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        if self.use_sqlite:
            # 统计SQLite数据库中的条目数
            cache_entries = 0
            try:
                conn = sqlite3.connect(self.db_file)
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM nli_cache')
                cache_entries = cursor.fetchone()[0]
                conn.close()
            except:
                cache_entries = 0

            return {
                'cache_entries': cache_entries,
                'cache_type': 'SQLite',
                'model_name': self.model_name,
                'cache_file': self.db_file
            }
        else:
            # 统计CSV文件中的条目数
            cache_entries = 0
            if os.path.exists(self.csv_cache_file):
                try:
                    df = pd.read_csv(self.csv_cache_file)
                    cache_entries = len(df)
                except:
                    cache_entries = 0

            return {
                'cache_entries': cache_entries,
                'cache_type': 'CSV',
                'model_name': self.model_name,
                'cache_file': self.csv_cache_file
            }

    def clear_cache(self):
        """清空CSV文件缓存"""
        if os.path.exists(self.csv_cache_file):
            os.remove(self.csv_cache_file)
        if self.verbose:
            logger.info("NLI CSV cache cleared")

    def save_cache(self):
        """手动保存CSV缓存（现在每次操作都自动保存，此方法保留兼容性）"""
        if self.verbose:
            logger.info("Cache is automatically saved after each operation")


class OllamaNLICalculator:
    """基于Ollama的NLI计算器，兼容现有NLICalculator接口"""

    def __init__(self, model_name: str = "qwen2.5:32b", verbose: bool = False,
                 ollama_host: str = "http://localhost:11434", use_sampling: bool = False,
                 num_samples: int = 5):
        """
        初始化Ollama NLI计算器

        Args:
            model_name: Ollama模型名称
            verbose: 是否输出详细信息
            ollama_host: Ollama服务地址
            use_sampling: 是否使用采样方法估计概率（模拟logprob）
            num_samples: 采样次数（仅在use_sampling=True时有效）
        """
        self.model_name = model_name
        self.verbose = verbose
        self.ollama_host = ollama_host
        self.use_sampling = use_sampling
        self.num_samples = num_samples
        self.nli_cache = {}  # 内存缓存

        if self.verbose:
            logger.info(f"Initializing Ollama NLI Calculator with model: {model_name}")
            if use_sampling:
                logger.info(f"Using sampling method with {num_samples} samples to estimate probabilities")

        # 验证模型是否可用
        self._verify_model_availability()

        # 加载NLI提示词模板
        self.nli_prompt_template = self._load_nli_prompt_template()

    def _verify_model_availability(self):
        """验证Ollama模型是否可用"""
        try:
            response = requests.get(f"{self.ollama_host}/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                available_models = [model["name"] for model in models]
                if self.model_name not in available_models:
                    logger.warning(f"Model {self.model_name} not found in available models: {available_models}")
                    logger.info("You may need to pull the model first: ollama pull {self.model_name}")
                elif self.verbose:
                    logger.info(f"Model {self.model_name} is available")
            else:
                logger.error(f"Failed to connect to Ollama at {self.ollama_host}")
        except Exception as e:
            logger.error(f"Error verifying model availability: {e}")

    def _load_nli_prompt_template(self) -> str:
        """加载NLI提示词模板"""
        template = """You are an expert in Natural Language Inference (NLI). Given two statements, determine their relationship:

Premise: "{premise}"
Hypothesis: "{hypothesis}"

Classify the relationship as one of:
1. ENTAILMENT: The hypothesis is definitely true given the premise
2. CONTRADICTION: The hypothesis is definitely false given the premise
3. NEUTRAL: The hypothesis might be true or false given the premise

Think step by step:
1. What does the premise state?
2. What does the hypothesis claim?
3. Does the premise guarantee the truth/falsity of the hypothesis?

Provide your response as JSON with probabilities for each category that sum to 1.0:
{{
    "entailment": 0.0-1.0,
    "contradiction": 0.0-1.0,
    "neutral": 0.0-1.0
}}

Be precise and conservative in your assessment."""
        return template

    def _call_ollama(self, prompt: str) -> Dict[str, Any]:
        """调用Ollama API"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # 低温度确保一致性
                    "top_p": 0.9
                }
            }

            response = requests.post(
                f"{self.ollama_host}/api/generate",
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error calling Ollama API: {e}")
            return None

    def _extract_nli_scores_from_response(self, response_text: str) -> Optional[NLIResult]:
        """从Ollama响应中提取NLI分数"""
        try:
            # 方法1: 尝试解析JSON
            json_match = re.search(r'\{[^}]*"entailment"[^}]*\}', response_text, re.IGNORECASE | re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                try:
                    scores = json.loads(json_str)
                    entailment = float(scores.get("entailment", 0))
                    neutral = float(scores.get("neutral", 0))
                    contradiction = float(scores.get("contradiction", 0))

                    # 归一化分数确保和为1
                    total = entailment + neutral + contradiction
                    if total > 0:
                        entailment /= total
                        neutral /= total
                        contradiction /= total
                    else:
                        # 默认均匀分布
                        entailment, neutral, contradiction = 0.33, 0.34, 0.33

                    return NLIResult(
                        entailment=entailment,
                        neutral=neutral,
                        contradiction=contradiction,
                        logits=None
                    )
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    if self.verbose:
                        logger.warning(f"Failed to parse JSON scores: {e}")

            # 方法2: 文本模式匹配
            entailment_match = re.search(r'entailment["\s:]*([0-9.]+)', response_text, re.IGNORECASE)
            neutral_match = re.search(r'neutral["\s:]*([0-9.]+)', response_text, re.IGNORECASE)
            contradiction_match = re.search(r'contradiction["\s:]*([0-9.]+)', response_text, re.IGNORECASE)

            if entailment_match and neutral_match and contradiction_match:
                entailment = float(entailment_match.group(1))
                neutral = float(neutral_match.group(1))
                contradiction = float(contradiction_match.group(1))

                # 归一化
                total = entailment + neutral + contradiction
                if total > 0:
                    entailment /= total
                    neutral /= total
                    contradiction /= total

                return NLIResult(
                    entailment=entailment,
                    neutral=neutral,
                    contradiction=contradiction,
                    logits=None
                )

            # 方法3: 关键词分析（简单启发式）
            response_lower = response_text.lower()
            if "entailment" in response_lower and "contradiction" not in response_lower:
                return NLIResult(entailment=0.8, neutral=0.15, contradiction=0.05, logits=None)
            elif "contradiction" in response_lower and "entailment" not in response_lower:
                return NLIResult(entailment=0.05, neutral=0.15, contradiction=0.8, logits=None)
            elif "neutral" in response_lower:
                return NLIResult(entailment=0.2, neutral=0.6, contradiction=0.2, logits=None)

        except Exception as e:
            logger.error(f"Error extracting NLI scores: {e}")

        # 默认返回均匀分布
        return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33, logits=None)

    def _estimate_scores_via_sampling(self, text1: str, text2: str, num_samples: int = 10) -> NLIResult:
        """
        通过多次采样估计NLI分数（模拟logprob方法）
        由于Ollama不支持logprob，我们通过多次采样来估计概率分布
        """
        if self.verbose:
            logger.info(f"Estimating NLI scores via {num_samples} samples")

        # 构建简化的提示词，要求模型只输出一个词
        simple_prompt = f"""Given these two statements, what is their relationship?

Premise: "{text1}"
Hypothesis: "{text2}"

Answer with exactly one word: entailment, neutral, or contradiction."""

        predictions = []

        for i in range(num_samples):
            try:
                payload = {
                    "model": self.model_name,
                    "prompt": simple_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,  # 增加随机性以获得不同的采样
                        "top_p": 0.9,
                        "num_predict": 3  # 限制输出长度
                    }
                }

                response = requests.post(
                    f"{self.ollama_host}/api/generate",
                    json=payload,
                    timeout=30
                )

                if response.status_code == 200:
                    response_text = response.json().get("response", "").strip().lower()

                    # 提取预测结果
                    if "entailment" in response_text:
                        predictions.append("entailment")
                    elif "contradiction" in response_text:
                        predictions.append("contradiction")
                    elif "neutral" in response_text:
                        predictions.append("neutral")
                    else:
                        # 如果无法识别，随机分配
                        predictions.append("neutral")

                else:
                    logger.warning(f"Sample {i+1} failed: {response.status_code}")
                    predictions.append("neutral")

            except Exception as e:
                logger.warning(f"Sample {i+1} error: {e}")
                predictions.append("neutral")

        # 计算概率分布
        total_samples = len(predictions)
        entailment_count = predictions.count("entailment")
        neutral_count = predictions.count("neutral")
        contradiction_count = predictions.count("contradiction")

        entailment_prob = entailment_count / total_samples
        neutral_prob = neutral_count / total_samples
        contradiction_prob = contradiction_count / total_samples

        if self.verbose:
            logger.info(f"Sampling results: E={entailment_count}/{total_samples}, "
                       f"N={neutral_count}/{total_samples}, C={contradiction_count}/{total_samples}")

        return NLIResult(
            entailment=entailment_prob,
            neutral=neutral_prob,
            contradiction=contradiction_prob,
            logits=None
        )

    def get_cache_key(self, text1: str, text2: str) -> str:
        """生成缓存键"""
        combined_text = f"{text1}||{text2}||{self.model_name}"
        return hashlib.md5(combined_text.encode('utf-8')).hexdigest()

    def compute_nli_scores(self, text1: str, text2: str) -> NLIResult:
        """
        计算两个文本之间的完整NLI分数

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        try:
            if self.verbose:
                logger.info(f"Computing NLI scores for: '{text1[:50]}...' vs '{text2[:50]}...'")
                logger.info(f"Method: {'Sampling' if self.use_sampling else 'JSON Parsing'}")

            # 根据配置选择方法
            if self.use_sampling:
                # 使用采样方法估计概率分布
                nli_result = self._estimate_scores_via_sampling(text1, text2, self.num_samples)
            else:
                # 使用JSON解析方法
                prompt = self.nli_prompt_template.format(premise=text1, hypothesis=text2)

                # 调用Ollama
                response = self._call_ollama(prompt)
                if response is None:
                    logger.warning("Failed to get response from Ollama")
                    return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33, logits=None)

                response_text = response.get("response", "")
                if self.verbose:
                    logger.info(f"Ollama response: {response_text[:200]}...")

                # 提取分数
                nli_result = self._extract_nli_scores_from_response(response_text)

            if self.verbose:
                logger.info(f"Final scores - E: {nli_result.entailment:.3f}, "
                          f"N: {nli_result.neutral:.3f}, C: {nli_result.contradiction:.3f}")

            return nli_result

        except Exception as e:
            logger.error(f"Error computing NLI scores: {e}")
            return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33, logits=None)

    def compute_nli_scores_cached(self, text1: str, text2: str) -> NLIResult:
        """计算NLI分数（带缓存）"""
        cache_key = self.get_cache_key(text1, text2)

        # 检查缓存
        if cache_key in self.nli_cache:
            if self.verbose:
                logger.info("Cache hit for NLI computation")
            return self.nli_cache[cache_key]

        # 计算新结果
        result = self.compute_nli_scores(text1, text2)

        # 缓存结果
        self.nli_cache[cache_key] = result

        return result

    def compute_entailment_score(self, text1: str, text2: str) -> float:
        """
        计算entailment分数（向后兼容）

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            float: entailment分数
        """
        nli_result = self.compute_nli_scores(text1, text2)
        return nli_result.entailment

    def compute_similarity_matrix(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """
        计算响应列表的相似度矩阵

        Args:
            responses: 响应文本列表
            use_score: 使用的分数类型 ("entailment", "neutral", "contradiction")

        Returns:
            np.ndarray: 相似度矩阵
        """
        n = len(responses)
        similarity_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    nli_result = self.compute_nli_scores_cached(responses[i], responses[j])
                    if use_score == "entailment":
                        similarity_matrix[i, j] = nli_result.entailment
                    elif use_score == "neutral":
                        similarity_matrix[i, j] = nli_result.neutral
                    elif use_score == "contradiction":
                        similarity_matrix[i, j] = nli_result.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")

        return similarity_matrix

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'total_entries': len(self.nli_cache),
            'model_name': self.model_name,
            'cache_type': 'Memory',
            'ollama_host': self.ollama_host
        }

    def clear_cache(self):
        """清空缓存"""
        self.nli_cache.clear()
        if self.verbose:
            logger.info("Ollama NLI cache cleared")


def get_available_ollama_models(ollama_host: str = "http://localhost:11434") -> List[str]:
    """获取可用的Ollama模型列表"""
    try:
        response = requests.get(f"{ollama_host}/api/tags")
        if response.status_code == 200:
            models = response.json().get("models", [])
            return [model["name"] for model in models]
        else:
            logger.error(f"Failed to get Ollama models: {response.status_code}")
            return []
    except Exception as e:
        logger.error(f"Error getting Ollama models: {e}")
        return []


class VLLMNLICalculator:
    """基于vLLM的NLI计算器，支持真正的logprobs功能"""

    def __init__(self, model_name: str, verbose: bool = False,
                 vllm_host: str = "http://localhost:8000", api_key: str = "token-abc123"):
        """
        初始化vLLM NLI计算器

        Args:
            model_name: vLLM服务的模型名称
            verbose: 是否输出详细信息
            vllm_host: vLLM服务地址
            api_key: API密钥
        """
        self.model_name = model_name
        self.verbose = verbose
        self.vllm_host = vllm_host
        self.api_key = api_key
        self.nli_cache = {}  # 内存缓存

        if self.verbose:
            logger.info(f"Initializing vLLM NLI Calculator with model: {model_name}")

        # 验证vLLM服务是否可用
        self._verify_vllm_availability()

    def _verify_vllm_availability(self):
        """验证vLLM服务是否可用"""
        try:
            response = requests.get(f"{self.vllm_host}/v1/models",
                                  headers={"Authorization": f"Bearer {self.api_key}"})
            if response.status_code == 200:
                models = response.json().get("data", [])
                available_models = [model["id"] for model in models]
                if self.model_name not in available_models:
                    logger.warning(f"Model {self.model_name} not found in available models: {available_models}")
                elif self.verbose:
                    logger.info(f"Model {self.model_name} is available on vLLM server")
            else:
                logger.error(f"Failed to connect to vLLM at {self.vllm_host}")
        except Exception as e:
            logger.error(f"Error verifying vLLM availability: {e}")

    def _call_vllm_with_logprobs(self, prompt: str, target_tokens: List[str]) -> Dict[str, float]:
        """
        调用vLLM API获取特定token的logprobs

        Args:
            prompt: 输入提示词
            target_tokens: 目标token列表（如["entailment", "neutral", "contradiction"]）

        Returns:
            Dict[str, float]: token到概率的映射
        """
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": 1,
                "temperature": 0.0,
                "logprobs": 10,  # 返回top-10的logprobs
                "echo": False
            }

            response = requests.post(
                f"{self.vllm_host}/v1/completions",
                json=payload,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                choices = result.get("choices", [])
                if choices:
                    logprobs_data = choices[0].get("logprobs", {})
                    top_logprobs = logprobs_data.get("top_logprobs", [])

                    if top_logprobs:
                        # 获取第一个token位置的logprobs
                        token_logprobs = top_logprobs[0]

                        # 提取目标token的概率
                        token_probs = {}
                        for token in target_tokens:
                            # 尝试不同的token变体（大小写、空格等）
                            variants = [token, token.lower(), token.upper(), token.capitalize(),
                                      f" {token}", f" {token.lower()}", f" {token.upper()}"]

                            found_prob = None
                            for variant in variants:
                                if variant in token_logprobs:
                                    found_prob = np.exp(token_logprobs[variant])  # 转换logprob到概率
                                    break

                            token_probs[token] = found_prob if found_prob is not None else 0.0

                        if self.verbose:
                            logger.info(f"Token probabilities: {token_probs}")

                        return token_probs

            logger.warning(f"Failed to get logprobs from vLLM: {response.status_code}")
            return {token: 0.33 for token in target_tokens}  # 默认均匀分布

        except Exception as e:
            logger.error(f"Error calling vLLM API: {e}")
            return {token: 0.33 for token in target_tokens}

    def compute_nli_scores_logprobs(self, text1: str, text2: str) -> NLIResult:
        """
        使用logprobs方法计算NLI分数

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        try:
            if self.verbose:
                logger.info(f"Computing NLI scores using logprobs for: '{text1[:50]}...' vs '{text2[:50]}...'")

            # 构建提示词，让模型预测下一个token
            prompt = f"""Given the premise and hypothesis below, determine their relationship.

Premise: {text1}
Hypothesis: {text2}

The relationship is:"""

            # 获取目标token的概率
            target_tokens = ["entailment", "neutral", "contradiction"]
            token_probs = self._call_vllm_with_logprobs(prompt, target_tokens)

            # 归一化概率
            total_prob = sum(token_probs.values())
            if total_prob > 0:
                entailment_prob = token_probs["entailment"] / total_prob
                neutral_prob = token_probs["neutral"] / total_prob
                contradiction_prob = token_probs["contradiction"] / total_prob
            else:
                # 如果所有概率都是0，使用均匀分布
                entailment_prob = neutral_prob = contradiction_prob = 1.0 / 3

            if self.verbose:
                logger.info(f"Normalized scores - E: {entailment_prob:.3f}, "
                          f"N: {neutral_prob:.3f}, C: {contradiction_prob:.3f}")

            return NLIResult(
                entailment=entailment_prob,
                neutral=neutral_prob,
                contradiction=contradiction_prob,
                logits=None
            )

        except Exception as e:
            logger.error(f"Error computing NLI scores with logprobs: {e}")
            return NLIResult(entailment=0.33, neutral=0.34, contradiction=0.33, logits=None)

    def get_cache_key(self, text1: str, text2: str) -> str:
        """生成缓存键"""
        combined_text = f"{text1}||{text2}||{self.model_name}"
        return hashlib.md5(combined_text.encode('utf-8')).hexdigest()

    def compute_nli_scores(self, text1: str, text2: str) -> NLIResult:
        """
        计算两个文本之间的完整NLI分数（主要接口）

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            NLIResult: 包含entailment, neutral, contradiction三个分数
        """
        return self.compute_nli_scores_logprobs(text1, text2)

    def compute_nli_scores_cached(self, text1: str, text2: str) -> NLIResult:
        """计算NLI分数（带缓存）"""
        cache_key = self.get_cache_key(text1, text2)

        # 检查缓存
        if cache_key in self.nli_cache:
            if self.verbose:
                logger.info("Cache hit for NLI computation")
            return self.nli_cache[cache_key]

        # 计算新结果
        result = self.compute_nli_scores(text1, text2)

        # 缓存结果
        self.nli_cache[cache_key] = result

        return result

    def compute_entailment_score(self, text1: str, text2: str) -> float:
        """
        计算entailment分数（向后兼容）

        Args:
            text1: 前提文本
            text2: 假设文本

        Returns:
            float: entailment分数
        """
        nli_result = self.compute_nli_scores(text1, text2)
        return nli_result.entailment

    def compute_similarity_matrix(self, responses: List[str], use_score: str = "entailment") -> np.ndarray:
        """
        计算响应列表的相似度矩阵

        Args:
            responses: 响应文本列表
            use_score: 使用的分数类型 ("entailment", "neutral", "contradiction")

        Returns:
            np.ndarray: 相似度矩阵
        """
        n = len(responses)
        similarity_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    nli_result = self.compute_nli_scores_cached(responses[i], responses[j])
                    if use_score == "entailment":
                        similarity_matrix[i, j] = nli_result.entailment
                    elif use_score == "neutral":
                        similarity_matrix[i, j] = nli_result.neutral
                    elif use_score == "contradiction":
                        similarity_matrix[i, j] = nli_result.contradiction
                    else:
                        raise ValueError(f"Unknown score type: {use_score}")

        return similarity_matrix

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'total_entries': len(self.nli_cache),
            'model_name': self.model_name,
            'cache_type': 'Memory',
            'vllm_host': self.vllm_host
        }

    def clear_cache(self):
        """清空缓存"""
        self.nli_cache.clear()
        if self.verbose:
            logger.info("vLLM NLI cache cleared")
