#!/usr/bin/env python3
"""
导出测试结果摘要到CSV文件
"""

import csv
import os
from datetime import datetime
from pymongo import MongoClient
import yaml

def load_config(config_file: str = "config.yaml"):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"Error loading config: {e}")
        return {}

def export_test_results_summary():
    """导出测试结果摘要"""
    config = load_config()
    
    # 连接MongoDB
    output_config = config.get('output', {})
    mongo_config = output_config.get('mongo', {})
    
    client = MongoClient(f"mongodb://{mongo_config.get('host', 'localhost')}:{mongo_config.get('port', 27017)}/")
    db = client[mongo_config.get('database', 'LLM-UQ')]
    
    # 使用测试collection
    test_collection_name = mongo_config.get('test_collection', 'test_response')
    collection = db[test_collection_name]
    
    print(f"连接到MongoDB collection: {test_collection_name}")
    
    # 查询所有测试数据
    cursor = collection.find({}).sort("execution_timestamp", 1)
    
    # 准备CSV文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"test_results_summary_{timestamp}.csv"
    
    # 定义简化的CSV字段
    fieldnames = [
        'task_name',
        'dataset_source', 
        'prompt_index',
        'attempt_number',
        'input_text_preview',
        'reference_answer',
        'model_response',
        'parsed_answer',
        'model_name',
        'timestamp'
    ]
    
    # 写入CSV文件
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        count = 0
        for doc in cursor:
            # 准备行数据
            row = {
                'task_name': doc.get('task_name', 'Unknown'),
                'dataset_source': doc.get('dataset_source', ''),
                'prompt_index': doc.get('prompt_index', ''),
                'attempt_number': doc.get('task_attempt_prompt', ''),
                'input_text_preview': (doc.get('input_text', '')[:100] + '...' if len(str(doc.get('input_text', ''))) > 100 else doc.get('input_text', '')),
                'reference_answer': doc.get('reference_answer', ''),
                'model_response': (doc.get('actual_response', doc.get('raw_response', ''))[:200] + '...' if len(str(doc.get('actual_response', doc.get('raw_response', '')))) > 200 else doc.get('actual_response', doc.get('raw_response', ''))),
                'parsed_answer': doc.get('parsed_answer', ''),
                'model_name': doc.get('model_identifier', ''),
                'timestamp': doc.get('execution_timestamp', '').isoformat() if doc.get('execution_timestamp') else ''
            }
            
            writer.writerow(row)
            count += 1
    
    print(f"导出完成！")
    print(f"文件名: {csv_filename}")
    print(f"导出记录数: {count}")
    
    # 显示统计信息
    print("\n测试结果摘要:")
    
    # 按任务和答案统计
    pipeline = [
        {"$group": {
            "_id": {
                "task_name": "$task_name",
                "dataset_source": "$dataset_source",
                "parsed_answer": "$parsed_answer"
            },
            "count": {"$sum": 1}
        }},
        {"$sort": {"_id.task_name": 1, "_id.parsed_answer": 1}}
    ]
    
    answer_stats = list(collection.aggregate(pipeline))
    print("\n按任务和答案统计:")
    current_task = None
    for stat in answer_stats:
        task_name = stat['_id'].get('task_name', 'Unknown')
        dataset_source = stat['_id'].get('dataset_source', '')
        parsed_answer = stat['_id'].get('parsed_answer', 'Empty')
        
        if task_name != current_task:
            print(f"\n{task_name} ({dataset_source}):")
            current_task = task_name
        
        print(f"  {parsed_answer}: {stat['count']} 次")
    
    client.close()
    return csv_filename

if __name__ == "__main__":
    export_test_results_summary()
