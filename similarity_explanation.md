# 相似度计算详解

## 什么是相似度？

在不确定性量化的上下文中，**相似度**是用来衡量两个文本响应之间语义或词汇相似程度的数值指标。相似度的范围通常在 0 到 1 之间：
- **0** 表示完全不相似
- **1** 表示完全相同
- **0.5** 表示中等相似度

## 为什么需要相似度？

在 EigValLaplacian 方法中，相似度用于：

1. **构建相似度矩阵 W**：描述所有响应对之间的相似关系
2. **计算图拉普拉斯矩阵**：基于相似度构建图结构
3. **量化不确定性**：相似度越高的响应集合，不确定性越低

## 两种相似度计算方法

### 1. Jaccard 相似度（词汇层面）

**原理**：基于词汇重叠计算相似度

**公式**：
```
Jaccard(A, B) = |A ∩ B| / |A ∪ B|
```

**计算步骤**：
1. 将文本转换为小写并分词
2. 转换为词汇集合
3. 计算交集和并集
4. 相似度 = 交集大小 / 并集大小

**示例**：
```python
text1 = "The cat is sleeping"
text2 = "A cat is resting"

words1 = {"the", "cat", "is", "sleeping"}
words2 = {"a", "cat", "is", "resting"}

intersection = {"cat", "is"}  # 2个词
union = {"the", "cat", "is", "sleeping", "a", "resting"}  # 6个词

jaccard_similarity = 2/6 = 0.333
```

**特点**：
- ✅ 计算简单快速
- ✅ 不需要预训练模型
- ❌ 只考虑词汇重叠，忽略语义
- ❌ 对同义词不敏感

### 2. NLI 相似度（语义层面）

**原理**：使用自然语言推理（Natural Language Inference）模型计算语义相似度

**NLI 模型输出三个概率**：
- **Entailment（蕴含）**：text1 逻辑上蕴含 text2
- **Contradiction（矛盾）**：text1 与 text2 矛盾
- **Neutral（中性）**：text1 与 text2 无明确逻辑关系

**两种 Affinity 模式**：

#### Entail 模式
```python
similarity = P(entailment)
```
- 直接使用蕴含概率作为相似度
- 高蕴含概率 → 高相似度

#### Contra 模式  
```python
similarity = 1 - P(contradiction)
```
- 使用 1 减去矛盾概率
- 低矛盾概率 → 高相似度

**示例**：
```python
text1 = "The cat is sleeping on the mat"
text2 = "A cat is resting on a rug"

# NLI 模型输出：
# entailment: 0.85
# neutral: 0.10  
# contradiction: 0.05

# Entail 模式相似度 = 0.85
# Contra 模式相似度 = 1 - 0.05 = 0.95
```

**特点**：
- ✅ 考虑语义相似性
- ✅ 能识别同义词和释义
- ✅ 基于大规模预训练模型
- ❌ 计算开销较大
- ❌ 需要 GPU 加速

## 相似度矩阵的构建

对于 n 个响应，构建 n×n 的相似度矩阵 W：

```python
W[i,j] = similarity(response_i, response_j)
```

**重要特性**：
1. **对角线为 1**：W[i,i] = 1（自相似度）
2. **对称性**：W[i,j] = W[j,i]
3. **双向平均**：W[i,j] = (sim(i→j) + sim(j→i)) / 2

## 实际测试结果分析

从我们的测试结果可以看出不同相似度方法的效果：

```
=== 测试数据 ===
responses = [
    "The cat is sleeping on the mat.",      # 猫在垫子上睡觉
    "A cat is resting on a rug.",           # 猫在地毯上休息（语义相似）
    "The dog is running in the park.",      # 狗在公园跑步
    "A canine is jogging through the garden.", # 犬类在花园慢跑（语义相似）
    "The weather is sunny today."           # 今天天气晴朗（完全不同）
]

=== 结果对比 ===
方法                平均相似度    不确定性分数    解释
NLI Entail         0.0978        3.9581         低相似度→高不确定性
NLI Contra         0.4922        2.0785         高相似度→低不确定性  
Jaccard            0.1914        2.8385         中等相似度→中等不确定性
```

**结果解释**：
- **NLI Entail** 更严格，只有强蕴含关系才给高分
- **NLI Contra** 更宽松，只要不矛盾就给较高分
- **Jaccard** 基于词汇重叠，介于两者之间

## 选择建议

**使用 Jaccard 相似度当**：
- 计算资源有限
- 需要快速计算
- 文本主要是词汇层面的变化

**使用 NLI 相似度当**：
- 需要语义理解
- 有 GPU 计算资源
- 文本包含释义、同义词替换
- 需要更精确的语义相似度

**Entail vs Contra**：
- **Entail**：更保守，适合需要严格语义一致性的场景
- **Contra**：更宽松，适合容忍语义变化的场景
