"""
Small test harness to run selected UQ methods over a few Mongo groups,
then save results to MongoDB test collection and a local JSON file.

Usage examples:
  python test_uq_methods.py \
    --mongo-db LLM-UQ \
    --source-collection response_collections \
    --test-results-collection UQ_results_test \
    --methods eigval_jaccard,ecc_jaccard,eigval_nli,ecc_nli,se,numsets,embed_qwen,embed_e5 \
    --limit-groups 3 \
    --output-json ./uq_test_results.json
"""

import argparse
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple

# Optional YAML config
try:
    import yaml  # type: ignore
except Exception:
    yaml = None


def _apply_yaml_config(args):
    if not getattr(args, "config", None):
        return args
    if yaml is None:
        raise RuntimeError("PyYAML is required to use --config. Install via `pip install pyyaml`.")
    with open(args.config, "r") as f:
        cfg = yaml.safe_load(f) or {}
    section = cfg.get("uq_test") or cfg
    mongo = section.get("mongo", {})
    filters = section.get("filters", {})
    args.mongo_host = mongo.get("host", args.mongo_host)
    args.mongo_port = int(mongo.get("port", args.mongo_port))
    args.mongo_db = mongo.get("db", args.mongo_db)
    args.source_collection = mongo.get("source_collection", args.source_collection)
    args.test_results_collection = mongo.get("test_results_collection", args.test_results_collection)
    if isinstance(section.get("methods"), list):
        args.methods = ",".join(section.get("methods"))
    args.dataset_source = filters.get("dataset_source", args.dataset_source)
    args.prompt_variant = filters.get("prompt_variant", args.prompt_variant)
    if "limit_groups" in section:
        args.limit_groups = int(section.get("limit_groups", args.limit_groups))
    args.output_json = section.get("output_json", args.output_json)
    return args

from pymongo import MongoClient
from tqdm import tqdm

# Jaccard and NLI methods
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ
from uq_methods.implementations.eccentricity_jaccard import EccentricityJaccardUQ
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIEntailUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.num_sets import NumSetsUQ
from uq_methods.base import SemanticEntropyUQ

# Embedding methods (moved out)
from uq_methods.implementations.embedding_qwen import EmbeddingQwenUQ
from uq_methods.implementations.embedding_e5 import EmbeddingE5UQ


def parse_args():
    p = argparse.ArgumentParser(description="Test selected UQ methods and save to Mongo test collection and JSON.")
    p.add_argument("--mongo-host", default="localhost")
    p.add_argument("--mongo-port", type=int, default=27017)
    p.add_argument("--mongo-db", default="LLM-UQ")
    p.add_argument("--source-collection", default="response_collections")
    p.add_argument("--test-results-collection", default="UQ_results_test")
    p.add_argument("--methods", default="eigval_jaccard,ecc_jaccard,eigval_nli,ecc_nli,se,numsets,embed_qwen,embed_e5")
    p.add_argument("--dataset-source", default=None)
    p.add_argument("--prompt-variant", default=None)
    p.add_argument("--limit-groups", type=int, default=5)
    p.add_argument("--output-json", default="uq_test_results.json")
    p.add_argument("--config", default=None, help="Path to YAML config that can override CLI args")
    args = p.parse_args()
    _apply_yaml_config(args)
    return args


def _infer_reference_text(docs: List[Dict[str, Any]]) -> Optional[str]:
    try:
        refs = [d.get("reference_answer") for d in docs if d.get("reference_answer")]
        if refs:
            from collections import Counter
            return Counter(refs).most_common(1)[0][0]
    except Exception:
        pass
    return None


def _group_cursor(col_src, dataset_source: Optional[str], prompt_variant: Optional[str], limit_groups: int):
    match: Dict[str, Any] = {}
    if dataset_source:
        match["dataset_source"] = dataset_source
    if prompt_variant:
        match["prompt_variant"] = prompt_variant
    pipeline = [
        {"$match": match} if match else {"$match": {}},
        {
            "$group": {
                "_id": {
                    "task_type": "$task_name",
                    "dataset_source": "$dataset_source",
                    "prompt_variant": "$prompt_variant",
                    "prompt_seed": "$prompt_seed",
                    "prompt_index": "$prompt_index",
                },
                "count": {"$sum": 1},
            }
        },
        {"$sort": {"_id.dataset_source": 1, "_id.task_type": 1}},
    ]
    if limit_groups and limit_groups > 0:
        pipeline.append({"$limit": int(limit_groups)})
    return col_src.aggregate(pipeline, allowDiskUse=True)


def _fetch_group_docs(col_src, gid: Dict[str, Any]) -> List[Dict[str, Any]]:
    q = {
        "task_name": gid["task_type"],
        "dataset_source": gid["dataset_source"],
        "prompt_variant": gid["prompt_variant"],
        "prompt_seed": gid["prompt_seed"],
        "prompt_index": gid["prompt_index"],
    }
    return list(col_src.find(q))


def _build_group_key(d0: Dict[str, Any]) -> Dict[str, Any]:
    from analyze_uq_from_mongo import parse_message_id
    task_type = d0.get("task_name")
    dataset_source = d0.get("dataset_source")
    prompt_variant = d0.get("prompt_variant")
    prompt_seed = d0.get("prompt_seed")
    prompt_index = d0.get("prompt_index")
    task_id = d0.get("task_id")
    message_id = parse_message_id(task_id, dataset_source) or str(prompt_seed)
    input_text = d0.get("input_text")
    return {
        "task_type": task_type,
        "dataset_source": dataset_source,
        "prompt_variant": prompt_variant,
        "prompt_seed": prompt_seed,
        "prompt_index": prompt_index,
        "message_id": message_id,
        "input_text": input_text,
    }


def main():
    args = parse_args()
    mongo = MongoClient(f"mongodb://{args.mongo_host}:{args.mongo_port}/")
    db = mongo[args.mongo_db]
    col_src = db[args.source_collection]
    col_out = db[args.test_results_collection]
    try:
        col_out.create_index([
            ("group_key.task_type", 1), ("group_key.dataset_source", 1), ("group_key.prompt_variant", 1),
            ("group_key.prompt_seed", 1), ("group_key.prompt_index", 1), ("group_key.message_id", 1),
            ("method.method_name", 1)
        ], name="uq_test_group_method")
    except Exception:
        pass

    method_flags = [m.strip() for m in args.methods.split(",") if m.strip()]
    methods: List[Tuple[str, Any]] = []
    if "eigval_jaccard" in method_flags:
        methods.append(("EigValLaplacian_Jaccard", EigValLaplacianJaccardUQ()))
    if "ecc_jaccard" in method_flags:
        methods.append(("Eccentricity_Jaccard", EccentricityJaccardUQ()))
    if "eigval_nli" in method_flags:
        methods.append(("EigValLaplacian_NLI_Entail", EigValLaplacianNLIEntailUQ()))
    if "ecc_nli" in method_flags:
        methods.append(("Eccentricity_NLI_Entail", EccentricityNLIEntailUQ()))
    if "se" in method_flags:
        methods.append(("SemanticEntropy", SemanticEntropyUQ()))
    if "numsets" in method_flags:
        methods.append(("NumSets", NumSetsUQ()))
    if "embed_qwen" in method_flags:
        methods.append(("Embedding_Qwen", EmbeddingQwenUQ()))
    if "embed_e5" in method_flags or "embed_nv" in method_flags:
        methods.append(("E5", EmbeddingE5UQ()))

    all_results: List[Dict[str, Any]] = []

    groups = list(_group_cursor(col_src, args.dataset_source, args.prompt_variant, args.limit_groups))
    if not groups:
        print("No groups found.")
        return

    for g in tqdm(groups, desc="Groups"):
        gid = g["_id"]
        docs = _fetch_group_docs(col_src, gid)
        if not docs:
            continue
        group_key = _build_group_key(docs[0])
        responses = [d.get("parsed_answer") for d in docs if d.get("parsed_answer")]
        if len(responses) < 2:
            continue
        reference_text = _infer_reference_text(docs)

        for name, inst in methods:
            try:
                # set reference text for embedding methods
                if hasattr(inst, "set_reference_text"):
                    inst.set_reference_text(reference_text)
                result = inst.compute_uncertainty(responses)
                record = {
                    "group_key": group_key,
                    "method": {
                        "method_name": name,
                        "model_category": None,
                        "method_params": {},
                    },
                    "outputs": {
                        "uq_value": result.get("uncertainty_score") or result.get("uncertainty") or result.get("score"),
                        "metrics": {k: v for k, v in result.items() if k not in ("uncertainty_score", "uncertainty", "score")}
                    },
                    "meta": {
                        "n_responses": len(responses)
                    },
                    "timestamps": {"created_at": datetime.now(timezone.utc)}
                }
                col_out.insert_one(record)
                all_results.append(record)
            except Exception as e:
                record = {
                    "group_key": group_key,
                    "method": {"method_name": name},
                    "outputs": {"uq_value": None, "metrics": {"error": str(e)}},
                    "meta": {"n_responses": len(responses)},
                    "timestamps": {"created_at": datetime.now(timezone.utc)}
                }
                col_out.insert_one(record)
                all_results.append(record)

    with open(args.output_json, "w") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
    print(f"Saved {len(all_results)} test results to Mongo ({args.test_results_collection}) and {args.output_json}")


if __name__ == "__main__":
    main()

