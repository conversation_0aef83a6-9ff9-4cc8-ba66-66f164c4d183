#!/usr/bin/env python3
"""
Progress Manager for UQ Analysis
Handles progress tracking, reporting, and logging.
"""

import os
import sys
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from tqdm import tqdm

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

log = logging.getLogger(__name__)


class ProgressManager:
    """Manages progress tracking and reporting for UQ analysis."""
    
    def __init__(self, progress_config: Dict[str, Any]):
        """
        Initialize the progress manager.
        
        Args:
            progress_config: Progress configuration
        """
        self.report_interval = progress_config.get("progress_report_interval", 50)
        
        # Progress tracking
        self.start_time = None
        self.current_task = None
        self.task_progress = {}
        self.global_stats = {
            "total_groups": 0,
            "processed_groups": 0,
            "total_methods": 0,
            "processed_methods": 0,
            "successful_computations": 0,
            "failed_computations": 0,
            "skipped_computations": 0
        }
        
        # Progress bars
        self.progress_bars = {}
        
    def start_analysis(self, total_tasks: int):
        """
        Start the analysis process.
        
        Args:
            total_tasks: Total number of tasks to process
        """
        self.start_time = time.time()
        log.info(f"Starting UQ analysis with {total_tasks} tasks")
        
        # Create main progress bar
        self.progress_bars["main"] = tqdm(
            total=total_tasks,
            desc="Tasks",
            position=0,
            leave=True
        )
    
    def start_task(self, task_name: str, total_groups: int, total_methods: int):
        """
        Start processing a specific task.
        
        Args:
            task_name: Name of the task
            total_groups: Total number of groups to process
            total_methods: Total number of methods per group
        """
        self.current_task = task_name
        
        self.task_progress[task_name] = {
            "start_time": time.time(),
            "total_groups": total_groups,
            "processed_groups": 0,
            "total_methods": total_methods,
            "processed_methods": 0,
            "successful_computations": 0,
            "failed_computations": 0,
            "skipped_computations": 0
        }
        
        # Update global stats
        self.global_stats["total_groups"] += total_groups
        self.global_stats["total_methods"] += total_methods
        
        log.info(f"Starting task {task_name}: {total_groups} groups, {total_methods} methods each")
        
        # Create task progress bar
        total_computations = total_groups * total_methods
        self.progress_bars[task_name] = tqdm(
            total=total_computations,
            desc=f"{task_name}",
            position=1,
            leave=False
        )
    
    def update_group_progress(self, task_name: str, group_index: int, 
                            successful: int, failed: int, skipped: int):
        """
        Update progress for a processed group.
        
        Args:
            task_name: Name of the task
            group_index: Index of the processed group
            successful: Number of successful computations
            failed: Number of failed computations
            skipped: Number of skipped computations
        """
        if task_name not in self.task_progress:
            return
        
        task_stats = self.task_progress[task_name]
        task_stats["processed_groups"] += 1
        task_stats["successful_computations"] += successful
        task_stats["failed_computations"] += failed
        task_stats["skipped_computations"] += skipped
        task_stats["processed_methods"] += (successful + failed + skipped)
        
        # Update global stats
        self.global_stats["processed_groups"] += 1
        self.global_stats["successful_computations"] += successful
        self.global_stats["failed_computations"] += failed
        self.global_stats["skipped_computations"] += skipped
        self.global_stats["processed_methods"] += (successful + failed + skipped)
        
        # Update progress bar
        if task_name in self.progress_bars:
            self.progress_bars[task_name].update(successful + failed + skipped)
        
        # Report progress at intervals
        if (group_index + 1) % self.report_interval == 0:
            self.report_task_progress(task_name)
    
    def finish_task(self, task_name: str):
        """
        Finish processing a task.
        
        Args:
            task_name: Name of the task
        """
        if task_name not in self.task_progress:
            return
        
        task_stats = self.task_progress[task_name]
        elapsed_time = time.time() - task_stats["start_time"]
        
        log.info(f"Finished task {task_name} in {elapsed_time:.2f}s")
        self.report_task_progress(task_name, final=True)
        
        # Close task progress bar
        if task_name in self.progress_bars:
            self.progress_bars[task_name].close()
            del self.progress_bars[task_name]
        
        # Update main progress bar
        if "main" in self.progress_bars:
            self.progress_bars["main"].update(1)
    
    def finish_analysis(self):
        """Finish the entire analysis process."""
        if self.start_time:
            total_elapsed = time.time() - self.start_time
            log.info(f"Analysis completed in {total_elapsed:.2f}s")
        
        self.report_global_progress(final=True)
        
        # Close all progress bars
        for pbar in self.progress_bars.values():
            pbar.close()
        self.progress_bars.clear()
    
    def report_task_progress(self, task_name: str, final: bool = False):
        """
        Report progress for a specific task.
        
        Args:
            task_name: Name of the task
            final: Whether this is the final report
        """
        if task_name not in self.task_progress:
            return
        
        stats = self.task_progress[task_name]
        elapsed = time.time() - stats["start_time"]
        
        processed_groups = stats["processed_groups"]
        total_groups = stats["total_groups"]
        successful = stats["successful_computations"]
        failed = stats["failed_computations"]
        skipped = stats["skipped_computations"]
        
        completion_pct = (processed_groups / total_groups * 100) if total_groups > 0 else 0
        
        status = "COMPLETED" if final else "PROGRESS"
        
        log.info(f"[{status}] {task_name}: "
                f"{processed_groups}/{total_groups} groups ({completion_pct:.1f}%) "
                f"in {elapsed:.1f}s - "
                f"Success: {successful}, Failed: {failed}, Skipped: {skipped}")
        
        if not final and total_groups > 0:
            # Estimate remaining time
            rate = processed_groups / elapsed if elapsed > 0 else 0
            if rate > 0:
                remaining_groups = total_groups - processed_groups
                eta = remaining_groups / rate
                log.info(f"[ETA] {task_name}: {eta:.1f}s remaining")
    
    def report_global_progress(self, final: bool = False):
        """
        Report global progress across all tasks.
        
        Args:
            final: Whether this is the final report
        """
        stats = self.global_stats
        elapsed = time.time() - self.start_time if self.start_time else 0
        
        status = "FINAL SUMMARY" if final else "GLOBAL PROGRESS"
        
        log.info(f"[{status}] "
                f"Groups: {stats['processed_groups']}/{stats['total_groups']}, "
                f"Methods: {stats['processed_methods']}/{stats['total_methods']}, "
                f"Success: {stats['successful_computations']}, "
                f"Failed: {stats['failed_computations']}, "
                f"Skipped: {stats['skipped_computations']}, "
                f"Time: {elapsed:.1f}s")
    
    def get_task_stats(self, task_name: str) -> Optional[Dict[str, Any]]:
        """
        Get statistics for a specific task.
        
        Args:
            task_name: Name of the task
            
        Returns:
            Task statistics or None
        """
        return self.task_progress.get(task_name)
    
    def get_global_stats(self) -> Dict[str, Any]:
        """
        Get global statistics.
        
        Returns:
            Global statistics dictionary
        """
        stats = self.global_stats.copy()
        if self.start_time:
            stats["elapsed_time"] = time.time() - self.start_time
        return stats
    
    def log_error(self, task_name: str, group_key: Dict[str, Any],
                 method_name: str, error: str):
        """
        Log an error that occurred during processing.

        Args:
            task_name: Name of the task
            group_key: Group identifier
            method_name: UQ method name
            error: Error message
        """
        input_hash = str(hash(group_key.get('input_text', '')))[:8]
        log.error(f"[{task_name}] {method_name} failed for group "
                 f"(seed={group_key.get('prompt_seed')}, "
                 f"input_hash={input_hash}): {error}")
    
    def log_success(self, task_name: str, group_key: Dict[str, Any],
                   method_name: str, uq_value: float):
        """
        Log a successful computation.

        Args:
            task_name: Name of the task
            group_key: Group identifier
            method_name: UQ method name
            uq_value: Computed UQ value
        """
        input_hash = str(hash(group_key.get('input_text', '')))[:8]
        log.debug(f"[{task_name}] {method_name} computed UQ={uq_value:.4f} for group "
                 f"(seed={group_key.get('prompt_seed')}, "
                 f"input_hash={input_hash})")
    
    def log_skip(self, task_name: str, group_key: Dict[str, Any],
                method_name: str, reason: str = "already computed"):
        """
        Log a skipped computation.

        Args:
            task_name: Name of the task
            group_key: Group identifier
            method_name: UQ method name
            reason: Reason for skipping
        """
        input_hash = str(hash(group_key.get('input_text', '')))[:8]
        log.debug(f"[{task_name}] {method_name} skipped for group "
                 f"(seed={group_key.get('prompt_seed')}, "
                 f"input_hash={input_hash}): {reason}")


def setup_logging(logging_config: Dict[str, Any]):
    """
    Setup logging configuration.
    
    Args:
        logging_config: Logging configuration
    """
    level = getattr(logging, logging_config.get("level", "INFO").upper())
    format_str = logging_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    log_file = logging_config.get("file")
    
    # Create logs directory if needed
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[
            logging.StreamHandler(),  # Console output
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("pymongo").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
