#!/usr/bin/env python3
"""
UQ Method Discovery and Loading System
Automatically discovers and loads all UQ method implementations.
"""

import os
import sys
import importlib
import inspect
import logging
from typing import Dict, List, Type, Any, Optional
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


class UQMethodLoader:
    """Discovers and loads UQ method implementations."""
    
    def __init__(self, implementations_dir: str = "uq_methods/implementations"):
        """
        Initialize the method loader.
        
        Args:
            implementations_dir: Directory containing UQ method implementations
        """
        self.implementations_dir = Path(implementations_dir)
        self.loaded_methods: Dict[str, Type[BaseUQMethod]] = {}
        self.method_instances: Dict[str, BaseUQMethod] = {}
        
    def discover_methods(self) -> List[str]:
        """
        Discover all UQ method implementations in the implementations directory.
        
        Returns:
            List of discovered method class names
        """
        discovered = []
        
        if not self.implementations_dir.exists():
            log.warning(f"Implementations directory not found: {self.implementations_dir}")
            return discovered
            
        # Scan all Python files in the implementations directory
        for py_file in self.implementations_dir.glob("*.py"):
            if py_file.name.startswith("__"):
                continue
                
            module_name = py_file.stem
            try:
                # Import the module
                module_path = f"uq_methods.implementations.{module_name}"
                module = importlib.import_module(module_path)
                
                # Find classes that inherit from BaseUQMethod
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (obj != BaseUQMethod and 
                        issubclass(obj, BaseUQMethod) and 
                        obj.__module__ == module_path):
                        
                        self.loaded_methods[name] = obj
                        discovered.append(name)
                        log.debug(f"Discovered UQ method: {name} from {module_path}")
                        
            except Exception as e:
                log.warning(f"Failed to import {module_path}: {str(e)}")
                continue
                
        log.info(f"Discovered {len(discovered)} UQ methods: {discovered}")
        return discovered
    
    def load_method(self, method_name: str, method_params: Optional[Dict[str, Any]] = None) -> Optional[BaseUQMethod]:
        """
        Load and instantiate a specific UQ method.
        
        Args:
            method_name: Name of the UQ method class
            method_params: Parameters to pass to the method constructor
            
        Returns:
            Instantiated UQ method or None if failed
        """
        if method_name not in self.loaded_methods:
            log.error(f"Method {method_name} not found in discovered methods")
            return None
            
        try:
            method_class = self.loaded_methods[method_name]
            
            # Create instance with parameters
            if method_params:
                # Filter parameters that are accepted by the constructor
                sig = inspect.signature(method_class.__init__)
                valid_params = {k: v for k, v in method_params.items() 
                              if k in sig.parameters}
                instance = method_class(**valid_params)
            else:
                instance = method_class()
                
            self.method_instances[method_name] = instance
            log.debug(f"Loaded UQ method: {method_name}")
            return instance
            
        except Exception as e:
            log.error(f"Failed to load method {method_name}: {str(e)}")
            return None
    
    def load_enabled_methods(self, enabled_methods: List[str], 
                           method_params: Dict[str, Dict[str, Any]]) -> Dict[str, BaseUQMethod]:
        """
        Load all enabled UQ methods.
        
        Args:
            enabled_methods: List of method names to load
            method_params: Dictionary of method-specific parameters
            
        Returns:
            Dictionary mapping method names to instances
        """
        loaded = {}
        
        for method_name in enabled_methods:
            params = method_params.get(method_name, {})
            instance = self.load_method(method_name, params)
            if instance:
                loaded[method_name] = instance
            else:
                log.warning(f"Failed to load enabled method: {method_name}")
                
        log.info(f"Successfully loaded {len(loaded)} methods: {list(loaded.keys())}")
        return loaded
    
    def get_method_info(self, method_name: str) -> Dict[str, Any]:
        """
        Get information about a specific method.
        
        Args:
            method_name: Name of the method
            
        Returns:
            Dictionary containing method information
        """
        if method_name not in self.loaded_methods:
            return {}
            
        method_class = self.loaded_methods[method_name]
        
        info = {
            "class_name": method_name,
            "module": method_class.__module__,
            "doc": method_class.__doc__ or "",
            "required_samples": None
        }
        
        # Try to get required samples if method is instantiated
        if method_name in self.method_instances:
            try:
                info["required_samples"] = self.method_instances[method_name].get_required_samples()
            except:
                pass
                
        return info
    
    def list_all_methods(self) -> Dict[str, Dict[str, Any]]:
        """
        List all discovered methods with their information.
        
        Returns:
            Dictionary mapping method names to their information
        """
        return {name: self.get_method_info(name) for name in self.loaded_methods.keys()}


def create_method_loader() -> UQMethodLoader:
    """Create and initialize a UQ method loader."""
    loader = UQMethodLoader()
    loader.discover_methods()
    return loader


if __name__ == "__main__":
    # Test the method loader
    logging.basicConfig(level=logging.DEBUG)
    
    loader = create_method_loader()
    methods = loader.list_all_methods()
    
    print("Discovered UQ Methods:")
    for name, info in methods.items():
        print(f"  {name}: {info['module']}")
        if info['doc']:
            print(f"    {info['doc'][:100]}...")
