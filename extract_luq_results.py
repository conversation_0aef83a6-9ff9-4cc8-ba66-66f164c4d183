#!/usr/bin/env python3
"""
Extract LUQ analysis results from MongoDB and save as JSON
专门提取LUQ分析结果用于检查
"""

import json
import datetime
from typing import Dict, List, Any
from pymongo import MongoClient

def clean_for_json(obj):
    """Clean MongoDB objects for JSON serialization"""
    if isinstance(obj, dict):
        return {k: clean_for_json(v) for k, v in obj.items() if k != '_id'}
    elif isinstance(obj, list):
        return [clean_for_json(item) for item in obj]
    elif isinstance(obj, datetime.datetime):
        return obj.isoformat()
    else:
        return obj

def extract_luq_results():
    """Extract LUQ results from MongoDB"""
    print("🔍 Extracting LUQ Results from MongoDB")
    print("=" * 50)
    
    # Connect to MongoDB
    client = MongoClient("localhost", 27017)
    db = client["LLM-UQ"]
    collection = db["UQ_result_LUQ_counterfactual"]
    
    try:
        # Get all LUQ results
        results = list(collection.find({}))
        print(f"Found {len(results)} LUQ result documents")
        
        if not results:
            print("❌ No LUQ results found")
            return
        
        # Extract and structure the data
        extracted_data = {
            "extraction_info": {
                "timestamp": datetime.datetime.now().isoformat(),
                "total_documents": len(results),
                "collection": "UQ_result_LUQ_counterfactual",
                "method": "LUQUQ"
            },
            "results": []
        }
        
        for i, result in enumerate(results):
            print(f"Processing document {i+1}/{len(results)}")
            
            # Extract group information
            group_key = result.get('group_key', {})
            
            # Extract LUQ results
            uq_results = result.get('uq_results', {})
            luq_data = uq_results.get('LUQUQ', {})
            
            if 'full_result' not in luq_data:
                print(f"  ⚠️  Document {i+1} missing full_result")
                continue
                
            full_result = luq_data['full_result']
            
            # Structure the extracted data
            extracted_result = {
                "document_id": i + 1,
                "group_info": {
                    "task_name": group_key.get('task_name'),
                    "dataset_source": group_key.get('dataset_source'),
                    "prompt_seed": group_key.get('prompt_seed'),
                    "input_hash": group_key.get('input_hash'),
                    "input_text": group_key.get('input_text', '')[:200] + "..." if len(group_key.get('input_text', '')) > 200 else group_key.get('input_text', '')
                },
                "luq_summary": {
                    "uncertainty_score": full_result.get('uncertainty_score'),
                    "overall_consistency": full_result.get('overall_consistency'),
                    "num_responses": full_result.get('num_responses'),
                    "total_sentences": sum(full_result.get('num_sentences_per_response', [])),
                    "avg_sentences_per_response": sum(full_result.get('num_sentences_per_response', [])) / full_result.get('num_responses', 1),
                    "total_nli_computations": full_result.get('metadata', {}).get('total_nli_computations', 0)
                },
                "per_sample_scores": {
                    "luq_scores_per_sample": full_result.get('luq_scores_per_sample', []),
                    "consistency_scores_per_sample": full_result.get('consistency_scores_per_sample', []),
                    "num_sentences_per_response": full_result.get('num_sentences_per_response', [])
                },
                "nli_details": full_result.get('nli_details', []),
                "metadata": {
                    "model_name": full_result.get('metadata', {}).get('model_name'),
                    "method_info": luq_data.get('method_info', {}),
                    "computed_at": luq_data.get('computed_at'),
                    "status": luq_data.get('status')
                }
            }
            
            extracted_data["results"].append(extracted_result)
        
        # Save to JSON file
        output_file = "luq_analysis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(clean_for_json(extracted_data), f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Successfully extracted {len(extracted_data['results'])} results")
        print(f"💾 Saved to: {output_file}")
        
        # Print summary statistics
        print(f"\n📊 Summary Statistics:")
        if extracted_data["results"]:
            uncertainties = [r["luq_summary"]["uncertainty_score"] for r in extracted_data["results"] if r["luq_summary"]["uncertainty_score"] is not None]
            consistencies = [r["luq_summary"]["overall_consistency"] for r in extracted_data["results"] if r["luq_summary"]["overall_consistency"] is not None]
            
            if uncertainties:
                print(f"  Uncertainty scores: {min(uncertainties):.4f} - {max(uncertainties):.4f} (avg: {sum(uncertainties)/len(uncertainties):.4f})")
            if consistencies:
                print(f"  Consistency scores: {min(consistencies):.4f} - {max(consistencies):.4f} (avg: {sum(consistencies)/len(consistencies):.4f})")
            
            total_nli = sum(r["luq_summary"]["total_nli_computations"] for r in extracted_data["results"])
            total_sentences = sum(r["luq_summary"]["total_sentences"] for r in extracted_data["results"])
            print(f"  Total NLI computations: {total_nli:,}")
            print(f"  Total sentences analyzed: {total_sentences:,}")
        
        # Create a simplified summary file
        create_summary_file(extracted_data["results"])
        
    finally:
        client.close()

def create_summary_file(results: List[Dict]):
    """Create a simplified summary file for quick inspection"""
    summary_data = {
        "summary": {
            "total_groups": len(results),
            "extraction_timestamp": datetime.datetime.now().isoformat()
        },
        "groups": []
    }
    
    for result in results:
        summary_data["groups"].append({
            "document_id": result["document_id"],
            "prompt_seed": result["group_info"]["prompt_seed"],
            "input_preview": result["group_info"]["input_text"],
            "uncertainty_score": result["luq_summary"]["uncertainty_score"],
            "consistency_score": result["luq_summary"]["overall_consistency"],
            "num_responses": result["luq_summary"]["num_responses"],
            "total_sentences": result["luq_summary"]["total_sentences"],
            "nli_computations": result["luq_summary"]["total_nli_computations"],
            "nli_details_count": len(result["nli_details"])
        })
    
    # Sort by uncertainty score (descending)
    summary_data["groups"].sort(key=lambda x: x["uncertainty_score"] or 0, reverse=True)
    
    summary_file = "luq_results_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, indent=2, ensure_ascii=False)
    
    print(f"📋 Summary saved to: {summary_file}")

def inspect_nli_details_sample():
    """Inspect a sample of NLI details for verification"""
    try:
        with open("luq_analysis_results.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data["results"]:
            return
        
        # Get first result with NLI details
        first_result = data["results"][0]
        nli_details = first_result.get("nli_details", [])
        
        if not nli_details:
            print("⚠️  No NLI details found in first result")
            return
        
        print(f"\n🔍 NLI Details Sample (First Group):")
        print(f"  Group: Document {first_result['document_id']}")
        print(f"  Prompt seed: {first_result['group_info']['prompt_seed']}")
        print(f"  Total samples: {len(nli_details)}")
        
        # Show first sample's NLI computations
        if nli_details:
            first_sample = nli_details[0]
            print(f"  First sample: {first_sample['num_sentences']} sentences, {first_sample['num_contexts']} contexts")
            print(f"  Sample uncertainty: {first_sample.get('sample_uncertainty', 'N/A')}")
            
            # Show first few NLI computations
            nli_comps = first_sample.get("nli_computations", [])[:5]
            print(f"  First 5 NLI computations:")
            for i, comp in enumerate(nli_comps):
                print(f"    {i+1}. Sentence {comp['s']} vs Context {comp['c']}: e={comp['e']}, n={comp['n']}, d={comp['d']}, luq={comp['l']}")
        
    except Exception as e:
        print(f"❌ Error inspecting NLI details: {e}")

if __name__ == "__main__":
    extract_luq_results()
    print("\n" + "="*50)
    inspect_nli_details_sample()
    print(f"\n🎉 LUQ results extraction completed!")
    print(f"📁 Files created:")
    print(f"  - luq_analysis_results.json (complete results)")
    print(f"  - luq_results_summary.json (quick summary)")
