from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseUQMethod(ABC):
    """极简UQ方法接口"""
    
    @abstractmethod
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        计算不确定性度量
        Args:
            responses: 同一prompt的多次响应
        Returns:
            包含不确定性指标的字典
        """
        pass
    
    @abstractmethod
    def get_required_samples(self) -> int:
        """返回该方法需要的样本数量"""
        pass
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return self.__class__.__name__.replace("UQ", "").lower()

class SemanticEntropyUQ(BaseUQMethod):
    """语义熵UQ方法"""
    
    def __init__(self, similarity_threshold: float = 0.85):
        self.similarity_threshold = similarity_threshold
    
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """计算语义熵"""
        # 简化的语义熵计算
        unique_responses = list(set(responses))
        response_counts = [responses.count(r) for r in unique_responses]
        total = len(responses)
        
        # 计算熵
        import numpy as np
        probabilities = [count/total for count in response_counts]
        entropy = -sum(p * np.log(p) for p in probabilities if p > 0)
        
        return {
            "entropy": entropy,
            "unique_responses": len(unique_responses),
            "total_responses": len(responses),
            "most_common_response": max(set(responses), key=responses.count),
            "response_distribution": dict(zip(unique_responses, response_counts))
        }
    
    def get_required_samples(self) -> int:
        return 10

class ResponseLengthVarianceUQ(BaseUQMethod):
    """响应长度方差UQ方法"""
    
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """计算响应长度的方差"""
        import numpy as np
        
        lengths = [len(response) for response in responses]
        mean_length = np.mean(lengths)
        std_length = np.std(lengths)
        
        return {
            "mean_length": mean_length,
            "std_length": std_length,
            "min_length": min(lengths),
            "max_length": max(lengths),
            "coefficient_of_variation": std_length / mean_length if mean_length > 0 else 0
        }
    
    def get_required_samples(self) -> int:
        return 5