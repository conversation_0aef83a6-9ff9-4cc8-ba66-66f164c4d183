import numpy as np
import logging
from typing import List, Dict, Any
from scipy.linalg import eigh
from uq_methods.base import BaseUQMethod
from core.similarity_cache import jaccard_similarity_cached

log = logging.getLogger(__name__)


class EccentricityJaccardUQ(BaseUQMethod):
    """
    Eccentricity method using Jaccard Score for similarity computation.
    Estimates the sequence-level uncertainty of a language model following the method of
    "Eccentricity" as provided in the paper https://arxiv.org/abs/2305.19187.
    
    Method calculates a frobenious (euclidian) norm between all eigenvectors that are informative embeddings
    of graph Laplacian (lower norm -> closer embeddings -> higher eigenvectors -> greater uncertainty).
    """

    def __init__(self, verbose: bool = False, thres: float = 0.9):
        """
        Initialize Eccentricity with Jaccard score similarity.
        
        Parameters:
            verbose (bool): Whether to print debug information
            thres (float): Threshold for eigenvalue filtering
        """
        self.verbose = verbose
        self.thres = thres

    def _compute_jaccard_similarity(self, text1: str, text2: str) -> float:
        """Compute Jaccard similarity between two texts (cached)."""
        return jaccard_similarity_cached(text1, text2)

    def _compute_similarity_matrix(self, responses: List[str]) -> np.ndarray:
        """Compute similarity matrix using Jaccard scores."""
        n = len(responses)
        W = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    W[i, j] = self._compute_jaccard_similarity(responses[i], responses[j])
        
        W = (W + np.transpose(W)) / 2
        return W

    def _compute_eccentricity(self, responses: List[str]) -> Any:
        """Compute the eccentricity uncertainty score and component vector C_Ecc_s_j."""
        if len(responses) < 2:
            return 0.0, np.asarray([])

        W = self._compute_similarity_matrix(responses)
        D = np.diag(W.sum(axis=1))
        D_sqrt = np.sqrt(D)
        D_sqrt_inv = np.linalg.inv(D_sqrt)
        L = np.eye(D.shape[0]) - D_sqrt_inv @ W @ D_sqrt_inv

        eigenvalues, eigenvectors = eigh(L)

        if self.thres is not None:
            keep_mask = eigenvalues < self.thres
            eigenvalues = eigenvalues[keep_mask]
            eigenvectors = eigenvectors[:, keep_mask]

        smallest_eigenvectors = eigenvectors.T
        C_Ecc_s_j = (-1) * np.asarray(
            [np.linalg.norm(x - x.mean(0), 2) for x in smallest_eigenvectors]
        )
        U_Ecc = np.linalg.norm(C_Ecc_s_j, 2)

        return U_Ecc, C_Ecc_s_j

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """Compute uncertainty using Eccentricity with Jaccard similarity."""
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "error": "Need at least 2 responses",
                "method": "Eccentricity_Jaccard",
                "C_Ecc_s_j": []
            }
        
        try:
            uncertainty_score, C_Ecc_s_j = self._compute_eccentricity(responses)
            similarity_matrix = self._compute_similarity_matrix(responses)
            mean_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])

            if self.verbose:
                log.debug(f"Generated responses: {responses}")
                log.debug(f"Uncertainty score: {uncertainty_score}")
                log.debug(f"Mean similarity: {mean_similarity}")

            return {
                "uncertainty_score": uncertainty_score,
                "C_Ecc_s_j": C_Ecc_s_j.tolist(),
                "mean_similarity": mean_similarity,
                "num_responses": len(responses),
                "method": "Eccentricity_Jaccard",
                "similarity_matrix": similarity_matrix.tolist(),
                "metadata": {
                    "method": "Eccentricity_Jaccard",
                    "similarity_score": "Jaccard_score",
                    "thres": self.thres,
                    "verbose": self.verbose
                }
            }

        except Exception as e:
            log.error(f"Error computing Eccentricity Jaccard uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "Eccentricity_Jaccard",
                "C_Ecc_s_j": []
            }

    def get_required_samples(self) -> int:
        return 5

    def get_method_name(self) -> str:
        return "Eccentricity_Jaccard" 