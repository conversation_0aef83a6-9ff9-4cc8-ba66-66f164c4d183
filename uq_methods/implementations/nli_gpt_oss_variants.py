"""
GPT-OSS:20b变体的NLI UQ方法
为所有使用NLI的UQ方法创建基于gpt-oss:20b的变体版本
"""

import logging
from typing import Dict, Any

# 导入原始UQ方法
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.semantic_entropy import SemanticEntropyNLIUQ
from uq_methods.implementations.num_sets import NumSetsUQ
from uq_methods.implementations.luq import LUQUQ
from uq_methods.implementations.luq_sentence import LUQSENTENCEUQ
from uq_methods.implementations.kernel_language_entropy import KernelLanguageEntropyUQ

# 导入Ollama NLI计算器
from core.nli_calculator import OllamaNLICalculator

log = logging.getLogger(__name__)


class EigValLaplacianNLIUQ_GPT_OSS_20B(EigValLaplacianNLIUQ):
    """
    EigValLaplacian NLI方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """
    
    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        super().__init__(model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,  # 使用JSON解析方式
            verbose=False
        )

        # 更新模型名称标识
        self.model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized EigValLaplacianNLIUQ with GPT-OSS:20b")
    
    def get_method_name(self) -> str:
        """重写方法名"""
        base_name = super().get_method_name()
        return f"{base_name}_GPT_OSS_20B"


class EccentricityNLIEntailUQ_GPT_OSS_20B(EccentricityNLIEntailUQ):
    """
    Eccentricity NLI方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """
    
    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        super().__init__(model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,
            verbose=False
        )

        # 更新模型名称标识
        self.model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized EccentricityNLIEntailUQ with GPT-OSS:20b")
    
    def get_method_name(self) -> str:
        """重写方法名"""
        return f"Eccentricity_NLI_{self.affinity.capitalize()}_GPT_OSS_20B"


class SemanticEntropyNLIUQ_GPT_OSS_20B(SemanticEntropyNLIUQ):
    """
    Semantic Entropy NLI方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """

    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        super().__init__(model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,
            verbose=False
        )

        # 更新模型名称标识
        self.model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized SemanticEntropyNLIUQ with GPT-OSS:20b")
    
    def get_method_name(self) -> str:
        """重写方法名"""
        return "SemanticEntropy_NLI_GPT_OSS_20B"


class NumSetsUQ_GPT_OSS_20B(NumSetsUQ):
    """
    NumSets方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """

    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        super().__init__(model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,
            verbose=False
        )

        # 更新模型名称标识
        self.model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized NumSetsUQ with GPT-OSS:20b")
    
    def get_method_name(self) -> str:
        """重写方法名"""
        return "NumSets_NLI_GPT_OSS_20B"


class LUQUQ_GPT_OSS_20B(LUQUQ):
    """
    LUQ方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """

    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        super().__init__(model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,
            verbose=False
        )

        # 更新模型名称标识
        self.model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized LUQUQ with GPT-OSS:20b")
    
    def get_method_name(self) -> str:
        """重写方法名"""
        return "LUQ_NLI_GPT_OSS_20B"


class LUQSentenceUQ_GPT_OSS_20B(LUQSENTENCEUQ):
    """
    LUQ Sentence方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """

    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        kwargs.pop('nli_model_name', None)
        super().__init__(nli_model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,
            verbose=False
        )

        # 更新模型名称标识
        self.nli_model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized LUQSENTENCEUQ with GPT-OSS:20b")

    def get_method_name(self) -> str:
        """重写方法名"""
        return "LUQSentence_NLI_GPT_OSS_20B"


class KernelLanguageEntropyUQ_GPT_OSS_20B(KernelLanguageEntropyUQ):
    """
    Kernel Language Entropy方法的GPT-OSS:20b变体
    使用gpt-oss:20b模型进行NLI计算
    """

    def __init__(self, **kwargs):
        # 使用默认的DeBERTa模型进行初始化，然后替换NLI计算器
        kwargs.pop('model_name', None)
        super().__init__(model_name="microsoft/deberta-large-mnli", **kwargs)

        # 替换为Ollama NLI计算器
        self.nli_calc = OllamaNLICalculator(
            model_name="gpt-oss:20b",
            use_sampling=False,
            verbose=False
        )

        # 更新模型名称标识
        self.model_name = "gpt-oss:20b"

        if hasattr(self, 'verbose') and self.verbose:
            log.info("Initialized KernelLanguageEntropyUQ with GPT-OSS:20b")
    
    def get_method_name(self) -> str:
        """重写方法名"""
        return "KernelLanguageEntropy_NLI_GPT_OSS_20B"


# 导出所有GPT-OSS变体类
__all__ = [
    'EigValLaplacianNLIUQ_GPT_OSS_20B',
    'EccentricityNLIEntailUQ_GPT_OSS_20B', 
    'SemanticEntropyNLIUQ_GPT_OSS_20B',
    'NumSetsUQ_GPT_OSS_20B',
    'LUQUQ_GPT_OSS_20B',
    'LUQSentenceUQ_GPT_OSS_20B',
    'KernelLanguageEntropyUQ_GPT_OSS_20B'
]


def get_all_gpt_oss_methods() -> Dict[str, Any]:
    """
    获取所有GPT-OSS变体方法的字典
    
    Returns:
        Dict[str, Any]: 方法名到类的映射
    """
    return {
        'eigval_nli_gpt_oss': EigValLaplacianNLIUQ_GPT_OSS_20B,
        'ecc_nli_gpt_oss': EccentricityNLIEntailUQ_GPT_OSS_20B,
        'se_gpt_oss': SemanticEntropyNLIUQ_GPT_OSS_20B,
        'numsets_gpt_oss': NumSetsUQ_GPT_OSS_20B,
        'luq_gpt_oss': LUQUQ_GPT_OSS_20B,
        'luq_sentence_gpt_oss': LUQSentenceUQ_GPT_OSS_20B,
        'kle_gpt_oss': KernelLanguageEntropyUQ_GPT_OSS_20B
    }


def get_gpt_oss_method_display_names() -> Dict[str, str]:
    """
    获取GPT-OSS方法的显示名称映射
    
    Returns:
        Dict[str, str]: 方法键到显示名称的映射
    """
    return {
        'eigval_nli_gpt_oss': 'EigValLaplacian_NLI_GPT-OSS-20B',
        'ecc_nli_gpt_oss': 'Eccentricity_NLI_GPT-OSS-20B',
        'se_gpt_oss': 'SemanticEntropy_NLI_GPT-OSS-20B',
        'numsets_gpt_oss': 'NumSets_NLI_GPT-OSS-20B',
        'luq_gpt_oss': 'LUQ_NLI_GPT-OSS-20B',
        'luq_sentence_gpt_oss': 'LUQSentence_NLI_GPT-OSS-20B',
        'kle_gpt_oss': 'KernelLanguageEntropy_NLI_GPT-OSS-20B'
    }
