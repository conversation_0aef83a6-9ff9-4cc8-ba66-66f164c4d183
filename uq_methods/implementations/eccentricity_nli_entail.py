import numpy as np
import logging

from typing import List, Dict, Any
from scipy.linalg import eigh
from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator

log = logging.getLogger(__name__)


class EccentricityNLIEntailUQ(BaseUQMethod):
    """
    Eccentricity method using NLI Score with configurable affinity ('entail' or 'contra') for similarity computation.
    Estimates the sequence-level uncertainty of a language model following the method of
    "Eccentricity" as provided in the paper https://arxiv.org/abs/2305.19187.

    Method calculates a frobenious (euclidian) norm between all eigenvectors that are informative embeddings
    of graph Laplacian (lower norm -> closer embeddings -> higher eigenvectors -> greater uncertainty).
    """

    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", verbose: bool = False, thres: float = 0.9, affinity: str = "entail"):
        """
        Initialize Eccentricity with NLI score similarity using specified affinity.

        Parameters:
            model_name (str): NLI model to use for similarity computation
            verbose (bool): Whether to print debug information
            thres (float): Threshold for eigenvalue filtering
            affinity (str): 'entail' or 'contra'
        """
        self.model_name = model_name
        self.verbose = verbose
        self.thres = thres
        self.affinity = affinity.lower() if isinstance(affinity, str) else "entail"
        if self.affinity not in ("entail", "contra"):
            log.warning(f"Unknown affinity '{affinity}', defaulting to 'entail'")
            self.affinity = "entail"
        # Use shared cached NLI calculator
        self.nli_calc = get_nli_calculator(model_name)

    def _compute_nli_scores(self, text1: str, text2: str) -> tuple:
        """Compute all three NLI scores between two texts (cached)."""
        try:
            res = self.nli_calc.compute_nli_scores_cached(text1, text2)
            return float(res.entailment), float(res.neutral), float(res.contradiction)
        except Exception as e:
            log.warning(f"Error computing NLI score: {str(e)}")
            return 0.33, 0.34, 0.33  # Default to uniform distribution
    
    def _compute_nli_entail_score(self, text1: str, text2: str) -> float:
        """Compute NLI entailment score between two texts (backward compatibility)."""
        entailment, _, _ = self._compute_nli_scores(text1, text2)
        return entailment

    def _compute_nli_contra_similarity(self, text1: str, text2: str) -> float:
        """Compute similarity as 1 - contradiction between two texts."""
        _, _, contradiction = self._compute_nli_scores(text1, text2)
        return 1.0 - contradiction

    def _compute_similarity_matrix(self, responses: List[str]) -> np.ndarray:
        """Compute similarity matrix using NLI scores with selected affinity, then symmetrize once."""
        n = len(responses)
        W = np.zeros((n, n))

        use_entail = self.affinity == "entail"
        for i in range(n):
            for j in range(n):
                if i == j:
                    W[i, j] = 1.0
                else:
                    if use_entail:
                        W[i, j] = self._compute_nli_entail_score(responses[i], responses[j])
                    else:  # contra
                        W[i, j] = self._compute_nli_contra_similarity(responses[i], responses[j])

        # Symmetrize to match reference implementation
        W = (W + np.transpose(W)) / 2
        return W

    def _compute_eccentricity(self, responses: List[str]) -> Any:
        """Compute the eccentricity uncertainty score and component vector C_Ecc_s_j."""
        if len(responses) < 2:
            return 0.0, np.asarray([])

        W = self._compute_similarity_matrix(responses)
        D = np.diag(W.sum(axis=1))
        D_sqrt = np.sqrt(D)
        D_sqrt_inv = np.linalg.inv(D_sqrt)
        L = np.eye(D.shape[0]) - D_sqrt_inv @ W @ D_sqrt_inv

        eigenvalues, eigenvectors = eigh(L)

        if self.thres is not None:
            keep_mask = eigenvalues < self.thres
            eigenvalues = eigenvalues[keep_mask]
            eigenvectors = eigenvectors[:, keep_mask]

        smallest_eigenvectors = eigenvectors.T
        C_Ecc_s_j = (-1) * np.asarray(
            [np.linalg.norm(x - x.mean(0), 2) for x in smallest_eigenvectors]
        )
        U_Ecc = np.linalg.norm(C_Ecc_s_j, 2)

        return U_Ecc, C_Ecc_s_j

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """Compute uncertainty using Eccentricity with NLI similarity and selected affinity."""
        method_name = "Eccentricity_NLI_Entail" if self.affinity == "entail" else "Eccentricity_NLI_Contra"
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "error": "Need at least 2 responses",
                "method": method_name,
                "C_Ecc_s_j": []
            }

        try:
            uncertainty_score, C_Ecc_s_j = self._compute_eccentricity(responses)
            similarity_matrix = self._compute_similarity_matrix(responses)
            mean_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])

            if self.verbose:
                log.debug(f"Generated responses: {responses}")
                log.debug(f"Uncertainty score: {uncertainty_score}")
                log.debug(f"Mean similarity: {mean_similarity}")

            return {
                "uncertainty_score": uncertainty_score,
                "C_Ecc_s_j": C_Ecc_s_j.tolist(),
                "mean_similarity": mean_similarity,
                "num_responses": len(responses),
                "method": method_name,
                "similarity_matrix": similarity_matrix.tolist(),
                "metadata": {
                    "method": method_name,
                    "similarity_score": "NLI_score",
                    "affinity": self.affinity,
                    "model_name": self.model_name,
                    "thres": self.thres,
                    "verbose": self.verbose
                }
            }

        except Exception as e:
            log.error(f"Error computing Eccentricity NLI {self.affinity.capitalize()} uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": method_name,
                "C_Ecc_s_j": []
            }

    def get_required_samples(self) -> int:
        return 5

    def get_method_name(self) -> str:
        return "Eccentricity_NLI_Entail" if self.affinity == "entail" else "Eccentricity_NLI_Contra"