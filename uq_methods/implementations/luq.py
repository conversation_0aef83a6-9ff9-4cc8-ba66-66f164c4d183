import numpy as np
import logging
from typing import List, Dict, Any

from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator

log = logging.getLogger(__name__)


class LUQUQ(BaseUQMethod):
    """
    LUQ: Long-text Uncertainty Quantification for LLMs

    Estimates sequence-level uncertainty using NLI-based consistency checking
    between different response samples. Based on the paper:
    https://aclanthology.org/2024.emnlp-main.299.pdf
    """

    def __init__(
        self,
        model_name: str = "microsoft/deberta-large-mnli",
        verbose: bool = False
    ):
        """
        Initialize LUQ uncertainty quantification method.

        Args:
            model_name: NLI model name for consistency checking
            verbose: Whether to print debug information
        """
        self.model_name = model_name
        self.verbose = verbose

        # Initialize NLI calculator for consistency checking
        self.nli_calc = get_nli_calculator(model_name)

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences for LUQ analysis.

        Args:
            text: Input text to split

        Returns:
            List of sentences suitable for NLI analysis
        """
        # Clean and preprocess the text first
        text = self._preprocess_text(text)
        if not text:
            return []

        # Use sentence-splitter for sentence segmentation
        sentences = self._nlp_sentence_split(text)

        if sentences and self.verbose:
            log.info(f"Split text ({len(text)} chars) into {len(sentences)} sentences")

        return sentences

    def _preprocess_text(self, text: str) -> str:
        """
        Clean text by removing markdown formatting and noise.

        Args:
            text: Raw input text

        Returns:
            Cleaned text ready for sentence segmentation
        """
        import re

        text = text.strip()
        if not text:
            return ""

        # Remove markdown formatting
        text = re.sub(r'^(#{1,6})\s+(.+)$', r'\2.', text, flags=re.MULTILINE)  # Headers
        text = re.sub(r'^[-=*_]{3,}$', '', text, flags=re.MULTILINE)  # Separators
        text = re.sub(r'\*\*\*(.*?)\*\*\*', r'\1', text)  # Bold italic
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)      # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)          # Italic
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # Links

        # Remove list markers
        text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

        # Normalize whitespace
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = ' '.join(text.split())

        return text

    def _nlp_sentence_split(self, text: str) -> List[str]:
        """
        Split text into sentences using sentence-splitter library.
        """
        from sentence_splitter import SentenceSplitter
        splitter = SentenceSplitter(language='en')
        sentences = splitter.split(text)
        if self.verbose:
            log.info(f"Used sentence-splitter, found {len(sentences)} sentences")
        return sentences

    def _compute_nli_score(self, premise: str, hypothesis: str) -> float:
        """
        Compute NLI-based entailment probability following LUQ paper.

        LUQ focuses on entailment vs contradiction, ignoring neutral.

        Args:
            premise: Complete response text (r')
            hypothesis: Sentence to check (s_j)

        Returns:
            Entailment probability score
        """
        nli_result = self.nli_calc.compute_nli_scores_cached(premise, hypothesis)

        # LUQ paper: normalize between entailment and contradiction only
        entail_prob = nli_result.entailment
        contradict_prob = nli_result.contradiction

        total_prob = entail_prob + contradict_prob
        if total_prob > 0:
            entail_score = entail_prob / total_prob
        else:
            # If both are 0, default to neutral (0.5)
            entail_score = 0.5

        return entail_score

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute LUQ uncertainty score for the given responses.

        Args:
            responses: List of response texts to evaluate

        Returns:
            Dictionary containing uncertainty scores and detailed metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 1.0,
                "error": "Need at least 2 responses for LUQ computation",
                "method": "LUQ"
            }

        try:
            if self.verbose:
                log.info(f"Starting LUQ computation for {len(responses)} responses")

            # Split responses into sentences
            sentences_list = []
            for i, response in enumerate(responses):
                sentences = self._split_into_sentences(response)
                sentences_list.append(sentences)
                if self.verbose:
                    log.info(f"Response {i+1} split into {len(sentences)} sentences")

            # Compute detailed LUQ scores
            detailed_results = self._compute_luq_detailed(sentences_list)

            return {
                "uncertainty_score": detailed_results["overall_uncertainty"],
                "method": "LUQ",
                "num_responses": len(responses),
                "luq_scores_per_sample": detailed_results["luq_scores_per_sample"],
                "consistency_scores_per_sample": detailed_results["consistency_scores_per_sample"],
                "overall_consistency": detailed_results["overall_consistency"],
                "num_sentences_per_response": detailed_results["num_sentences_per_response"],
                "nli_details": detailed_results["nli_details"],
                "metadata": {
                    "model_name": self.model_name,
                    "total_nli_computations": sum(len(detail["nli_computations"]) for detail in detailed_results["nli_details"])
                }
            }

        except Exception as e:
            log.error(f"Error computing LUQ uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "LUQ"
            }

    def _compute_luq_detailed(self, sentences_list: List[List[str]]) -> Dict[str, Any]:
        """
        Core LUQ algorithm implementation.

        For each response sample:
        1. Use other samples as context
        2. Check consistency of each sentence against other samples using NLI
        3. Compute average consistency scores
        4. Convert to uncertainty scores

        Args:
            sentences_list: List of sentence lists for each response

        Returns:
            LUQ results with minimal NLI details for storage efficiency
        """
        num_samples = len(sentences_list)

        # Initialize result containers
        luq_scores_per_sample = np.zeros(num_samples)
        consistency_scores_per_sample = np.zeros(num_samples)
        num_sentences_per_response = [len(sample) for sample in sentences_list]

        # Minimal NLI tracking for storage efficiency
        nli_details = []

        for index, sentences in enumerate(sentences_list):
            if self.verbose:
                log.info(f"Processing sample {index+1}/{num_samples}")

            # Get other samples as context
            context_samples = [" ".join(sample) for sample in sentences_list if sample != sentences]

            if not context_samples or not sentences:
                # Default values for edge cases
                luq_scores_per_sample[index] = 0.5
                consistency_scores_per_sample[index] = 0.5
                continue

            # Compute consistency scores for each sentence
            scores = np.zeros((len(sentences), len(context_samples)))
            nli_computations = []

            for sent_i, sentence in enumerate(sentences):
                for context_i, context in enumerate(context_samples):
                    context = context.replace("\n", " ").strip()

                    # Compute NLI score
                    nli_result = self.nli_calc.compute_nli_scores_cached(context, sentence)
                    consistency_score = self._compute_nli_score(context, sentence)
                    scores[sent_i, context_i] = consistency_score

                    # Store minimal NLI info
                    nli_computations.append({
                        "s": sent_i,  # sentence index
                        "c": context_i,  # context index
                        "e": round(float(nli_result.entailment), 3),
                        "n": round(float(nli_result.neutral), 3),
                        "d": round(float(nli_result.contradiction), 3),
                        "l": round(float(consistency_score), 3)  # luq score
                    })

            # Calculate sample-level metrics following LUQ paper
            scores_per_sentence = scores.mean(axis=-1)
            sample_consistency = scores_per_sentence.mean()
            consistency_scores_per_sample[index] = sample_consistency

            # Convert consistency to uncertainty
            sample_uncertainty = 1.0 - sample_consistency
            luq_scores_per_sample[index] = sample_uncertainty

            # Store minimal sample details
            nli_details.append({
                "sample_index": index,
                "num_sentences": len(sentences),
                "num_contexts": len(context_samples),
                "sample_consistency": round(float(sample_consistency), 4),
                "sample_uncertainty": round(float(sample_uncertainty), 4),
                "nli_computations": nli_computations
            })

        # Calculate overall metrics
        overall_uncertainty = luq_scores_per_sample.mean()
        overall_consistency = consistency_scores_per_sample.mean()

        return {
            "overall_uncertainty": float(overall_uncertainty),
            "luq_scores_per_sample": luq_scores_per_sample.tolist(),
            "consistency_scores_per_sample": consistency_scores_per_sample.tolist(),
            "overall_consistency": float(overall_consistency),
            "num_sentences_per_response": num_sentences_per_response,
            "nli_details": nli_details
        }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 2

    def get_method_name(self) -> str:
        """Get the method name."""
        return "LUQ"

    def __str__(self):
        return "LUQ"