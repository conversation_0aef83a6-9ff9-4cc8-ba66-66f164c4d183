# LUQ NLI Details Schema

## 概述
为了避免MongoDB文档大小限制（16MB），LUQ实现只保存句子和响应的索引信息，而不保存完整的文本内容。

## 数据结构

### nli_details 数组
每个元素对应一个响应样本的分析结果：

```json
{
  "sample_index": 0,           // 当前分析的响应索引
  "num_sentences": 45,         // 当前响应的句子数量
  "num_contexts": 9,           // 用作上下文的其他响应数量
  "sample_consistency": 0.4521, // 样本一致性分数
  "sample_uncertainty": 0.5479,  // 样本不确定性分数
  "nli_computations": [...],    // NLI计算详情（见下）
  "sentence_scores": [...]      // 句子级别分数（见下）
}
```

### nli_computations 数组
每个元素对应一次NLI计算：

```json
{
  "s": 12,      // 句子索引（在当前响应中的位置）
  "c": 3,       // 上下文索引（对应其他响应的索引）
  "e": 0.234,   // entailment 分数
  "n": 0.456,   // neutral 分数  
  "d": 0.310,   // contradiction 分数
  "l": 0.430    // LUQ一致性分数 (entailment/(entailment+contradiction))
}
```

### sentence_scores 数组
每个元素对应一个句子的汇总信息：

```json
{
  "sentence_index": 12,        // 句子索引
  "context_scores": [0.43, 0.56, 0.21, ...], // 与各上下文的一致性分数
  "avg_consistency": 0.4233,   // 平均一致性分数
  "uncertainty": 0.5767        // 句子不确定性 (1 - avg_consistency)
}
```

## 索引映射

### 响应索引映射
- `sample_index`: 当前分析的响应在原始响应列表中的位置
- `c` (context index): 上下文响应的相对索引，需要映射到实际响应索引

**映射公式**：
```python
# 获取实际的上下文响应索引
actual_context_indices = [i for i in range(len(responses)) if i != sample_index]
actual_context_index = actual_context_indices[c]
```

### 句子索引映射
- `s` (sentence index): 句子在当前响应中的位置
- 可以通过 `num_sentences_per_response[sample_index]` 获取该响应的总句子数

## 使用示例

### 获取特定NLI计算的完整信息
```python
# 假设有一个NLI计算记录
nli_comp = {
  "s": 12,    # 第12个句子
  "c": 3,     # 第3个上下文响应
  "e": 0.234, # entailment分数
  "l": 0.430  # LUQ分数
}

sample_index = 0  # 当前样本索引

# 获取实际的上下文响应索引
context_indices = [i for i in range(len(responses)) if i != sample_index]
actual_context_index = context_indices[nli_comp["c"]]

# 现在可以索引到：
# - 当前句子：通过句子分割responses[sample_index]得到第nli_comp["s"]个句子
# - 上下文响应：responses[actual_context_index]
# - NLI分数：nli_comp["e"], nli_comp["n"], nli_comp["d"]
# - LUQ分数：nli_comp["l"]
```

### 重建完整的NLI矩阵
```python
def rebuild_nli_matrix(nli_details, sample_index):
    """重建特定样本的NLI分数矩阵"""
    sample_detail = nli_details[sample_index]
    num_sentences = sample_detail["num_sentences"]
    num_contexts = sample_detail["num_contexts"]
    
    # 初始化矩阵
    nli_matrix = np.zeros((num_sentences, num_contexts))
    
    # 填充矩阵
    for comp in sample_detail["nli_computations"]:
        nli_matrix[comp["s"], comp["c"]] = comp["l"]
    
    return nli_matrix
```

## 存储优化

### 字段名缩写
- `s` = sentence_index (节省空间)
- `c` = context_index  
- `e` = entailment
- `n` = neutral
- `d` = contradiction  
- `l` = luq_score

### 精度控制
- 所有浮点数保留3位小数，减少存储空间
- 移除不必要的文本内容（句子文本、上下文文本）
- 移除冗余的矩阵存储

## 数据完整性
虽然不保存文本内容，但保留了所有必要的索引信息，可以：
1. 重建完整的NLI计算过程
2. 验证LUQ算法的正确性  
3. 进行后续的详细分析
4. 通过索引获取原始文本内容
