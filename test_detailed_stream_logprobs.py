#!/usr/bin/env python3
"""
详细测试流式输出模式下logprobs的脚本
"""

import logging
import time
import json
from openai import OpenAI
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DetailedStreamLogprobsTester:
    """详细流式输出logprobs测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY", 'null'),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
    
    def test_detailed_stream_logprobs(self, prompt: str, model: str = "qwen3-32b"):
        """详细测试流式输出模式下的logprobs"""
        logger.info(f"开始详细测试流式输出模式下的logprobs...")
        logger.info(f"模型: {model}")
        logger.info(f"Prompt: {prompt[:100]}...")
        
        try:
            # 流式调用LLM
            stream = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                logprobs=True,
                top_logprobs=5,
                stream=True,  # 启用流式输出
                extra_body={"enable_thinking": False},
            )
            
            logger.info("开始接收流式响应...")
            
            full_content = ""
            all_logprobs = []
            chunk_count = 0
            logprobs_chunks = 0
            
            for chunk in stream:
                chunk_count += 1
                logger.info(f"\n--- Chunk {chunk_count} ---")
                
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    # 提取content
                    if hasattr(choice, 'delta') and choice.delta:
                        delta_content = choice.delta.content or ""
                        full_content += delta_content
                        logger.info(f"Content delta: '{delta_content}'")
                    
                    # 详细分析logprobs
                    chunk_logprobs = None
                    if hasattr(choice, 'logprobs') and choice.logprobs:
                        chunk_logprobs = choice.logprobs
                        all_logprobs.append(chunk_logprobs)
                        logprobs_chunks += 1
                        logger.info(f"✅ Logprobs found in chunk {chunk_count}")
                        logger.info(f"Logprobs type: {type(chunk_logprobs)}")
                        
                        # 详细分析logprobs结构
                        self.analyze_logprobs_structure(chunk_logprobs, chunk_count)
                    else:
                        logger.info("❌ No logprobs in this chunk")
                    
                    # 检查finish_reason
                    if hasattr(choice, 'finish_reason') and choice.finish_reason:
                        logger.info(f"Finish reason: {choice.finish_reason}")
                else:
                    logger.info("No choices in chunk")
            
            logger.info(f"\n=== 流式输出详细分析完成 ===")
            logger.info(f"总chunk数: {chunk_count}")
            logger.info(f"包含logprobs的chunk数: {logprobs_chunks}")
            logger.info(f"Logprobs覆盖率: {logprobs_chunks/chunk_count*100:.1f}%")
            logger.info(f"完整内容长度: {len(full_content)}")
            logger.info(f"完整内容: {full_content}")
            
            return {
                'full_content': full_content,
                'all_logprobs': all_logprobs,
                'chunk_count': chunk_count,
                'logprobs_chunks': logprobs_chunks
            }
            
        except Exception as e:
            logger.error(f"详细流式输出测试失败: {e}")
            raise
    
    def analyze_logprobs_structure(self, logprobs, chunk_num):
        """分析logprobs的详细结构"""
        logger.info(f"  Chunk {chunk_num} logprobs分析:")
        
        if hasattr(logprobs, '__dict__'):
            logger.info(f"    Logprobs attributes: {list(logprobs.__dict__.keys())}")
        
        if hasattr(logprobs, 'content'):
            content_logprobs = logprobs.content
            logger.info(f"    Content logprobs type: {type(content_logprobs)}")
            
            if isinstance(content_logprobs, list):
                logger.info(f"    Content logprobs length: {len(content_logprobs)}")
                if content_logprobs:
                    first_item = content_logprobs[0]
                    logger.info(f"    First content logprob: {first_item}")
                    
                    # 分析第一个logprob的结构
                    if hasattr(first_item, '__dict__'):
                        logger.info(f"    First logprob attributes: {list(first_item.__dict__.keys())}")
                    
                    # 尝试转换为字典查看内容
                    try:
                        if hasattr(first_item, '__dict__'):
                            first_dict = first_item.__dict__
                            logger.info(f"    First logprob dict: {first_dict}")
                    except Exception as e:
                        logger.info(f"    Cannot convert first logprob to dict: {e}")
        
        # 尝试其他可能的属性
        for attr in ['token', 'logprob', 'top_logprobs', 'text_offset']:
            if hasattr(logprobs, attr):
                value = getattr(logprobs, attr)
                logger.info(f"    {attr}: {value}")
    
    def test_non_stream_detailed(self, prompt: str, model: str = "qwen3-32b"):
        """详细测试非流式输出模式下的logprobs"""
        logger.info(f"开始详细测试非流式输出模式下的logprobs...")
        logger.info(f"模型: {model}")
        logger.info(f"Prompt: {prompt[:100]}...")
        
        try:
            # 非流式调用LLM
            completion = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                logprobs=True,
                top_logprobs=5,
                stream=False,  # 非流式输出
                extra_body={"enable_thinking": False},
            )
            
            logger.info("非流式响应接收完成...")
            
            if completion.choices and len(completion.choices) > 0:
                choice = completion.choices[0]
                
                # 提取content
                content = ""
                if hasattr(choice, 'message') and choice.message:
                    content = choice.message.content or ""
                
                # 提取logprobs
                logprobs = None
                if hasattr(choice, 'message') and choice.message:
                    if hasattr(choice.message, 'logprobs'):
                        logprobs = choice.message.logprobs
                
                logger.info(f"非流式输出完成")
                logger.info(f"内容长度: {len(content)}")
                logger.info(f"Logprobs类型: {type(logprobs)}")
                
                # 详细分析非流式logprobs
                if logprobs:
                    logger.info("非流式logprobs详细分析:")
                    self.analyze_logprobs_structure(logprobs, "non_stream")
                
                return {
                    'content': content,
                    'logprobs': logprobs
                }
            else:
                logger.warning("非流式输出没有返回choices")
                return {'content': '', 'logprobs': None}
                
        except Exception as e:
            logger.error(f"非流式输出测试失败: {e}")
            raise
    
    def compare_detailed(self, prompt: str, model: str = "qwen3-32b"):
        """详细比较流式和非流式输出的logprobs差异"""
        logger.info("开始详细比较流式和非流式输出的logprobs...")
        
        # 测试流式输出
        stream_result = self.test_detailed_stream_logprobs(prompt, model)
        
        # 等待一下，避免API限制
        time.sleep(2)
        
        # 测试非流式输出
        non_stream_result = self.test_non_stream_detailed(prompt, model)
        
        # 详细比较结果
        logger.info("\n=== 详细比较结果 ===")
        logger.info(f"流式输出内容长度: {len(stream_result['full_content'])}")
        logger.info(f"非流式输出内容长度: {len(non_stream_result['content'])}")
        logger.info(f"内容是否相同: {stream_result['full_content'] == non_stream_result['content']}")
        
        logger.info(f"流式输出总chunk数: {stream_result['chunk_count']}")
        logger.info(f"流式输出包含logprobs的chunk数: {stream_result['logprobs_chunks']}")
        logger.info(f"流式输出logprobs覆盖率: {stream_result['logprobs_chunks']/stream_result['chunk_count']*100:.1f}%")
        logger.info(f"非流式输出logprobs类型: {type(non_stream_result['logprobs'])}")
        
        # 分析logprobs的差异
        logger.info("\n=== Logprobs差异分析 ===")
        
        if stream_result['all_logprobs'] and non_stream_result['logprobs']:
            logger.info("✅ 两种模式都返回了logprobs")
            logger.info(f"流式模式logprobs chunks数: {len(stream_result['all_logprobs'])}")
            
            # 比较logprobs的结构
            stream_first = stream_result['all_logprobs'][0] if stream_result['all_logprobs'] else None
            non_stream_logprobs = non_stream_result['logprobs']
            
            logger.info(f"流式第一个logprobs类型: {type(stream_first)}")
            logger.info(f"非流式logprobs类型: {type(non_stream_logprobs)}")
            
            # 检查是否类型相同
            if type(stream_first) == type(non_stream_logprobs):
                logger.info("✅ 两种模式的logprobs类型相同")
            else:
                logger.info("❌ 两种模式的logprobs类型不同")
        else:
            logger.info("❌ 至少一种模式没有返回logprobs")
        
        return {
            'stream_result': stream_result,
            'non_stream_result': non_stream_result
        }

def test_detailed_stream_logprobs():
    """详细测试流式输出logprobs"""
    logger.info("开始详细测试流式输出模式下的logprobs...")
    
    try:
        # 创建测试器
        tester = DetailedStreamLogprobsTester()
        
        # 测试prompt
        test_prompt = "请简单回答：1+1等于几？"
        
        # 详细比较流式和非流式输出
        results = tester.compare_detailed(test_prompt)
        
        logger.info("详细流式输出logprobs测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise

if __name__ == "__main__":
    test_detailed_stream_logprobs()
