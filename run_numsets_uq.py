import argparse
import json
import pandas as pd
from uq_methods.implementations.num_sets import NumSetsUQ
from pymongo import MongoClient
from tqdm import tqdm
import re

def split_module_reasoning(text):
    # 提取Module和Reasoning部分，兼容顺序和缺失
    module, reasoning = '', ''
    module_match = re.search(r'Module:\s*(.*?)(?:\n|$)', text, re.DOTALL)
    reasoning_match = re.search(r'Reasoning:\s*(.*?)(?:\nModule:|$)', text, re.DOTALL)
    if module_match:
        module = module_match.group(1).strip()
    if reasoning_match:
        reasoning = reasoning_match.group(1).strip()
    return module, reasoning

def main():
    parser = argparse.ArgumentParser(description="NumSets UQ Analysis Runner (CSV批量模式)")
    parser.add_argument('--csv', type=str, default='data/commit_responses_20250723.csv', help='CSV文件路径，包含commit_sha、prompt_type、response等字段')
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    parser.add_argument('--mongo_db', type=str, default='commit_analysis')
    parser.add_argument('--mongo_collection', type=str, default='deberta_numsets_results')
    args = parser.parse_args()

    # 读取CSV
    df = pd.read_csv(args.csv)
    if not {'commit_sha', 'prompt_type', 'response_text'}.issubset(df.columns):
        raise ValueError('CSV文件必须包含 commit_sha, prompt_type, response_text 字段')

    # 按commit_sha和prompt_type分组
    grouped = df.groupby(['commit_sha', 'prompt_type'])

    # 初始化UQ方法和MongoDB
    uq = NumSetsUQ()
    mongo_client = MongoClient(host=args.mongo_host, port=args.mongo_port)
    mongo_db = mongo_client[args.mongo_db]
    mongo_col = mongo_db[args.mongo_collection]

    for (commit_sha, prompt_type), group in tqdm(grouped, desc='UQ分析分组'):
        responses = group['response_text'].dropna().tolist()
        if len(responses) < 2:
            continue
        meta = {
            'commit_sha': commit_sha,
            'prompt_type': prompt_type,
            'response_count': len(responses)
        }
        if prompt_type == 'single_word':
            uq_result = uq.compute_uncertainty(responses)
            save_doc = {
                'commit_sha': commit_sha,
                'prompt_type': prompt_type,
                'responses': responses,
                'uq_result': uq_result,
                'meta': meta
            }
        else:
            modules, reasonings = [], []
            for resp in responses:
                m, r = split_module_reasoning(resp)
                if m: modules.append(m)
                if r: reasonings.append(r)
            module_uq_result = uq.compute_uncertainty(modules) if modules else {}
            reasoning_uq_result = uq.compute_uncertainty(reasonings) if reasonings else {}
            save_doc = {
                'commit_sha': commit_sha,
                'prompt_type': prompt_type,
                'responses': responses,
                'modules': modules,
                'reasonings': reasonings,
                'module_uq_result': module_uq_result,
                'reasoning_uq_result': reasoning_uq_result,
                'meta': meta
            }
        mongo_col.insert_one(save_doc)
        print(f"已保存: commit_sha={commit_sha}, prompt_type={prompt_type}")

    print('全部分析完成！')

if __name__ == '__main__':
    main() 