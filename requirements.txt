# Core dependencies
openai>=1.0.0
pyyaml>=6.0
pandas>=2.0.0
pymongo>=4.0.0
rich>=13.0.0
jinja2>=3.0.0
python-dotenv>=1.0.0

# Scientific computing
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Machine Learning & NLP
torch>=2.0.0
transformers>=4.30.0
sentence-transformers>=2.2.0

# Async and networking
aiofiles>=23.0.0
asyncio-throttle>=1.0.0
aiodns>=3.0.0

# Progress and visualization
tqdm>=4.65.0

# Optional dependencies for specific features
# For GPU acceleration (optional, install based on your system)
# torch>=2.0.0+cu118  # For CUDA 11.8
# torch>=2.0.0+cpu    # For CPU-only

# Development dependencies (optional)
# jupyter>=1.0.0
# matplotlib>=3.5.0
# seaborn>=0.11.0
# plotly>=5.0.0

# Note: sqlite3 is a built-in Python module, no need to install
# Note: Some UQ methods may require additional models to be downloaded:
#   - microsoft/deberta-large-mnli (for NLI-based methods)
#   - Qwen/Qwen3-Embedding-0.6B (for Qwen embedding methods)
#   - intfloat/multilingual-e5-large-instruct (for E5 embedding methods)