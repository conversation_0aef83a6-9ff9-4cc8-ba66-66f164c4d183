import os
import json
import csv
from pathlib import Path

def extract_commit_data(repo_path, repo_name):
    """Extract commit data from JSON files in the given directory."""
    commits_data = []
    
    # Get all commit files
    commit_files = sorted([f for f in os.listdir(repo_path) if f.startswith('commit_') and f.endswith('.json')])
    
    for commit_file in commit_files:
        file_path = os.path.join(repo_path, commit_file)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # Extract required fields
            sha = data.get('sha', '')
            commit_info = data.get('commit', {})
            
            # Get author info
            author_info = commit_info.get('author', {})
            author_name = author_info.get('name', '')
            date = author_info.get('date', '')
            
            # Get commit message
            message = commit_info.get('message', '')
            
            commits_data.append({
                'sha': sha,
                'author': author_name,
                'date': date,
                'message': message
            })
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    return commits_data

def save_to_csv(commits_data, output_path):
    """Save commit data to CSV file."""
    if not commits_data:
        print(f"No data to save for {output_path}")
        return
    
    with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['sha', 'author', 'date', 'message']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for commit in commits_data:
            writer.writerow(commit)
    
    print(f"Saved {len(commits_data)} commits to {output_path}")

def main():
    # Base data directory
    data_dir = Path("data")
    
    # Process PyTorch commits
    pytorch_path = data_dir / "pytorch_commits"
    if pytorch_path.exists():
        pytorch_commits = extract_commit_data(pytorch_path, "pytorch")
        pytorch_csv_path = data_dir / "pytorch_commits_sample.csv"
        save_to_csv(pytorch_commits, pytorch_csv_path)
    
    # Process TensorFlow commits
    tensorflow_path = data_dir / "tensorflow_commits"
    if tensorflow_path.exists():
        tensorflow_commits = extract_commit_data(tensorflow_path, "tensorflow")
        tensorflow_csv_path = data_dir / "tensorflow_commits_sample.csv"
        save_to_csv(tensorflow_commits, tensorflow_csv_path)

if __name__ == "__main__":
    main()