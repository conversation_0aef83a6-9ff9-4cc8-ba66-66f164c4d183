#!/usr/bin/env python3
"""
运行测试脚本
执行所有单元测试和集成测试
"""

import unittest
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def discover_and_run_tests():
    """发现并运行所有测试"""
    
    # 测试目录
    test_dir = project_root / 'tests'
    
    if not test_dir.exists():
        logger.error(f"Test directory {test_dir} does not exist")
        return False
    
    # 发现测试
    loader = unittest.TestLoader()
    start_dir = str(test_dir)
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


def run_specific_test(test_module):
    """运行特定的测试模块"""
    try:
        # 导入测试模块
        module = __import__(f'tests.{test_module}', fromlist=[test_module])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        logger.error(f"Failed to import test module {test_module}: {e}")
        return False
    except Exception as e:
        logger.error(f"Error running test module {test_module}: {e}")
        return False


def check_test_dependencies():
    """检查测试依赖"""
    required_modules = [
        'unittest',
        'pandas',
        'numpy',
        'yaml'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"Missing required modules for testing: {missing_modules}")
        return False
    
    return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run tests for UQ analysis system")
    parser.add_argument('--module', type=str, help='Run specific test module (e.g., test_config_manager)')
    parser.add_argument('--check-deps', action='store_true', help='Check test dependencies only')
    parser.add_argument('--list-tests', action='store_true', help='List available test modules')
    
    args = parser.parse_args()
    
    # 检查依赖
    if args.check_deps:
        if check_test_dependencies():
            print("All test dependencies are available")
            return 0
        else:
            print("Some test dependencies are missing")
            return 1
    
    # 列出测试
    if args.list_tests:
        test_dir = project_root / 'tests'
        if test_dir.exists():
            test_files = list(test_dir.glob('test_*.py'))
            print("Available test modules:")
            for test_file in test_files:
                print(f"  - {test_file.stem}")
        else:
            print("No test directory found")
        return 0
    
    # 检查测试依赖
    if not check_test_dependencies():
        logger.error("Test dependencies check failed")
        return 1
    
    logger.info("Starting test execution...")
    
    # 运行测试
    if args.module:
        # 运行特定模块
        logger.info(f"Running specific test module: {args.module}")
        success = run_specific_test(args.module)
    else:
        # 运行所有测试
        logger.info("Running all tests...")
        success = discover_and_run_tests()
    
    if success:
        logger.info("All tests passed!")
        return 0
    else:
        logger.error("Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
