sha,message,date,author
6ae99aa5bc616f63649be3831cb10bf0d09c477e,"onnx/caffe2 tests: Do not execute models with CPU-only operators on GPU.

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/20720

Reviewed By: bddppq

Differential Revision: D15422322

Pulled By: houseroad

fbshipit-source-id: c79795434157ff5f0a7b2774fd40edc71cf35ba7",2019-05-20T22:58:27Z,<PERSON>
6a951a6f4c06dff162e3b81e99a964c8b6ad84f0,"Fix a KaTeX crash and many docstring issues (#49684)

Summary:
The first commit fixes the `MultiheadAttention` docstrings, which are causing a cryptic KaTeX crash.

The second commit fixes many documentation issues in `torch/_torch_docs.py`, and closes gh-43667 (missing ""Keyword arguments"" headers). It also fixes a weird duplicate docstring for `torch.argmin`; there's more of these, it looks like they were written based on whether the C++ implementation has an overload. That makes little sense to a Python user though, and the content is simply duplicate.

The `Shape:` heading for https://pytorch.org/docs/master/generated/torch.nn.MultiheadAttention.html looked bad, here's what it looks like with this PR:

<img width=""475"" alt=""image"" src=""https://user-images.githubusercontent.com/98330/102797488-09a44e00-43b0-11eb-8788-acdf4e936f2f.png"">

Pull Request resolved: https://github.com/pytorch/pytorch/pull/49684

Reviewed By: ngimel

Differential Revision: D25730909

Pulled By: mruberry

fbshipit-source-id: d25bcf8caf928e7e8e918017d119de12e10a46e9",2020-12-30T22:15:49Z,Ralf Gommers
81c2412721446a38434c715ff544f9915949b490,"[caffe2] Switch to using `public_include_directories

Summary:
caffe2 uses `-I` all over the place, but really we should use the Buck built-in version of this

Alternatively, the `exported_header` clean up means we need to standardize to a single path

Test Plan:
```
buck build caffe2:torch-cpp-cpu
buck build caffe2/...
```

Reviewed By: malfet

Differential Revision: D19150098

fbshipit-source-id: e99aaf69d6c474afaedbd5f693a7736d3d67aafc",2020-03-31T15:12:30Z,Michael Lee (Engineering)
68cfc52452802f918003fcc17d72c42a8d869c11,"MomemtumSGDUpdate -- version of MomentumSGD with update.

Summary:
It gives a significant perf boost to do the parameter update inside MomentumSGD, instead of with a separate WeightedSum op.
To ensure backwards compatibility, I made it a separate op.

Also added an unit test.

Reviewed By: prigoyal

Differential Revision: D4262446

fbshipit-source-id: 38e7ee6d7677b398658ac7fe9b7a59b569e033f4",2016-12-07T19:49:24Z,Aapo Kyrola
cd2929c70798dbd810adef353e045960858ba060,"ConvTransposeMobileOp respects the `shared_buffer` arg.

Summary:
This makes ConvTransposeMobileOp inline with other implementations,
allows us to account for these buffers in the workspace, and is generally a good
thing to do.

Differential Revision: D4767431

fbshipit-source-id: b14a96a089136e305ab42680772272f4e5f16f53",2017-03-31T17:24:38Z,Andrew Tulloch
64dd1419c58997bf8c9dd4c8a22ae8cc4a241d0b,Fix Variable indexing bugs (#96),2016-10-03T18:49:21Z,Adam Paszke
40869884cd33e7172552a583402508d02ff0d509,"Add outer export to onnx (#53603) (#54869)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54869

Add symbolic fuction to support torch.outer export to onnx.
Support for transfo-xl-wt103 model.

Test Plan: Imported from OSS

Reviewed By: nikithamalgifb

Differential Revision: D27408978

Pulled By: SplitInfinity

fbshipit-source-id: 70c89a9fc1a5e4a4ddcf674afb1e82e492a7d3b9",2021-04-01T04:11:25Z,DeyuHuang
b685864f504155f8926498b82307c9afba7b0cc5,"[quant][graphmode][fx] Add reference option support for linear_static_fp16 (#52650)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/52650

linear_dynamic_fp16 has following dtypes for activation, weight, bias, output:
(fp32, fp16, fp32, fp32)

linear_static_fp16 has following dtypes:
(fp16, fp16, fp16, fp16)

Test Plan: Imported from OSS

Reviewed By: vkuzo

Differential Revision: D26599803

fbshipit-source-id: b4a8345d355125070be718a227288cc848cc8bbc",2021-02-27T16:23:45Z,Jerry Zhang
85bd6bc0105162293fa0bbfb7b661f85ec67f85a,"Cache pretrained mobilenet_v2 and mobilenet_v3_large models in Docker (#100302)

Follow the example I did for ONNX in https://github.com/pytorch/pytorch/pull/96793, this caches the pretrained `mobilenet_v2 model` and `mobilenet_v3_large` used by CI jobs.  I think there might be an issue either with AWS or with the domain download.pytorch.org as the connection to the latter has been failing a lots in the past few days.

Related flaky jobs:
* https://github.com/pytorch/pytorch/actions/runs/4835873487/jobs/8618836446
* https://github.com/pytorch/pytorch/actions/runs/4835783539/jobs/8618404639
* https://github.com/pytorch/pytorch/actions/runs/4835783539/jobs/8618404639

```
Downloading: ""https://download.pytorch.org/models/mobilenet_v2-b0353104.pth"" to /var/lib/jenkins/.cache/torch/hub/checkpoints/mobilenet_v2-b0353104.pth
Traceback (most recent call last):
  File ""/opt/conda/envs/py_3.8/lib/python3.8/urllib/request.py"", line 1354, in do_open
    h.request(req.get_method(), req.selector, req.data, headers,
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1256, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1302, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1251, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1011, in _send_output
    self.send(msg)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 951, in send
    self.connect()
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1418, in connect
    super().connect()
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 922, in connect
    self.sock = self._create_connection(
  File ""/opt/conda/envs/py_3.8/lib/python3.8/socket.py"", line 808, in create_connection
    raise err
  File ""/opt/conda/envs/py_3.8/lib/python3.8/socket.py"", line 796, in create_connection
    sock.connect(sa)
OSError: [Errno 99] Cannot assign requested address
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/100302
Approved by: https://github.com/ZainRizvi",2023-05-01T19:31:37Z,Huy Do
d2a9b256f00742b3fd1271ad087fc4e02144aed8,"[DCP][Test]Remove broken 2d checkpoint test (#106367)

Removing this broken test as we are not going to land the fix for 2D regression. Instead, we are going to migrate to use device_mesh and dtensor state_dict for 2D.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/106367
Approved by: https://github.com/fduwjj",2023-08-01T19:54:37Z,wz337
cf3638a9ccb39d29b4fc01d19bf3f847655c1e03,"[dynamo] Clear cache on dynamo dashboard accuracy tests (#95726)

Might fix some flaky accuracy tests?

Pull Request resolved: https://github.com/pytorch/pytorch/pull/95726
Approved by: https://github.com/ngimel, https://github.com/anijain2305, https://github.com/desertfire",2023-03-01T00:50:15Z,William Wen
9d209e78348ee5c3e1ead700d240fb476b3bc4de,"Revert ""[ao] making _is_activation_post_process private (#87520)""

This reverts commit 45c62a337756ff9db97cd64d2d42d9e65dda0a85.

Reverted https://github.com/pytorch/pytorch/pull/87520 on behalf of https://github.com/bigfootjon due to Diff reverted internally",2022-11-21T16:48:26Z,PyTorch MergeBot
fd3ed2e4f7661b586f525c05f6252903bf9d59ac,"[Caffe2] Use more irange()s in loops. (#72262)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/72262

Using an explicitly typed loop variable risks type mismatches with the loop
bound; using an const auto variable and c10::irange eliminates this
possibility. This change modifies loops in files under
fbsource/fbcode/caffe2/aten from the format
`for(TYPE var=x0;var<x_max;x++)` to `for(const auto var: irange(xmax))`

This was achieved by running r-barnes's loop upgrader script (D28874212)
modified for the appropriate directory.

Test Plan: arc sanity

Reviewed By: r-barnes

Differential Revision: D33952435

fbshipit-source-id: ad72a9e2448d3c03f16bcccda904ffe5003fd557
(cherry picked from commit 8140be4ba9c37f934d013c2cd0c25bdd8d2e7c19)",2022-02-08T19:00:36Z,Sweet Tea Dorminy
a09c4d3997f2e57eedc22cb38c896f9e8f9a5607,"[pt][quant] Vectorized qmul and more methods on qint data types (#34376)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/34376

Vectorized implementation of qmul. qmul is now ~16x faster on my development machine. This implementation works for qint8, quint8 and qint32. Also added some commonly used operations, such as multiply operator, requantize operation etc., to qint vector classes for future use.

```
#!/usr/bin/env python

import time
import torch
import torch.nn as nn
torch.set_num_threads(1)
# print(torch.__config__.parallel_info())

A = torch.rand(1, 54, 54, 256)
B = torch.rand(1, 54, 54, 256)

scale = .05
zero_point = 50

for dtype in [torch.quint8, torch.qint8]:

    qA = torch.quantize_per_tensor(A, scale=scale, zero_point=zero_point,
            dtype=dtype)
    qB = torch.quantize_per_tensor(B, scale=scale, zero_point=zero_point,
            dtype=dtype)

    NITER = 1000
    s = time.time()
    for i in range(NITER):
        out = torch.ops.quantized.mul(qA, qB, scale=scale, zero_point=zero_point)
    time_per_iter = (time.time() - s) / NITER

    print('dtype: {} time per iter ms: {:.3f}'.format(dtype, time_per_iter * 1000))
```
### Before
dtype: torch.quint8 time per iter ms: 6.714
dtype: torch.qint8 time per iter ms: 6.780

### After
dtype: torch.quint8 time per iter ms: 0.431
dtype: torch.qint8 time per iter ms: 0.417

### Test
Modified qmul tests to include qint8 and qint32 data types.

python test/test_quantized.py TestQuantizedOps.test_qmul_relu_same_qparams
python test/test_quantized.py TestQuantizedOps.test_qmul_relu_different_qparams
python test/test_quantized.py TestQuantizedOps.test_qmul_broadcast
ghstack-source-id: 99862681

Differential Revision: D20308515

fbshipit-source-id: 4fa65b2ba433cfd59260fc183a70f53a6fcc36b4",2020-03-10T23:48:48Z,Daya Khudia
fa597ee17fbf8364e7ccae21bdc8a367efe48975,"Fix torch.randperm for CUDA (#59352)

Summary:
Context https://github.com/pytorch/pytorch/issues/58545

The logic is that we are going to keep it consistent for both
torch.randperm and torch.randint

1. Generators can have either a fully-specified or non-fully specified device
2. As long as the device type match with the result, we don't error out

Pull Request resolved: https://github.com/pytorch/pytorch/pull/59352

Test Plan:
```
python test/test_tensor_creation_ops.py -k TestRandomTensorCreation
```

Reviewed By: ngimel

Differential Revision: D28855920

Pulled By: zhouzhuojie

fbshipit-source-id: f8141a2c4b2f177e1aa7baec6999b65916cba02c",2021-06-04T15:55:14Z,Zhuojie Zhou
6a8c2758d558baabf3e012ba45ef6349c957ba4e,"Add better performing versions for groupwise and depthwise convolutions (#22869)

Summary:
Groupwise and depthwise convolutions become faster with this diff
Pull Request resolved: https://github.com/pytorch/pytorch/pull/22869

Test Plan:
buck test mode/dev caffe2/test:quantized -- 'test_qconv'  --print-passing-details

```
Running 2 tests
Started new test run: https://our.intern.facebook.com/intern/testinfra/testrun/562950091484224
      ✓ caffe2/test:quantized - test_qconv (test_quantized.TestQuantizedConv) 2.731 1/2 (passed)
Test output:
> test_qconv (test_quantized.TestQuantizedConv) ... ok
>
> ----------------------------------------------------------------------
> Ran 1 test in 2.732s
>
> OK
      ✓ caffe2/test:quantized - test_qconv_unpack (test_quantized.TestQuantizedConv) 5.187 2/2 (passed)
Test output:
> test_qconv_unpack (test_quantized.TestQuantizedConv) ... ok
>
> ----------------------------------------------------------------------
> Ran 1 test in 5.188s
>
> OK
Finished test run: https://our.intern.facebook.com/intern/testinfra/testrun/562950091484224
Summary (total time 15.66s):
  PASS: 2
  FAIL: 0
  SKIP: 0
  FATAL: 0
  TIMEOUT: 0
  OMIT: 0

```

buck test mode/dev caffe2/test:quantized -- 'test_conv_api'
```
Running 2 tests
Started new test run: https://our.intern.facebook.com/intern/testinfra/testrun/3940649676010406
      ✓ caffe2/test:quantized - test_conv_api (test_nn_quantized.ModuleAPITest) 0.040 1/2 (passed)
      ✓ caffe2/test:quantized - test_conv_api (test_quantized_conv.FunctionalAPITest) 5.402 2/2 (passed)
Finished test run: https://our.intern.facebook.com/intern/testinfra/testrun/3940649676010406
Summary (total time 11.83s):
  PASS: 2
  FAIL: 0
  SKIP: 0
  FATAL: 0
  TIMEOUT: 0
  OMIT: 0
```

Differential Revision: D16264144

Pulled By: dskhudia

fbshipit-source-id: 32fa43e5c3d97c8aaa6e0858327a2ac0aef8df5c",2019-07-26T00:51:39Z,Daya Khudia
908ba05a06c4c40bf103b3272d05acd2d554130f,"[Pytorch] Add python binding to use mobile cpu allocator. (#52376)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/52376

Using default cpu allocator for ops executed on qnnpack backend will result in
asan failures with heap overflow since qnnpack (and xnnpack) can access input
beyond their and/beginning.

Here we are enabling this feature specifically to enable dynamic sparse linear op test
using qnnpack engine. In dynamic linear op, the fp32 bias is not packed and
hence can result in out-of-bound access.

Test Plan: CI

Reviewed By: z-a-f

Differential Revision: D26491943

fbshipit-source-id: bcc2485e957c7abdef0853c36f6e0f876c20cee3",2021-02-18T02:20:18Z,Kimish Patel
1e8a16224f2a0fff7d6fef8809f9756f690ec4ab,"PackSegments: return value presence.

Summary:
Optionally return a blob of shape [batch size, max length] that is
false only in locations where the output tensor was padded.
One can separately convert lengths to segment ids and cast, but
this is more convenient, and possibly more efficient.

Differential Revision: D6006073

fbshipit-source-id: af6c4ea31972566e7d059dcd3fdd8afba97a88e9",2017-10-12T18:04:35Z,Tilak Sharma
348e0af6e191f9ad94d873ab6c484aaa34174dfc,"Remove unused binary fb_run_plan_mpi

Summary:
TSIA

This caused a compilation problem on gcc-6, see
https://github.com/caffe2/caffe2/issues/456.

Differential Revision: D5002823

fbshipit-source-id: 764aae1eaf78ee9918455b95a12e982597b85fdc",2017-05-04T22:09:56Z,Pieter Noordhuis
14d4bdb4067cee72290d22cd4bf3a623f49fcab2,"Reformat output data format to make it more general for other binaries (#9555)

Summary:
This is to simplify the data format during benchmarking. After this change, we can use the same benchmarking harness data conversion method to parse data from multiple binaries.

This change should be coordinated with the PR: https://github.com/facebook/FAI-PEP/pull/63
Pull Request resolved: https://github.com/pytorch/pytorch/pull/9555

Reviewed By: pjh5

Differential Revision: D8903024

Pulled By: sf-wind

fbshipit-source-id: 61cabcff99f0873729142ec6cb6dc230c685d13a",2018-07-23T17:54:25Z,Fei Sun
cd7408e9505e3a7ae00e72a69ab17389ce086475,"Add aten _assert_tensor_metadata op (#84617)

Example:
```
graph():
    %arg0 : [#users=3] = placeholder[target=arg0]
    %arg_guard_equality_check : [#users=1] = call_function[target=torch._tensor_equal](args = (%arg0, (1, 1, 2), (2, 2, 1), torch.float32), kwargs = {})
    %_assert_true : [#users=0] = call_function[target=torch._assert_true](args = (%arg_guard_equality_check, Guard evaluation failed equality check for arg0), kwargs = {})
    %add : [#users=1] = call_function[target=operator.add](args = (%arg0, 1), kwargs = {})
    return ([arg0, arg0], (add, add))
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/84617
Approved by: https://github.com/jansel",2022-09-19T20:48:09Z,Michael Voznesensky
61a0df5af060bc44ca5cde4303e815ee3b7761ce,"Canonicalize THC/THCTensorMasked.cuh include

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/13977

Reviewed By: jerryzh168

Differential Revision: D13062564

fbshipit-source-id: 77d42585198cd75bc8a2625787604552e5369787",2018-11-14T22:30:44Z,Edward Yang
7705175f8378a769232aa6b1dde38e70ab749be9,"CPUBlas: Use opmath_type for alpha/beta scalars (#65839)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/65839
Approved by: https://github.com/ngimel",2022-07-04T18:04:53Z,Peter Bell
5651e1e3ad435ec45b9435b9d78ff2fbc715fa0b,"Add auto_linear formulas and some others (#69727)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/69727

Still need to test the backward ones. We would need to update gradgradcheck to check forward over backward.

Test Plan: Imported from OSS

Reviewed By: albanD

Differential Revision: D33031728

Pulled By: soulitzer

fbshipit-source-id: 86c59df5d2196b5c8dbbb1efed9321e02ab46d30",2021-12-20T20:13:11Z,soulitzer
fc4209bd4fe9b06e6bfc6ef6db38b08f4b2616e1,"Fix the bucketization wrong doc for right argument (#45684)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/45684

Test Plan: Imported from OSS

Reviewed By: zou3519

Differential Revision: D24057996

Pulled By: glaringlee

fbshipit-source-id: 3db1c24f3cae9747effa4b1f3c5c3baf6888c9a1",2020-10-02T01:15:09Z,lixinyu
c6bc766184cf149da21f959c19997bc93f3c1f4f,"Remove unnecessary copy constructor (#83030)

Re-land of https://github.com/pytorch/pytorch/pull/82626 which somehow broke deploy build
Fixes annoying warnings when building with clang:
```
../c10/util/variant.h:2256:9: warning: definition of implicit copy constructor for 'impl<c10::Scalar, c10::basic_string_view<char>>' is deprecated because it has a user-declared copy assignment operator [-Wdeprecated-copy]
  impl& operator=(const impl& other) = default;
        ^
```

Preliminary step for switching MacOS builds to ones with `-Werror`
Pull Request resolved: https://github.com/pytorch/pytorch/pull/83030
Approved by: https://github.com/kit1980, https://github.com/seemethere",2022-08-09T01:07:13Z,Nikita Shulga
a3588b6ed9fe4021ee4c218f9f9b4215ddd227b2,"Updating submodules

Summary:
GitHub commits:

https://github.com/pytorch/fbgemm/commit/62c3b48cf4beb1c56922c4afebcfe9c35f3513d8

Test Plan: n/a

Reviewed By: 2d2d2d2d2d

fbshipit-source-id: 41d1346f2405bce84984b02e3a951bb0e30868b7",2019-11-18T08:33:43Z,svcscm
8ed906030c58ebb3b07438ecb41bbdb4644cc4d5,"add fp16 support for mkldnn conv and deconv on CPU (#99496)

The PR is part of https://github.com/pytorch/pytorch/issues/97068, which is to add fp16 support for mkldnn conv and mkldnn deconv to leverage  avx_ne_convert, avx512-fp16, and amx-fp16 via the oneDNN library.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99496
Approved by: https://github.com/jgong5, https://github.com/cpuhrsch",2023-09-19T03:39:45Z,CaoE
3837a962d32565264d86cda6033043cba9300f7d,"Fix typo in Concat and Softmax

Reviewed By: Maratyszcza

Differential Revision: D6629260

fbshipit-source-id: 06fff59a770312b6948b3b5e1c04db6f539ea268",2017-12-23T01:27:59Z,Hao Lu
e33b4b6761d2cf9745e94af1308d98ebd168a3ab,"Use c10::variant-based enums for Reduction

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/27942

Test Plan: Imported from OSS

Differential Revision: D18202857

Pulled By: yf225

fbshipit-source-id: 0303ce2508e3b7665c6a91ae270a7d0ef0e45900",2019-10-29T21:13:37Z,Will Feng
7cb72704ccaffc47ad0fb37698b364e3e0d0f11b,"Constrain sdpa to fx strides (#111721)

Fix for https://github.com/pytorch/pytorch/issues/109607. sdpa requires last dimension strides to be 1. Add constraint so that we run the op with the strides we observed in tracing.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/111721
Approved by: https://github.com/drisspg, https://github.com/Chillee, https://github.com/jansel
ghstack dependencies: #111976",2023-10-26T23:59:56Z,Elias Ellison
506d41d65981983f0ea9661bdeca20f4f04462a5,"Improve disable name match (#71499)

Summary:
Allows disabling issues to disable all parametrized tests with dtypes.

Tested locally with:
1. .pytorch-disabled-tests.json as
```
{""test_bitwise_ops (__main__.TestBinaryUfuncs)"": [""https://github.com/pytorch/pytorch/issues/99999"", [""mac""]]}
```
and running `python test_binary_ufuncs.py --import-disabled-tests -k test_bitwise_ops` yields all tests skipped.

2. .pytorch-disabled-tests.json as
```
{""test_bitwise_ops_cpu_int16 (__main__.TestBinaryUfuncsCPU)"": [""https://github.com/pytorch/pytorch/issues/99999"", [""mac""]]}
```
and running `python test_binary_ufuncs.py --import-disabled-tests -k test_bitwise_ops` yields only `test_bitwise_ops_cpu_int16` skipped.

NOTE: this only works with dtype parametrization, not all prefixes, e.g., disabling `test_async_script` would NOT disable `test_async_script_capture`. This is the most intuitive behavior, I believe, but I can be convinced otherwise.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/71499

Reviewed By: mruberry

Differential Revision: *********

Pulled By: janeyx99

fbshipit-source-id: 98a84f9e80402978fa8d22e0f018e6c6c4339a72
(cherry picked from commit 3f778919caebd3f5cae13963b4824088543e2311)",2022-01-25T00:44:47Z,Jane Xu
0c55f1bdecf2df6d0fb3ebff8efde70d4da6aafc,"[torchelastic] Improve process termination logic (#61602)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/61602

The diff introduces signal handlers and SignalException that is raised when the agent process receives SIGTERM or SIGINT.

When any of these signals received, the termination handler will raise the `SignalException`. The exception will then be processed by the main agent loop. The `shutdown(signum)` will be invoked, that would propagate the received signal to the child processes. The default 30 seconds timeout introduced: if child processes will not be able gracefully terminate during this timeout, the agent process would kill the processes via SIGKILL.

Test Plan: unittests, sandcastle

Reviewed By: cbalioglu

Differential Revision: *********

fbshipit-source-id: 3dbca2125676dc18d417cc3e3bb0301fdd42737a",2021-07-23T17:58:39Z,Aliaksandr Ivanou
49611a33297140a9c736b8630142af8438c526ac,"[PyTorch] MHA: simplify gemm_nt (#72460)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/72460

Just call existing matmul (which, IIUC, handles batching itself) rather than doing a few view ops. (Please let me know if this is actually a bad idea and why!)
ghstack-source-id: 149067333

Test Plan: CI

Reviewed By: ngimel

Differential Revision: *********

fbshipit-source-id: ace37ad3110e1134db6c8b638ae302f0d556e00a
(cherry picked from commit 258231c0f951bd701da179eaedc1ef795416c53f)",2022-02-15T02:12:50Z,Scott Wolchok
b19cf868e8ba5c068738b0ce940701027b67f84f,"Back out ""Support fp8 in AOTInductor + support optional<> in C ABI (#112527)"" (#113747)

Test Plan: sandcastle

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/113747
Approved by: https://github.com/chenyang78, https://github.com/khabinov",2023-11-15T22:42:22Z,Wei Wei
1a2edf6dca0111dc32a041284a0f3d9688fecef2,"[AOTI] Fix _mm_plus_mm codegen (#131689)

Summary: Fixes https://github.com/pytorch/pytorch/issues/128474

Pull Request resolved: https://github.com/pytorch/pytorch/pull/131689
Approved by: https://github.com/chenyang78",2024-07-25T13:43:18Z,Bin Bao
0c6a18de8de2a791a0d8b3526eab6401df49197f,"Add torch.promote_types function

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/26655

Test Plan: Imported from OSS

Differential Revision: D17556196

Pulled By: nairbv

fbshipit-source-id: eeebce8968bfb2ffd25c066595bc19e5dee6ea6f",2019-09-27T23:46:43Z,Brian Vaughan
0048243f70f37a3ae74725fb21c88704d3ab62bb,"Check compiler -v to determine compiler (fix #33701) (#37293)

Summary:
As described in the issue (https://github.com/pytorch/pytorch/issues/33701) the compiler check
	for building cpp extensions does not work with ccache.
	In this case we check compiler -v to determine which
	compiler is actually used and check it.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37293

Differential Revision: D21256913

Pulled By: ezyang

fbshipit-source-id: 5483a10cc2dbcff98a7f069ea9dbc0c12b6502dc",2020-04-27T17:46:07Z,Lukas Koestler
66d27504e3feddfc731457fc0cea7b1c1dff7dbe,"allow building docker without torchvision (#26168)

Summary:
There is an issue with the torchvision version not matching the pytorch version if one builds the docker from a tag, see issue https://github.com/pytorch/pytorch/issues/25917.  The current solution requires one to re-init the submodules or manually change the version of torchvision.  This PR allows one to build the docker image without torchvision, which not only fixes the above mentioned bug but also frees non-image pytorch users from the tyranny of torchvision :laughing:.

In all seriousness, for NLP researchers especially torchvision isn't a necessity for pytorch and all non-essential items shouldn't be in the docker.  This option removes one extra thing that can go wrong.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/26168

Differential Revision: D17550001

Pulled By: soumith

fbshipit-source-id: 48b8b9e22b75eef3afb392c618742215d3920e9d",2019-09-24T16:09:58Z,David Pollack
6988e40b484205c4354da8a7691dacfb23a90bbe,"[quant][fx] Lower operator.matmul in convert_fx (#113954)

Summary: We support lowering `torch.matmul` but not
`operator.matmul`. This commit adds support for the latter,
which enables lowering the shorthand `@`. This address
https://github.com/pytorch/pytorch/issues/111450.

Test Plan:
python test/test_quantization.py TestQuantizeFx

Reviewers: jerryzh168

Subscribers: jerryzh168, supriyar
Pull Request resolved: https://github.com/pytorch/pytorch/pull/113954
Approved by: https://github.com/jerryzh168",2023-12-08T21:53:57Z,andrewor14
97da60d5116fa2e81a8177ea95716832023599ed,"Updating submodules

Summary:
GitHub commits:

https://github.com/facebook/fbthrift/commit/ea8bae1f0f2a57618e8316bcdb2ecc4d34d9f538
https://github.com/facebook/folly/commit/134472ee45780ca2afa6f64cb7baac318c60a7c3
https://github.com/facebook/proxygen/commit/37e6cf9d62637be4936bfcebab188ccdf374e0fc
https://github.com/facebook/rocksdb/commit/eb367d45c0d96969f66aff0a16bee201f52beb1a
https://github.com/facebookincubator/mvfst/commit/76de6e15c0b6c0bcad664d59e40d113f86ccc0ea
https://github.com/pytorch/fbgemm/commit/e1b1a55309701f0f6d70afd7ad659d6f22c3e1ab

Test Plan: n/a

Reviewed By: wittgenst

fbshipit-source-id: 9d0d688d81be822900475223a787c5649e143e85",2020-02-25T01:31:33Z,svcscm
826550a32ed447abb7cc32de21bf7c192c6c2dd3,"Update the onnx Gemm op to FC/FCTransposed logic in caffe2 onnx backend (#10108)

Summary:
The broadcast is used by default when the opset version is greater then 6.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10108

Reviewed By: bddppq

Differential Revision: D9176934

Pulled By: houseroad

fbshipit-source-id: b737bd87b0ddc241c657d35856d1273c9950eeba",2018-08-20T23:02:06Z,JerryShih
f0ea6862ba9fab34878fb2fa881f817b71156283,"Support for pruning delays in Adagrad Optimizer (#34527)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/34527

Adding support for prune_delays and prune ratios in Adagrad optimizer.

Test Plan:
Tested via unit tests in masked_adagrad_optimizer_test. Added unit test  for prune_delay versions of MaskedAdagrad

buck build caffe2/caffe2/fb/optimizers:masked_adagrad_optimizer_test; buck-out/gen/caffe2/caffe2/fb/optimizers/masked_adagrad_optimizer_test#binary.par

buck test caffe2/caffe2/fb/dper/layer_models/tests/split_1:sparse_nn_test -- 'test_pruning'

All Dper tests passed https://our.intern.facebook.com/intern/testinfra/testrun/7599824380741217

Reviewed By: chocjy

Differential Revision: D20313419

fbshipit-source-id: 5c2c8d4e0fc2ec538bcd6f145c6b87a2381f90f3",2020-04-09T19:46:58Z,Dhruv Choudhary
1280363badddd622481695ac677dc8b75cad6a48,"Port `mean` kernel to structured kernels. (#61643)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/61643

Tracking issue: #55070

Test Plan: Imported from OSS

Reviewed By: ejguan

Differential Revision: *********

Pulled By: ezyang

fbshipit-source-id: dc95baf593096c03fb5f292ee6c36de3cc7f2b35",2021-08-13T15:20:19Z,Yukio Siraichi
532670cce0f1acf3d3fcc98a9a1c26f5428e184e,[cuda] add all gencode archs,2015-07-24T01:59:32Z,Yangqing Jia
e867831b8426a7fd9e4dcb86a2660e98c0962da1,"extend replaceConvolutionWithAtenConv to handle conv_transpose3d (#76888)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76888
Approved by: https://github.com/eellison",2022-05-13T16:40:12Z,Jiayi Sun
d3b6c5e556a75871eb5d62b35ba5703c6953f75d,Support output_padding in ConvTranspose while doing ONNX exporting (#4583),2018-01-11T17:31:06Z,Lu Fang
c5333cdfba36932dd1aaa14d6961e7cfc31c0f00,"[nnc] tensorexpr for quantized::add (#70188)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/70188

Test Plan: Imported from OSS

Reviewed By: ZolotukhinM

Differential Revision: *********

Pulled By: IvanKobzarev

fbshipit-source-id: bd4e451bfd7531f31f216def2c3c1ba2f2e566e7",2021-12-21T20:28:24Z,Ivan Kobzarev
79534867ac549c29b7895c43740d411ff63af867,"Migrate about 100 kernel to C10 full dispatcher (#54109)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54109

Codemod command generated by https://github.com/pytorch/pytorch/pull/54098

ghstack-source-id: 124114894

Test Plan: CI

Reviewed By: smessmer

Differential Revision: D27100359

fbshipit-source-id: 8338405274a2a020856af6e4a35a2fb21438f2a8",2021-03-17T20:33:39Z,Wenlei Xie
3a77f9aaaf60b348ac4fc3bdcedc3e3238fb272f,"[quant][api] Move torch.ao.quantization.pt2e.quantizer to torch.ao.quantization.quantizer (#105885)

Summary: moving quantizer to torch.ao.quantization to make it a public api, since pt2e is a folder for implementations

Test Plan:
CIs

sanity check: ""buck test //executorch/backends/xnnpack/test:test_xnnpack_quantized_models -- test_resnet18""

Differential Revision: D47727838

Pull Request resolved: https://github.com/pytorch/pytorch/pull/105885
Approved by: https://github.com/andrewor14",2023-07-26T18:20:09Z,Jerry Zhang
a919742149601888c793447c1a6ab262979f1dde,"c10::optional -> std::optional in PyTorch (#137333)

Test Plan: Sandcastle

Differential Revision: D63876535

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137333
Approved by: https://github.com/Skylion007, https://github.com/albanD",2024-10-11T00:16:10Z,Richard Barnes
362525724bdba375defd6405cfe1b46a6ea222d3,"type promote clamp (#77035)

Fixes #76630
When clamp(Tensor, Tensor) is structured, big parts of this PR won't be needed, but for now let's fix type promotion to make behavior more regular.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/77035
Approved by: https://github.com/mruberry",2022-05-09T05:54:17Z,Natalia Gimelshein
3611d26a25bd889627403a808ea667ac99c09904,"[JIT] Optimize FunctionSchema::checkArg for the Tensor case. (#48034)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/48034

The Tensor case is one of the most common and the existing check can be
made faster. This results in a ~21% improvement on DeepAndWide model and
would improve other models as well.

Before the change:
```
505[ms]
491[ms]
514[ms]
538[ms]
514[ms]
554[ms]
556[ms]
512[ms]
516[ms]
527[ms]
```

After the change:
```
406[ms]
394[ms]
414[ms]
423[ms]
449[ms]
397[ms]
410[ms]
389[ms]
395[ms]
414[ms]
```

Differential Revision: D24999486

Test Plan: Imported from OSS

Reviewed By: zdevito

Pulled By: ZolotukhinM

fbshipit-source-id: 7139a3a38f9c44e8ea793afe2fc662ff51cc0460",2020-11-17T03:00:49Z,Mikhail Zolotukhin
e913f77c60b8c86434da3b8d88e6e6b6b2319e0b,"Revert ""Made FlexAttention rewrite getitem calls to use aten.index in score_mod (#124799)""

This reverts commit 9bccafc31c9d489b727155e95633efd19adbceaa.

Reverted https://github.com/pytorch/pytorch/pull/124799 on behalf of https://github.com/clee2000 due to broke tests but only on crossref https://github.com/pytorch/pytorch/actions/runs/8841521519/job/24279075171, added no td label so itll actually run this time ([comment](https://github.com/pytorch/pytorch/pull/124799#issuecomment-2078530797))",2024-04-26T02:35:14Z,PyTorch MergeBot
33a950924ad8a370d9945515c51feb0eb75fbf3b,"Skip Slice if it's no op (#19155)

Summary:
If it's identity op, just skip the slice and return the input.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/19155

Reviewed By: zrphercule

Differential Revision: D14890238

Pulled By: houseroad

fbshipit-source-id: f87b93df2cca0cb0e8ae2a1d95ba148044eafd4a",2019-04-11T19:23:30Z,Lu Fang
534db77e738ce53625a4b1a870f6fda332e2e8a2,"Autotune pointwise/reduction in max_autotune mode (#94556)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/94556
Approved by: https://github.com/ngimel",2023-02-10T04:29:07Z,Jason Ansel
3182642b2c7880618998d90e08edbd2a19619df7,"[functorch] Beef up transform limitations doc (pytorch/functorch#879)

I want to be able to point someone at this page whenever we get asked
about the limitations of vmap. Please let me know if there are things
we're still missing from here",2022-06-17T17:22:07Z,Richard Zou
ae1c365dbdbf667ae24c57eec9f2e6b9debf16bd,Add TH_INDEX_BASE to nDimension and stride functions,2017-03-26T13:25:53Z,albanD
3107f1dcd5f5aa6e2053a7f610d1478277f2a415,"fix align_corners doc

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23707

Differential Revision: D16617565

Pulled By: ezyang

fbshipit-source-id: 9ae581e9233d8c2b92f35b9486af1dab30ce8e3a",2019-08-02T19:29:29Z,Tongzhou Wang
5dd07324578f5110a2ec5c213fb559bc49004c7a,"[ZeRO] Add ctor support for multiple param groups (#72578)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/72578

**Overview**
This adds `ZeroRedundancyOptimizer` constructor support for multiple parameter groups (i.e. passing an `iterable` of `dict`s instead of an `iterable` of `torch.Tensor` as the `parameters` argument) to mirror the API for non-sharded optimizers.

Fixes https://github.com/pytorch/pytorch/issues/71347 and https://github.com/pytorch/pytorch/issues/59973.

This modifies `test_collect_shards()` to skip if ROCm.

**Test Plan**
I adjusted the existing constructor test, and I added a test for parity between constructing with two parameter groups up front versus constructor with one parameter group and adding the second parameter group after (via `add_param_group()`) versus a non-sharded optimizer.

Test Plan: Imported from OSS

Reviewed By: rohan-varma

Differential Revision: D34106940

Pulled By: awgu

fbshipit-source-id: 7e70fc0b3cec891646e0698eaedf02ff4354c128
(cherry picked from commit 40f2d45172ba3286b64000a466e42c055cca8ddc)",2022-02-15T16:47:06Z,Andrew Gu
2bf68e72d5d443e1aae7533894595a008299347f,Add hook system to autograd and nn,2016-08-23T18:50:12Z,Adam Paszke
dcc1e1cd87d1b341a853ad700520919a7a39821e,"[BE] Use `!{{ common.checkout_pytorch(""recursive"") }}` in binary builds workflows

Pull Request resolved: https://github.com/pytorch/pytorch/pull/71663",2022-01-21T23:43:29Z,Nikita Shulga
87c5f02f3dccd67b61bdb5098c6048f5981dc1e2,"jit: Conv3d + BatchNorm3d fusion (#40082)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/40082

Differential Revision: D22120340

Pulled By: jerryzh168

fbshipit-source-id: fce6c5f03fe7ab6c60620cbdf547d5a466a470e3",2020-06-22T18:14:18Z,"Zhang, Xiaobing"
84b7daadb22ff913f3aaa5fa59754f55228845a3,"Relax verify of VariableFlags (#4191)

* Fix another leak in pybind11 code.

This time caused by an upstream pybind11 bug:

https://github.com/pybind/pybind11/pull/1216

This changes causes the code to go down a non-buggy pathway.

* Relax verify of VariableFlags

If we trace with a defined tensor, but see a run with a undefined
tensors we now allow that run to happen, replacing the tensor with
zeros.

This also fixes a bug where stage 0 tensors were not
checked against their verify flags.

This change does _not_ handle all bad situations that can happen.
For instance if the first thing traced has a undefined tensor but
a later tensor is defined, then it will fail because the graph itself
does not contain the trace for the derivative of the tensor.
However it is possible to work around this later case by
dry-running the function:

   z = Variable(...,requires_grad=True)
   x,y = f(z)
   (x.sum() + y.sum()).backward()",2017-12-15T17:57:31Z,Zachary DeVito
f71c3d265ab52589f983dd252d61461db4e7dbbd,"[ROCm] remove triton-rocm commit pin and merge pins with triton.txt (#133438)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133438
Approved by: https://github.com/jithunnair-amd, https://github.com/malfet",2024-08-24T18:26:49Z,Jack Taylor
8423ab4f99fb499d540316e047f0d4a0c9ad630c,"Fix `CosineAnnealingWarmRestart` annotation (#61106)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/44770.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/61106

Reviewed By: 1ntEgr8

Differential Revision: D29635764

Pulled By: walterddr

fbshipit-source-id: ddc45a7f04532a76d033ae7774706da1fa8608f7",2021-07-09T14:52:22Z,Philip Meier
d957c2d5de004083b404031bbcbee21dac52c22f,"[Doc] update default magma cuda version in readme (#122125)

Since we use cuda 12.1 by default now, it would be better to update the doc.

Many people (including me), want to directly copy-paste commands in readme 😉  Let's make our life easier.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/122125
Approved by: https://github.com/malfet",2024-05-28T03:37:23Z,youkaichao
8520ce5f78227a81fa1a0b37dd73f9a43bf31fc2,"Fix incorrect trace of post-accumulate grad hook on tensor with zero dims (#135226)

Fix incorrect trace of post-accumulate grad hook on tensor with zero dimensions

Fixes #135207

Pull Request resolved: https://github.com/pytorch/pytorch/pull/135226
Approved by: https://github.com/xmfan",2024-09-06T18:19:52Z,wdziurdz
cfbd06d7a1d0dedfc770938755eba5d754d800df,"add all pools, Batchnorm and Tanh (i.e. all ideeped MKLDNN ops) to MKLDNNFuser (#56541)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/56541

Reviewed By: pbelevich

Differential Revision: D27930353

Pulled By: Krovatkin

fbshipit-source-id: 4d5b932bad4154e8bdd6e35498354e13b39c87a1",2021-04-27T15:57:50Z,Nikolay Korovaiko
f11120967e7c28f5ad9bae261e6a65fb2183927e,"Support EnumerableShardingSpec in ShardedTensor. (#59061)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/59061

Overall Design: https://github.com/pytorch/pytorch/issues/55207

This PR builds upon https://github.com/pytorch/pytorch/pull/58517 and
https://github.com/pytorch/pytorch/pull/57409 to support creating a
ShardedTensor using EnumerableShardingSpec.
ghstack-source-id: 130780376

Test Plan:
1) unit tests
2) waitforbuildbot

Reviewed By: SciPioneer

Differential Revision: D28734551

fbshipit-source-id: 656f5f2b22041dae071bc475f19fe94c969716e8",2021-06-10T06:19:55Z,Pritam Damania
8d7607e3461870b884cf56f43efea4c9e9f6c0f8,"Add attribute exhaustive_search in _blacklist_caffe2_args (#12805)

Summary:
- exhaustive_search attribute will be blacklisted so it
     will be discarded from the coverted onnx model. At present
     it throws error while verifying the onnx model

Signed-off-by: Parth Raichura <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/12805

Differential Revision: D10502374

Pulled By: ezyang

fbshipit-source-id: 0926dfa3237a8a431184e7f7250146e5b0cbfb85",2018-10-23T05:45:50Z,Parth Raichura
e89685b0b541386825479bf120f6a1aa6d000238,"Revert ""[inductor] Use decomposition for _to_copy (#90314)""

This reverts commit 3fdb5f2dda7164f6282e80c39799843527d135e7.

Reverted https://github.com/pytorch/pytorch/pull/90314 on behalf of https://github.com/desertfire due to regresses performance on hf_Bert",2022-12-08T18:29:06Z,PyTorch MergeBot
a721d27c5124e9a601ae8bb1c1129f636a461633,"Make TORCH_SHOW_DISPATCH_TRACE actually work (#82277)

It looks like DEBUG macro is never actually set anywhere, see
https://github.com/pytorch/pytorch/issues/82276

Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/82277
Approved by: https://github.com/malfet",2022-07-27T00:06:17Z,Edward Z. Yang
bd0e9a73c79128153be56a4de1a3086ae1f4e960,"Fix some simple build error on MacOS (#949)

Issue #948

Signed-off-by: Zhou Chang <<EMAIL>>",2017-03-07T14:47:49Z,Zhou Chang
326a4cc8156a4d9eb11bb4ef2a275f81bfe96742,"Support map autograd and pytree in/out. (#101633)

Rebased https://github.com/pytorch/pytorch/pull/100494 and added dummy AOTConfig.

This PR adds autograd and pytree support for map operator.

Implementation-wise:

1. We temporarily make two HigherOrderOperators, ""map"" and ""map_impl"":
- ""map"" is user-facing. Currently, it unwraps the pytrees in inputs and create a flat_fn for it. Dynamo currently cannot deal with pytree.tree_flatten and pytree.tree_unflatten, we therefore make it a HigherOrderOperator to trigger dynamo logic of handling HigherOrderOperators.
- ""map_impl"" is the actual operator that works with the rest of torch subsystems such as functionalization, make_fx. It accepts flattend arguments, and a num_mapped_args integer denoting how many of the flattend arguments need to mapped i.e. their first dimension will be unstacked.

2. We create the forward and backward graph in autograd key and call torch.autograd.Function. Currently, the backward graph is recomputation-based and we need to partition the joint graph in the future to be more efficient.

Example traced graphs for map operators:
### Case 1: simple f and autograd
```python
def f(x, y):
    return x + y

def g(xs, y):
    out = control_flow.map(f, xs, y)
    return torch.autograd.grad(out, (xs, y), torch.ones_like(out))

gm = make_fx(g, tracing_mode=""symbolic"")(torch.ones(3, 4, 5, requires_grad=True), torch.ones(5, requires_grad=True))
# gm.print_readable() produces following:
class g(torch.nn.Module):
    def forward(self, xs_1: f32[3, s1, s2], y_1: f32[s2]):
        # No stacktrace found for following nodes
        body_graph_0 = self.body_graph_0
        map_impl = torch.ops.map_impl(body_graph_0, 1, xs_1, y_1);  body_graph_0 = None
        getitem: f32[3, s1, s2] = map_impl[0];  map_impl = None
        ones_like: f32[3, s1, s2] = torch.ops.aten.ones_like.default(getitem, pin_memory = False)
        is_same_size = torch.ops.aten.is_same_size.default(getitem, ones_like);  getitem = None
        body_graph_1 = self.body_graph_1
        map_impl_1 = torch.ops.map_impl(body_graph_1, 2, xs_1, ones_like, y_1);  body_graph_1 = xs_1 = ones_like = None
        getitem_1 = map_impl_1[0]
        getitem_2: f32[3, s1, s2] = map_impl_1[1]
        getitem_3: f32[3, s2] = map_impl_1[2];  map_impl_1 = None
        sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(getitem_3, [0], True);  getitem_3 = None
        sym_size: Sym(s2) = torch.ops.aten.sym_size(y_1, 0);  y_1 = None
        view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = sym_size = None
        return (getitem_2, view)

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s2]):
            # No stacktrace found for following nodes
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(arg1_1, arg2_1);  arg1_1 = arg2_1 = None
            return [add]

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s1, s2], arg3_1: f32[s2]):
            # No stacktrace found for following nodes
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(arg1_1, arg3_1);  arg1_1 = None
            is_same_size = torch.ops.aten.is_same_size.default(add, arg2_1);  add = None
            sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(arg2_1, [0], True)
            sym_size: Sym(s2) = torch.ops.aten.sym_size(arg3_1, 0);  arg3_1 = None
            view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = sym_size = None
            return [None, arg2_1, view]
```
### Case 2: list input/output f and autograd
```python
def f(x, y):
    return [x[0].cos() + y.sin(), x[1].sin() * y.cos()]

def g(xs, y):
    out = control_flow.map(f, xs, y)
    flat_out, _ = pytree.tree_flatten(out)
    flat_inp, _ = pytree.tree_flatten((xs, y))
    requires_grad_inp = [inp for inp in flat_inp if inp.requires_grad]
    return torch.autograd.grad(flat_out, requires_grad_inp, [torch.ones_like(out) for out in flat_out])

gm = make_fx(g, tracing_mode=""symbolic"")(
    [torch.ones(3, 4, 5), torch.ones(3, 4, 5, requires_grad=True)],
    torch.ones(5, requires_grad=True))

# gm.print_readable() produces following:
class g(torch.nn.Module):
    def forward(self, xs, y):
        xs_1: f32[3, s1, s2], xs_2: f32[3, s1, s2], y_1: f32[s2], = fx_pytree.tree_flatten_spec([xs, y], self._in_spec)
        # No stacktrace found for following nodes
        body_graph_0 = self.body_graph_0
        map_impl = torch.ops.map_impl(body_graph_0, 2, xs_1, xs_2, y_1);  body_graph_0 = None
        getitem: f32[3, s1, s2] = map_impl[0]
        getitem_1: f32[3, s1, s2] = map_impl[1];  map_impl = None
        ones_like: f32[3, s1, s2] = torch.ops.aten.ones_like.default(getitem, pin_memory = False)
        ones_like_1: f32[3, s1, s2] = torch.ops.aten.ones_like.default(getitem_1, pin_memory = False)
        is_same_size = torch.ops.aten.is_same_size.default(getitem, ones_like);  getitem = None
        is_same_size_1 = torch.ops.aten.is_same_size.default(getitem_1, ones_like_1);  getitem_1 = None
        body_graph_1 = self.body_graph_1
        map_impl_1 = torch.ops.map_impl(body_graph_1, 4, xs_1, xs_2, ones_like, ones_like_1, y_1);  body_graph_1 = xs_1 = xs_2 = ones_like = ones_like_1 = None
        getitem_2 = map_impl_1[0]
        getitem_3 = map_impl_1[1]
        getitem_4: f32[3, s1, s2] = map_impl_1[2]
        getitem_5: f32[3, s2] = map_impl_1[3];  map_impl_1 = None
        sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(getitem_5, [0], True);  getitem_5 = None
        sym_size: Sym(s2) = torch.ops.aten.sym_size(y_1, 0);  y_1 = None
        view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = sym_size = None
        return pytree.tree_unflatten([getitem_4, view], self._out_spec)

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s1, s2], arg3_1: f32[s2]):
            # No stacktrace found for following nodes
            cos: f32[s1, s2] = torch.ops.aten.cos.default(arg1_1);  arg1_1 = None
            sin: f32[s2] = torch.ops.aten.sin.default(arg3_1)
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(cos, sin);  cos = sin = None
            sin_1: f32[s1, s2] = torch.ops.aten.sin.default(arg2_1);  arg2_1 = None
            cos_1: f32[s2] = torch.ops.aten.cos.default(arg3_1);  arg3_1 = None
            mul: f32[s1, s2] = torch.ops.aten.mul.Tensor(sin_1, cos_1);  sin_1 = cos_1 = None
            return [add, mul]

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s1, s2], arg3_1: f32[s1, s2], arg4_1: f32[s1, s2], arg5_1: f32[s2]):
            # No stacktrace found for following nodes
            cos: f32[s1, s2] = torch.ops.aten.cos.default(arg1_1);  arg1_1 = None
            sin: f32[s2] = torch.ops.aten.sin.default(arg5_1)
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(cos, sin);  cos = sin = None
            sin_1: f32[s1, s2] = torch.ops.aten.sin.default(arg2_1)
            cos_1: f32[s2] = torch.ops.aten.cos.default(arg5_1)
            mul: f32[s1, s2] = torch.ops.aten.mul.Tensor(sin_1, cos_1)
            is_same_size = torch.ops.aten.is_same_size.default(add, arg3_1);  add = None
            is_same_size_1 = torch.ops.aten.is_same_size.default(mul, arg4_1);  mul = None
            mul_1: f32[s1, s2] = torch.ops.aten.mul.Tensor(arg4_1, sin_1);  sin_1 = None
            mul_2: f32[s1, s2] = torch.ops.aten.mul.Tensor(arg4_1, cos_1);  arg4_1 = cos_1 = None
            sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(mul_1, [0], True);  mul_1 = None
            sym_size: Sym(s2) = torch.ops.aten.sym_size(arg5_1, 0)
            view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = None

            #
            sin_2: f32[s2] = torch.ops.aten.sin.default(arg5_1)
            neg: f32[s2] = torch.ops.aten.neg.default(sin_2);  sin_2 = None
            mul_3: f32[s2] = torch.ops.aten.mul.Tensor(view, neg);  view = neg = None
            cos_2: f32[s1, s2] = torch.ops.aten.cos.default(arg2_1);  arg2_1 = None
            mul_4: f32[s1, s2] = torch.ops.aten.mul.Tensor(mul_2, cos_2);  mul_2 = cos_2 = None
            sum_2: f32[1, s2] = torch.ops.aten.sum.dim_IntList(arg3_1, [0], True);  arg3_1 = None
            view_1: f32[s2] = torch.ops.aten.view.default(sum_2, [sym_size]);  sum_2 = sym_size = None
            cos_3: f32[s2] = torch.ops.aten.cos.default(arg5_1);  arg5_1 = None
            mul_5: f32[s2] = torch.ops.aten.mul.Tensor(view_1, cos_3);  view_1 = cos_3 = None
            add_1: f32[s2] = torch.ops.aten.add.Tensor(mul_3, mul_5);  mul_3 = mul_5 = None
            return [None, None, mul_4, add_1]
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/101633
Approved by: https://github.com/zou3519",2023-05-17T16:52:26Z,ydwu4
ad8386c1320c3abddaf6cbdd580866de1720a735,"Lint fix

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76876

Approved by: https://github.com/ngimel",2022-05-05T04:50:17Z,anjali411
bccb727b65b3f670c57aaac9be4f0afbbb495956,"Remove wrong ""input"" arg from scatter_() docstring (#7550)",2018-05-14T19:33:47Z,Martin Drawitsch
6ceec53579dc88e9a3823dc602013fbd405c2a14,"[dynamo][cpp-guards] Fix test for CPP guard manager (#123515)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/123515
Approved by: https://github.com/guilhermeleobas, https://github.com/jansel",2024-04-07T17:53:51Z,Animesh Jain
730965c246192c94c804e5ac4a95f175dca2fb18,"Improve `torch.flatten` docs and add tests to test_view_ops (#49501)

Summary:
Addresses https://github.com/pytorch/pytorch/issues/39474

Pull Request resolved: https://github.com/pytorch/pytorch/pull/49501

Reviewed By: mruberry

Differential Revision: D25734450

Pulled By: soulitzer

fbshipit-source-id: 993667dd07acd81a4616465e0a3b94bde449193e",2020-12-31T04:32:51Z,Jeffrey Wan
4d53c632e0409e66f40f9c74486a58ce41ab70a2,"Remove unnecessary cuda flags.

-Xcompiler -std=c++11 is not needed, otherwise gcc produces warnings.",2017-01-04T08:08:41Z,Yangqing Jia
9dea86f86bddbe9ff767e2346f8b3aba7cd5b41c,"Make ProfiledTensorType hashable

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23116

Differential Revision: D16519748

Pulled By: Krovatkin

fbshipit-source-id: 25090678d82d5dc9ca0a48aef45eeb62b8ac8d45",2019-07-30T20:02:26Z,Nikolay Korovaiko
8011405a7b8e33eefdeb2b6ccc7535c4403088e3,"Adding docs on how to run backwards compatability test (#81431)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/81431
Approved by: https://github.com/salilsdesai",2022-07-13T22:03:40Z,John Clow
5ce88e7e71771d8ac730b32d5c4155b9cbe3c8ea,"remove unnecessary import introduced in PR 106535 (#107440)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/107440
Approved by: https://github.com/fduwjj
ghstack dependencies: #106535",2023-08-18T04:34:35Z,Xilun Wu
3a26772c53f856cc6bf24adf3256abb16843983c,"[functorch] Actually made some modifications to fix OpOverload changes (pytorch/functorch#579)

* actually fixed recent issues

* fixed new issues

* fixed overload issue

* Fix epsilons",2022-03-11T00:20:10Z,Horace He
e645771e954d342c232e33ea61e38710cc1fdb85,"Revert ""as_strided: Fix default storage_offset for reference implementation (#89513)""

This reverts commit ba70a8be03f2fca222deee030bf7d9d15260b549.

Reverted https://github.com/pytorch/pytorch/pull/89513 on behalf of https://github.com/kit1980 due to Broke multiple workflows, 2 unexpected successes for autograd tests",2022-12-06T07:14:16Z,PyTorch MergeBot
20a2e526efb353e302f5b8778b63e45a5eade240,"build a generic future<T> (#29579)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/29579

Per #28923, this diff is to move Future<Message> to torch::utils and extend it to be Future<T>, most of implementations are copied from FutureMessage and ivalue::Future. merge ivalue::Future with Future<T> will be done separately.

The main difference between Future<T>  and FutureMessage is the error handling, instead of checking message type inside Future to handle error, this future<T> owns has_error_ and error_ states.

also this future passes value_, has_error_ and error_ states to callbacks for easily read future states.

In next diff, a torch script rpc async API will be created, before the API returns, it will create an ivalue::Future and passes it to Future<T>'s call back where state of ivalue::Future will be set.  In this way, the torch script rpc async API  can still return a ivalue::Future and call wait() to get its state appropriately afterwards.
ghstack-source-id: 95479525

Test Plan: unit tests

Differential Revision: *********

fbshipit-source-id: 48a65712656a72c2feb0bb3ec8b308c0528986a6",2019-12-13T00:54:23Z,Yanli Zhao
fc3103b1162a3312e4ad74005d0b698a333c0750,"fixing a naming issue in creating a residual loop node in a bailout graph (#31400)

Summary:
This addresses the issue of differentiating between `%4` in
`%12 : int, %y.1 : Tensor = prim::Loop(%9, %6, %4, %3)` and `%y.5 : Double(3) = aten::cat(%22, %4) # test_jit.py:3772:24` in `%4` loop's body in a residual continuation loop, because these should be different values.

```
[DUMP profiling_graph_executor_impl.cpp:124] with prim::BailoutTemplate_0 = graph(%z.1 : int,
[DUMP profiling_graph_executor_impl.cpp:124]       %size.1 : int):
[DUMP profiling_graph_executor_impl.cpp:124]   %2 : Tensor = prim::Constant[value= 1  1 [ CPUDoubleType{2} ]]()
[DUMP profiling_graph_executor_impl.cpp:124]   %3 : Double(2) = prim::BailOut[index=0](%2, %z.1, %size.1)
[DUMP profiling_graph_executor_impl.cpp:124]   %4 : int = prim::Constant[value=0]() # test_jit.py:3772:54
[DUMP profiling_graph_executor_impl.cpp:124]   %5 : None = prim::Constant()
[DUMP profiling_graph_executor_impl.cpp:124]   %6 : bool = prim::Constant[value=1]() # test_jit.py:3770:16
[DUMP profiling_graph_executor_impl.cpp:124]   %counters.1 : int[] = prim::ListConstruct()
[DUMP profiling_graph_executor_impl.cpp:124]   %8 : int = prim::Constant[value=8]()
[DUMP profiling_graph_executor_impl.cpp:124]   %9 : int = aten::__round_to_zero_floordiv(%size.1, %8)
[DUMP profiling_graph_executor_impl.cpp:124]   %10 : int = aten::mul(%9, %8)
[DUMP profiling_graph_executor_impl.cpp:124]   %11 : int = aten::sub(%size.1, %10)
[DUMP profiling_graph_executor_impl.cpp:124]   %12 : int, %y.1 : Tensor = prim::Loop(%9, %6, %4, %3) # test_jit.py:3770:16
[DUMP profiling_graph_executor_impl.cpp:124]     block0(%i.2 : int, %15 : int, %y.7 : Tensor):
[DUMP profiling_graph_executor_impl.cpp:124]       %17 : Double(2) = prim::BailOut[index=1](%y.7, %z.1, %counters.1, %9, %11, %i.2, %15)
[DUMP profiling_graph_executor_impl.cpp:124]       %18 : int[] = aten::append(%counters.1, %15) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %19 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %20 : Tensor = aten::ones(%19, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %21 : Double(1) = prim::BailOut[index=2](%20, %z.1, %counters.1, %9, %11, %i.2, %15, %17)
[DUMP profiling_graph_executor_impl.cpp:124]       %22 : Tensor[] = prim::ListConstruct(%17, %21)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.5 : Double(3) = aten::cat(%22, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %24 : int = prim::Constant[value=1]()
[DUMP profiling_graph_executor_impl.cpp:124]       %25 : int = aten::add(%15, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %26 : int[] = aten::append(%counters.1, %25) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %27 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %28 : Tensor = aten::ones(%27, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %29 : Double(1) = prim::BailOut[index=3](%28, %z.1, %counters.1, %9, %11, %i.2, %y.5, %25)
[DUMP profiling_graph_executor_impl.cpp:124]       %30 : Tensor[] = prim::ListConstruct(%y.5, %29)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.9 : Double(4) = aten::cat(%30, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %32 : int = aten::add(%25, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %33 : int[] = aten::append(%counters.1, %32) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %34 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %35 : Tensor = aten::ones(%34, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %36 : Double(1) = prim::BailOut[index=4](%35, %z.1, %counters.1, %9, %11, %i.2, %y.9, %32)
[DUMP profiling_graph_executor_impl.cpp:124]       %37 : Tensor[] = prim::ListConstruct(%y.9, %36)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.10 : Double(5) = aten::cat(%37, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %39 : int = aten::add(%32, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %40 : int[] = aten::append(%counters.1, %39) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %41 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %42 : Tensor = aten::ones(%41, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %43 : Double(1) = prim::BailOut[index=5](%42, %z.1, %counters.1, %9, %11, %i.2, %y.10, %39)
[DUMP profiling_graph_executor_impl.cpp:124]       %44 : Tensor[] = prim::ListConstruct(%y.10, %43)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.11 : Double(6) = aten::cat(%44, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %46 : int = aten::add(%39, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %47 : int[] = aten::append(%counters.1, %46) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %48 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %49 : Tensor = aten::ones(%48, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %50 : Double(1) = prim::BailOut[index=6](%49, %z.1, %counters.1, %9, %11, %i.2, %y.11, %46)
[DUMP profiling_graph_executor_impl.cpp:124]       %51 : Tensor[] = prim::ListConstruct(%y.11, %50)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.12 : Double(7) = aten::cat(%51, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %53 : int = aten::add(%46, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %54 : int[] = aten::append(%counters.1, %53) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %55 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %56 : Tensor = aten::ones(%55, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %57 : Double(1) = prim::BailOut[index=7](%56, %z.1, %counters.1, %9, %11, %i.2, %y.12, %53)
[DUMP profiling_graph_executor_impl.cpp:124]       %58 : Tensor[] = prim::ListConstruct(%y.12, %57)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.13 : Double(8) = aten::cat(%58, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %60 : int = aten::add(%53, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %61 : int[] = aten::append(%counters.1, %60) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %62 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %63 : Tensor = aten::ones(%62, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %64 : Double(1) = prim::BailOut[index=8](%63, %z.1, %counters.1, %9, %11, %i.2, %y.13, %60)
[DUMP profiling_graph_executor_impl.cpp:124]       %65 : Tensor[] = prim::ListConstruct(%y.13, %64)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.14 : Double(9) = aten::cat(%65, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %67 : int = aten::add(%60, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %68 : int[] = aten::append(%counters.1, %67) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %69 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %70 : Tensor = aten::ones(%69, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %71 : Double(1) = prim::BailOut[index=9](%70, %z.1, %counters.1, %9, %11, %i.2, %y.14, %67)
[DUMP profiling_graph_executor_impl.cpp:124]       %72 : Tensor[] = prim::ListConstruct(%y.14, %71)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.15 : Tensor = aten::cat(%72, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %74 : int = aten::add(%67, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       -> (%6, %74, %y.15)
[DUMP profiling_graph_executor_impl.cpp:124]   %75 : Double(10) = prim::BailOut[index=10](%y.1, %z.1, %counters.1, %11, %12)
[DUMP profiling_graph_executor_impl.cpp:124]   %76 : int, %y : Tensor = prim::Loop(%11, %6, %12, %75) # test_jit.py:3770:16
[DUMP profiling_graph_executor_impl.cpp:124]     block0(%i.1 : int, %79 : int, %y.6 : Tensor):
[DUMP profiling_graph_executor_impl.cpp:124]       %81 : Double(*) = prim::BailOut[index=11](%y.6, %z.1, %counters.1, %11, %i.1, %79)
[DUMP profiling_graph_executor_impl.cpp:124]       %82 : int[] = aten::append(%counters.1, %79) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %83 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %84 : Tensor = aten::ones(%83, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %85 : Double(1) = prim::BailOut[index=12](%84, %counters.1, %11, %i.1, %79, %81)
[DUMP profiling_graph_executor_impl.cpp:124]       %86 : Tensor[] = prim::ListConstruct(%81, %85)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.4 : Tensor = aten::cat(%86, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %88 : int = prim::Constant[value=1]()
[DUMP profiling_graph_executor_impl.cpp:124]       %89 : int = aten::add(%79, %88)
[DUMP profiling_graph_executor_impl.cpp:124]       -> (%6, %89, %y.4)
[DUMP profiling_graph_executor_impl.cpp:124]   %90 : Double(12) = prim::BailOut[index=13](%y, %counters.1)
[DUMP profiling_graph_executor_impl.cpp:124]   %91 : (Tensor, int[]) = prim::TupleConstruct(%90, %counters.1)
[DUMP profiling_graph_executor_impl.cpp:124]   return (%91)
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/31400

Differential Revision: D19172750

Pulled By: Krovatkin

fbshipit-source-id: 85d3aac4e80b65b83b6be3c0bca8075a731a2b7e",2019-12-19T08:32:43Z,Nikolay Korovaiko
aeed8a6ea4650d1092289a60e71d8d83875a0ba6,Remove duplicate entries and add optional marks in THCUNN.h,2016-11-15T20:22:14Z,Adam Paszke
8fe91d16b022954206c378f637cb3de81f8f219a,"Remove CUDA 11.6 note from complex docs (#100118)

Removes note in the complex docs pointing to the CUDA 11.6 wheels introduced in https://github.com/pytorch/pytorch/pull/80363.
Background: this warning was added via https://github.com/pytorch/pytorch/issues/79876 which pointed out a slow compilation time in 11.3. The 11.6 pip wheels were thus recommended but are not build anymore as our current support is 11.7, 11.8 (and 12.1 experimental in nightlies).

The note is confusing users as it doesn't explain why 11.6 is needed.
Reference: https://discuss.pytorch.org/t/complex-numbers-cuda-11-6-documentation-warning/178588/1

Pull Request resolved: https://github.com/pytorch/pytorch/pull/100118
Approved by: https://github.com/msaroufim",2023-04-27T16:26:24Z,pbialecki
6efcb6c718c6954df2157d93989ec7ed1821aafe,"Fix wrong ufmt exclusions in `.lintrunner.toml` (#124135)

Part of: #123062

In this pull request(#123809), there were some exclusions that should have been removed, but weren't.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/124135
Approved by: https://github.com/ezyang",2024-04-17T12:22:50Z,Yuanhao Ji
a4355d6b9a6adb06043ac75f99c0b4af35d026c5,"Revert ""Add --filter-rank to torchrun: allow logs filtering by rank (#118562)""

This reverts commit 73229b4f931f8cd1799b0905d61e3d8e85157bcd.

Reverted https://github.com/pytorch/pytorch/pull/118562 on behalf of https://github.com/xmfan due to breaks MAST precheck, flag naming conflict ([comment](https://github.com/pytorch/pytorch/pull/118562#issuecomment-1924916601))",2024-02-02T23:56:20Z,PyTorch MergeBot
444b52ff40cf4afce7bc3fdcf021a88eab3b954c,"[Dynamo] Simplify torch function mode stack guard (#135444)

The semantics of ignored modes previously had edge cases, this eliminates these by in essence filtering any ignored modes out of both the ref stack and the current torch function mode stack. This is purely to fix complexity in #135422.  The ignored modes handling will be removed in a future PR after https://github.com/pytorch/pytorch/pull/135422 lands, since we will then trace through DeviceContexts vs inserting them into the graph which needed these extra workarounds for correctness.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/135444
Approved by: https://github.com/anijain2305, https://github.com/williamwen42
ghstack dependencies: #134732, #133137, #135443",2024-09-09T23:02:11Z,Michael Lazos
8e4161517e112478a1c1f0290fedb91965f95aff,"div_kernel: throw when dividing by integer zero (#32629)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/327
Pull Request resolved: https://github.com/pytorch/pytorch/pull/32629

Differential Revision: *********

Pulled By: ezyang

fbshipit-source-id: f5bbb298f150efe63a698e8a0b53a84871d16560",2020-01-28T05:37:49Z,Wojciech Baranowski
631f0351313da2edeeb72cf8bb963035b11425d9,"Update forward AD not supported error message

Pull Request resolved: https://github.com/pytorch/pytorch/pull/75105

Approved by: https://github.com/albanD",2022-04-02T13:51:18Z,soulitzer
dcaa111dc8359a293e9efb3cd968073bb2038ac2,"support intersection by polyfill (#130672)

Fixes https://github.com/pytorch/pytorch/issues/130557

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130672
Approved by: https://github.com/anijain2305",2024-07-14T10:44:26Z,awayzjj
b3e24c53eb349f9d2614906599a3f62fd197c035,"use performance-unnecessary-value-param in clang-tidy (#102615)

performance-unnecessary-value-param has been disabled in clang-tidy for a long time. However, this check is actually useful and able to some interesting performance problems.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102615
Approved by: https://github.com/malfet, https://github.com/Skylion007",2023-07-28T17:36:59Z,cyy
42bd1abc62c17c349e1d53d512f3e0964ad1d77a,"[Inductor Cutlass backend] Tolerate dynamic shapes (#121497)

Previously, when the Cutlass backend was enabled, using dynamic shapes could lead to exceptions during JIT.

With this change, there are guards in place to just disable the Cutlass backend if dynamic dimensions are involved.

In addition, if no choices for a GEMM are available using the selected backends, then an ATen Kernel is used as fallback, even if the ATen backend is not enabled.

Test:
CI
Additional unit test in test_cutlass_backend.py

Pull Request resolved: https://github.com/pytorch/pytorch/pull/121497
Approved by: https://github.com/jansel",2024-04-21T22:48:20Z,Kai Londenberg
92a17f454ae3b4956a62d3cf6cb19988c7015766,"[1/N][dtensor] introduce StridedShard placement type and _split_tensor() logic (#126697)

**Summary**
This PR adds a new private placement type `_StridedShard` for FSDP2 + TP style tensor sharding. The previously used `Shard` placement type cannot produce correct `full_tensor()` result because it assumes the tensor to be first sharded over `dp` mesh dimension then `tp` mesh dimension which does not hold true in FSDP2 + TP case.

**Test**
`pytest test/distributed/_tensor/test_utils.py -s -k strided_sharding`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/126697
Approved by: https://github.com/wanchaol",2024-08-06T21:34:41Z,Xilun Wu
22d258427baf226fe67f888de044a62941c66dd7,"[BE][Easy] enable UFMT for `torch/distributed/_shard/` (#128867)

Part of #123062

- #123062

Pull Request resolved: https://github.com/pytorch/pytorch/pull/128867
Approved by: https://github.com/fegin
ghstack dependencies: #128866",2024-06-18T06:31:39Z,Xuehai Pan
f1f99ab310840b2476075b4a97f610b0c4e05140,"Fix conv1d with explicit precision (#75824)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/75824

X-link: https://github.com/pytorch/fx2trt/pull/57

Reviewed By: jerryzh168

Differential Revision: D35621145

fbshipit-source-id: 639c373f5e3e187eb6971d7f2d4b5e92f11dfc62
(cherry picked from commit 69602c0089941096e242fbb065685267e50bf26e)",2022-04-19T23:45:37Z,Shirong Wu
0a38aed02537de2fc2cd6d85fdef4de6dd1f8065,"Auto set libuv_ROOT env var for Gloo submodule on Windows platform (#45484)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/45484

Reviewed By: lw

Differential Revision: D23990724

Pulled By: mrshenli

fbshipit-source-id: 1987ce7eb7d3f9d3120c07e954cd6581cd3caf59",2020-09-29T15:56:46Z,gunandrose4u
eef72f3f8ac752350f3fc73b5aa67424ffdc799f,"[NNC] Update Buf on mutation instead of creating new ones (#57513)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/57513

Test Plan: Imported from OSS

Reviewed By: VitalyFedyunin

Differential Revision: D28226917

Pulled By: navahgar

fbshipit-source-id: 4e74c56a85b7aadc285b872b8ef8f8e26f31c8ce",2021-05-06T08:05:49Z,Raghavan Raman
4f2d869095034301b903cd2ef807b416547c0d9c,"Fix distributed issue by including distributed files (#87615)

This fixes regression in distributed headers installation.
Caused by following PR: https://github.com/pytorch/pytorch/pull/85953
which removed the inclusions

Fixes #87173

Test plan from wheel build by this CI: https://github.com/pytorch/pytorch/actions/runs/3314742519

```
[ec2-user@ip-10-0-9-132 c10d]$ pwd
/home/<USER>/actions-runner/_work/_temp/artifacts/torch/include/torch/csrc/distributed/c10d
[ec2-user@ip-10-0-9-132 c10d]$ ls -las
total 300
 4 drwxr-xr-x 2 <USER> <GROUP>  4096 Oct 24 19:12 .
 0 drwxr-xr-x 4 <USER> <GROUP>    29 Oct 24 19:12 ..
12 -rw-r--r-- 1 <USER> <GROUP>  9051 Oct 24 17:28 Backend.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   216 Oct 24 17:28 c10d.h
 4 -rw-r--r-- 1 <USER> <GROUP>  3880 Oct 24 17:28 comm.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   604 Oct 24 17:28 debug.h
 4 -rw-r--r-- 1 <USER> <GROUP>  1717 Oct 24 17:28 default_comm_hooks.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1316 Oct 24 17:28 error.h
 4 -rw-r--r-- 1 <USER> <GROUP>   962 Oct 24 17:28 exception.h
 4 -rw-r--r-- 1 <USER> <GROUP>  1461 Oct 24 17:28 FileStore.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   771 Oct 24 17:28 GlooDeviceFactory.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1154 Oct 24 17:28 HashStore.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  4058 Oct 24 17:28 logger.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2059 Oct 24 17:28 logging.h
 8 -rw-r--r-- 1 <USER> <GROUP>  7979 Oct 24 17:28 NCCLUtils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2756 Oct 24 17:28 Ops.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1814 Oct 24 17:28 ParamCommsUtils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1478 Oct 24 17:28 PrefixStore.hpp
16 -rw-r--r-- 1 <USER> <GROUP> 13235 Oct 24 17:28 ProcessGroupGloo.hpp
12 -rw-r--r-- 1 <USER> <GROUP> 11298 Oct 24 17:28 ProcessGroup.hpp
12 -rw-r--r-- 1 <USER> <GROUP>  8645 Oct 24 17:28 ProcessGroupMPI.hpp
28 -rw-r--r-- 1 <USER> <GROUP> 26526 Oct 24 17:28 ProcessGroupNCCL.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  3805 Oct 24 17:28 ProcessGroupRoundRobin.hpp
12 -rw-r--r-- 1 <USER> <GROUP> 10361 Oct 24 17:28 ProcessGroupUCC.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  5062 Oct 24 17:28 ProcessGroupWrapper.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  4201 Oct 24 17:28 PyProcessGroup.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1072 Oct 24 17:28 python_comm_hook.h
24 -rw-r--r-- 1 <USER> <GROUP> 23859 Oct 24 17:28 reducer.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2330 Oct 24 17:28 reducer_timer.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1683 Oct 24 17:28 sequence_num.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2108 Oct 24 17:28 socket.h
 4 -rw-r--r-- 1 <USER> <GROUP>  2589 Oct 24 17:28 Store.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  3264 Oct 24 17:28 TCPStore.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  6944 Oct 24 17:28 TraceUtils.h
 8 -rw-r--r-- 1 <USER> <GROUP>  4539 Oct 24 17:28 Types.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   580 Oct 24 17:28 UCCForNCCL.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2301 Oct 24 17:28 UCCTracing.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  4933 Oct 24 17:28 UCCUtils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   584 Oct 24 17:28 UnixSockUtils.hpp
24 -rw-r--r-- 1 <USER> <GROUP> 20796 Oct 24 17:28 Utils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   575 Oct 24 17:28 WinSockUtils.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  4259 Oct 24 17:28 Work.hpp
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/87615
Approved by: https://github.com/malfet",2022-10-24T19:38:07Z,atalman
0ca1ff3dce71b3fa4a905512ec7be381be753240,"Revert ""Add support for capturing tensors with score_mod (#124444)""

This reverts commit 7c253a777641791247f7fcc19fe5c60f24be32b9.

Reverted https://github.com/pytorch/pytorch/pull/124444 on behalf of https://github.com/jeanschmidt due to Breaking internal tests, check D56522566 ([comment](https://github.com/pytorch/pytorch/pull/124444#issuecomment-2076908582))",2024-04-25T10:56:38Z,PyTorch MergeBot
ae71c5c7e6922c54189399dc1e5554cef7e0011d,"Optimized bincount for the CPU by removing extra size() calls (#35822)

Summary:
By removing the calls of `size` that were effectively nops, I've managed to make `bincount_cpu` run around 6 times faster on my machine. EDIT: (Running Windows 10, I'm suspecting this may be a Windows-specific bug)

For histogramming 1e7 samples with 1e5 bins, best of 20 with 10 runs each
Before: 3.201189
After: 0.466188
Pull Request resolved: https://github.com/pytorch/pytorch/pull/35822

Differential Revision: D20919885

Pulled By: ezyang

fbshipit-source-id: 1657056d69a02f1e61434f4cc8fa800f8d4e1fe8",2020-04-08T18:07:05Z,Marian Ivanov
4543cf4eb16611ab8847064e3b826f756e810c51,"[JIT] add support for torch.lu to torchscript (#33724)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/33724

Fix for https://github.com/pytorch/pytorch/issues/33381, partial fix of https://github.com/pytorch/pytorch/issues/30786

Test Plan: Imported from OSS

Differential Revision: D20077321

Pulled By: eellison

fbshipit-source-id: a1e6a0370712b36c9f66979098ac2f9d500ca5f6",2020-02-27T02:28:47Z,Elias Ellison
00996006d10395cb564ff8245622846aa355cf57,Remove type inference from value,2017-10-12T05:51:50Z,Lu Fang
1a4ee2a6bbe2c987c38f3c246f0b5192d7f79e3f,"Add XPU support for storage resize_ (#105262)

We'd like to add XPU device support for storage resize_

Pull Request resolved: https://github.com/pytorch/pytorch/pull/105262
Approved by: https://github.com/mikaylagawarecki",2023-07-18T12:46:00Z,zhuhong61
9cda7b9e4725f37093ed383691d5713ddd168af4,"[hotfix] Do not import torch.ao.quantization._pt2e from dynamo (#100194)

Summary: Importing torch.ao.quantization._pt2e from dynamo led to
internal test failures related to memory profiling. For now,
let's express the path using a simple string instead.

Reviewers: jerryzh168, kimishpatel

Pull Request resolved: https://github.com/pytorch/pytorch/pull/100194
Approved by: https://github.com/jerryzh168",2023-04-27T19:17:28Z,andrewor14
9e5045e978d8800a6dbeb919745169e4de18927c,"[pytorch] clean up normalized_dynamic_type() hack (#44889)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/44889

This HACK doesn't seem to be necessary any more - there is no 'real'
type in generated Declarations.yaml file.
Verified by comparing generated code before/after.

Test Plan: Imported from OSS

Reviewed By: ezyang

Differential Revision: D23761624

Pulled By: ljk53

fbshipit-source-id: de996f04d77eebea3fb9297dd90a8ebeb07647bb",2020-09-19T06:47:48Z,Jiakai Liu
e4d7676c1b756a0b332dffa71d6c66b8ee2af418,"[CPU] Expand `torch.special.i1` to Half and BF16 (#137899)

To match behavior of `torch.special.i0`

Noticed while looking at the failures in https://github.com/pytorch/pytorch/pull/137849

Also, add explicit high-precision template specialization for  `calc_i0` and `calc_i1` for `BFloat16` and `Half`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137899
Approved by: https://github.com/Skylion007",2024-10-15T17:00:58Z,Nikita Shulga
9d4cb0d3ebb41fd8a2c83af0612a569d3fedf586,"Fix param and buffer mapping for state_dict when there are state_dict hooks (#137609)

Resolve #137540

Summary:

We might get different state_dict and named_parameters result when the module has registered custom state_dict_hooks.
For exported_program's state_dict, we want the state_dict to reflect the actual module hierarchy at runtime, and it might be different from the model's state_dict() output if the model has state_dict hooks.
To do weight swapping, one needs to either re-export or turn-off the hooks when saving model's state_dict().
Previously, ExportedProgram uses nn.Module's state_dict() method to populate its own state_dict, but it doesn't work for some models (e.g. llama3_3_vision) because ExportedProgram's state_dict and an nn.Module's state_dict have some subtle differences semantically.

nn.Module's state_dict is about how the state should be serialized, and it reflects the structure of the original user model code. In contrast, export specializes on a “run” of a model, and its state_dict needs to reflect the runtime module hierarchy.

One example where these two are different is TorchTune's Llama3_2_vision text decoder. Here, a FusionLayer is added as a local optimization and it is not part of the ""static model definition"".  In runtime, we have mod.layers[3].layer.sa_norm.scale.

But in nn.Module's state_dict, the authors of the model added a state_dict hook to remove the ""layer"" in mod.state_dict() to reflect the static model definition, so we have mod.state_dict()[""layers.3.sa_norm.scale""].
In this Diff, we change ExportedProgram to populate its state_dict using named_parameters() and named_buffers() instead. So in ExportedProgram's state_dict, we have ""layers.3.layer.sa_norm.scale"", which reflects the runtime module hierarchy.

Now one problem this presents is weight swapping. Since ExportedProgram's state and the model's state is not the same anymore, weight swapping procedure also needs to change slightly.

In internal Ads and RecSys models deployment, weight swapping is where they have one model that is currently being being deployed and serving traffic, and they want to swap out the weights with newly trained model weights without having to redo the whole exporting/lowering process and create a new artifact. So they would move the deployed model’s pointer to the state dict over to the new state dict. Because of this, it’s previously a requirement that the FQNs are matching between the exported and the eager model’s state dict.

The new ExportedProgram's state dict still supports weight swapping, but the state_dict to be swapped needs to be obtained from torch.export.exported_program instead of model.state_dict() if the model has state_dict hooks.
The new requirement is that the FQNs are matching between the exported’s state dict and the state_dict obtained from `_disabled_load_state_dict_hooks(M)` context manager. One benefit of having this new API is that we are now in full control within export of gathering and updating the model state.
If a model doesn't have any state_dict hooks, one can still use model.state_dict() for weight swapping, so it's BC.

Test Plan:
```
buck2 run 'fbcode//mode/dev-nosan' fbcode//caffe2/test:test_export  -- -r  test_export_for_training_with_state_dict_hooks
```

Differential Revision: D64080561

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137609
Approved by: https://github.com/angelayi, https://github.com/pianpwk",2024-10-11T01:33:50Z,Shangdi Yu
3072c97017a2ad2a22b11edf08d0842c22688f44,"Gelu Backward, Contribution from Kevin Stephano (#58249)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/58249

Test Plan: Imported from OSS

Reviewed By: ejguan

Differential Revision: D28425629

Pulled By: Krovatkin

fbshipit-source-id: 494ab165d548aa76f036344ab1c19c5fd64bae82",2021-05-14T02:36:38Z,Nikolay Korovaiko
3cf267bfa6d0a8d8f3d6e355ee4da84a444fdd5e,"Embedding: Remove dispatch in parallel region (#60597)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/60597

Ref #56794

Test Plan: Imported from OSS

Reviewed By: jbschlosser

Differential Revision: D29446191

Pulled By: ngimel

fbshipit-source-id: d6ff010104ae621d5e3d9c269ed2b48407e71d67",2021-06-30T19:26:19Z,Peter Bell
be3b16daad2dcd861ca9931025194041689478c6,"[decomp] Fix baddbmm decomposition (#109714)

The decomposition is currently registered without the pw_cast_for_opmath
decorator, due to the ordering of decorators being meaningful.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/109714
Approved by: https://github.com/lezcano",2023-09-28T17:12:41Z,Peter Bell
414ec6ce97a360a60b46ccee9cf84d764b978016,"Turn off automatic_dynamic_shapes in prep for dynamic-by-default (#103320)

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/103320
Approved by: https://github.com/Skylion007",2023-06-09T17:56:05Z,Edward Z. Yang
98d6a6eb7da57e2edb56102c7a4a274761ef26f0,"[inductor] clean up TODO comments. (#133718)

clean up TODO comments.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133718
Approved by: https://github.com/henrylhtsang",2024-08-16T22:11:59Z,Xu Han
d027aef8f86bd70c4ae525fc1bef92f1301e71c2,"Revert ""Removed q_num_blocks from constructor (#130819)""

This reverts commit 03c660468eb57772e82c1034613f5ff8781c775a.

Reverted https://github.com/pytorch/pytorch/pull/130819 on behalf of https://github.com/atalman due to Internal problem with previous PR in stack https://github.com/pytorch/pytorch/pull/130818 ([comment](https://github.com/pytorch/pytorch/pull/130819#issuecomment-2233359569))",2024-07-17T13:43:35Z,PyTorch MergeBot
82eb09aafd7e4ee6e4fb0580f2221ea6253d218b,"[Environment Variable][4/N] Use thread-safe getenv functions (#137843)

Follows #137328

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137843
Approved by: https://github.com/ezyang",2024-10-21T02:58:59Z,cyy
269e92669a8f25003e639230e18eeabb863533a7,"[c2] Remove unused private fields (#69709)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/69709

Fix logical bug in `caffe2/ideep/operators/conv_op.cc`, which
contained an always false statement (fusion_type_ == X && fusion_type_ == Y ) statement

Test Plan: Imported from OSS

Reviewed By: r-barnes

Differential Revision: D32997006

Pulled By: malfet

fbshipit-source-id: 23e4db1b17cf8a77eae6a8691847ffa484d4736c",2021-12-14T19:25:24Z,Nikita Shulga
68238606f3e9f70ac000187ed17ea347fdc0c549,"Revert ""Reland: Add PyObject preservation for UntypedStorage (#103907)""

This reverts commit 56b848157c259b4e53225e2516d603e9c8cfab79.

Reverted https://github.com/pytorch/pytorch/pull/103907 on behalf of https://github.com/huydhn due to Sorry for reverting your change, but it is failing torchdistx build which uses check_pyobj here https://github.com/pytorch/torchdistx/blob/9c1b9f5cb2fa36bfb8b70ec07c40ed42a33cc87a/src/python/torchdistx/_C/deferred_init.cc#L87 ([comment](https://github.com/pytorch/pytorch/pull/103907#issuecomment-1712121158))",2023-09-08T19:27:07Z,PyTorch MergeBot
640b4863394d0a0e4d6e6f89c2b52ba35f501fd9,"add clang-tidy to github actions (#27755)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/27755

This gives us nice annotations. See
https://github.com/suo/pytorch/pull/22/files for an approximation of
what it will look like (ignore the warnings on the lint.yml file).

I deleted the old azure pipelines one since making the code work for
both was annoying, and unlike flake8 this one does not affect master

Test Plan: Imported from OSS

Differential Revision: D17888974

Pulled By: suo

fbshipit-source-id: d8928a1451b6ef500dc1889284cab2845ecdeeea",2019-10-12T00:00:14Z,Michael Suo
d37c2d7c8d63563bffdbebe8224ef28795b952e4,"Revert D17495965: TensorRT 6.0 support and PyTorch->ONNX->TRT6 unit test

Test Plan: revert-hammer

Differential Revision:
D17495965

Original commit changeset: 3e8dbe8943f5

fbshipit-source-id: d47fcbec22b0d61df41d7dbf15cfdde196ac818f",2019-10-25T20:56:34Z,Junjie Bai
9c144bc4fe993fcc767c8755cbb7b06b592af3cb,"Dont increment generation if forward of backward exists, and warning on deallocation of live tensors (#97168)

Refining the logic for when it is okay to ignore previously live outputs from cudagraphs. If there is a forward that has been invoked without invocation of the corresponding backwards, dont allow overwriting outputs.

Differential Revision: [D44228369](https://our.internmc.facebook.com/intern/diff/D44228369)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/97168
Approved by: https://github.com/ezyang, https://github.com/jansel",2023-03-21T23:26:58Z,Elias Ellison
c9511e8ac9cfe633a0a58005757ece5801f1bd7b,"[foreach][BE] cleaning up MultiTensorApply.cuh (#110228)

Followup edits to #109402 as suggested by @r-barnes

Pull Request resolved: https://github.com/pytorch/pytorch/pull/110228
Approved by: https://github.com/drisspg",2023-09-28T16:29:12Z,Jane Xu
85851b1e8fc12fc824edd24aa1f1e0f9075b0cd7,"remove useless clang-tidy suppression  (#92287)

remove NOLINTNEXTLINE(cppcoreguidelines-pro-type-member-init)
remove NOLINTNEXTLINE(performance-move-const-arg)
remove NOLINTNEXTLINE(performance-no-automatic-move)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/92287
Approved by: https://github.com/albanD",2023-01-21T02:33:24Z,cyy
f98edfcc48c903d0d22a0105b0fafe4ca58121e6,"Make TorchElastic timer importable on Windows (#88522)

Also, add `torch.distributed` to test imports, so that we would not
regress in the future

Fixes https://github.com/pytorch/pytorch/issues/85427
Pull Request resolved: https://github.com/pytorch/pytorch/pull/88522
Approved by: https://github.com/d4l3k",2022-11-10T17:42:20Z,Nikita Shulga
b347b8c19155a78f18ca98e9e3d02fd4c89fb1f6,"[quant][fx] Support some default ops in the native backend config (#74600)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/74600

Following https://github.com/pytorch/pytorch/pull/74210, this PR adds the support for some ops
using the DefaultNodeQuantizeHandler in the backend_config_dict defintion for pytorch native backend

TODO: There is still a few ops we didn't handle with backend_config_dict path: gelu and softmax, need to discuss if we still need them, if so we can change the test
to use backend_config_dict and remove the DefaultNodeQuantizeHandler after that

Test Plan:
python test/test_quantization.py TestQuantizeFxOps

Imported from OSS

Reviewed By: andrewor14

Differential Revision: *********

fbshipit-source-id: 70351d2810ca1ac7dc09d4a9c239f6757ccb51ca
(cherry picked from commit 5e68f755a32ba7d90d6c73db9c2017f9c58d7fa5)",2022-03-25T02:54:07Z,Jerry Zhang
77d29bcee200f04bece4a86283acfb8e1ec830ad,"[primTorch] special: ndtr, ndtri, log_ndtr, erfcx (#86077)

- Adds prims and _refs for `erfcx` and `ndtri`.
- Adds _refs for `ndtr`, and `log_ndtr`.

cc @kshitij12345 @lezcano @mruberry
Pull Request resolved: https://github.com/pytorch/pytorch/pull/86077
Approved by: https://github.com/mruberry",2022-10-13T01:18:30Z,Khushi Agrawal
ce9614662392574af4163a3c8456a731056c5c38,"[PT2] Fix node metadata setting in group_batch_fusion_aten (#134543)

Summary: Current impl results in `meta` missing fields like`val`, use `FakeTensorProp` to update the information

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134543
Approved by: https://github.com/frank-wei",2024-08-29T18:32:04Z,Xintong Hu
be5191a00b9c9683e1e3b0f85c2f0ec6e0b4139a,Add documentation for keepdim.,2017-05-04T18:19:46Z,Gregory Chanan
524adfbffd82666cc9281325a9f0ae4250c7fad1,"Use new FFT operators in stft (#47601)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/47601

Fixes https://github.com/pytorch/pytorch/issues/42175#issuecomment-719933913

Test Plan: Imported from OSS

Reviewed By: ngimel

Differential Revision: D25457217

Pulled By: mruberry

fbshipit-source-id: 455d216edd0b962eb7967ecb47cccc8d6865975b",2020-12-10T18:27:21Z,Peter Bell
43416e3059a787629d28799f832c854d554a3ca2,"Correctly read the cache key for remote cache (#121151)

Summary: While investigating why we were calling put each time, I noticed that memcache backend returns a list instead of direct result, which means that we were correctly fetching the cached result but not using it.

Test Plan: The test should now work as expected

Differential Revision: D54500851

Pull Request resolved: https://github.com/pytorch/pytorch/pull/121151
Approved by: https://github.com/aakhundov",2024-03-05T07:33:20Z,Oguz Ulgen
bac0878780e3c2ea2933e07ef64cbbcb53eb0996,"Error if compiled nondeterministic backward called in deterministic mode (#114780)

Part of #113707

Pull Request resolved: https://github.com/pytorch/pytorch/pull/114780
Approved by: https://github.com/ezyang, https://github.com/albanD",2024-01-15T22:45:40Z,Kurt Mohler
bb99008c9e7c357b88047bcd6971dc2078341484,"Only thunkify proxies in some situations (#132421)

The goal of this PR is to avoid stack overflow when we create extremely long chains of thunks, and then evaluate them (e.g., as occurs if you sum(long list of symint)). The basic idea behind this PR is to only thunkify proxies if they're being created in places where they may or may not be used--crucially, symint operations that occur in user code we are tracing are eagerly placed into the graph, even if they may eventually be dead.

I annotated the PR with explanation of changes.

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/132421
Approved by: https://github.com/Skylion007, https://github.com/zou3519
ghstack dependencies: #132674, #132675",2024-08-07T01:25:28Z,Edward Z. Yang
84a9694ed0c85c0bc844915adf9f2c8fd53bacb1,"Fix windows msbuild bug (#18748)

Summary:
Fix the bug introduced by #18681 where an undefined variable was being used to limit max cpu count when building for Windows without Ninja.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/18748

Differential Revision: D14733209

Pulled By: soumith

fbshipit-source-id: 52fc0dd4dde99da75a6956b63f02da2e647eed4f",2019-04-02T21:25:28Z,vaeksare
1fa0bb6d9da4d375d8bc29d319b5dcc934e8b602,"Use workspace to persist and restore images for Windows CI build and … (#38971)

Summary:
Inspired by malfet

> By the way, once we have build_artifacts property, can someone try if its faster to use it as mean of transferring images between build and test instead of using AWS (i.e. use artifacts instead of jenkins/pytorch/win-test-helpers/upload_image.py /download_image.py pair)

Use CircleCI to store intermediate binaries and make them available to be downloaded as artifacts instead of uploading to S3.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38971

Differential Revision: D21717080

Pulled By: seemethere

fbshipit-source-id: e3498b058778d02ae2f38daefbc7118a1a2cbe76",2020-05-25T23:52:10Z,Yang Gu
153e2e96d4aaf0c3d97dea7ee9375b2ad26d679f,"Make Sequential ref-counted (#9151)

Summary:
In the C++ API, `Sequential` currently was not refcounted itself, but stored `shared_ptr<AnyModule>` to get the reference semantics. This is unfortunate because most modules in the API are accessed via `->`, e.g. `Linear l(1, 2); l->forward(...);`. `Sequential` was different in that it had value semantics itself, thus was accessed via `.`.

This PR makes `Sequential` store `AnyModule` (without extra indirection), and uses the same pImpl mechanism we use for all other modules to make `Sequential` have reference semantics itself. This makes it consistent with the rest of the library. It also removes one level of indirection inside of `Sequential`, which is cool.

One thing I had to change was that the `ModuleHolder` with which the whole pImpl thing is implemented previously did some tricks to make `Linear(3, 4)` actually construct `Linear(LinearOptions(3, 4))`. This doesn't work well with `Sequential` since it takes a variadic parameter pack. Instead, I made `ModuleHolder` forward all arguments to the underlying module, and then further pushed the trick to forward parameters to modules' options types into the actual Modules. This adds one constructor per Module in the library. This is not something user modules have to do (unless they want this nice forwarding themselves). It makes the code simpler overall.

ezyang ebetica apaszke
Pull Request resolved: https://github.com/pytorch/pytorch/pull/9151

Reviewed By: ezyang

Differential Revision: D8809298

Pulled By: goldsborough

fbshipit-source-id: da68452c3de912fbc67af330ba93b5220de6909f",2018-07-12T00:15:08Z,Peter Goldsborough
64efd88845467f5255be50ddbd784ce8db3b688f,"Add directly referenced header files  for ""ceil_div.h"" (#99607)

std::enable_if_t is defined in <type_traits>. Directly referencing header files is good programming style

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99607
Approved by: https://github.com/albanD, https://github.com/kit1980",2023-04-28T01:05:01Z,zhi.cai
fa7ae6cdbca4b16f4c2cfce09d6e05574b89a518,"can't infer device on benchmarked function with no args or kwargs (#133290)

when we call benchmarker.benchmark(fn, (), {}) it attempts to infer the device from the args and kwargs, which are both empty. in this case the default behavior is to assume CPU, since `is_cpu_device` is implemented as `all([x.device == ""cpu"" for x in ... if x is Tensor])`, and `all([]) == True`. I've added a PR that makes this raise an error, but we should just fix this one callsite first

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133290
Approved by: https://github.com/eellison",2024-08-13T20:13:44Z,Nicolas Macchioni
32ecaa0870dcd291f3336450c612d581ff8512cb,regenerate docs w/ recent changes (#126),2017-10-24T20:11:32Z,Trevor Killeen
441d75ce569f89bad3e2f1f2a2075e68ae3bc76b,Adapts basic operations to new THXVector interface,2017-04-07T14:51:12Z,Pedro Porto Buarque de Gusmao
c458bb985e620376e60493ab83e426f098926634,"make it easier to grep for unary/binary op kernels (#60128)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/60128

Test Plan: Imported from OSS

Reviewed By: wenleix

Differential Revision: D29175499

Pulled By: bdhirsh

fbshipit-source-id: 1838900276e0b956edf25cdddcff438ff685a50e",2021-06-17T00:47:55Z,Brian Hirsh
c6aa03bd4e9fe5b223eef2e97fda0bc61d593b1f,"Add allow_xpu to enable XPU UTs (#130312)

# Motivation
enable UTs under folder test/xpu/

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130312
Approved by: https://github.com/EikanWang, https://github.com/gujinghui, https://github.com/albanD",2024-07-12T15:49:38Z,"Yu, Guangye"
eb15b1a016c6facaf8605dde2c20b5de1586542d,"[dtensor][MTPG] make sharding prop lru cache not shared among threads (#134294)

**Summary**
Before this PR, `sharding propagator` is shared among threads. The result is the cache result of rank 0 would be accessible by other ranks e.g. rank 1 and this could lead to wrong DTensor resharding. This PR fixes it by making the cache a local variable at thread level, and it fixes `dstack` test (#126493), `inner` (https://github.com/pytorch/pytorch/issues/126852), and `vstack` (https://github.com/pytorch/pytorch/issues/126868). It also fixes `poisson_nll` (https://github.com/pytorch/pytorch/issues/131446) as a bi-product.

**Test**
`pytest test/distributed/_tensor/test_dtensor_ops.py`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134294
Approved by: https://github.com/wz337, https://github.com/awgu",2024-08-23T18:59:19Z,Xilun Wu
ee143d31ef071b277e21ac5eb3d7b725f60e7756,"Fix ImageInput op in resnet50_trainer.py

Summary:
Fix #1269 (from fa0fcd4053dd42a4ec3a2a12085662179f0e11df).
Closes https://github.com/caffe2/caffe2/pull/1314

Reviewed By: bwasti

Differential Revision: ********

Pulled By: bddppq

fbshipit-source-id: 7d7c45f8b997c25f34530f826729d700a9c522d4",2017-10-10T18:15:07Z,Luke Yeager
38a9984451ac6f6d453fb06ec13583df09f31227,"[TensorExpr] Properly handle all dtypes in evaluation of CompareSelect exprs. (#42493)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/42493

Test Plan: Imported from OSS

Reviewed By: nickgg

Differential Revision: *********

Pulled By: ZolotukhinM

fbshipit-source-id: cf7073d6ea792998a9fa3989c7ec486419476de0",2020-08-04T19:17:09Z,Mikhail Zolotukhin
a4e7b8001c41b5e8a088281a09992233b3ac38db,"refuse to generate a symbolic variable if a float input is inf (#139846)

Fixes `PYTORCH_TEST_WITH_INDUCTOR=1 tlp python test/test_torch.py TestTorchDeviceTypeCPU.test_cauchy_cpu_float64` when `specialize_float=False`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/139846
Approved by: https://github.com/ruidazeng, https://github.com/ezyang
ghstack dependencies: #139569, #139457, #139568, #139572",2024-11-07T05:52:33Z,Bob Ren
1cefc589054dcb698686f118bd04875c592bd2a7,"init tls grad_mode/local_dispatch_key set while fork new thread in (#113246)

TorchDynamo will guard grad_mode and the local dispatch key set.
https://github.com/pytorch/pytorch/blob/3a429423fcf72430e7a36c79e263c877d7a4ef72/torch/csrc/dynamo/guards.cpp#L13-L16

While using ThroughputBenchmark, those tls state will not be init as same as the main thread status.
https://github.com/pytorch/pytorch/blob/3a429423fcf72430e7a36c79e263c877d7a4ef72/torch/csrc/utils/throughput_benchmark-inl.h#L64-L94

Run following scripts
```
import torch
linear = torch.nn.Linear(128, 128)
compiled = torch.compile(linear)
x = torch.rand(10, 128)
with torch.no_grad(), torch.cpu.amp.autocast(enabled=True, dtype=torch.bfloat16):
    compiled(x)
    compiled(x)

from torch._dynamo import config
config.error_on_recompile = True
from torch.utils import ThroughputBenchmark
with torch.no_grad(), torch.cpu.amp.autocast(enabled=True, dtype=torch.bfloat16):
    bench = ThroughputBenchmark(compiled)
    bench.add_input(x)
    stats = bench.benchmark(
        num_calling_threads=10,
        num_warmup_iters=100,
        num_iters=100,
    )
    print(stats)
```
will lead to 2 re-compile reasons:
```
triggered by the following guard failure(s): ___check_global_state()
triggered by the following guard failure(s): tensor 'x' dispatch key set mismatch.
```

This will trigger a re-compile in torchdynamo. But since `ThroughputBenchmark` is used for sharing weight within threads, the model should not be changed anymore while running the benchmark. So this PR is to init the tls state as same as main thread. Then we can use ` ThroughputBenchmark` to run torchdynamo optimized models.

throughputbenchmark
Pull Request resolved: https://github.com/pytorch/pytorch/pull/113246
Approved by: https://github.com/jgong5, https://github.com/desertfire",2024-01-03T01:20:33Z,haozhe.zhu
d52404779fad7cd82b9075adec5ce7c127e9c224,"Revert D5803245: [caffe2][MPSCNN][segmentation] Make android segmentation net run with MPSCNN

Summary:
This reverts commit 6808e9c3504389c113c7a16504d6554e83bdcc3e

bypass-lint

Differential Revision: D5803245

fbshipit-source-id: e6e2e90dd196ae958d729af2e19942e922207a2a",2017-09-12T01:31:59Z,Hao Lu
d59ecc02df70bad2273858c2fad2b4993133a3d3,"[DDP] Fix when buffers are reassigned in module (#64472)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/64472

Sometimes, user module can reassign tensor buffer, as in:

```
self.buffer = torch.randn(1, 2) # in init
self.buffer += 1 # in forward
```

in this case, `self.modules_buffers` will become outdated and we should
repopulate self.modules_buffers if we need to sync module buffers.

See https://github.com/pytorch/pytorch/issues/63916 for full description of the
issue.
ghstack-source-id: 137526309

Test Plan: CI

Reviewed By: zhaojuanmao

Differential Revision: D30745921

fbshipit-source-id: 25eb1edbf445703a481802e07f3058d38ea6fc64",2021-09-09T02:13:33Z,Rohan Varma
e5f5bcf6d4ec022558caf4d0611d928497394a88,"[inductor] include global cache dir in inductor resources (#102130)

Summary: adding global cache dir glob to inductor resources

Test Plan: sandcastle + CI + tested locally

Differential Revision: D46131451

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102130
Approved by: https://github.com/jansel",2023-07-17T15:44:16Z,Nicolas Macchioni
1b66915f3916273edcf9dc5c3853bb31f21ed8f4,"Have type_parser return const reference (#70477)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/70477

Test Plan: Sandcastle

Reviewed By: cccclai

Differential Revision: D33340030

fbshipit-source-id: b2a295b7c1c01e86971f6b9bbdd7d3718a2d3f0c",2022-01-04T00:17:18Z,Richard Barnes
c5abe8844a87f4ed47ac5126d65f6021208417b7,"Add IDEEP fallbacks for Resnet50 training ops (#8541)

Summary:
1. Add fallback gradient ops
2. In fallback ops, set the output Tensor as CPUTensor instead of IDEEPTensor if ndim = 0. Because IDEEPTensor doesn't support 0 dim.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/8541

Reviewed By: yinghai

Differential Revision: D9115233

Pulled By: wesolwsk

fbshipit-source-id: 163e6a76f02bd781c95d1060ccbacf2cab90055e",2018-08-03T22:48:13Z,wuhuikx
22964d1007d1d87964df1db609da53b35b00316f,"[DSD] Deprecate submodules feature for DSD (#127793)

Summary:
Getting a partial of the state_dict and set the state_dict with the type of Dict[nn.Module, Dict[str, Any]] is too complicated and can confuse users. The features can be achieved by simple pre-processing and post-processing by users. So this PR adds the deprecation warning to the feature.

The previous PR, https://github.com/pytorch/pytorch/pull/127070, assumes
no one is using the feature and remove it without the grace period. This
seems to be too aggresive and causes some concerns. This PR adds the
deprecation warning and tests.

We will remove the support in 2.5.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/127793
Approved by: https://github.com/LucasLLC",2024-06-03T19:31:48Z,Chien-Chin Huang
b592e675166f84645ce62098f7ddd3d4482942ed,"Use C++17 [[fallthrough]]; (#102849)

Test Plan: Sandcastle

Reviewed By: meyering

Differential Revision: D46385240

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102849
Approved by: https://github.com/Skylion007",2023-06-06T07:06:26Z,Richard Barnes
1932bc69e9fcf176726fddb4a75c5e3a0a797c91,"Move GHA to ONNX (#65975)

Summary:
- Delete CircleCI ONNX config
- Add sharded ONNX job to the list of generated workflows
- Move ONNX runtime installation from `pytorch-job-specs.yml` to `.jenkins/caffe2/test.sh`
- Limit MKLDNN to AVX2 ISA while running  Caffe2 tests

Pull Request resolved: https://github.com/pytorch/pytorch/pull/65975

Reviewed By: seemethere

Differential Revision: D31327206

Pulled By: malfet

fbshipit-source-id: 15aa53e4481e846c62b4ee2db5c03047d68679a4",2021-10-05T16:29:27Z,Nikita Shulga
d9aeb7e71ba3a17c14d437cdd2b8e1e3ca2f09a1,"clamp now has subgradient 1 at min and max (#7049)

* subgradient 1 at min and max for clamp

* clamp max and clamp min too

* add comment",2018-04-30T13:21:56Z,Tongzhou Wang
04ad0134ae51a50a1f657c1e4b86c3c16f0e9158,"[FSDP] Use `reduce_scatter_tensor()` (#87240)

Let us silence some more warnings 👍🏼
Pull Request resolved: https://github.com/pytorch/pytorch/pull/87240
Approved by: https://github.com/rohan-varma",2022-10-24T03:39:38Z,Andrew Gu
a777dea3b338efb1110f37ff6ff844d0ad54c5ef,"Remove dtype check on meta device (#136774)

Summary:
# Latest Update

This diff is no longer needed because we did need the check to exist, to make meta behave the same as other devices, see D54526190.

---------------------------------

# Background

T176105639

| case | embedding bag weight | per_sample_weight | fbgemm lookup | forward in meta |
| A | fp32 | fp32 | good | good |
| B | fp16 | fp32 | good| failed [check](https://fburl.com/code/k3n3h031) that forces weight dtype ==  per_sample_weights dtype |
| C | fp16 | fp16 | P1046999270, RuntimeError: ""expected scalar type Float but found Half from fbgemm call"" | good |
| D | fp32 | fp16 | N/A | N/A |

Currently we are in case A. Users need to add `use_fp32_embedding` in training to force embedding bag dtype to be fp32. However, users actually hope for case B to use fp16 as the embedding bag weight. When deleting `use_fp32_embedding`, they would fail the [check](https://fburl.com/code/k3n3h031) that forces `weight dtype ==  per_sample_weights dtype ` in meta_registration.

The check is actually not necessary. Is it because the backend fbgemm does support case B. Additionally, later on in the `meta_embedding_bag`, `weight` and `per_sample_weights` don't need to be in the same dtype (https://fburl.com/code/q0tho05h, weight is src, per_sample_weights is scale) for `is_fast_path_index_select`.

# This diff
Therefore, this diff remove the unnecessary [check](https://fburl.com/code/k3n3h031) to support case B in meta forward. With such, users are able to use fp16 to be the emb bag dtype without the need to force per_sample_weights the same dtype in meta forward (see Test Plan).

# Reference diffs to resolve this issue
Diff 1: D52591217
This passes embedding bag dtype to feature_processor to make per_sample_weights same dtype as emb bag weight. However, `is_meta` also needs to be passed because of case C. fbgemm still does not support per_sample_weights = fp16 (see the above table). Therefore users are forced to only make per_sample_weights fp16 when it is on meta. The solution requires too many hacks.

Diff 2: D53232739
Basically doing the same thing in diff 1 D52591217, except that the hack is added in TorchRec library. This adds an if in EBC and PEA for: when emb bag weight is fp16, it forces per_sample_weight fp16 too. However, it would then result in fbgemm issue too and has broken a bunch of prod models.

Test Plan:
# APS
The following command will run icvr_launcher which triggers ads_launcher and run forward in meta device:
```
buck2 run mode/opt -c python.package_style=inplace //aps_models/ads/icvr:icvr_launcher_publish -- mode=mast_ig_fm_when_combo0_uhm_publish launcher.fbl_entitlement=ads_global_tc_ads_score launcher.data_project=oncall_ads_model_platform launcher.tags=[ads_ranking_taxonomy_exlarge_fm_prod] stages.train=false
```

Result:
 {F1461463993}

Reviewed By: ezyang

Differential Revision: D54175438

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136774
Approved by: https://github.com/ezyang",2024-10-12T05:45:21Z,Angel Yang
c0bfe2a6ed06a4e66f681d736453415643a60ff7,"Clean up conversion registration

Summary:
[x] get registry working
[x] move all current ops to registry

Reviewed By: yinghai

Differential Revision: D8706115

fbshipit-source-id: 8dfce79039b57dea1c15e8e291cdd74f39766ade",2018-07-06T20:35:44Z,Bram Wasti
45010833069c02162fee40291ef7d444dfe7f41b,"dedupe test skipping in common_distributed and test_distributed (#38078)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38078

`common_distributed` and `test_distributed` have some error codes that overlap but are for different reasons, for example, code 75 in `test_distributed` is ""no cuda available"" but in common_distributed it is ""need at least 2 CUDA devices"".

This is an issue because the tests in `test_distributed` now use the utils in `common_distributed`, so we could get the wrong reason for skipping tests.

It is also the source of test failures in https://github.com/pytorch/pytorch/pull/37990.

This diff makes it so that the test skipping logic is deduped and put into `common_distributed.py`, where it can be reused and then imported into `test_distributed`
ghstack-source-id: 103782583

Test Plan: CI

Differential Revision: D21466768

fbshipit-source-id: 53b5af36672ebd8b51ba8b42709d87e96cadef20",2020-05-09T06:17:28Z,Rohan Varma
45a3231bb8a0c8b7498917a2eb43a8e9ccf04f96,"[codemod] Enforce proper use of emplacy functions

Summary: The goal of this diff is enforce proper use of ""emplacy"" functions. In each case, this saves at worst a move constructor call, and at best a full copy of the object (in the case of a constructor call where the object does not have a move constructor).

Test Plan: CI.

Reviewed By: marksantaniello

Differential Revision: D27888714

fbshipit-source-id: 235d0b31066463588c7e4ab86e132c430a352500",2021-05-05T03:56:28Z,Nicolas Jean van Kempen
252e68a83b56343f723a35c3cd16b05f66b8e726,"Revert ""Add support for `torch.Generator` type in TorchScript (#110413)""

This reverts commit 54493fe8c4b1cca4c5ff993b99eb3e3dbc984226.

Reverted https://github.com/pytorch/pytorch/pull/110413 on behalf of https://github.com/huydhn due to Sorry for reverting your change but it is, unfortunately, still breaking internal builds ([comment](https://github.com/pytorch/pytorch/pull/110413#issuecomment-1811625557))",2023-11-15T00:51:23Z,PyTorch MergeBot
0951f4424aba2be8bc94fc0cb2712bd6cef1791b,CUDA 9.2 adds support to GCC 7.3.1. (#7880),2018-05-29T20:53:06Z,xkszltl
af8b04d5f622ea6e2b07594b73ac741ef7dddefa,"Add create_graph_input debug log (#108836)

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/108836
Approved by: https://github.com/mlazos, https://github.com/voznesenskym",2023-09-08T14:10:22Z,Edward Z. Yang
def76eee1cf90f136c9ccdf82afa03ee1e54c365,"[auto] Update onnx to e2e8003 - add output shape as input for reshape (#608)
https://github.com/onnx/onnx/commit/e2e8003ec36800038959569cc6f3057ffee69fc9",2018-03-16T23:32:44Z,onnxbot
041bff77b620ecf3a9280b012746bf10a49d2972,"Make tools/actions_local_runner.py PY-3.X compatible (#58787)

Summary:
Do not use `shlex.join`, which is a simple join over quoted args, i.e.
https://github.com/python/cpython/blob/a9e43615c2e1fc5dd60063c1509e8b1c5daad095/Lib/shlex.py#L318-L320

Pull Request resolved: https://github.com/pytorch/pytorch/pull/58787

Reviewed By: driazati

Differential Revision: D28619996

Pulled By: malfet

fbshipit-source-id: dd4e939a88e2923b41084da2b5fbdbee859c0104",2021-05-22T00:39:04Z,Nikita Shulga
5817695bfa577f0ea08bef715bcae48ef9d34a02,"[pt2] Fix arange to match ATen behavior (#93353)

Fixes #92676

`arange` infers the output dtype from the argument types, but in order to reduce
falling back to ATen, inductor preferred to cast whole number float arguments to
int which gave the wrong output dtype. Instead, this decomposes floating point
arange into the prim equivalent for integers.

This also changes the signature of `prims.arange` to

```python
prims.iota(length, *, start, step, **factory_kwargs)
```

which only supports integers arguments. This is done because calculating the
output size from `start, end, step` is surprisingly complex and liable to off by
one errors so should not be duplicated in each backend.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/93353
Approved by: https://github.com/ngimel, https://github.com/lezcano",2023-02-02T17:46:48Z,Peter Bell
ca7ce2fca1769b49d0ef6a39d6df76c34e1431d0,"[ts-migration][1/N]: Add prim::Loop for constant number of iterations and condition (#131418)

#### Description
This PR adds prim::Loop support for the simplest case where the number of iteration is constant and the loop termination condition is also a constant.

[PR by stages](https://docs.google.com/document/d/1q6OprW3HBHbYPwEyE_DikBn-uzmhnN284Cmen_CnlhI/edit?usp=sharing)

#### Test Plan
Add reprod example.
* `pytest test/export/test_converter.py -s -k test_ts2ep_with_loop`
Pull Request resolved: https://github.com/pytorch/pytorch/pull/131418
Approved by: https://github.com/angelayi",2024-08-06T16:51:08Z,Jiashen Cao
0f419abf40d337a063184ccf24c21acfa789e5ba,"Roll nomnigraph build into caffe2 (#11303)

Summary:
We need to remove nomnigraph from the list of public libraries in order to support libtorch extensions. Easiest way to do this is to include it into the Caffe2 source like all other caffe2/core/ code.

However, because the headers are in a different place, we need to include them for linked libraries (pybind, tests, etc).

On an upside, this means that nomnigraph is now default hidden visibility too.

FYI peterjc123 xkszltl goldsborough bwasti Yangqing
Pull Request resolved: https://github.com/pytorch/pytorch/pull/11303

Reviewed By: pjh5

Differential Revision: ********

Pulled By: orionr

fbshipit-source-id: 5db3eb20bc5ddc873ce9151236b74663fbb33ed8",2018-09-07T02:35:36Z,Orion Reblitz-Richardson
de400fa5acb4ba2fd045d504391a4aaecfb8c65e,"[JIT] handle specially mapped ops (#41503)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/41503

Fix for https://github.com/pytorch/pytorch/issues/41192

We can map fill_ and zero_ to their functional equivalents full_like and zeros_like

Test Plan: Imported from OSS

Reviewed By: jamesr66a

Differential Revision: *********

Pulled By: eellison

fbshipit-source-id: f1c62684dc55682c0b3845022e0461ec77d07179",2020-07-20T18:59:08Z,Elias Ellison
57133e6ae6601f968a82b19b7eeec692d5b9aec6,"Add FP16 support (CudaHalfStorage, CudaHalfTensor)",2016-03-07T10:21:14Z,Adam Paszke
34ef473d92afd1ff7e89bd9749e2c3cd6361ffc6,"[Tensorpipe Agent] Timeouts for RPC requests (#38448)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38448

This PR implements timeout support for RPCs, and respects the new per-RPC timeout functionality.

A map containing RPC futures, keyed by an expiration time, is populated by the send function for each RPC.

A separate watchdog thread polls this map and sets all incomplete futures with errors.
Note: we cannot set errors to a future with the lock held (this will trigger callbacks immediately and, if one of the callback functions tries to acquire the lock that we held when setting the error, we have a lock order cycle). Thus we add all incomplete futures to a list, and then iterate through the list outside the lock to set errors on those futures if necessary.
ghstack-source-id: 104227075

Test Plan: Will patch the testing diff on top of this to run tests.

Differential Revision: D21468526

fbshipit-source-id: 4514484ece6fb6be673427d44c7f3164ab3d9d7c",2020-05-18T18:50:28Z,Omkar Salpekar
143d2881a844934c95c4ada63b38179d97e65af3,"[Profiler] Memory profiler part 10: Mark optimizer state (#88925)

This is also a fairly simple pass, since we're simply collecting values from the python tracer.

Differential Revision: [D40868664](https://our.internmc.facebook.com/intern/diff/D40868664/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/88925
Approved by: https://github.com/chaekit",2022-11-26T18:33:19Z,Taylor Robie
1905bbb01dc1e92f6248a11543e6d1bee2ff161c,"Include ATen/core/functional.h directly instead of torch/csrc/utils/functional.h. (#16377)

Summary:
One more shim removed.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/16377

Differential Revision: D13821816

Pulled By: ZolotukhinM

fbshipit-source-id: 007f014d404de51841437db7eef28367a2f6e46b",2019-01-30T21:30:30Z,Mikhail Zolotukhin
0696db820266c1402315f78458d57ea1cd48a2a7,"Revert ""Teach dynamo about torch.func.jvp (#119926)""

This reverts commit 17489784b635187316c6c856c5fe6b6a28d8a15a.

Reverted https://github.com/pytorch/pytorch/pull/119926 on behalf of https://github.com/peterbell10 due to broken mac jobs on main ([comment](https://github.com/pytorch/pytorch/pull/119926#issuecomment-2010327997))",2024-03-20T18:34:41Z,PyTorch MergeBot
b7be4b1e4803351d05d5ed5b219532f7dc5daaa7,"[AMD] Turn on fast path for index_put (#136136)

Summary:
This slow path is bad because it has a sync point which makes CPU really slow. I'm not very sure if AMD actually needs this with the newer rocm versino

{F1870213925}

Test Plan: CI

Differential Revision: D62731130

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136136
Approved by: https://github.com/danzimm, https://github.com/jeffdaily, https://github.com/eqy",2024-10-15T08:39:17Z,Xiaodong Wang
33e4b3e52d9dbb9b5e38913c372a05b69a0aed03,[functorch] Rename pointwise operator CompileCache to PointwiseOperatorCompileCache (pytorch/functorch#243),2021-11-04T23:19:02Z,Animesh Jain
cfc71f56e4f0e981f3ab6e7f49e537be48ac3384,"[quant][fx][graphmode] Support standalone module in _convert_do_not_use (#70151)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/70151

this supports converting an observed standalone module to quantized standalone module
in the new convert flow (convert observers to quant-dequant operators)

Test Plan:
```
python test/test_quant_trt.py TestConvertFxDoNotUse
```

Imported from OSS

Reviewed By: supriyar

Differential Revision: D33205163

fbshipit-source-id: 01ea44fb2a8ffe30bec1dd5678e7a72797bafafc",2021-12-30T20:29:32Z,Jerry Zhang
099a545376f805ce4da10bcb5cfc7bc71bbcba7c,"Hipify Caffe2 binaries (#10468)

Summary:
petrex
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10468

Reviewed By: yinghai

Differential Revision: ********

Pulled By: bddppq

fbshipit-source-id: 5da88aa4d79a5142f8e744cdcd8ae85951bc387c",2018-08-14T03:51:28Z,Junjie Bai
cf11fc0dcbb9c907cf6e851109b92f4157e445c9,"dynamo: Only log if we've disabled eval_frame once. (#134529)

This spams logs pretty badly otherwise

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134529
Approved by: https://github.com/chuanhaozhuge, https://github.com/oulgen",2024-08-30T00:35:23Z,Colin L. Rice
8028162103bf4393a4acc6e07105160c58895a04,Update the script to avoid the protobuf lib issue and add ZFNet (#6966),2018-04-25T23:38:43Z,Lu Fang
a11a49af585c5d0bc408e1a610438b770b813b8d,"Add NCCL work sequence number to work info (#120596)

Summary: Expose sequence number to work info. The number can help applications identify a NCCL work more precisely.

Test Plan:
1. pytest test/distributed/test_c10d_nccl.py::WorkHookTest::test_on_completion_hook_seq
2. pytest test/distributed/test_c10d_nccl.py::WorkHookTest

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/120596
Approved by: https://github.com/kwen2501",2024-02-28T07:54:37Z,Chao Zhou
44e2b8da280f5f095c1157741fd6b7c8bb1f896c,"Automated submodule update: FBGEMM (#72068)

Summary:
This is an automated pull request to update the first-party submodule for [pytorch/FBGEMM](https://github.com/pytorch/FBGEMM).

New submodule commit: https://github.com/pytorch/FBGEMM/commit/35d4dd4eb39f6fceb1bc3a3dafce1ed1f6449bd1

Pull Request resolved: https://github.com/pytorch/pytorch/pull/72068

Test Plan: Ensure that CI jobs succeed on GitHub before landing.

Reviewed By: malfet

Differential Revision: D33892960

fbshipit-source-id: 462b24ab3a81862bbfdc8e80fe07ea262e11829f
(cherry picked from commit c5d2b40fa61e185fab1237c07a0ddc875bcb9203)",2022-02-01T16:19:35Z,Facebook Community Bot
b96f49885f3c51d4ad0a2f02478aae77cf274f1c,"caffe2 python ideep conv_op test_int8_convolution skip for python 3

Summary: This test was failing in 3.7,  turns out it was ommitted by test director in 3.6 so I added a skip for both versions

Test Plan: unittests is skipped in 3.7 and 3.6 all other tests pass.

Reviewed By: tomdz

Differential Revision: D17820967

fbshipit-source-id: 571f0ec7fe1b0cb50ead4e0d18c00151a701f36a",2019-10-09T04:29:32Z,Jason Fried
34f1f2208be5cd31f515d795142f2caef6860c34,"Build c10 HIP test

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/15233

Reviewed By: ezyang

Differential Revision: D13471002

Pulled By: bddppq

fbshipit-source-id: b42c3bc2b9db672ce50a52eb700cc6ed13d3535f",2018-12-14T23:34:38Z,bddppq
9907a3eb652682d0c60cd9b5e0edfb3940a004d9,"Update Argmin/Argmax ONNX Export (#38329)

Summary:
Update Argmin/Argmax ONNX export in opset 12 to export with ""select_last_index"", and export correctly cases where the same value appears multiple time in the input tensor.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38329

Reviewed By: hl475

Differential Revision: D21613799

Pulled By: houseroad

fbshipit-source-id: 4597e23561f444c4e56d30c735dae7e9a8a41c5e",2020-05-19T23:49:01Z,Lara Haidar
6294a9a87722797c7bbed31bef485d6359869411,"C++ API parity: RReLU

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/27437

Test Plan: Imported from OSS

Differential Revision: D17835413

Pulled By: pbelevich

fbshipit-source-id: 5d943fdac4fd2633e7f7ca13db1a7fed5636ca50",2019-10-11T02:12:51Z,Pavel Belevich
df136df8d5f646d49bcd68cf6e0b9ce3c553a700,"Remove upload_test_stat_aggregates script (#139915)

Instead of moving these queries to ClickHouse, we're just going to remove it since it's not really used.  We do want something for test aggregates, but we can make a new script instead
Pull Request resolved: https://github.com/pytorch/pytorch/pull/139915
Approved by: https://github.com/huydhn",2024-11-07T20:14:12Z,Catherine Lee
8d8a99c2442f7485124bea7134624b70df787c33,Add ONNX Pad reflect and edge mode support (#3048),2017-10-10T21:02:08Z,Lu Fang
c8974d649d684a33a5c02a0b112a6e0743201d97,"[test] AOTAutograd: support mutations on buffers that happen during th bw (#112906)

I can hold off on reviews / landing until I talk to Driss and we confirm that we need this for FP8. This PR also needs testing and probably shouldn't land until Tugsuu's input mutation handling [PR](https://github.com/pytorch/pytorch/pull/111046) goes through.

What this PR tries to solve is when you have a model that tries to mutate some nn module state (a buffer), but during the **backward**. It appears that this might be necessary for FP8's delayed scaling.

Today, AOTAutograd will just not realize if you happened to mutate any graph inputs when running the backward pass, and functionalize them away but not realize that they were input mutations. This PR tries to:

(a) detect this situation (input mutations during the backward)

(b) put `copy_()`'s in the graph to properly handle the input mutation when we can. In cases where we can't keep the copy_() in the graph, we just error loudly (I imagine that these cases will be extremely rare, but we can fix them if they ever come up).

This is mostly a prototype for now, not ready for review.

I made this example locally to test out:
```
import torch

class MutatingAutogradFn(torch.autograd.Function):

    @staticmethod
    def forward(ctx, x, buf):
        ctx.save_for_backward(buf)
        return x

    @staticmethod
    def backward(ctx, x_grad):
        buf = ctx.saved_tensors[0]
        buf.add_(x_grad)
        return x_grad * 3, None

class Mod(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.buf = torch.ones(2)

    @torch._dynamo.allow_in_graph
    def backward_mutating_fn(self, x, buf):
        return MutatingAutogradFn.apply(x, buf)

    def forward(self, x):
        tmp = self.backward_mutating_fn(x, self.buf)
        return tmp + self.buf

m = Mod()

x = torch.ones(2, requires_grad=True)
out = m(x)
# After the fw, buf should not have been mutated
print(m.buf)
out.sum().backward()
# bw has run, so buf should now be mutated
print(m.buf)
print(x.grad)
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112906
Approved by: https://github.com/ezyang",2023-11-28T19:32:51Z,Brian Hirsh
0fa9f7af838ee53c4e25e8fb0619807657f0160c,[functorch] Fix CI,2021-11-29T21:56:34Z,Richard Zou
493ae7820100ab6afdfc19883dd9ad8c5198f1e2,"[inductor] nan-checker (#112091)

This PR is spilt out of https://github.com/pytorch/pytorch/pull/108193 . It adds the ability to add assertion after each triton kernel calls to make sure all tensor arguments are not nan/inf. It helps me find a few bugs when working on benchmark fusion (due to messing up some kernel/graph level states when generating kernel code).

Right now we have to disable cudagraphs to enable the nan/inf checks. Otherwise we will see errors like: https://gist.github.com/shunting314/053db66c4f121e5f4c5de159bf0032ed . My best guess is it's due to GPU->CPU copy during capturing for cudagraphs. cc @voznesenskym @penguinwu @EikanWang @jgong5 @Guobing-Chen @XiaobingSuper @zhuhaozhe @blzheng @wenzhe-nrv @jiayisunx @peterbell10 @ipiszy @yf225 @chenyang78 @kadeng @muchulee8 @aakhundov @ColinPeppler @eellison  if there is easy way to make it work with cudagraphs.  But even if the nan-checker is not compatible with cudagraphs, it's probably still fine since it's just for debugging purpose.

Test command:
```
TORCHINDUCTOR_BENCHMARK_KERNEL=1 TORCHINDUCTOR_NAN_ASSERTS=1 python benchmarks/dynamo/huggingface.py --backend inductor --amp --performance --only BertForMaskedLM --training --disable-cudagraphs
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112091
Approved by: https://github.com/eellison, https://github.com/jansel",2023-11-01T22:48:10Z,Shunting Zhang
1acced4ebaa8d394d2c0bae9a7c698c6dee017a0,"Implemented getCodeText(string attr) in llvm/cuda codegen and added python bindings for it - #52974 (#53664)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/53664

Test Plan: Imported from OSS

Reviewed By: bertmaher

Differential Revision: *********

Pulled By: huiguoo

fbshipit-source-id: 281fe6c25f4664636b29d51dba396056a222a9e7",2021-03-11T19:55:41Z,Hui Guo
e8ec84864fc1ca75fae226f5d3fa816557de7ffb,"[StaticRuntime] Add aten::narrow (#48991)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/48991

Add native impl of `aten::narrow` to skip dispatcher, because `aten::narrow` calls `aten::slice` in its implementation, here we reduce the dispatcher overhead by two-fold by calling the native impl of `aten::slice`.

Reviewed By: bwasti

Differential Revision: D25387119

fbshipit-source-id: c020da2556a35bc57a8a2e21fa45dd491ea516a0",2020-12-08T21:47:01Z,Hao Lu
a6e94d274fa9d4a262fb047a10b7dce4a20e4e06,"[Pytorch] Add python binding to use mobile cpu allocator. (#52323)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/52323

Using default cpu allocator for ops executed on qnnpack backend will result in
asan failures with heap overflow since qnnpack (and xnnpack) can access input
beyond their and/beginning.

Here we are enabling this feature specifically to enable dynamic sparse linear op test
using qnnpack engine. In dynamic linear op, the fp32 bias is not packed and
hence can result in out-of-bound access.

Test Plan: test_set_default_mobile_cpu_allocator.py

Reviewed By: z-a-f

Differential Revision: D26263481

fbshipit-source-id: a49227cac7e6781b0db4a156ca734d7671972d9f",2021-02-17T16:40:04Z,Kimish Patel
e886122e9811df44af40251a94b27e7509b20911,"[dtensor][debug] add module level tracing and readable display (#128369)

**Summary**
Currently, CommDebugMode only allows displaying collective tracing at a model level whereas a user may require a more detailed breakdown. In order to make this possible, I have changed the ModuleParamaterShardingTracker by adding a string variable to track the current sub-module as well as a dictionary keeping track of the depths of the submodules in the model tree. CommModeDebug class was changed by adding a new dictionary keeping track of the module collective counts as well as a function that displays the counts in a way that is easy for the user to read. Two examples using MLPModule and Transformer have been added to showcase the new changes. The expected output of the simpler MLPModule example is:

<img width=""255"" alt=""Screenshot 2024-06-10 at 4 58 50 PM"" src=""https://github.com/pytorch/pytorch/assets/50644008/cf2161ef-2663-49c1-a8d5-9f97e96a1791"">

**Test Plan**
torchrun --standalone --nnodes=1 --nproc-per-node=4 torch/distributed/_tensor/examples/display_sharding_example.py

Pull Request resolved: https://github.com/pytorch/pytorch/pull/128369
Approved by: https://github.com/XilunWu",2024-06-13T20:19:55Z,Anshul Sinha
08820cb030927200c92733436f454f5fcf19c6bd,"[xla hash update] update the pinned xla hash (#80113)

This PR is auto-generated nightly by [this action](https://github.com/pytorch/pytorch/blob/master/.github/workflows/_update-commit-hash.yml).
Update the pinned xla hash.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/80113
Approved by: https://github.com/pytorchbot",2022-06-23T06:35:55Z,PyTorch MergeBot
b994ce359ecd9eb47b876732a0fe2bb0a19fde32,"Revert ""[cuDNN V8 API] (reopen) Allow the number of kernels profiled under torch.backends.cudnn.benchmark = True to be limitedCudnnv8 benchmark limit (#77002)""

This reverts commit c274f2ad52504e0d20724b05171da33c340e60f8.

Reverted https://github.com/pytorch/pytorch/pull/77002 on behalf of https://github.com/malfet due to please, as it breaks internal CI, but also no CUDA heads should be included from `torch/csrc/Module.cpp`, but rather should be implemented/registered in `torch/csrc/cuda/Module.cpp`",2022-05-24T21:52:35Z,PyTorch MergeBot
