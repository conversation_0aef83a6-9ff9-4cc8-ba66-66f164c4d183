#!/usr/bin/env python3
"""
数据处理脚本：解析Twitter情感分析回复和Commit分析的思考内容
"""

import pandas as pd
import re
import json
from typing import Dict, Any, Optional, Tuple
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestResultsDataProcessor:
    """测试结果数据处理器"""
    
    def __init__(self):
        self.twitter_patterns = {
            'label_with_reasoning': [
                r'\[Label\]:\s*(Positive|Negative|Neutral)\s*\n?(?:Reason[s]?|Reasoning)?:?\s*(.+?)(?:\n|$)',
                r'\[Label\]:\s*(Positive|Negative|Neutral)\s*(.+?)(?:\n|$)',
                r'(Positive|Negative|Neutral):\s*(.+?)(?:\n|$)',
                r'\[Label\]:\s*(Positive|Negative|Neutral)(?:\s+(.+?))?(?:\n|$)',
            ],
            'simple_label': [
                r'\[Label\]:\s*(Positive|Negative|Neutral)',
                r'^(Positive|Negative|Neutral)(?:\s*$|\s*:)',
                r'(Positive|Negative|Neutral)'
            ]
        }
        
        self.commit_patterns = {
            'module_with_reasoning': [
                r'(?:Module|Component):\s*(\w+)\s*\n?(?:Reason[s]?|Reasoning|Analysis)?:?\s*(.+?)(?:\n|$)',
                r'(?:Module|Component):\s*(\w+)(?:\s+(.+?))?(?:\n|$)',
                r'(\w+):\s*(.+?)(?:\n|$)',
            ],
            'simple_module': [
                r'(?:Module|Component):\s*(\w+)',
                r'^(\w+)(?:\s*$)',
                r'(\w+)'
            ]
        }
    
    def process_csv_file(self, csv_file: str, output_file: str = None) -> pd.DataFrame:
        """
        处理测试结果CSV文件
        
        Args:
            csv_file: 输入CSV文件路径
            output_file: 输出CSV文件路径（可选）
            
        Returns:
            处理后的DataFrame
        """
        logger.info(f"开始处理CSV文件: {csv_file}")
        
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        logger.info(f"原始数据行数: {len(df)}")
        
        # 处理每一行数据
        processed_rows = []
        
        for idx, row in df.iterrows():
            processed_row = self._process_single_row(row)
            processed_rows.append(processed_row)
            
            if (idx + 1) % 100 == 0:
                logger.info(f"已处理 {idx + 1} 行数据")
        
        # 创建新的DataFrame
        processed_df = pd.DataFrame(processed_rows)
        
        # 如果指定了输出文件，保存结果
        if output_file:
            processed_df.to_csv(output_file, index=False, encoding='utf-8')
            logger.info(f"处理结果已保存到: {output_file}")
        
        return processed_df
    
    def _process_single_row(self, row: pd.Series) -> Dict[str, Any]:
        """处理单行数据"""
        processed = row.to_dict()
        
        # 确定任务类型
        task_category = row.get('task_category', '')
        
        if 'sentiment' in task_category.lower() or 'twitter' in str(row.get('task_id', '')).lower():
            # 处理Twitter情感分析任务
            processed = self._process_twitter_response(processed)
        elif 'commit' in task_category.lower() or 'pytorch' in str(row.get('task_id', '')).lower():
            # 处理Commit分析任务
            processed = self._process_commit_response(processed)
        
        return processed
    
    def _process_twitter_response(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理Twitter情感分析响应"""
        raw_response = str(row_data.get('raw_response', ''))
        thinking_content = str(row_data.get('thinking_content', ''))
        
        # 解析情感标签和理由
        label, reasoning = self._extract_twitter_sentiment(raw_response)
        
        # 更新字段
        row_data['extracted_label'] = label
        row_data['extracted_reasoning'] = reasoning
        row_data['response_type'] = 'twitter_sentiment'
        
        # 检查思考内容
        if thinking_content and thinking_content != 'nan':
            thinking_label, thinking_reasoning = self._extract_twitter_sentiment(thinking_content)
            row_data['thinking_label'] = thinking_label
            row_data['thinking_reasoning'] = thinking_reasoning
        
        # 质量评估
        row_data['parsing_quality'] = self._assess_twitter_parsing_quality(label, reasoning, raw_response)
        
        return row_data
    
    def _process_commit_response(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理Commit分析响应"""
        raw_response = str(row_data.get('raw_response', ''))
        thinking_content = str(row_data.get('thinking_content', ''))
        
        # 解析模块和分析
        module, analysis = self._extract_commit_module(raw_response)
        
        # 更新字段
        row_data['extracted_module'] = module
        row_data['extracted_analysis'] = analysis
        row_data['response_type'] = 'commit_analysis'
        
        # 检查思考内容
        if thinking_content and thinking_content != 'nan':
            thinking_module, thinking_analysis = self._extract_commit_module(thinking_content)
            row_data['thinking_module'] = thinking_module
            row_data['thinking_analysis'] = thinking_analysis
            row_data['has_thinking'] = True
        else:
            row_data['has_thinking'] = False
        
        # 质量评估
        row_data['parsing_quality'] = self._assess_commit_parsing_quality(module, analysis, raw_response)
        
        return row_data
    
    def _extract_twitter_sentiment(self, text: str) -> Tuple[Optional[str], Optional[str]]:
        """从文本中提取Twitter情感标签和理由"""
        if not text or text == 'nan':
            return None, None
        
        # 先尝试有推理的模式
        for pattern in self.twitter_patterns['label_with_reasoning']:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                label = match.group(1).strip()
                reasoning = match.group(2).strip() if len(match.groups()) > 1 and match.group(2) else None
                if reasoning and len(reasoning) > 3:  # 确保有有意义的推理
                    return label, reasoning
                else:
                    return label, None
        
        # 如果没找到有推理的，尝试简单标签
        for pattern in self.twitter_patterns['simple_label']:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip(), None
        
        return None, None
    
    def _extract_commit_module(self, text: str) -> Tuple[Optional[str], Optional[str]]:
        """从文本中提取Commit模块和分析"""
        if not text or text == 'nan':
            return None, None
        
        # 先尝试有分析的模式
        for pattern in self.commit_patterns['module_with_reasoning']:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                module = match.group(1).strip()
                analysis = match.group(2).strip() if len(match.groups()) > 1 and match.group(2) else None
                if analysis and len(analysis) > 10:  # 确保有有意义的分析
                    return module, analysis
                else:
                    return module, None
        
        # 如果没找到有分析的，尝试简单模块
        for pattern in self.commit_patterns['simple_module']:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                module = match.group(1).strip()
                # 检查是否是常见的模块名
                if len(module) > 15 or not module.isalnum():  # 过滤掉过长或非字母数字的结果
                    continue
                return module, None
        
        return None, None
    
    def _assess_twitter_parsing_quality(self, label: str, reasoning: str, raw_text: str) -> str:
        """评估Twitter解析质量"""
        if not label:
            return 'failed'
        
        if reasoning and len(reasoning) > 10:
            return 'excellent'
        elif label in ['Positive', 'Negative', 'Neutral']:
            return 'good'
        else:
            return 'poor'
    
    def _assess_commit_parsing_quality(self, module: str, analysis: str, raw_text: str) -> str:
        """评估Commit解析质量"""
        if not module:
            return 'failed'
        
        if analysis and len(analysis) > 20:
            return 'excellent'
        elif module and len(module) <= 15 and module.isalnum():
            return 'good'
        else:
            return 'poor'
    
    def generate_summary_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """生成处理摘要报告"""
        total_rows = len(df)
        
        # Twitter任务统计
        twitter_rows = df[df['response_type'] == 'twitter_sentiment']
        twitter_stats = {
            'total': len(twitter_rows),
            'with_label': len(twitter_rows[twitter_rows['extracted_label'].notna()]),
            'with_reasoning': len(twitter_rows[twitter_rows['extracted_reasoning'].notna()]),
            'quality_excellent': len(twitter_rows[twitter_rows['parsing_quality'] == 'excellent']),
            'quality_good': len(twitter_rows[twitter_rows['parsing_quality'] == 'good']),
            'quality_poor': len(twitter_rows[twitter_rows['parsing_quality'] == 'poor']),
            'quality_failed': len(twitter_rows[twitter_rows['parsing_quality'] == 'failed']),
        }
        
        # Commit任务统计
        commit_rows = df[df['response_type'] == 'commit_analysis']
        commit_stats = {
            'total': len(commit_rows),
            'with_module': len(commit_rows[commit_rows['extracted_module'].notna()]),
            'with_analysis': len(commit_rows[commit_rows['extracted_analysis'].notna()]),
            'with_thinking': len(commit_rows[commit_rows['has_thinking'] == True]),
            'quality_excellent': len(commit_rows[commit_rows['parsing_quality'] == 'excellent']),
            'quality_good': len(commit_rows[commit_rows['parsing_quality'] == 'good']),
            'quality_poor': len(commit_rows[commit_rows['parsing_quality'] == 'poor']),
            'quality_failed': len(commit_rows[commit_rows['parsing_quality'] == 'failed']),
        }
        
        return {
            'total_processed': total_rows,
            'twitter_sentiment': twitter_stats,
            'commit_analysis': commit_stats,
            'processing_timestamp': pd.Timestamp.now().isoformat()
        }
    
    def print_summary_report(self, df: pd.DataFrame):
        """打印处理摘要报告"""
        report = self.generate_summary_report(df)
        
        print("=" * 60)
        print("数据处理摘要报告")
        print("=" * 60)
        print(f"总处理行数: {report['total_processed']}")
        print()
        
        print("Twitter情感分析任务:")
        twitter = report['twitter_sentiment']
        print(f"  总数: {twitter['total']}")
        print(f"  成功提取标签: {twitter['with_label']} ({twitter['with_label']/max(twitter['total'], 1)*100:.1f}%)")
        print(f"  包含推理: {twitter['with_reasoning']} ({twitter['with_reasoning']/max(twitter['total'], 1)*100:.1f}%)")
        print(f"  质量分布:")
        print(f"    优秀: {twitter['quality_excellent']}")
        print(f"    良好: {twitter['quality_good']}")
        print(f"    一般: {twitter['quality_poor']}")
        print(f"    失败: {twitter['quality_failed']}")
        print()
        
        print("Commit分析任务:")
        commit = report['commit_analysis']
        print(f"  总数: {commit['total']}")
        print(f"  成功提取模块: {commit['with_module']} ({commit['with_module']/max(commit['total'], 1)*100:.1f}%)")
        print(f"  包含分析: {commit['with_analysis']} ({commit['with_analysis']/max(commit['total'], 1)*100:.1f}%)")
        print(f"  包含思考: {commit['with_thinking']} ({commit['with_thinking']/max(commit['total'], 1)*100:.1f}%)")
        print(f"  质量分布:")
        print(f"    优秀: {commit['quality_excellent']}")
        print(f"    良好: {commit['quality_good']}")
        print(f"    一般: {commit['quality_poor']}")
        print(f"    失败: {commit['quality_failed']}")
        print("=" * 60)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='处理测试结果CSV文件')
    parser.add_argument('input_file', help='输入CSV文件路径')
    parser.add_argument('-o', '--output', help='输出CSV文件路径')
    parser.add_argument('--report', action='store_true', help='生成详细报告')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = TestResultsDataProcessor()
    
    # 处理数据
    processed_df = processor.process_csv_file(args.input_file, args.output)
    
    # 打印摘要报告
    processor.print_summary_report(processed_df)
    
    if args.report:
        # 生成详细报告
        report_file = args.input_file.replace('.csv', '_processing_report.json')
        report = processor.generate_summary_report(processed_df)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
