#!/usr/bin/env python3
"""
专门测试logprobs功能的脚本
"""

import os
import logging
from openai import OpenAI

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_dashscope_logprobs():
    """测试DashScope API的logprobs功能"""
    print("="*60)
    print("测试DashScope API的logprobs功能")
    print("="*60)
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key or api_key == 'null':
        print("✗ 请设置DASHSCOPE_API_KEY环境变量")
        return
    
    # 创建客户端
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    # 测试不同的模型和参数
    test_cases = [
        {
            "name": "qwen3-14b with logprobs=True",
            "model": "qwen3-14b",
            "logprobs": True,
            "extra_body": {"enable_thinking": False}
        },
        {
            "name": "qwen3-32b with logprobs=True",
            "model": "qwen3-32b", 
            "logprobs": True,
            "extra_body": {"enable_thinking": False}
        },
        {
            "name": "qwen3-14b with logprobs=5",
            "model": "qwen3-14b",
            "logprobs": 5,
            "extra_body": {"enable_thinking": False}
        },
        {
            "name": "qwen3-14b without logprobs",
            "model": "qwen3-14b",
            "logprobs": None,
            "extra_body": {"enable_thinking": False}
        }
    ]
    
    test_prompt = "Say hello in one word."
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        
        try:
            # 准备参数
            params = {
                "model": test_case["model"],
                "messages": [{"role": "user", "content": test_prompt}],
                "extra_body": test_case["extra_body"]
            }
            
            # 添加logprobs参数
            if test_case["logprobs"] is not None:
                params["logprobs"] = test_case["logprobs"]
            
            print(f"API参数: {params}")
            
            # 调用API
            completion = client.chat.completions.create(**params)
            
            print(f"API调用成功，收到 {len(completion.choices)} 个选择")
            
            if completion.choices:
                choice = completion.choices[0]
                print(f"Choice类型: {type(choice)}")
                print(f"Choice属性: {dir(choice)}")
                
                # 检查message
                if hasattr(choice, 'message'):
                    print(f"Message内容: {choice.message.content}")
                else:
                    print("No message found")
                
                # 检查logprobs
                if hasattr(choice, 'logprobs'):
                    logprobs = choice.logprobs
                    print(f"Logprobs存在: {logprobs is not None}")
                    if logprobs:
                        print(f"Logprobs类型: {type(logprobs)}")
                        print(f"Logprobs属性: {dir(logprobs)}")
                        
                        # 尝试不同的访问方式
                        if hasattr(logprobs, 'content'):
                            print(f"Logprobs.content: {logprobs.content}")
                        if hasattr(logprobs, 'token_logprobs'):
                            print(f"Logprobs.token_logprobs: {logprobs.token_logprobs}")
                        if hasattr(logprobs, '__dict__'):
                            print(f"Logprobs.__dict__: {logprobs.__dict__}")
                    else:
                        print("Logprobs为None")
                else:
                    print("No logprobs attribute found")
                
                # 检查finish_reason
                if hasattr(choice, 'finish_reason'):
                    print(f"Finish reason: {choice.finish_reason}")
                
            else:
                print("No choices returned")
                
        except Exception as e:
            print(f"测试失败: {e}")
            logger.error(f"API调用错误: {e}")

def test_openai_compatible_logprobs():
    """测试OpenAI兼容模式的logprobs功能"""
    print("\n" + "="*60)
    print("测试OpenAI兼容模式的logprobs功能")
    print("="*60)
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key or api_key == 'null':
        print("✗ 请设置DASHSCOPE_API_KEY环境变量")
        return
    
    # 创建客户端
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    test_prompt = "Say hello in one word."
    
    try:
        # 尝试不同的logprobs参数
        print("尝试logprobs=True...")
        completion = client.chat.completions.create(
            model="qwen3-14b",
            messages=[{"role": "user", "content": test_prompt}],
            logprobs=True,
            extra_body={"enable_thinking": False}
        )
        
        print(f"API调用成功")
        print(f"Completion对象类型: {type(completion)}")
        print(f"Completion属性: {dir(completion)}")
        
        if hasattr(completion, 'choices') and completion.choices:
            choice = completion.choices[0]
            print(f"Choice对象类型: {type(choice)}")
            print(f"Choice属性: {dir(choice)}")
            
            # 详细检查logprobs
            if hasattr(choice, 'logprobs'):
                logprobs = choice.logprobs
                print(f"Logprobs对象: {logprobs}")
                print(f"Logprobs类型: {type(logprobs)}")
                
                if logprobs:
                    print(f"Logprobs属性: {dir(logprobs)}")
                    # 尝试访问所有可能的属性
                    for attr in dir(logprobs):
                        if not attr.startswith('_'):
                            try:
                                value = getattr(logprobs, attr)
                                print(f"  {attr}: {type(value)} - {value}")
                            except Exception as e:
                                print(f"  {attr}: 访问失败 - {e}")
                else:
                    print("Logprobs为None")
            else:
                print("Choice没有logprobs属性")
        
    except Exception as e:
        print(f"测试失败: {e}")
        logger.error(f"API调用错误: {e}")

if __name__ == "__main__":
    print("开始测试logprobs功能...")
    
    # 测试1: 基本logprobs功能
    test_dashscope_logprobs()
    
    # 测试2: OpenAI兼容模式
    test_openai_compatible_logprobs()
    
    print("\n测试完成！")


