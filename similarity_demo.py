#!/usr/bin/env python3
"""
相似度计算演示脚本
直观展示不同相似度计算方法的差异
"""

import sys
import os
import numpy as np
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIUQ
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ

def jaccard_similarity_manual(text1: str, text2: str) -> float:
    """手动计算Jaccard相似度（用于演示）"""
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    return intersection / union if union > 0 else 0.0

def demonstrate_jaccard():
    """演示Jaccard相似度计算"""
    print("=" * 60)
    print("Jaccard 相似度计算演示")
    print("=" * 60)
    
    examples = [
        ("The cat is sleeping", "The cat is resting"),
        ("The cat is sleeping", "A dog is running"),
        ("The cat is sleeping", "The cat is sleeping"),
        ("Hello world", "World hello"),
        ("Machine learning is fun", "Deep learning is interesting")
    ]
    
    for text1, text2 in examples:
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        similarity = len(intersection) / len(union)
        
        print(f"\n文本1: '{text1}'")
        print(f"文本2: '{text2}'")
        print(f"词汇集合1: {words1}")
        print(f"词汇集合2: {words2}")
        print(f"交集: {intersection} (大小: {len(intersection)})")
        print(f"并集: {union} (大小: {len(union)})")
        print(f"Jaccard相似度: {similarity:.3f}")
        print("-" * 40)

def demonstrate_nli():
    """演示NLI相似度计算"""
    print("\n" + "=" * 60)
    print("NLI 相似度计算演示")
    print("=" * 60)
    
    # 创建NLI计算器
    nli_entail = EigValLaplacianNLIUQ(affinity="entail", verbose=False)
    nli_contra = EigValLaplacianNLIUQ(affinity="contra", verbose=False)
    
    examples = [
        ("The cat is sleeping", "A cat is resting"),
        ("The cat is sleeping", "The dog is running"),
        ("It's raining outside", "The weather is wet"),
        ("I love pizza", "I hate pizza"),
        ("The book is red", "The book is blue")
    ]
    
    print("正在计算NLI分数...")
    
    for text1, text2 in examples:
        # 获取完整的NLI分数
        nli_result = nli_entail.nli_calc.compute_nli_scores_cached(text1, text2)
        
        entail_sim = float(nli_result.entailment)
        contra_sim = 1.0 - float(nli_result.contradiction)
        
        print(f"\n文本1: '{text1}'")
        print(f"文本2: '{text2}'")
        print(f"NLI分数:")
        print(f"  - Entailment: {nli_result.entailment:.3f}")
        print(f"  - Neutral: {nli_result.neutral:.3f}")
        print(f"  - Contradiction: {nli_result.contradiction:.3f}")
        print(f"相似度计算:")
        print(f"  - Entail模式: {entail_sim:.3f}")
        print(f"  - Contra模式: {contra_sim:.3f}")
        print("-" * 40)

def compare_similarity_matrices():
    """比较不同方法的相似度矩阵"""
    print("\n" + "=" * 60)
    print("相似度矩阵对比")
    print("=" * 60)
    
    responses = [
        "The cat is sleeping",
        "A cat is resting", 
        "The dog is running",
        "It's sunny today"
    ]
    
    print("测试响应:")
    for i, resp in enumerate(responses):
        print(f"{i+1}. '{resp}'")
    
    # 计算不同方法的相似度矩阵
    jaccard_method = EigValLaplacianJaccardUQ()
    nli_entail_method = EigValLaplacianNLIUQ(affinity="entail")
    nli_contra_method = EigValLaplacianNLIUQ(affinity="contra")
    
    print("\n正在计算相似度矩阵...")
    
    jaccard_matrix = jaccard_method._compute_similarity_matrix(responses)
    nli_entail_matrix = nli_entail_method._compute_similarity_matrix(responses)
    nli_contra_matrix = nli_contra_method._compute_similarity_matrix(responses)
    
    print("\n=== Jaccard 相似度矩阵 ===")
    print_matrix(jaccard_matrix, responses)
    
    print("\n=== NLI Entail 相似度矩阵 ===")
    print_matrix(nli_entail_matrix, responses)
    
    print("\n=== NLI Contra 相似度矩阵 ===")
    print_matrix(nli_contra_matrix, responses)
    
    # 计算平均相似度
    def get_upper_triangle_mean(matrix):
        n = matrix.shape[0]
        upper_triangle = matrix[np.triu_indices_from(matrix, k=1)]
        return np.mean(upper_triangle)
    
    jaccard_mean = get_upper_triangle_mean(jaccard_matrix)
    entail_mean = get_upper_triangle_mean(nli_entail_matrix)
    contra_mean = get_upper_triangle_mean(nli_contra_matrix)
    
    print(f"\n=== 平均相似度对比 ===")
    print(f"Jaccard:     {jaccard_mean:.3f}")
    print(f"NLI Entail:  {entail_mean:.3f}")
    print(f"NLI Contra:  {contra_mean:.3f}")

def print_matrix(matrix, labels):
    """打印格式化的矩阵"""
    n = len(labels)
    
    # 打印列标题
    print("     ", end="")
    for i in range(n):
        print(f"  {i+1:>5}", end="")
    print()
    
    # 打印矩阵行
    for i in range(n):
        print(f"{i+1:>3}: ", end="")
        for j in range(n):
            print(f"{matrix[i,j]:>6.3f}", end="")
        print(f"  # {labels[i][:20]}...")

def analyze_uncertainty_relationship():
    """分析相似度与不确定性的关系"""
    print("\n" + "=" * 60)
    print("相似度与不确定性关系分析")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "高相似度案例",
            "responses": [
                "The cat is sleeping",
                "A cat is resting",
                "The cat is napping",
                "A feline is sleeping"
            ]
        },
        {
            "name": "低相似度案例", 
            "responses": [
                "The cat is sleeping",
                "The dog is running",
                "It's raining today",
                "I like pizza"
            ]
        },
        {
            "name": "混合相似度案例",
            "responses": [
                "The cat is sleeping",
                "A cat is resting",
                "The dog is running", 
                "It's sunny today"
            ]
        }
    ]
    
    methods = [
        ("Jaccard", EigValLaplacianJaccardUQ()),
        ("NLI Entail", EigValLaplacianNLIUQ(affinity="entail")),
        ("NLI Contra", EigValLaplacianNLIUQ(affinity="contra"))
    ]
    
    for case in test_cases:
        print(f"\n=== {case['name']} ===")
        for i, resp in enumerate(case['responses']):
            print(f"{i+1}. '{resp}'")
        
        print("\n结果:")
        for method_name, method in methods:
            result = method.compute_uncertainty(case['responses'])
            print(f"{method_name:>12}: 不确定性={result['uncertainty_score']:.3f}, "
                  f"平均相似度={result['mean_similarity']:.3f}")

if __name__ == "__main__":
    print("🔍 相似度计算方法演示")
    print("这个脚本将展示不同相似度计算方法的工作原理和差异")
    
    try:
        demonstrate_jaccard()
        demonstrate_nli()
        compare_similarity_matrices()
        analyze_uncertainty_relationship()
        
        print("\n" + "=" * 60)
        print("✅ 演示完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
