#!/usr/bin/env python3
"""
导出测试结果到CSV文件
"""

import csv
import os
from datetime import datetime
from pymongo import MongoClient
import yaml

def load_config(config_file: str = "config.yaml"):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"Error loading config: {e}")
        return {}

def export_test_results():
    """导出测试结果"""
    config = load_config()
    
    # 连接MongoDB
    output_config = config.get('output', {})
    mongo_config = output_config.get('mongo', {})
    
    client = MongoClient(f"mongodb://{mongo_config.get('host', 'localhost')}:{mongo_config.get('port', 27017)}/")
    db = client[mongo_config.get('database', 'LLM-UQ')]
    
    # 使用测试collection
    test_collection_name = mongo_config.get('test_collection', 'test_response')
    collection = db[test_collection_name]
    
    print(f"连接到MongoDB collection: {test_collection_name}")
    
    # 查询所有测试数据
    cursor = collection.find({}).sort("execution_timestamp", 1)
    
    # 准备CSV文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"test_results_{timestamp}.csv"
    
    # 定义CSV字段
    fieldnames = [
        'run_id',
        'task_id', 
        'task_name',
        'dataset_source',
        'task_category',
        'input_text',
        'reference_answer',
        'model_identifier',
        'prompt_variant',
        'prompt_seed',
        'prompt_index',
        'task_attempt_prompt',
        'task_attempt_total',
        'raw_response',
        'thinking_content',
        'actual_response',
        'raw_answer',
        'parsed_answer',
        'parsed_reason',
        'finish_reason',
        'execution_timestamp',
        'generation_config_temperature',
        'generation_config_top_p',
        'generation_config_enable_thinking',
        'generation_config_enable_logprobs'
    ]
    
    # 写入CSV文件
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        count = 0
        for doc in cursor:
            # 准备行数据
            row = {}
            
            # 基本字段
            for field in fieldnames:
                if field.startswith('generation_config_'):
                    # 处理嵌套的generation_config字段
                    config_field = field.replace('generation_config_', '')
                    generation_config = doc.get('generation_config', {})
                    row[field] = generation_config.get(config_field, '')
                else:
                    row[field] = doc.get(field, '')
            
            # 处理特殊字段
            if 'execution_timestamp' in doc:
                row['execution_timestamp'] = doc['execution_timestamp'].isoformat() if doc['execution_timestamp'] else ''
            
            # 截断长文本字段以便查看
            if row['raw_response'] and len(str(row['raw_response'])) > 500:
                row['raw_response'] = str(row['raw_response'])[:500] + '...'
            
            if row['thinking_content'] and len(str(row['thinking_content'])) > 300:
                row['thinking_content'] = str(row['thinking_content'])[:300] + '...'
                
            if row['input_text'] and len(str(row['input_text'])) > 200:
                row['input_text'] = str(row['input_text'])[:200] + '...'
            
            writer.writerow(row)
            count += 1
    
    print(f"导出完成！")
    print(f"文件名: {csv_filename}")
    print(f"导出记录数: {count}")
    
    # 显示统计信息
    print("\n统计信息:")
    
    # 按任务统计
    pipeline = [
        {"$group": {
            "_id": "$task_name",
            "count": {"$sum": 1}
        }}
    ]
    
    task_stats = list(collection.aggregate(pipeline))
    print("按任务统计:")
    for stat in task_stats:
        print(f"  {stat['_id']}: {stat['count']} 条记录")
    
    # 按prompt统计
    pipeline = [
        {"$group": {
            "_id": {
                "task_name": "$task_name",
                "prompt_index": "$prompt_index"
            },
            "count": {"$sum": 1}
        }},
        {"$sort": {"_id.task_name": 1, "_id.prompt_index": 1}}
    ]
    
    prompt_stats = list(collection.aggregate(pipeline))
    print("\n按prompt统计:")
    for stat in prompt_stats:
        task_name = stat['_id'].get('task_name', 'Unknown')
        prompt_index = stat['_id'].get('prompt_index', 0)
        print(f"  {task_name} - prompt_{prompt_index:02d}: {stat['count']} 条记录")
    
    client.close()
    return csv_filename

if __name__ == "__main__":
    export_test_results()
