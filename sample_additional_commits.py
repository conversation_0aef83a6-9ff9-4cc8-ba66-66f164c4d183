"""Sample additional PyTorch commits excluding already sampled ones.

用法示例:
    python sample_additional_commits.py \
        --existing sampled_commits.csv \
        --commits-dir data/pytorch_commits \
        --n 200 \
        --output sampled_commits_extra_200.csv

也可以合并输出到一个新的500合并文件:
    python sample_additional_commits.py --merge-output sampled_commits_merged.csv

功能:
1. 读取已采样CSV (默认 sampled_commits.csv) 获取已用 sha 集合
2. 枚举原始 commit JSON (data/pytorch_commits/commit_*.json)
3. 过滤掉已用 sha, 从剩余中随机采样 N 条 (默认 200)
4. 输出一个新增样本文件; 可选合并成一个新的合并文件
5. 支持 --seed 设定随机种子, --dry-run 只查看数量不写文件

字段: sha, message, date, author (与现有 sampled_commits.csv 保持一致)
"""
from __future__ import annotations
import argparse
import csv
import json
import random
from pathlib import Path
from typing import List, Dict, Set

def load_existing_shas(existing_csv: Path) -> Set[str]:
    shas = set()
    if not existing_csv.exists():
        print(f"[WARN] Existing sample file not found: {existing_csv}, treat as empty.")
        return shas
    with existing_csv.open('r', encoding='utf-8') as f:
        header = f.readline().strip().split(',')  # 预读header
        f.seek(0)
        reader = csv.DictReader(f)
        if 'sha' not in reader.fieldnames:
            raise ValueError(f"CSV {existing_csv} missing 'sha' column; headers={reader.fieldnames}")
        for row in reader:
            sha = row.get('sha')
            if sha:
                shas.add(sha)
    print(f"[INFO] Loaded {len(shas)} existing shas from {existing_csv}")
    return shas

def enumerate_commit_files(commits_dir: Path):
    if not commits_dir.exists():
        raise FileNotFoundError(f"Commits directory not found: {commits_dir}")
    files = sorted(commits_dir.glob('commit_*.json'))
    print(f"[INFO] Found {len(files)} commit json files in {commits_dir}")
    return files

def extract_commit_info(path: Path) -> Dict[str, str]:
    with path.open('r', encoding='utf-8') as f:
        data = json.load(f)
    commit = data.get('commit', {}) or {}
    author = commit.get('author', {}) or {}
    return {
        'sha': data.get('sha', ''),
        'message': commit.get('message', ''),
        'date': author.get('date', ''),
        'author': author.get('name', ''),
    }

def sample_additional(commits_dir: Path, existing_shas: Set[str], n: int, seed: int | None):
    files = enumerate_commit_files(commits_dir)
    remaining_files = []
    for fp in files:
        try:
            with fp.open('r', encoding='utf-8') as f:
                data = json.load(f)
            sha = data.get('sha', '')
            if sha and sha not in existing_shas:
                remaining_files.append(fp)
        except Exception as e:
            print(f"[WARN] Skip {fp.name}: {e}")
    print(f"[INFO] Remaining (not previously sampled) commit files: {len(remaining_files)}")
    if len(remaining_files) == 0:
        print("[INFO] No remaining commits to sample.")
        return []
    if seed is not None:
        random.seed(seed)
        print(f"[INFO] Random seed set to {seed}")
    k = min(n, len(remaining_files))
    if k < n:
        print(f"[WARN] Requested {n} but only {k} remaining commits available.")
    selected = random.sample(remaining_files, k)
    print(f"[INFO] Sampling {k} additional commits")
    sampled_rows = [extract_commit_info(p) for p in selected]
    return sampled_rows

def write_csv(rows, path: Path):
    if not rows:
        print(f"[INFO] No rows to write for {path}, skipping.")
        return
    with path.open('w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
        writer.writeheader()
        writer.writerows(rows)
    print(f"[INFO] Wrote {len(rows)} rows to {path}")

def merge_csv(existing_csv: Path, additional_rows, output_csv: Path):
    merged_rows = []
    if existing_csv.exists():
        with existing_csv.open('r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                merged_rows.append(row)
    existing_sha_set = {r['sha'] for r in merged_rows}
    new_unique = [r for r in additional_rows if r['sha'] not in existing_sha_set]
    merged_rows.extend(new_unique)
    write_csv(merged_rows, output_csv)
    print(f"[INFO] Merged file {output_csv} total rows={len(merged_rows)} (new added {len(new_unique)})")

def parse_args():
    ap = argparse.ArgumentParser(description="Sample additional commits excluding existing sampled ones.")
    ap.add_argument('--existing', default='sampled_commits.csv', help='已有采样CSV路径')
    ap.add_argument('--commits-dir', default='data/pytorch_commits', help='原始commit json目录')
    ap.add_argument('-n', '--n', type=int, default=200, help='需要新增采样数量')
    ap.add_argument('--output', default='sampled_commits_extra_200.csv', help='新增采样输出文件')
    ap.add_argument('--merge-output', help='若提供, 生成合并后的总CSV (不覆盖 --output)')
    ap.add_argument('--seed', type=int, help='随机种子 (可复现)')
    ap.add_argument('--dry-run', action='store_true', help='只打印信息不写任何文件')
    return ap.parse_args()

def main():
    args = parse_args()
    existing_csv = Path(args.existing)
    commits_dir = Path(args.commits_dir)
    output_csv = Path(args.output)
    existing_shas = load_existing_shas(existing_csv)
    additional_rows = sample_additional(commits_dir, existing_shas, args.n, args.seed)
    if args.dry_run:
        print('[DRY-RUN] 不写文件.')
        return
    write_csv(additional_rows, output_csv)
    if args.merge_output:
        merge_csv(existing_csv, additional_rows, Path(args.merge_output))

if __name__ == '__main__':
    main()
