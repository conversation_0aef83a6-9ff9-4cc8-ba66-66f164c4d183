#!/usr/bin/env python3
"""
测试脚本：验证修改后的 EigValLaplacianNLIUQ 类
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIUQ
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ

def test_nli_entail():
    """测试 NLI entail 模式"""
    print("=== 测试 NLI Entail 模式 ===")
    
    # 创建实例
    uq_method = EigValLaplacianNLIUQ(affinity="entail", verbose=True)
    
    # 测试数据
    responses = [
        "The cat is sleeping on the mat.",
        "A cat is resting on a rug.",
        "The dog is running in the park.",
        "A canine is jogging through the garden.",
        "The weather is sunny today."
    ]
    
    # 计算不确定性
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Method: {result['method']}")
    print(f"Uncertainty Score: {result['uncertainty_score']:.4f}")
    print(f"Mean Similarity: {result['mean_similarity']:.4f}")
    print(f"Affinity: {result['metadata']['affinity']}")
    print()

def test_nli_contra():
    """测试 NLI contra 模式"""
    print("=== 测试 NLI Contra 模式 ===")
    
    # 创建实例
    uq_method = EigValLaplacianNLIUQ(affinity="contra", verbose=True)
    
    # 测试数据
    responses = [
        "The cat is sleeping on the mat.",
        "A cat is resting on a rug.",
        "The dog is running in the park.",
        "A canine is jogging through the garden.",
        "The weather is sunny today."
    ]
    
    # 计算不确定性
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Method: {result['method']}")
    print(f"Uncertainty Score: {result['uncertainty_score']:.4f}")
    print(f"Mean Similarity: {result['mean_similarity']:.4f}")
    print(f"Affinity: {result['metadata']['affinity']}")
    print()

def test_jaccard_comparison():
    """测试 Jaccard 方法作为对比"""
    print("=== 测试 Jaccard 方法（对比） ===")
    
    # 创建实例
    uq_method = EigValLaplacianJaccardUQ(verbose=True)
    
    # 测试数据
    responses = [
        "The cat is sleeping on the mat.",
        "A cat is resting on a rug.",
        "The dog is running in the park.",
        "A canine is jogging through the garden.",
        "The weather is sunny today."
    ]
    
    # 计算不确定性
    result = uq_method.compute_uncertainty(responses)
    
    print(f"Method: {result['method']}")
    print(f"Uncertainty Score: {result['uncertainty_score']:.4f}")
    print(f"Mean Similarity: {result['mean_similarity']:.4f}")
    print()

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    uq_method = EigValLaplacianNLIUQ(affinity="entail")
    
    # 测试单个响应
    single_response = ["Only one response"]
    result = uq_method.compute_uncertainty(single_response)
    print(f"单个响应 - Uncertainty: {result['uncertainty_score']}, Error: {result.get('error', 'None')}")
    
    # 测试空响应
    empty_responses = []
    result = uq_method.compute_uncertainty(empty_responses)
    print(f"空响应 - Uncertainty: {result['uncertainty_score']}, Error: {result.get('error', 'None')}")
    
    # 测试相同响应
    identical_responses = ["Same response"] * 3
    result = uq_method.compute_uncertainty(identical_responses)
    print(f"相同响应 - Uncertainty: {result['uncertainty_score']:.4f}")
    print()

if __name__ == "__main__":
    print("开始测试修改后的 EigValLaplacianNLIUQ 类...")
    print()
    
    try:
        test_nli_entail()
        test_nli_contra()
        test_jaccard_comparison()
        test_edge_cases()
        
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
