# 不确定性方法整理 from https://github.com/zlin7/UQ-NLG 

## 1. numsets 方法生成 <EMAIL>

### 代码实现
```python
def get_numsets(self, num_gens):
    # 获取每个样本的语义集合数量
    return [len(set(_)) for _ in self._get_semantic_ids(num_gens)]
```

### 伪代码逻辑
```
For each sample in the batch:
    1. 计算每个生成的语义集合 id（如聚类标签）
    2. 统计不同语义集合的数量
Return: 每个样本的语义集合数量
```

### 文档说明
**numsets**：  
该方法用于衡量每个输入样本下，生成的答案被分为多少个不同的语义集合。集合数量越多，表示生成的多样性和不确定性越高。

---

## 2. 如何使用 jaccard similarity

### 代码实现
```python
def _get_jaccard_matrix(self, num_gens:int):
    def jaccard_one(all_answers):
        all_answers = [set(ans.lower().split()) for ans in all_answers]
        ret = np.eye(len(all_answers))
        for i, ans_i in enumerate(all_answers):
            for j, ans_j in enumerate(all_answers[i+1:], i+1):
                ret[i,j] = ret[j,i] = len(ans_i.intersection(ans_j)) / max(len(ans_i.union(ans_j)),1)
        return ret
    # 计算每个样本的 jaccard 相似度矩阵
    self.mem['_get_jaccard_matrix'] = [jaccard_one(_['generations'][text_key]) for _ in self.generations]
    return [_[:num_gens, :num_gens] for _ in self.mem['_get_jaccard_matrix']]
```

### 伪代码逻辑
```
For each sample:
    For each pair of generated answers:
        1. 将答案分词为集合
        2. 计算 Jaccard 相似度 = 交集/并集
    3. 构建 Jaccard 相似度矩阵
Return: 每个样本的 Jaccard 相似度矩阵
```

### 文档说明
**Jaccard Similarity**：  
Jaccard 相似度用于衡量两个生成答案之间的词集合重叠程度。其值越高，表示两个答案越相似。可用于后续的聚类、谱聚类等不确定性分析。

---

## 3. 如何使用 agreement_w

### 代码实现
```python
def get_affinity_mat(logits, mode='agreement_w', temp=None, symmetric=True):
    if mode == 'agreement_w':
        W = torch.softmax(logits/temp, dim=-1)[:, :, 2]
        if symmetric:
            W = (W + W.permute(1,0))/2
    # ... 其他模式
    return W.cpu().numpy()
```

### 伪代码逻辑
```
For each pair of generations:
    1. 对 logits 进行 softmax（带温度）
    2. 取出“agreement”分数（softmax 后的第3个分量）
    3. 若 symmetric，则对称化
Return: affinity 矩阵
```

### 文档说明
**agreement_w**：  
agreement_w 模式下，使用 softmax 后的“agreement”分数作为生成之间的亲和力。常用于谱聚类、eccentricity、degree 等不确定性度量。

---

## 4.1 如何使用 disagreement_w

### 代码实现
```python
def get_affinity_mat(logits, mode='disagreement_w', temp=None, symmetric=True):
    if mode == 'disagreement_w':
        W = torch.softmax(logits/temp, dim=-1)[:, :, 0]  # 第0个分量为disagreement分数
        if symmetric:
            W = (W + W.permute(1,0))/2
        W = 1 - W  # 取1减去分数，表示不一致度
    # ... 其他模式
    return W.cpu().numpy()
```

### 伪代码逻辑
```
For each pair of generations:
    1. 对 logits 进行 softmax（带温度）
    2. 取出“disagreement”分数（softmax 后的第1个分量）
    3. 若 symmetric，则对称化
    4. 取 1 - 分数，表示不一致度
Return: affinity 矩阵
```

### 文档说明
**disagreement_w**：  
disagreement_w 模式下，使用 softmax 后的“disagreement”分数（通常为 logits 的第0个分量），并取 1-分数，作为生成之间的不一致度亲和力。常用于谱聚类、eccentricity、degree 等不确定性度量。

---

## 4. spectral_eigv_clip, degree, eccentricity 的用法

### spectral_eigv_clip
#### 代码实现
```python
def get_spectral_eigv(self, num_gens:int, affinity_mode:str, temperature:float, adjust:bool) -> List:
    clusterer = pc.SpetralClusteringFromLogits(affinity_mode=affinity_mode, eigv_threshold=None,
                                               cluster=False, temperature=temperature)
    sim_mats = getattr(self, '_get_jaccard_matrix' if affinity_mode == 'jaccard' else '_get_recovered_logits')(num_gens)
    return [clusterer.get_eigvs(_).clip(0 if adjust else -1).sum() for _ in tqdm.tqdm(sim_mats)]
```
#### 伪代码逻辑
```
For each sample:
    1. 计算 affinity 矩阵（如 agreement_w）
    2. 计算拉普拉斯矩阵
    3. 计算特征值
    4. 对特征值做 clip（如小于0设为0）
    5. 求和作为不确定性分数
Return: 每个样本的谱特征值和
```
#### 文档说明
**spectral_eigv_clip**：  
通过谱聚类的拉普拉斯矩阵特征值，衡量生成集合的结构复杂度。特征值和越大，表示生成分布越分散，不确定性越高。

---

### degree
#### 代码实现
```python
def get_degreeuq(self, num_gens:int, affinity_mode:str, temperature:float):
    sim_mats = getattr(self, '_get_jaccard_matrix' if affinity_mode == 'jaccard' else '_get_recovered_logits')(num_gens)
    Ws = [pc.get_affinity_mat(_, affinity_mode, temperature, symmetric=False) for _ in sim_mats]
    ret = np.asarray([np.sum(1-_, axis=1) for _ in Ws])
    return ret.mean(1), ret
```
#### 伪代码逻辑
```
For each sample:
    1. 计算 affinity 矩阵
    2. 对每一行，计算 1-affinity 的和（即每个生成与其他生成的不一致度）
Return: 每个样本的 degree 分数
```
#### 文档说明
**degree**：  
基于 affinity 矩阵，统计每个生成与其他生成的不一致度。degree 越大，表示生成之间差异越大，不确定性越高。

---

### eccentricity
#### 代码实现
```python
def get_eccentricity(self, num_gens:int, eigv_threshold:float, affinity_mode:str, temperature:float) -> List:
    projected = self._get_spectral_projected(num_gens, eigv_threshold, affinity_mode, temperature)
    ds = np.asarray([np.linalg.norm(x -x.mean(0)[None, :],2,axis=1) for x in projected])
    return np.linalg.norm(ds, 2,1), ds
```
#### 伪代码逻辑
```
For each sample:
    1. 计算 affinity 矩阵
    2. 谱聚类投影（取特征向量）
    3. 计算每个生成在投影空间的离心率（与均值的距离）
    4. 求范数作为不确定性分数
Return: 每个样本的 eccentricity 分数
```
#### 文档说明
**eccentricity**：  
在谱聚类投影空间中，计算每个生成与均值的距离，反映生成分布的离心程度。eccentricity 越大，表示生成分布越分散，不确定性越高。

---

## 附录：logits 的含义与获取

### 1. logits 是什么？

在不确定性分析流程中，`logits` 通常指的是**生成答案之间的语义相似度分数张量**，其 shape 通常为 `[num_generations, num_generations, 3]`。
- 最后一维的 3 表示三种语义关系分数：`[disagreement, neutral, agreement]`，分别对应“矛盾”、“中立”、“一致”。
- 这些分数通常来源于一个 NLI（自然语言推理）模型，对每对生成的答案进行推理判断。

### 2. logits 的来源

- **白盒（有模型权重）**：
  - 通过 NLI 模型（如 `models.nli.ClassifyWrapper`）对每个样本的所有生成答案两两组合，计算 NLI logits。
  - 代码示例：
    ```python
    from models.nli import ClassifyWrapper
    nli_model = ClassifyWrapper(device='cuda:0')
    sim_mat = nli_model.create_sim_mat_batched(question, generations)
    # sim_mat shape: [num_gens, num_gens, 3]
    ```
- **黑盒（无权重，已保存）**：
  - 直接从磁盘读取预先计算好的 logits（如 `.pkl` 或 `.npy` 文件），如 `dload.read_semantic_similarities_new`。

### 3. 如何获取 logits

- **通过 UQ_computer 的接口**：
    ```python
    num_gens = 20
    logits_list = uq._get_recovered_logits(num_gens)
    # logits_list 是一个 list，每个元素是 shape [num_gens, num_gens, 3] 的张量
    ```
- **手动调用 NLI 模型**：
    ```python
    from models.nli import ClassifyWrapper
    nli_model = ClassifyWrapper(device='cuda:0')
    sim_mat = nli_model.create_sim_mat_batched(question, generations)
    # sim_mat shape: [num_gens, num_gens, 3]
    ```
- **从磁盘读取**：
    ```python
    import dataeval.load as dload
    logits_list = dload.read_semantic_similarities_new(path, clean=True, debug=False)
    ```

### 4. logits 的典型结构
- logits[i, j, 0]：第 i 个生成和第 j 个生成之间的“disagreement”分数
- logits[i, j, 1]：...“neutral”分数
- logits[i, j, 2]：...“agreement”分数

这些分数通常是 NLI 模型输出的 softmax 之前的原始分数（logits），后续会用 softmax 归一化。

### 5. 总结
- **logits** 是生成答案两两之间的 NLI 推理分数张量，shape 为 `[num_gens, num_gens, 3]`。
- 获取方式：用 NLI 模型计算，或从磁盘读取，或通过 `UQ_computer` 的接口。
- 你可以直接用 `uq._get_recovered_logits(num_gens)` 获取用于不确定性分析的 logits。

---

## 附录2：如何用NLI模型生成 logits（项目代码示例）

在本项目中，可以直接使用 `models/nli.py` 中的 `ClassifyWrapper` 类来批量生成 logits。推荐使用 `create_sim_mat_batched` 方法。

### 主要方法说明

#### 1. 初始化 NLI 模型
```python
from models.nli import ClassifyWrapper
nli_model = ClassifyWrapper(model_name='microsoft/deberta-large-mnli', device='cuda:0')
```

#### 2. 批量生成 logits
假设你有一个问题 `question` 和一组生成答案 `generations`（list of str）：
```python
result = nli_model.create_sim_mat_batched(question, generations)
# result 是一个 dict，包括：
# result['mapping']  # 每个答案在 unique_ans 中的索引
# result['sim_mat']  # shape: [num_unique, num_unique, 3]，三类分数
```

#### 3. 结果结构
- `sim_mat[i, j, 0]`：第 i 个和第 j 个生成之间的“disagreement”分数
- `sim_mat[i, j, 1]`：...“neutral”分数
- `sim_mat[i, j, 2]`：...“agreement”分数

#### 4. 代码实现（摘自 models/nli.py）
```python
class ClassifyWrapper():
    def __init__(self, model_name='microsoft/deberta-large-mnli', device='cuda:3'):
        self.model_name = model_name
        self.model, self.tokenizer = models.load_model_and_tokenizer(model_name, device)

    @torch.no_grad()
    def _batch_pred(self, sen_1: list, sen_2: list, max_batch_size=128):
        inputs = [_[0] + ' [SEP] ' + _[1] for _ in zip(sen_1, sen_2)]
        inputs = self.tokenizer(inputs, padding=True, truncation=True)
        input_ids = torch.tensor(inputs['input_ids']).to(self.model.device)
        attention_mask = torch.tensor(inputs['attention_mask']).to(self.model.device)
        logits = []
        for st in range(0, len(input_ids), max_batch_size):
            ed = min(st + max_batch_size, len(input_ids))
            logits.append(self.model(input_ids=input_ids[st:ed],
                                attention_mask=attention_mask[st:ed])['logits'])
        return torch.cat(logits, dim=0)

    @torch.no_grad()
    def create_sim_mat_batched(self, question, answers):
        unique_ans = sorted(list(set(answers)))
        semantic_set_ids = {ans: i for i, ans in enumerate(unique_ans)}
        _rev_mapping = semantic_set_ids.copy()
        sim_mat_batch = torch.zeros((len(unique_ans), len(unique_ans),3))
        anss_1, anss_2, indices = [], [], []
        for i, ans_i in enumerate(unique_ans):
            for j, ans_j in enumerate(unique_ans):
                if i == j: continue
                anss_1.append(f"{question} {ans_i}")
                anss_2.append(f"{question} {ans_j}")
                indices.append((i,j))
        if len(indices) > 0:
            sim_mat_batch_flat = self._batch_pred(anss_1, anss_2)
            for _, (i,j) in enumerate(indices):
                sim_mat_batch[i,j] = sim_mat_batch_flat[_]
        return dict(
            mapping = [_rev_mapping[_] for _ in answers],
            sim_mat = sim_mat_batch
        )
```

### 5. 总结
- 推荐直接用 `ClassifyWrapper.create_sim_mat_batched(question, generations)` 获取 logits。
- 结果可直接用于不确定性分析的后续流程。

## 总结
- **numsets**：统计生成的语义集合数量，衡量多样性。
- **jaccard similarity**：基于词集合的重叠度，构建 affinity 矩阵。
- **agreement_w**：softmax 后的“agreement”分数，作为 affinity 矩阵。
- **spectral_eigv_clip/degree/eccentricity**：基于 affinity 矩阵的谱聚类方法，分别用特征值和、degree、离心率衡量不确定性。 