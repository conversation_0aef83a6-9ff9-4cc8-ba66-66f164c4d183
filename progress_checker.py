#!/usr/bin/env python3
"""
进度检查工具
用于查看LLM响应生成的进度状态
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from llm_response_generator import LLMResponseGenerator

def check_overall_progress():
    """检查整体进度"""
    print("="*60)
    print("LLM响应生成进度检查")
    print("="*60)
    
    # 连接MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["LLM-UQ"]
    collection = db["response"]
    
    # 获取所有运行记录
    runs = list(collection.aggregate([
        {"$group": {
            "_id": "$run_id",
            "total_docs": {"$sum": 1},
            "first_timestamp": {"$min": "$execution_timestamp"},
            "last_timestamp": {"$max": "$execution_timestamp"},
            "datasets": {"$addToSet": "$dataset_source"}
        }},
        {"$sort": {"last_timestamp": -1}}
    ]))
    
    if not runs:
        print("未找到任何运行记录")
        return
    
    print(f"找到 {len(runs)} 个运行记录:")
    print()
    
    for i, run in enumerate(runs, 1):
        run_id = run["_id"]
        total_docs = run["total_docs"]
        first_time = run["first_timestamp"]
        last_time = run["last_timestamp"]
        datasets = run["datasets"]
        
        print(f"运行 {i}: {run_id}")
        print(f"  文档总数: {total_docs}")
        print(f"  数据集: {', '.join(datasets)}")
        print(f"  开始时间: {first_time}")
        print(f"  最后时间: {last_time}")
        
        # 计算运行时长
        if first_time and last_time:
            duration = last_time - first_time
            print(f"  运行时长: {duration}")
        
        print()

def check_run_details(run_id: str):
    """检查特定运行的详细信息"""
    print(f"="*60)
    print(f"运行详情: {run_id}")
    print(f"="*60)
    
    # 连接MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["LLM-UQ"]
    collection = db["response"]
    
    # 获取该运行的所有文档
    docs = list(collection.find({"run_id": run_id}))
    
    if not docs:
        print(f"未找到运行ID为 {run_id} 的记录")
        return
    
    # 按数据集和prompt变体分组统计
    stats = {}
    for doc in docs:
        dataset = doc.get("dataset_source", "unknown")
        variant = doc.get("prompt_variant", "unknown")
        task_id = doc.get("task_id", "unknown")
        
        key = f"{dataset}_{variant}"
        if key not in stats:
            stats[key] = {"total": 0, "tasks": set()}
        
        stats[key]["total"] += 1
        stats[key]["tasks"].add(task_id)
    
    print(f"总文档数: {len(docs)}")
    print(f"统计信息:")
    print()
    
    for key, info in stats.items():
        dataset, variant = key.split("_", 1)
        print(f"  {dataset} - {variant}:")
        print(f"    总尝试次数: {info['total']}")
        print(f"    任务数: {len(info['tasks'])}")
        print(f"    平均每任务尝试次数: {info['total'] / len(info['tasks']):.1f}")
        print()

def check_dataset_progress(dataset_source: str):
    """检查特定数据集的进度"""
    print(f"="*60)
    print(f"数据集进度: {dataset_source}")
    print(f"="*60)
    
    # 连接MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["LLM-UQ"]
    collection = db["response"]
    
    # 获取该数据集的所有文档
    pipeline = [
        {"$match": {"dataset_source": dataset_source}},
        {"$group": {
            "_id": {
                "task_id": "$task_id",
                "prompt_variant": "$prompt_variant"
            },
            "attempts": {"$sum": 1},
            "last_attempt": {"$max": "$task_attempt_prompt"}
        }},
        {"$sort": {"_id.task_id": 1, "_id.prompt_variant": 1}}
    ]
    
    results = list(collection.aggregate(pipeline))
    
    if not results:
        print(f"未找到数据集 {dataset_source} 的记录")
        return
    
    print(f"总任务数: {len(results)}")
    print()
    
    # 按prompt变体分组
    variants = {}
    for result in results:
        variant = result["_id"]["prompt_variant"]
        if variant not in variants:
            variants[variant] = []
        variants[variant].append(result)
    
    for variant, tasks in variants.items():
        print(f"  {variant}:")
        print(f"    任务数: {len(tasks)}")
        
        # 统计完成情况
        completed = sum(1 for task in tasks if task["attempts"] >= 8)
        in_progress = len(tasks) - completed
        
        print(f"    已完成: {completed}")
        print(f"    进行中: {in_progress}")
        print(f"    完成率: {completed/len(tasks)*100:.1f}%")
        print()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python progress_checker.py overall                    # 检查整体进度")
        print("  python progress_checker.py run <run_id>              # 检查特定运行")
        print("  python progress_checker.py dataset <dataset_source>  # 检查特定数据集")
        return
    
    command = sys.argv[1]
    
    if command == "overall":
        check_overall_progress()
    elif command == "run" and len(sys.argv) >= 3:
        run_id = sys.argv[2]
        check_run_details(run_id)
    elif command == "dataset" and len(sys.argv) >= 3:
        dataset_source = sys.argv[2]
        check_dataset_progress(dataset_source)
    else:
        print("无效的命令")
        print("用法:")
        print("  python progress_checker.py overall")
        print("  python progress_checker.py run <run_id>")
        print("  python progress_checker.py dataset <dataset_source>")

if __name__ == "__main__":
    main()


