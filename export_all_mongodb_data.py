import pandas as pd
from pymongo import MongoClient
import argparse
from tqdm import tqdm

def export_all_commit_data(mongo_host='localhost', mongo_port=27017, 
                          mongo_db='commit_analysis', mongo_collection='responses',
                          output_file='data/all_commit_responses.csv'):
    """
    从MongoDB导出所有Commit响应数据到CSV文件
    """
    print(f"=== Exporting ALL Commit data from MongoDB ===")
    
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 先获取总数
    total_count = collection.count_documents({})
    print(f"Total commit records in MongoDB: {total_count}")
    
    if total_count == 0:
        print("No commit data found in MongoDB")
        client.close()
        return None
    
    # 分批查询和处理数据以避免内存问题
    batch_size = 10000
    all_data = []
    
    print("Fetching commit data in batches...")
    for skip in tqdm(range(0, total_count, batch_size), desc="Fetching batches"):
        batch_docs = list(collection.find({}).skip(skip).limit(batch_size))
        all_data.extend(batch_docs)
    
    print(f"Fetched {len(all_data)} commit records")
    
    # 转换为DataFrame
    df = pd.DataFrame(all_data)
    
    # 选择需要的列
    required_columns = ['commit_sha', 'repo_name', 'author', 'date', 'message', 
                       'prompt_type', 'prompt_text', 'response_text', 'model_name', 
                       'query_index', 'timestamp']
    
    # 检查哪些列存在
    available_columns = [col for col in required_columns if col in df.columns]
    df = df[available_columns]
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} commit records to {output_file}")
    print(f"Unique commits: {df['commit_sha'].nunique() if 'commit_sha' in df.columns else 'N/A'}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist() if 'prompt_type' in df.columns else 'N/A'}")
    
    # 显示每个commit_sha和prompt_type的响应数量概览
    if 'commit_sha' in df.columns and 'prompt_type' in df.columns:
        summary = df.groupby(['commit_sha', 'prompt_type']).size().unstack(fill_value=0)
        print(f"\nSample of responses per commit and prompt type (first 10 commits):")
        print(summary.head(10))
    
    client.close()
    return df

def export_all_twitter_data(mongo_host='localhost', mongo_port=27017, 
                           mongo_db='twitter_sentiment', mongo_collection='responses',
                           output_file='data/all_twitter_responses.csv'):
    """
    从MongoDB导出所有Twitter响应数据到CSV文件
    """
    print(f"\n=== Exporting ALL Twitter data from MongoDB ===")
    
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 先获取总数
    total_count = collection.count_documents({})
    print(f"Total twitter records in MongoDB: {total_count}")
    
    if total_count == 0:
        print("No twitter data found in MongoDB")
        client.close()
        return None
    
    # 分批查询和处理数据以避免内存问题
    batch_size = 10000
    all_data = []
    
    print("Fetching twitter data in batches...")
    for skip in tqdm(range(0, total_count, batch_size), desc="Fetching batches"):
        batch_docs = list(collection.find({}).skip(skip).limit(batch_size))
        all_data.extend(batch_docs)
    
    print(f"Fetched {len(all_data)} twitter records")
    
    # 转换为DataFrame
    df = pd.DataFrame(all_data)
    
    # 选择需要的列
    required_columns = ['tweet_index', 'prompt_type', 'text', 'validation', 
                       'response_text', 'model_name', 'query_index', 'timestamp']
    
    # 检查哪些列存在
    available_columns = [col for col in required_columns if col in df.columns]
    df = df[available_columns]
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} twitter records to {output_file}")
    print(f"Unique tweets: {df['tweet_index'].nunique() if 'tweet_index' in df.columns else 'N/A'}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist() if 'prompt_type' in df.columns else 'N/A'}")
    
    # 显示每个tweet_index和prompt_type的响应数量概览
    if 'tweet_index' in df.columns and 'prompt_type' in df.columns:
        summary = df.groupby(['tweet_index', 'prompt_type']).size().unstack(fill_value=0)
        print(f"\nSample of responses per tweet and prompt type (first 10 tweets):")
        print(summary.head(10))
    
    client.close()
    return df

def check_mongodb_collections():
    """检查MongoDB中的数据库和集合信息"""
    print("=== Checking MongoDB Collections ===")
    
    client = MongoClient(host='localhost', port=27017)
    
    # 检查commit数据库
    print("\n1. Commit Database (commit_analysis):")
    try:
        commit_db = client['commit_analysis']
        collections = commit_db.list_collection_names()
        print(f"   Collections: {collections}")
        
        if 'responses' in collections:
            commit_count = commit_db['responses'].count_documents({})
            print(f"   Records in 'responses': {commit_count}")
            
            # 显示一个样本文档的字段
            sample_doc = commit_db['responses'].find_one()
            if sample_doc:
                print(f"   Sample document fields: {list(sample_doc.keys())}")
    except Exception as e:
        print(f"   Error accessing commit database: {e}")
    
    # 检查twitter数据库
    print("\n2. Twitter Database (twitter_sentiment):")
    try:
        twitter_db = client['twitter_sentiment']
        collections = twitter_db.list_collection_names()
        print(f"   Collections: {collections}")
        
        if 'responses' in collections:
            twitter_count = twitter_db['responses'].count_documents({})
            print(f"   Records in 'responses': {twitter_count}")
            
            # 显示一个样本文档的字段
            sample_doc = twitter_db['responses'].find_one()
            if sample_doc:
                print(f"   Sample document fields: {list(sample_doc.keys())}")
    except Exception as e:
        print(f"   Error accessing twitter database: {e}")
    
    client.close()

def main():
    parser = argparse.ArgumentParser(description="Export ALL data from MongoDB to CSV files")
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    parser.add_argument('--commit_db', type=str, default='commit_analysis')
    parser.add_argument('--commit_collection', type=str, default='responses')
    parser.add_argument('--twitter_db', type=str, default='twitter_sentiment')
    parser.add_argument('--twitter_collection', type=str, default='responses')
    parser.add_argument('--commit_output', type=str, default='data/all_commit_responses.csv')
    parser.add_argument('--twitter_output', type=str, default='data/all_twitter_responses.csv')
    parser.add_argument('--check_only', action='store_true', help='Only check MongoDB collections without exporting')
    
    args = parser.parse_args()
    
    # 首先检查MongoDB集合
    check_mongodb_collections()
    
    if args.check_only:
        print("\nCheck completed. Use without --check_only to export data.")
        return
    
    print(f"\n{'='*60}")
    print("Starting full data export from MongoDB...")
    print(f"{'='*60}")
    
    # 导出commit数据
    commit_df = export_all_commit_data(
        mongo_host=args.mongo_host,
        mongo_port=args.mongo_port,
        mongo_db=args.commit_db,
        mongo_collection=args.commit_collection,
        output_file=args.commit_output
    )
    
    # 导出twitter数据
    twitter_df = export_all_twitter_data(
        mongo_host=args.mongo_host,
        mongo_port=args.mongo_port,
        mongo_db=args.twitter_db,
        mongo_collection=args.twitter_collection,
        output_file=args.twitter_output
    )
    
    # 总结
    print(f"\n{'='*60}")
    print("Export Summary:")
    print(f"{'='*60}")
    if commit_df is not None:
        print(f"✅ Commit data: {len(commit_df)} records exported to {args.commit_output}")
    else:
        print("❌ Commit data: Export failed or no data found")
        
    if twitter_df is not None:
        print(f"✅ Twitter data: {len(twitter_df)} records exported to {args.twitter_output}")
    else:
        print("❌ Twitter data: Export failed or no data found")
    
    print("\nReady for UQ analysis on the complete datasets!")

if __name__ == "__main__":
    main()
from pymongo import MongoClient
import argparse
from tqdm import tqdm

def export_all_commit_data(mongo_host='localhost', mongo_port=27017, 
                          mongo_db='commit_analysis', mongo_collection='responses',
                          output_file='data/all_commit_responses.csv'):
    """
    从MongoDB导出所有Commit响应数据到CSV文件
    """
    print(f"\n=== Exporting All Commit Data ===")
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 获取总文档数量
    total_docs = collection.count_documents({})
    print(f"Total commit documents in MongoDB: {total_docs}")
    
    if total_docs == 0:
        print("No commit documents found!")
        client.close()
        return None
    
    # 分批读取数据，避免内存问题
    batch_size = 10000
    all_documents = []
    
    print("Reading commit data from MongoDB...")
    with tqdm(total=total_docs, desc="Loading commit docs") as pbar:
        for batch_start in range(0, total_docs, batch_size):
            batch_docs = list(collection.find().skip(batch_start).limit(batch_size))
            all_documents.extend(batch_docs)
            pbar.update(len(batch_docs))
    
    # 转换为DataFrame
    df = pd.DataFrame(all_documents)
    
    # 选择需要的列（如果存在_id列，可能需要处理）
    if '_id' in df.columns:
        df = df.drop('_id', axis=1)
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} commit records to {output_file}")
    
    # 显示基本统计信息
    print(f"Unique commits: {df['commit_sha'].nunique()}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist()}")
    
    # 显示每个commit_sha和prompt_type的响应数量统计
    summary = df.groupby(['commit_sha', 'prompt_type']).size()
    print(f"\nSample of responses per commit and prompt type:")
    print(summary.head(10))
    print(f"Total commit-prompt combinations: {len(summary)}")
    
    client.close()
    return df

def export_all_twitter_data(mongo_host='localhost', mongo_port=27017, 
                           mongo_db='twitter_sentiment', mongo_collection='responses',
                           output_file='data/all_twitter_responses.csv'):
    """
    从MongoDB导出所有Twitter响应数据到CSV文件
    """
    print(f"\n=== Exporting All Twitter Data ===")
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 获取总文档数量
    total_docs = collection.count_documents({})
    print(f"Total twitter documents in MongoDB: {total_docs}")
    
    if total_docs == 0:
        print("No twitter documents found!")
        client.close()
        return None
    
    # 分批读取数据
    batch_size = 10000
    all_documents = []
    
    print("Reading twitter data from MongoDB...")
    with tqdm(total=total_docs, desc="Loading twitter docs") as pbar:
        for batch_start in range(0, total_docs, batch_size):
            batch_docs = list(collection.find().skip(batch_start).limit(batch_size))
            all_documents.extend(batch_docs)
            pbar.update(len(batch_docs))
    
    # 转换为DataFrame
    df = pd.DataFrame(all_documents)
    
    # 处理_id列
    if '_id' in df.columns:
        df = df.drop('_id', axis=1)
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} twitter records to {output_file}")
    
    # 显示基本统计信息
    print(f"Unique tweets: {df['tweet_index'].nunique()}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist()}")
    
    # 显示每个tweet_index和prompt_type的响应数量统计
    summary = df.groupby(['tweet_index', 'prompt_type']).size()
    print(f"\nSample of responses per tweet and prompt type:")
    print(summary.head(10))
    print(f"Total tweet-prompt combinations: {len(summary)}")
    
    client.close()
    return df

def check_mongodb_collections():
    """检查MongoDB中的数据库和集合"""
    print("=== Checking MongoDB Collections ===")
    
    # 检查commit数据
    try:
        client = MongoClient(host='localhost', port=27017)
        
        # 检查commit database
        commit_db = client['commit_analysis']
        commit_collections = commit_db.list_collection_names()
        print(f"Commit database collections: {commit_collections}")
        
        if 'responses' in commit_collections:
            commit_count = commit_db['responses'].count_documents({})
            print(f"Commit responses count: {commit_count}")
            
            # 显示一个示例文档的字段
            sample_commit = commit_db['responses'].find_one()
            if sample_commit:
                print(f"Commit document fields: {list(sample_commit.keys())}")
        
        # 检查twitter database
        twitter_db = client['twitter_sentiment']
        twitter_collections = twitter_db.list_collection_names()
        print(f"Twitter database collections: {twitter_collections}")
        
        if 'responses' in twitter_collections:
            twitter_count = twitter_db['responses'].count_documents({})
            print(f"Twitter responses count: {twitter_count}")
            
            # 显示一个示例文档的字段
            sample_twitter = twitter_db['responses'].find_one()
            if sample_twitter:
                print(f"Twitter document fields: {list(sample_twitter.keys())}")
        
        client.close()
        
    except Exception as e:
        print(f"Error checking MongoDB: {e}")

def main():
    parser = argparse.ArgumentParser(description="Export all data from MongoDB to CSV")
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    parser.add_argument('--commit_db', type=str, default='commit_analysis')
    parser.add_argument('--twitter_db', type=str, default='twitter_sentiment')
    parser.add_argument('--collection', type=str, default='responses')
    parser.add_argument('--commit_output', type=str, default='data/all_commit_responses.csv')
    parser.add_argument('--twitter_output', type=str, default='data/all_twitter_responses.csv')
    parser.add_argument('--check_only', action='store_true', help='Only check collections, do not export')
    
    args = parser.parse_args()
    
    # 首先检查MongoDB集合
    check_mongodb_collections()
    
    if args.check_only:
        print("Check complete. Use without --check_only to export data.")
        return
    
    # 导出commit数据
    try:
        commit_df = export_all_commit_data(
            mongo_host=args.mongo_host,
            mongo_port=args.mongo_port,
            mongo_db=args.commit_db,
            mongo_collection=args.collection,
            output_file=args.commit_output
        )
        print(f"✅ Commit data exported successfully!")
    except Exception as e:
        print(f"❌ Error exporting commit data: {e}")
    
    # 导出twitter数据
    try:
        twitter_df = export_all_twitter_data(
            mongo_host=args.mongo_host,
            mongo_port=args.mongo_port,
            mongo_db=args.twitter_db,
            mongo_collection=args.collection,
            output_file=args.twitter_output
        )
        print(f"✅ Twitter data exported successfully!")
    except Exception as e:
        print(f"❌ Error exporting twitter data: {e}")
    
    print(f"\n🎉 Export process completed!")
    print(f"Files generated:")
    print(f"- {args.commit_output}")
    print(f"- {args.twitter_output}")

if __name__ == "__main__":
    main()
from pymongo import MongoClient
import argparse
from tqdm import tqdm

def export_all_commit_data(mongo_host='localhost', mongo_port=27017, 
                          mongo_db='commit_analysis', mongo_collection='responses',
                          output_file='data/all_commit_responses.csv'):
    """
    从MongoDB导出所有Commit响应数据到CSV文件
    """
    print(f"\n=== Exporting ALL Commit data from MongoDB ===")
    print(f"Connecting to {mongo_host}:{mongo_port}/{mongo_db}.{mongo_collection}")
    
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 获取总数
    total_count = collection.count_documents({})
    print(f"Total documents in collection: {total_count}")
    
    if total_count == 0:
        print("No documents found in the collection")
        client.close()
        return None
    
    # 分批读取数据以避免内存问题
    batch_size = 10000
    all_documents = []
    
    print("Fetching documents in batches...")
    for skip in tqdm(range(0, total_count, batch_size), desc="Fetching batches"):
        batch = list(collection.find({}).skip(skip).limit(batch_size))
        all_documents.extend(batch)
    
    print(f"Fetched {len(all_documents)} documents")
    
    # 转换为DataFrame
    df = pd.DataFrame(all_documents)
    
    # 选择需要的列
    required_columns = ['commit_sha', 'repo_name', 'author', 'date', 'message', 
                       'prompt_type', 'prompt_text', 'response_text', 'model_name', 
                       'query_index', 'timestamp']
    
    # 检查哪些列存在
    existing_columns = [col for col in required_columns if col in df.columns]
    df = df[existing_columns]
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} records to {output_file}")
    print(f"Unique commits: {df['commit_sha'].nunique()}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist()}")
    
    # 显示每个commit_sha和prompt_type的响应数量统计
    summary = df.groupby(['commit_sha', 'prompt_type']).size().unstack(fill_value=0)
    print(f"\nResponses per commit and prompt type (showing first 10):")
    print(summary.head(10))
    
    # 整体统计
    print(f"\nOverall statistics:")
    print(f"Total unique commits: {df['commit_sha'].nunique()}")
    print(f"Total responses: {len(df)}")
    print(f"Average responses per commit: {len(df) / df['commit_sha'].nunique():.1f}")
    
    client.close()
    return df

def export_all_twitter_data(mongo_host='localhost', mongo_port=27017, 
                           mongo_db='twitter_sentiment', mongo_collection='responses',
                           output_file='data/all_twitter_responses.csv'):
    """
    从MongoDB导出所有Twitter响应数据到CSV文件
    """
    print(f"\n=== Exporting ALL Twitter data from MongoDB ===")
    print(f"Connecting to {mongo_host}:{mongo_port}/{mongo_db}.{mongo_collection}")
    
    # 连接MongoDB
    client = MongoClient(host=mongo_host, port=mongo_port)
    db = client[mongo_db]
    collection = db[mongo_collection]
    
    # 获取总数
    total_count = collection.count_documents({})
    print(f"Total documents in collection: {total_count}")
    
    if total_count == 0:
        print("No documents found in the collection")
        client.close()
        return None
    
    # 分批读取数据以避免内存问题
    batch_size = 10000
    all_documents = []
    
    print("Fetching documents in batches...")
    for skip in tqdm(range(0, total_count, batch_size), desc="Fetching batches"):
        batch = list(collection.find({}).skip(skip).limit(batch_size))
        all_documents.extend(batch)
    
    print(f"Fetched {len(all_documents)} documents")
    
    # 转换为DataFrame
    df = pd.DataFrame(all_documents)
    
    # 选择需要的列
    required_columns = ['tweet_index', 'prompt_type', 'text', 'validation', 
                       'response_text', 'model_name', 'query_index', 'timestamp']
    
    # 检查哪些列存在
    existing_columns = [col for col in required_columns if col in df.columns]
    df = df[existing_columns]
    
    # 按tweet_index和prompt_type排序
    df = df.sort_values(['tweet_index', 'prompt_type', 'query_index'])
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"Successfully exported {len(df)} records to {output_file}")
    print(f"Unique tweets: {df['tweet_index'].nunique()}")
    print(f"Prompt types: {df['prompt_type'].unique().tolist()}")
    
    # 显示每个tweet_index和prompt_type的响应数量统计
    summary = df.groupby(['tweet_index', 'prompt_type']).size().unstack(fill_value=0)
    print(f"\nResponses per tweet and prompt type (showing first 10):")
    print(summary.head(10))
    
    # 整体统计
    print(f"\nOverall statistics:")
    print(f"Total unique tweets: {df['tweet_index'].nunique()}")
    print(f"Total responses: {len(df)}")
    print(f"Average responses per tweet: {len(df) / df['tweet_index'].nunique():.1f}")
    
    client.close()
    return df

def check_mongodb_collections():
    """检查MongoDB中的可用集合"""
    print("=== Checking MongoDB Collections ===")
    
    # 检查commit数据库
    try:
        client = MongoClient(host='localhost', port=27017)
        
        # Commit database
        commit_db = client['commit_analysis']
        print(f"\nCommit database collections: {commit_db.list_collection_names()}")
        for col_name in commit_db.list_collection_names():
            count = commit_db[col_name].count_documents({})
            print(f"  {col_name}: {count} documents")
        
        # Twitter database  
        twitter_db = client['twitter_sentiment']
        print(f"\nTwitter database collections: {twitter_db.list_collection_names()}")
        for col_name in twitter_db.list_collection_names():
            count = twitter_db[col_name].count_documents({})
            print(f"  {col_name}: {count} documents")
            
        client.close()
        
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")

def main():
    parser = argparse.ArgumentParser(description="Export all data from MongoDB to CSV files")
    parser.add_argument('--mongo_host', type=str, default='localhost')
    parser.add_argument('--mongo_port', type=int, default=27017)
    
    # Commit database settings
    parser.add_argument('--commit_db', type=str, default='commit_analysis')
    parser.add_argument('--commit_collection', type=str, default='responses')
    parser.add_argument('--commit_output', type=str, default='data/all_commit_responses.csv')
    
    # Twitter database settings
    parser.add_argument('--twitter_db', type=str, default='twitter_sentiment')
    parser.add_argument('--twitter_collection', type=str, default='responses')
    parser.add_argument('--twitter_output', type=str, default='data/all_twitter_responses.csv')
    
    parser.add_argument('--check_only', action='store_true', help='Only check collections, do not export')
    
    args = parser.parse_args()
    
    # 首先检查集合
    check_mongodb_collections()
    
    if args.check_only:
        print("Check complete. Use without --check_only to export data.")
        return
    
    # 导出Commit数据
    try:
        commit_df = export_all_commit_data(
            mongo_host=args.mongo_host,
            mongo_port=args.mongo_port,
            mongo_db=args.commit_db,
            mongo_collection=args.commit_collection,
            output_file=args.commit_output
        )
        print(f"✅ Commit data exported successfully")
    except Exception as e:
        print(f"❌ Error exporting commit data: {e}")
    
    # 导出Twitter数据
    try:
        twitter_df = export_all_twitter_data(
            mongo_host=args.mongo_host,
            mongo_port=args.mongo_port,
            mongo_db=args.twitter_db,
            mongo_collection=args.twitter_collection,
            output_file=args.twitter_output
        )
        print(f"✅ Twitter data exported successfully")
    except Exception as e:
        print(f"❌ Error exporting twitter data: {e}")
    
    print(f"\n🎉 All data export completed!")
    print(f"Files generated:")
    print(f"  - {args.commit_output}")
    print(f"  - {args.twitter_output}")

if __name__ == "__main__":
    main()