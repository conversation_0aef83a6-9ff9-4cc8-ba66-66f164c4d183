#!/usr/bin/env python3
"""
对比三种NLI方法：DeBERTa、Ollama和vLLM
重点测试vLLM的logprobs功能
"""

import sys
import os
import logging
import time
from typing import List, Dict, Any, <PERSON><PERSON>

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.nli_calculator import OllamaNLICalculator, VLLMNLICalculator, get_available_ollama_models
from core.nli_shared import get_nli_calculator, get_vllm_nli_calculator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def get_test_cases() -> List[Tuple[str, str, str]]:
    """获取测试用例，包含预期的NLI关系"""
    return [
        (
            "The cat is sleeping on the mat",
            "A feline is resting on a rug",
            "ENTAILMENT"
        ),
        (
            "It is raining heavily outside",
            "The weather is sunny and clear",
            "CONTRADICTION"
        ),
        (
            "The student passed the exam",
            "The student is happy",
            "NEUTRAL"
        ),
        (
            "All birds can fly",
            "Penguins are birds but cannot fly",
            "CONTRADICTION"
        ),
        (
            "The book is on the table",
            "There is a novel somewhere in the room",
            "NEUTRAL"
        )
    ]


def get_prediction(nli_result) -> str:
    """从NLI结果获取预测标签"""
    if nli_result is None:
        return "ERROR"
    
    scores = [nli_result.entailment, nli_result.neutral, nli_result.contradiction]
    labels = ['ENTAILMENT', 'NEUTRAL', 'CONTRADICTION']
    return labels[scores.index(max(scores))]


def test_deberta_baseline(test_cases: List[Tuple[str, str, str]]) -> List[Dict[str, Any]]:
    """测试DeBERTa基线"""
    print(f"\n🎯 测试DeBERTa基线")
    print("="*50)
    
    try:
        calc = get_nli_calculator("microsoft/deberta-large-mnli")
        results = []
        
        for i, (text1, text2, expected) in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}/{len(test_cases)}: {expected}")
            
            start_time = time.time()
            result = calc.compute_nli_scores_cached(text1, text2)
            elapsed_time = time.time() - start_time
            
            prediction = get_prediction(result)
            print(f"   结果: E={result.entailment:.3f}, N={result.neutral:.3f}, C={result.contradiction:.3f}")
            print(f"   预测: {prediction} ({elapsed_time:.2f}s)")
            
            results.append({
                'method': 'DeBERTa',
                'case_id': i,
                'expected': expected,
                'prediction': prediction,
                'entailment': result.entailment,
                'neutral': result.neutral,
                'contradiction': result.contradiction,
                'time': elapsed_time,
                'success': True
            })
        
        return results
        
    except Exception as e:
        log.error(f"DeBERTa测试失败: {e}")
        return []


def test_ollama_method(model_name: str, test_cases: List[Tuple[str, str, str]]) -> List[Dict[str, Any]]:
    """测试Ollama方法"""
    print(f"\n🤖 测试Ollama方法 ({model_name})")
    print("="*50)
    
    try:
        calc = OllamaNLICalculator(model_name=model_name, verbose=False, use_sampling=False)
        results = []
        
        for i, (text1, text2, expected) in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}/{len(test_cases)}: {expected}")
            
            start_time = time.time()
            try:
                result = calc.compute_nli_scores(text1, text2)
                elapsed_time = time.time() - start_time
                
                prediction = get_prediction(result)
                print(f"   结果: E={result.entailment:.3f}, N={result.neutral:.3f}, C={result.contradiction:.3f}")
                print(f"   预测: {prediction} ({elapsed_time:.2f}s)")
                
                results.append({
                    'method': 'Ollama',
                    'case_id': i,
                    'expected': expected,
                    'prediction': prediction,
                    'entailment': result.entailment,
                    'neutral': result.neutral,
                    'contradiction': result.contradiction,
                    'time': elapsed_time,
                    'success': True
                })
                
            except Exception as e:
                print(f"   ❌ 错误: {e}")
                results.append({
                    'method': 'Ollama',
                    'case_id': i,
                    'expected': expected,
                    'prediction': 'ERROR',
                    'entailment': 0.33,
                    'neutral': 0.34,
                    'contradiction': 0.33,
                    'time': 0,
                    'success': False
                })
        
        return results
        
    except Exception as e:
        log.error(f"Ollama测试失败: {e}")
        return []


def test_vllm_method(model_name: str, test_cases: List[Tuple[str, str, str]], 
                    vllm_host: str = "http://localhost:8000") -> List[Dict[str, Any]]:
    """测试vLLM方法（使用logprobs）"""
    print(f"\n🚀 测试vLLM方法 ({model_name}) - 使用logprobs")
    print("="*50)
    
    try:
        calc = get_vllm_nli_calculator(model_name, vllm_host=vllm_host)
        results = []
        
        for i, (text1, text2, expected) in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}/{len(test_cases)}: {expected}")
            
            start_time = time.time()
            try:
                result = calc.compute_nli_scores_logprobs(text1, text2)
                elapsed_time = time.time() - start_time
                
                prediction = get_prediction(result)
                print(f"   结果: E={result.entailment:.3f}, N={result.neutral:.3f}, C={result.contradiction:.3f}")
                print(f"   预测: {prediction} ({elapsed_time:.2f}s)")
                
                results.append({
                    'method': 'vLLM',
                    'case_id': i,
                    'expected': expected,
                    'prediction': prediction,
                    'entailment': result.entailment,
                    'neutral': result.neutral,
                    'contradiction': result.contradiction,
                    'time': elapsed_time,
                    'success': True
                })
                
            except Exception as e:
                print(f"   ❌ 错误: {e}")
                results.append({
                    'method': 'vLLM',
                    'case_id': i,
                    'expected': expected,
                    'prediction': 'ERROR',
                    'entailment': 0.33,
                    'neutral': 0.34,
                    'contradiction': 0.33,
                    'time': 0,
                    'success': False
                })
        
        return results
        
    except Exception as e:
        log.error(f"vLLM测试失败: {e}")
        return []


def analyze_comparison_results(all_results: List[Dict[str, Any]]):
    """分析对比结果"""
    print(f"\n📊 三种方法对比分析")
    print("="*60)
    
    # 按方法分组
    methods = {}
    for result in all_results:
        method = result['method']
        if method not in methods:
            methods[method] = []
        methods[method].append(result)
    
    # 统计表现
    print(f"{'方法':<10} {'成功率':<8} {'准确率':<8} {'平均时间':<10} {'预测分布'}")
    print("-" * 70)
    
    for method_name, results in methods.items():
        successful_results = [r for r in results if r['success']]
        success_rate = len(successful_results) / len(results) if results else 0
        
        if successful_results:
            # 准确率
            correct_predictions = [r for r in successful_results if r['prediction'] == r['expected']]
            accuracy = len(correct_predictions) / len(successful_results)
            
            # 平均时间
            avg_time = sum(r['time'] for r in successful_results) / len(successful_results)
            
            # 预测分布
            predictions = [r['prediction'] for r in successful_results]
            pred_counts = {pred: predictions.count(pred) for pred in ['ENTAILMENT', 'NEUTRAL', 'CONTRADICTION']}
            pred_dist = f"E:{pred_counts.get('ENTAILMENT', 0)} N:{pred_counts.get('NEUTRAL', 0)} C:{pred_counts.get('CONTRADICTION', 0)}"
        else:
            accuracy = 0
            avg_time = 0
            pred_dist = "N/A"
        
        print(f"{method_name:<10} {success_rate:.1%}    {accuracy:.1%}    {avg_time:<10.2f} {pred_dist}")
    
    # 方法间一致性分析
    if len(methods) > 1:
        print(f"\n🎯 方法间一致性分析:")
        
        # 按测试用例分析
        case_count = max(len(results) for results in methods.values())
        consistent_cases = 0
        
        for case_id in range(1, case_count + 1):
            case_predictions = []
            for method_name, results in methods.items():
                case_result = next((r for r in results if r['case_id'] == case_id and r['success']), None)
                if case_result:
                    case_predictions.append(case_result['prediction'])
            
            if len(case_predictions) == len(methods) and len(set(case_predictions)) == 1:
                consistent_cases += 1
        
        consistency_rate = consistent_cases / case_count
        print(f"   所有方法一致的预测: {consistent_cases}/{case_count} ({consistency_rate:.1%})")


def main():
    """主函数"""
    print("🚀 NLI方法全面对比测试")
    print("🔍 对比DeBERTa、Ollama和vLLM三种方法")
    print("="*60)
    
    # 获取测试用例
    test_cases = get_test_cases()
    print(f"📝 准备了 {len(test_cases)} 个测试用例")
    
    all_results = []
    
    # 1. 测试DeBERTa基线
    deberta_results = test_deberta_baseline(test_cases)
    all_results.extend(deberta_results)
    
    # 2. 测试Ollama（如果可用）
    available_ollama_models = get_available_ollama_models()
    target_ollama_models = ["gpt-oss:20b", "llama3.3:latest"]
    available_targets = [model for model in target_ollama_models if model in available_ollama_models]
    
    if available_targets:
        print(f"\n✅ 可用Ollama模型: {available_targets}")
        # 只测试第一个可用模型
        ollama_results = test_ollama_method(available_targets[0], test_cases)
        all_results.extend(ollama_results)
    else:
        print(f"\n❌ 没有可用的Ollama模型")
    
    # 3. 测试vLLM（需要用户确认）
    vllm_model = input(f"\n请输入vLLM模型名称（如果vLLM服务正在运行），或按Enter跳过: ").strip()
    if vllm_model:
        vllm_results = test_vllm_method(vllm_model, test_cases)
        all_results.extend(vllm_results)
    else:
        print(f"⏭️ 跳过vLLM测试")
    
    # 分析结果
    if all_results:
        analyze_comparison_results(all_results)
        
        print(f"\n✅ 测试完成！")
        print(f"💡 总结:")
        print(f"   1. DeBERTa: 传统Transformer模型，准确但需要GPU")
        print(f"   2. Ollama: 本地部署，JSON解析方式")
        print(f"   3. vLLM: 支持logprobs，理论上最准确")
    else:
        print(f"❌ 没有获得任何测试结果")


if __name__ == "__main__":
    main()
