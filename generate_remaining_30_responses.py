#!/usr/bin/env python3
"""
生成剩余response以确保每个input text都有30条response
专门针对你的需求：只处理5个样本，每个样本30条response (5个prompt × 6次尝试)
"""

import os
import sys
import yaml
import pandas as pd
import uuid
import logging
from typing import Dict, List, Any, Optional
from pymongo import MongoClient
from datetime import datetime
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TargetedResponseGenerator:
    def __init__(self, config_path: str = "config.yaml"):
        """初始化生成器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 连接MongoDB
        mongo_config = self.config['output']['mongo']
        self.client = MongoClient(mongo_config['host'], mongo_config['port'])
        self.db = self.client[mongo_config['database']]
        self.collection = self.db[mongo_config['collection']]
        
    def get_task_status(self, task_name: str) -> Dict[str, Any]:
        """获取任务当前状态"""
        pipeline = [
            {'$match': {'task_name': task_name}},
            {'$group': {
                '_id': '$input_text',
                'count': {'$sum': 1}
            }},
            {'$sort': {'count': 1}}
        ]
        
        results = list(self.collection.aggregate(pipeline))
        
        sufficient = [r for r in results if r['count'] >= 30]
        insufficient = [r for r in results if r['count'] < 30]
        
        return {
            'total_inputs': len(results),
            'sufficient_inputs': len(sufficient),
            'insufficient_inputs': len(insufficient),
            'insufficient_details': insufficient
        }
    
    def get_unused_inputs(self, task_name: str, limit: int = 5) -> List[str]:
        """获取未使用的input_text"""
        task_config = self.config['tasks'][task_name]
        data_file = task_config['data_file']
        text_field = task_config['text_field']
        
        # 读取原始数据
        df = pd.read_csv(data_file)
        
        # 获取已使用的input_text
        used_inputs = set()
        for doc in self.collection.find({'task_name': task_name}, {'input_text': 1}):
            used_inputs.add(doc['input_text'])
        
        # 找到未使用的输入
        unused_inputs = []
        for _, row in df.iterrows():
            input_text = row[text_field]
            if input_text not in used_inputs:
                unused_inputs.append(input_text)
                if len(unused_inputs) >= limit:
                    break
        
        return unused_inputs
    
    def create_limited_config(self, task_name: str, selected_inputs: List[str]) -> str:
        """创建限制版本的配置文件，只处理指定的输入"""
        # 创建临时数据文件，只包含选定的输入
        task_config = self.config['tasks'][task_name]
        original_file = task_config['data_file']
        text_field = task_config['text_field']
        
        # 读取原始数据
        df = pd.read_csv(original_file)
        
        # 过滤出选定的输入
        filtered_df = df[df[text_field].isin(selected_inputs)]
        
        # 保存临时文件
        temp_file = f"temp_{task_name}_limited.csv"
        filtered_df.to_csv(temp_file, index=False)
        
        # 创建新的配置
        new_config = self.config.copy()
        new_config['tasks'][task_name]['data_file'] = temp_file
        new_config['tasks'][task_name]['sample_prompts'] = 5
        new_config['tasks'][task_name]['attempts_per_prompt'] = 6
        
        # 只启用指定任务
        for task in new_config['tasks']:
            new_config['tasks'][task]['enabled'] = (task == task_name)
        
        # 保存新配置
        temp_config_file = f"temp_config_{task_name}.yaml"
        with open(temp_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True)
        
        return temp_config_file, temp_file
    
    def run_generation(self, task_name: str, num_samples: int = 5):
        """运行生成过程"""
        logger.info(f"开始为 {task_name} 生成剩余response...")
        
        # 获取当前状态
        status = self.get_task_status(task_name)
        logger.info(f"当前状态: {status['total_inputs']} 个输入，{status['sufficient_inputs']} 个已足够，{status['insufficient_inputs']} 个不足")
        
        # 获取未使用的输入
        unused_inputs = self.get_unused_inputs(task_name, num_samples)
        logger.info(f"找到 {len(unused_inputs)} 个未使用的输入")
        
        if len(unused_inputs) < num_samples:
            logger.warning(f"可用输入不足，只能处理 {len(unused_inputs)} 个")
            num_samples = len(unused_inputs)
        
        if num_samples == 0:
            logger.info("没有可用的输入，跳过")
            return
        
        # 选择要处理的输入
        selected_inputs = unused_inputs[:num_samples]
        logger.info(f"选择处理以下 {len(selected_inputs)} 个输入:")
        for i, inp in enumerate(selected_inputs):
            preview = inp[:50] + "..." if len(inp) > 50 else inp
            logger.info(f"  {i+1}. {preview}")
        
        # 创建限制版本的配置
        temp_config_file, temp_data_file = self.create_limited_config(task_name, selected_inputs)
        
        try:
            # 使用现有的生成器
            from llm_response_generator import LLMResponseGenerator
            generator = LLMResponseGenerator(temp_config_file)
            
            # 运行生成
            run_id = generator.run(
                attempts_per_variant=6,  # 每个prompt 6次尝试
                resume=False,  # 不恢复，创建新的运行
                test_mode=False
            )
            
            logger.info(f"生成完成！运行ID: {run_id}")
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_config_file):
                os.remove(temp_config_file)
            if os.path.exists(temp_data_file):
                os.remove(temp_data_file)
    
    def show_final_status(self):
        """显示最终状态"""
        tasks = ['sentiment_analysis', 'explorative_coding', 'counterfactual_qa']
        
        logger.info("\n" + "="*60)
        logger.info("最终状态统计")
        logger.info("="*60)
        
        for task in tasks:
            status = self.get_task_status(task)
            logger.info(f"\n{task.upper()}:")
            logger.info(f"  总输入数量: {status['total_inputs']}")
            logger.info(f"  已有30+条response: {status['sufficient_inputs']}")
            logger.info(f"  不足30条response: {status['insufficient_inputs']}")
            
            if status['insufficient_details']:
                logger.info("  不足的输入详情:")
                for detail in status['insufficient_details'][:3]:
                    preview = detail['_id'][:40] + "..." if len(detail['_id']) > 40 else detail['_id']
                    logger.info(f"    - {detail['count']}条: {preview}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生成剩余response确保每个输入30条")
    parser.add_argument("--task", choices=['sentiment_analysis', 'explorative_coding', 'counterfactual_qa'],
                       help="指定要处理的任务")
    parser.add_argument("--samples", type=int, default=5, help="每个任务处理的样本数量")
    parser.add_argument("--config", default="config.yaml", help="配置文件路径")
    parser.add_argument("--status-only", action="store_true", help="只显示状态，不执行生成")
    
    args = parser.parse_args()
    
    generator = TargetedResponseGenerator(args.config)
    
    if args.status_only:
        generator.show_final_status()
        return
    
    tasks = [args.task] if args.task else ['sentiment_analysis', 'explorative_coding', 'counterfactual_qa']
    
    for task in tasks:
        logger.info(f"\n{'='*50}")
        logger.info(f"处理任务: {task}")
        logger.info(f"{'='*50}")
        
        generator.run_generation(task, args.samples)
    
    # 显示最终状态
    generator.show_final_status()

if __name__ == "__main__":
    main()
