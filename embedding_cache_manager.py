#!/usr/bin/env python3
"""
Embedding 缓存管理工具
用于查看、管理和使用结构化的嵌入缓存（SentenceTransformer）
缓存文件位置：cache/embeddings_{md5(model_name)}.pkl

功能：
- stats: 显示缓存条目数与模型
- export: 导出为 npy（向量列表）或 json（键->维度）
- clear: 清理指定模型缓存
- merge: 合并多个缓存文件
- search: 按文本哈希搜索（若已知 md5(text)）
"""
import argparse
import os
import pickle
import hashlib
import json
import numpy as np
from typing import List

CACHE_DIR = "cache"


def _model_cache_path(model_name: str) -> str:
    return os.path.join(CACHE_DIR, f"embeddings_{hashlib.md5(model_name.encode('utf-8')).hexdigest()}.pkl")


def cmd_stats(args):
    path = _model_cache_path(args.model)
    if not os.path.exists(path):
        print(f"No cache for model: {args.model}")
        return
    with open(path, 'rb') as f:
        data = pickle.load(f)
    print(f"Model: {args.model}")
    print(f"Cache file: {path}")
    print(f"Entries: {len(data)}")
    # Try to infer dim
    dim = None
    for v in data.values():
        try:
            arr = np.array(v)
            dim = arr.shape[-1]
            break
        except Exception:
            pass
    if dim is not None:
        print(f"Vector dim: {dim}")


def cmd_export(args):
    path = _model_cache_path(args.model)
    if not os.path.exists(path):
        print(f"No cache for model: {args.model}")
        return
    with open(path, 'rb') as f:
        data = pickle.load(f)
    os.makedirs(args.out_dir, exist_ok=True)
    if args.format == 'npy':
        vecs = [np.array(v) for v in data.values()]
        np.save(os.path.join(args.out_dir, 'embeddings.npy'), np.stack(vecs, axis=0))
        print(f"Saved {len(vecs)} vectors to {args.out_dir}/embeddings.npy")
    elif args.format == 'json':
        meta = {k: (np.array(v).shape[-1] if hasattr(v, '__len__') else None) for k, v in data.items()}
        with open(os.path.join(args.out_dir, 'embeddings_meta.json'), 'w') as f:
            json.dump(meta, f, indent=2)
        print(f"Saved metadata to {args.out_dir}/embeddings_meta.json")
    else:
        print("Unknown format. Use npy or json.")


def cmd_clear(args):
    path = _model_cache_path(args.model)
    if os.path.exists(path):
        os.remove(path)
        print(f"Removed cache file: {path}")
    else:
        print(f"No cache file found: {path}")


def cmd_merge(args):
    target = _model_cache_path(args.model)
    merged = {}
    # Load existing target
    if os.path.exists(target):
        with open(target, 'rb') as f:
            merged.update(pickle.load(f))
    # Merge sources
    for src in args.files:
        if not os.path.exists(src):
            print(f"Skip missing: {src}")
            continue
        with open(src, 'rb') as f:
            data = pickle.load(f)
        merged.update(data)
        print(f"Merged {len(data)} entries from {src}")
    os.makedirs(CACHE_DIR, exist_ok=True)
    with open(target, 'wb') as f:
        pickle.dump(merged, f, protocol=pickle.HIGHEST_PROTOCOL)
    print(f"Saved merged cache to {target} with {len(merged)} entries")


def cmd_search(args):
    path = _model_cache_path(args.model)
    if not os.path.exists(path):
        print(f"No cache for model: {args.model}")
        return
    with open(path, 'rb') as f:
        data = pickle.load(f)
    key = f"{args.text_md5}::{args.model}::norm={1 if args.normalize else 0}"
    if key in data:
        vec = np.array(data[key])
        print(f"Found: dim={vec.shape[-1]} first5={vec[:5].tolist()}")
    else:
        print("Not found")


def main():
    parser = argparse.ArgumentParser(description="Embedding cache manager")
    sub = parser.add_subparsers(dest='cmd')

    p = sub.add_parser('stats'); p.add_argument('--model', required=True); p.set_defaults(func=cmd_stats)
    p = sub.add_parser('export'); p.add_argument('--model', required=True); p.add_argument('--out-dir', required=True); p.add_argument('--format', choices=['npy','json'], default='npy'); p.set_defaults(func=cmd_export)
    p = sub.add_parser('clear'); p.add_argument('--model', required=True); p.set_defaults(func=cmd_clear)
    p = sub.add_parser('merge'); p.add_argument('--model', required=True); p.add_argument('files', nargs='+'); p.set_defaults(func=cmd_merge)
    p = sub.add_parser('search'); p.add_argument('--model', required=True); p.add_argument('--text-md5', required=True); p.add_argument('--normalize', action='store_true'); p.set_defaults(func=cmd_search)

    args = parser.parse_args()
    if not hasattr(args, 'func'):
        parser.print_help(); return
    args.func(args)


if __name__ == '__main__':
    main()

