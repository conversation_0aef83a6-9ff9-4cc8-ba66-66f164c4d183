from pymongo import MongoClient
from datetime import datetime, timezone
import os

class MongoDBConfig:
    def __init__(self, host="localhost", port=27017, database="commit_analysis", collection="responses"):
        self.host = host
        self.port = port
        self.database = database
        self.collection = collection
    
    def get_connection_string(self):
        return f"mongodb://{self.host}:{self.port}/"
    
    def get_client(self):
        return MongoClient(self.get_connection_string())
    
    def get_database(self):
        client = self.get_client()
        return client[self.database]
    
    def get_collection(self):
        db = self.get_database()
        return db[self.collection]

def save_response_to_mongodb(commit_data, prompt_type, prompt_text, response_text, query_index, model_name, config=None):
    """Save a response to MongoDB."""
    if config is None:
        config = MongoDBConfig()
    
    collection = config.get_collection()
    
    document = {
        "commit_sha": commit_data.get('sha'),
        "repo_name": commit_data.get('repo_name'),
        "author": commit_data.get('author'),
        "date": commit_data.get('date'),
        "message": commit_data.get('message'),
        "prompt_type": prompt_type,  # 'single_word', 'module_reasoning', 'reasoning_module'
        "prompt_text": prompt_text,
        "response_text": response_text,
        "model_name": model_name,
        "query_index": query_index,  # 1-30 for each prompt type
        "timestamp": datetime.now(timezone.utc)
    }
    
    result = collection.insert_one(document)
    return result.inserted_id

def get_responses_count(commit_sha, prompt_type=None):
    """Get count of responses for a specific commit and prompt type."""
    config = MongoDBConfig()
    collection = config.get_collection()
    
    query = {"commit_sha": commit_sha}
    if prompt_type:
        query["prompt_type"] = prompt_type
    
    return collection.count_documents(query)

def create_indexes():
    """Create indexes for efficient querying."""
    config = MongoDBConfig()
    collection = config.get_collection()
    
    # Create compound indexes for efficient querying
    collection.create_index([("commit_sha", 1), ("prompt_type", 1), ("query_index", 1)])
    collection.create_index([("repo_name", 1)])
    collection.create_index([("timestamp", -1)])

if __name__ == "__main__":
    create_indexes()
    print("MongoDB indexes created successfully")