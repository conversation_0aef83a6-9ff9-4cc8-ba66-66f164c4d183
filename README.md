# LLM 不确定性量化分析框架

一个完整的大语言模型不确定性量化评估框架，支持多种任务类型、11种UQ方法和完整的实验流程管理。

## 🌟 核心特性

- **🤖 多模型支持**: DeepSeek、Qwen、OpenAI等主流LLM
- **📊 11种UQ方法**: 从语义熵到图拉普拉斯特征值的全面覆盖
- **🎯 4类任务支持**: 情感分析、代码分析、主题标注、反事实问答
- **💾 MongoDB集成**: 完整的数据存储和管理系统
- **🔄 断点续跑**: 支持实验中断后的无缝恢复
- **📈 实时监控**: 详细的进度跟踪和日志记录

## 🚀 快速开始

### 1. 环境配置

```bash
# 安装依赖
pip install -r requirements.txt

# 设置API密钥
export DEEPSEEK_API_KEY="your_deepseek_api_key"
export DASHSCOPE_API_KEY="your_qwen_api_key"  # 可选

# 启动MongoDB (如果未安装)
# Ubuntu/Debian: sudo apt install mongodb
# macOS: brew install mongodb-community
sudo systemctl start mongodb
```

### 2. 生成LLM响应

```bash
# 使用默认配置生成响应
python generate_llm_responses.py

# 使用自定义配置
python generate_llm_responses.py --config config/qwen3-32b.yaml

# 测试模式 (小样本)
python generate_llm_responses.py --test
```

### 3. 运行UQ分析

```bash
# 分析所有响应数据
python run_uq_analysis.py --config configs/uq_analysis_config.yaml

# 分析特定任务
python run_uq_analysis.py --config configs/uq_analysis_config.yaml --task sentiment_analysis

# 恢复中断的分析
python run_uq_analysis.py --config configs/uq_analysis_config.yaml --resume
```

## 📊 支持的任务类型

### 1. 情感分析 (Sentiment Analysis)
- **数据集**: SemEval-2017 Twitter情感数据
- **任务**: 将推文分类为积极、消极或中性
- **Prompt数量**: 10个变体，每次随机选择5个
- **重复次数**: 每个prompt执行6次

### 2. 探索性代码分析 (Explorative Coding)
- **数据集**: PyTorch/TensorFlow提交记录
- **任务**: 根据commit消息识别相关模块
- **Prompt数量**: 10个变体，每次随机选择5个
- **重复次数**: 每个prompt执行6次

### 3. 主题标注 (Topic Labeling)
- **数据集**: 主题建模关键词数据
- **任务**: 为主题关键词生成描述性标签
- **Prompt数量**: 11个变体，每次随机选择5个
- **重复次数**: 每个prompt执行6次

### 4. 反事实问答 (Counterfactual QA)
- **数据集**: 历史反事实问题
- **任务**: 回答"如果...会怎样"类型的历史问题
- **Prompt数量**: 10个变体，每次随机选择5个
- **重复次数**: 每个prompt执行6次

## 🔬 支持的UQ方法 (11种)

### 基于语义的方法
1. **SemanticEntropyNLIUQ**: 使用NLI模型的语义熵计算
2. **LUQUQ**: 长文本不确定性量化，专门针对长响应优化
3. **NumSetsUQ**: 语义集合数量统计

### 基于图论的方法
4. **EigValLaplacianNLIUQ**: 图拉普拉斯矩阵特征值方法(NLI)
5. **EigValLaplacianJaccardUQ**: 图拉普拉斯矩阵特征值方法(Jaccard)
6. **EccentricityNLIEntailUQ**: 图偏心率方法(NLI)
7. **EccentricityJaccardUQ**: 图偏心率方法(Jaccard)

### 基于嵌入的方法
8. **EmbeddingQwenUQ**: 使用Qwen嵌入模型的相似性分析
9. **EmbeddingE5UQ**: 使用E5嵌入模型的相似性分析

### 高级方法
10. **KernelLanguageEntropyUQ**: 核语言熵方法
11. **LofreeCPUQ**: 无logit共形预测方法

## 🏗️ 项目架构

```
llm-uncertainty-1/
├── 📁 core/                      # 核心组件
│   ├── llm_client.py            # LLM客户端统一接口
│   ├── mongodb_client.py        # MongoDB数据库客户端
│   ├── nli_shared.py           # NLI模型共享组件
│   ├── embedding_cache.py      # 嵌入向量缓存
│   └── progress.py             # 进度管理
├── 📁 uq_methods/               # UQ方法实现
│   ├── base.py                 # UQ方法基类
│   └── implementations/        # 11种UQ方法实现
├── 📁 prompts/                  # Prompt模板管理
│   ├── 1_sentiment_analysis/   # 情感分析prompts
│   ├── 2_explorative_coding_commits/ # 代码分析prompts
│   ├── 3_topic_labeling/       # 主题标注prompts
│   └── 4_counterfactual_qa/    # 反事实问答prompts
├── 📁 configs/                  # 配置文件
│   ├── uq_analysis_config.yaml # UQ分析配置
│   └── uq_analysis_test.yaml   # 测试配置
├── 📁 data/                     # 数据文件
├── 📁 results/                  # 结果导出
├── generate_llm_responses.py    # LLM响应生成
├── run_uq_analysis.py          # UQ分析主程序
└── config.yaml                 # 主配置文件
```

## 🛠️ 详细使用指南

### LLM响应生成

#### 基本用法
```bash
# 生成所有任务的响应
python generate_llm_responses.py

# 指定特定任务
python generate_llm_responses.py --task sentiment_analysis

# 使用不同模型配置
python generate_llm_responses.py --config config/qwen3-32b.yaml
```

#### 配置文件示例 (config.yaml)
```yaml
# 模型配置
model:
  name: "deepseek-reasoner"
  base_url: "https://api.deepseek.com/v1"
  api_key_env: "DEEPSEEK_API_KEY"
  temperature: 1.3
  top_p: 0.95
  enable_thinking: true

# 任务配置
tasks:
  sentiment_analysis:
    enabled: true
    dataset_source: "twitter_sentiment"
    data_file: "sampled_semeval.csv"
    prompt_dir: "prompts/1_sentiment_analysis"
    num_prompts: 10
    sample_prompts: 5
    attempts_per_prompt: 6
```

### UQ分析配置

#### 方法选择
```yaml
uq_methods:
  enabled_methods:
    - "SemanticEntropyNLIUQ"      # 适合短文本
    - "LUQUQ"                     # 适合长文本
    - "EmbeddingQwenUQ"           # 基于嵌入
    - "NumSetsUQ"                 # 语义集合
    - "LofreeCPUQ"                # 共形预测
```

#### 方法参数调优
```yaml
method_params:
  SemanticEntropyNLIUQ:
    entailment_threshold: 0.5
    strict_entailment: true
  EccentricityJaccardUQ:
    thres: 0.9
  LofreeCPUQ:
    lambda1: 1.0
    lambda2: 1.0
```

## 📈 结果分析

### 数据导出
```bash
# 导出所有结果到CSV
python export_test_results.py

# 导出特定任务结果
python export_test_results.py --task sentiment_analysis

# 生成分析报告
python export_test_results_summary.py
```

### MongoDB查询示例
```python
from pymongo import MongoClient

client = MongoClient("mongodb://localhost:27017/")
db = client["LLM-UQ"]

# 查询特定任务的响应
responses = db.response_collections.find({
    "task_category": "sentiment_analysis"
})

# 查询UQ分析结果
uq_results = db.uq_analysis_results.find({
    "method_name": "SemanticEntropyNLIUQ"
})
```

## 🔧 高级功能

### 自定义UQ方法
```python
from uq_methods.base import BaseUQMethod

class MyCustomUQ(BaseUQMethod):
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        # 实现你的不确定性计算逻辑
        return {"uncertainty_score": score}

    def get_required_samples(self) -> int:
        return 5  # 需要的样本数量
```

### 添加新任务类型
1. 在 `config.yaml` 中添加任务配置
2. 创建对应的prompt模板目录
3. 准备数据文件
4. 运行生成和分析流程

### 批量实验管理
```bash
# 运行完整的实验流程
python run_complete_analysis.py

# 监控实验进度
python progress_checker.py

# 清理缓存
rm -rf cache/ logs/
```

## 📊 性能优化

### 缓存机制
- **嵌入缓存**: 自动缓存计算过的嵌入向量
- **NLI缓存**: 缓存NLI模型的推理结果
- **相似度缓存**: 缓存Jaccard相似度计算

### 并行处理
- 支持多线程UQ方法计算
- 异步MongoDB写入
- 批量数据处理

### 内存管理
- 大数据集分批处理
- 自动垃圾回收
- 内存使用监控

## 🐛 故障排除

### 常见问题

1. **MongoDB连接失败**
   ```bash
   sudo systemctl start mongodb
   # 或检查连接配置
   ```

2. **API密钥错误**
   ```bash
   export DEEPSEEK_API_KEY="your_actual_key"
   ```

3. **内存不足**
   - 减少 `max_concurrent_requests`
   - 使用测试模式: `--test`

4. **UQ方法计算失败**
   - 检查样本数量是否足够
   - 查看详细日志: `logs/uq_analysis.log`

### 日志分析
```bash
# 查看生成日志
tail -f llm_response_generator.log

# 查看UQ分析日志
tail -f logs/uq_analysis.log

# 查看错误信息
grep "ERROR" logs/*.log
```

## 📚 参考文献

- **LUQ**: Long-text Uncertainty Quantification for LLMs
- **Semantic Entropy**: Semantic Uncertainty via NLI
- **LofreeCP**: API Is Enough: Conformal Prediction for LLMs
- **Graph Methods**: Uncertainty Quantification via Graph Laplacian
- **Kernel Language Entropy**: Von Neumann Entropy for Language Models

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支: `git checkout -b feature/new-uq-method`
3. 提交更改: `git commit -am 'Add new UQ method'`
4. 推送分支: `git push origin feature/new-uq-method`
5. 提交Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件