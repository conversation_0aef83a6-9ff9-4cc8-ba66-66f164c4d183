# LUQSENTENCE Analysis Guide

## 概述

LUQSENTENCE是一个新的句子级别的LUQ不确定性量化方法，专门设计用于分析长文本响应。与原始LUQ方法不同，LUQSENTENCE：

1. **句子级匹配**: 使用embedding相似度匹配语义相关的句子对
2. **完整覆盖**: 匹配较短文本中的所有句子（100%覆盖）
3. **精确NLI**: 对匹配的句子对进行NLI计算，而不是句子vs整个响应
4. **详细分析**: 提供句子级别的详细一致性分析

## 快速开始

### 1. 测试模式（推荐先运行）

```bash
# 直接运行测试
python run_luqsentence_analysis.py --test

# 或使用tmux（推荐）
./run_luqsentence_tmux.sh
# 选择模式1（测试模式）
```

测试模式特点：
- 只处理5个counterfactual问题
- 运行时间：5-10分钟
- 结果保存到：`UQ_result_LUQSENTENCE_counterfactual_test`

### 2. 完整分析

```bash
# 使用tmux运行完整分析（强烈推荐）
./run_luqsentence_tmux.sh
# 选择模式2（完整分析）

# 或直接运行（不推荐，因为耗时较长）
python run_luqsentence_analysis.py
```

完整分析特点：
- 处理所有301个counterfactual响应
- 运行时间：2-4小时
- 结果保存到：`UQ_result_LUQSENTENCE_counterfactual`

## 文件说明

### 配置文件
- `configs/luqsentence_counterfactual_config.yaml` - 完整分析配置
- `configs/luqsentence_counterfactual_test.yaml` - 测试配置

### 运行脚本
- `run_luqsentence_analysis.py` - 主分析脚本
- `run_luqsentence_tmux.sh` - Tmux启动脚本
- `uq_methods/implementations/luq_sentence.py` - LUQSENTENCE方法实现

### 日志和检查点
- `logs/luqsentence_analysis_*.log` - 分析日志
- `checkpoints/luqsentence_counterfactual_resume.json` - 断点续传文件

## Tmux使用指南

### 基本命令
```bash
# 启动分析
./run_luqsentence_tmux.sh

# 连接到会话
tmux attach -t luqsentence_analysis

# 分离会话（保持运行）
Ctrl+B, 然后按 D

# 杀死会话
tmux kill-session -t luqsentence_analysis

# 查看所有会话
tmux list-sessions
```

### 监控进度
```bash
# 查看日志
tail -f logs/luqsentence_analysis_*.log

# 检查MongoDB结果数量
python -c "
from pymongo import MongoClient
c = MongoClient()
db = c['LLM-UQ']
test_count = db['UQ_result_LUQSENTENCE_counterfactual_test'].count_documents({})
full_count = db['UQ_result_LUQSENTENCE_counterfactual'].count_documents({})
print(f'Test results: {test_count}')
print(f'Full results: {full_count}')
"
```

## 方法配置

### LUQSENTENCE参数
```yaml
method_params:
  LUQSENTENCEUQ:
    nli_model_name: "microsoft/deberta-large-mnli"  # NLI模型
    embedding_model: "intfloat/multilingual-e5-large-instruct"  # 嵌入模型
    pct_k: 1.0  # 匹配比例（1.0 = 100%匹配）
    matching_mode: "bottom"  # 匹配模式（最相似）
    verbose: false  # 详细输出
```

### 匹配模式说明
- `bottom`: 选择最相似的句子对
- `top`: 选择最不相似的句子对  
- `random`: 随机选择句子对

## 结果分析

### MongoDB集合结构
```javascript
{
  "_id": ObjectId,
  "group_key": {
    "task_name": "counterfactual_qa",
    "dataset_source": "counterfactual_data",
    "category": "...",
    "row_index": 0,
    "llm_model": "qwen3-32b"
  },
  "uq_results": {
    "LUQSENTENCEUQ": {
      "uncertainty_score": 0.234,
      "method": "LUQSENTENCE",
      "num_responses": 5,
      "overall_consistency": 0.766,
      "matching_details": [...],
      "nli_details": [...],
      "metadata": {
        "total_sentence_pairs": 156,
        "embedding_model": "intfloat/multilingual-e5-large-instruct",
        "pct_k": 1.0,
        "matching_mode": "bottom"
      }
    }
  },
  "metadata": {
    "n_responses": 5,
    "method_count": 1,
    "successful_methods": 1
  }
}
```

### 关键指标
- `uncertainty_score`: 不确定性分数（0-1，越高越不确定）
- `overall_consistency`: 整体一致性分数（0-1，越高越一致）
- `total_sentence_pairs`: 总匹配句子对数量
- `matching_details`: 详细的句子匹配信息
- `nli_details`: 详细的NLI计算结果

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   ```bash
   sudo systemctl start mongod
   ```

2. **内存不足**
   - 减少batch_size
   - 使用测试模式
   - 确保有足够的GPU内存

3. **模型下载失败**
   - 检查网络连接
   - 确保有足够的磁盘空间

4. **分析中断**
   - 使用断点续传功能
   - 检查日志文件获取详细错误信息

### 检查系统状态
```bash
# 检查前提条件
python run_luqsentence_analysis.py --check-only

# 检查GPU使用情况
nvidia-smi

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 性能优化

### 建议配置
- **GPU**: 至少8GB显存
- **内存**: 至少16GB RAM
- **磁盘**: 至少10GB可用空间
- **网络**: 稳定的网络连接（用于模型下载）

### 调优参数
- `batch_size`: 减少以节省内存
- `progress_report_interval`: 增加以减少日志输出
- `pct_k`: 减少以加快处理速度（但会降低覆盖率）

## 联系支持

如果遇到问题，请检查：
1. 日志文件中的详细错误信息
2. MongoDB中的数据完整性
3. 系统资源使用情况
4. 网络连接状态
