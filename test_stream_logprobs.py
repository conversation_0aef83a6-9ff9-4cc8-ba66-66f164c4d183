#!/usr/bin/env python3
"""
测试流式输出模式下logprobs的脚本
"""

import logging
import time
from llm_response_generator import LLMResponseGenerator
from openai import OpenAI
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StreamLogprobsTester:
    """流式输出logprobs测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY", 'null'),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
    
    def test_stream_logprobs(self, prompt: str, model: str = "qwen3-32b"):
        """测试流式输出模式下的logprobs"""
        logger.info(f"开始测试流式输出模式下的logprobs...")
        logger.info(f"模型: {model}")
        logger.info(f"Prompt: {prompt[:100]}...")
        
        try:
            # 流式调用LLM
            stream = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                logprobs=True,
                top_logprobs=5,
                stream=True,  # 启用流式输出
                extra_body={"enable_thinking": False},
            )
            
            logger.info("开始接收流式响应...")
            
            full_content = ""
            all_logprobs = []
            chunk_count = 0
            
            for chunk in stream:
                chunk_count += 1
                logger.info(f"\n--- Chunk {chunk_count} ---")
                
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    # 提取content
                    if hasattr(choice, 'delta') and choice.delta:
                        delta_content = choice.delta.content or ""
                        full_content += delta_content
                        logger.info(f"Content delta: '{delta_content}'")
                    
                    # 提取logprobs
                    chunk_logprobs = None
                    if hasattr(choice, 'logprobs') and choice.logprobs:
                        chunk_logprobs = choice.logprobs
                        all_logprobs.append(chunk_logprobs)
                        logger.info(f"Logprobs in chunk: {type(chunk_logprobs)}")
                        
                        if isinstance(chunk_logprobs, list):
                            logger.info(f"Logprobs list length: {len(chunk_logprobs)}")
                            if chunk_logprobs:
                                logger.info(f"First logprob item: {chunk_logprobs[0] if len(chunk_logprobs) > 0 else 'None'}")
                        elif isinstance(chunk_logprobs, dict):
                            logger.info(f"Logprobs dict keys: {list(chunk_logprobs.keys())}")
                            if 'content' in chunk_logprobs:
                                content_logprobs = chunk_logprobs['content']
                                logger.info(f"Content logprobs type: {type(content_logprobs)}")
                                if isinstance(content_logprobs, list) and content_logprobs:
                                    logger.info(f"Content logprobs length: {len(content_logprobs)}")
                                    if len(content_logprobs) > 0:
                                        first_logprob = content_logprobs[0]
                                        logger.info(f"First content logprob: {first_logprob}")
                    else:
                        logger.info("No logprobs in this chunk")
                    
                    # 检查finish_reason
                    if hasattr(choice, 'finish_reason') and choice.finish_reason:
                        logger.info(f"Finish reason: {choice.finish_reason}")
                else:
                    logger.info("No choices in chunk")
            
            logger.info(f"\n=== 流式输出完成 ===")
            logger.info(f"总chunk数: {chunk_count}")
            logger.info(f"完整内容: {full_content}")
            logger.info(f"Logprobs chunks数: {len(all_logprobs)}")
            
            return {
                'full_content': full_content,
                'all_logprobs': all_logprobs,
                'chunk_count': chunk_count
            }
            
        except Exception as e:
            logger.error(f"流式输出测试失败: {e}")
            raise
    
    def test_non_stream_logprobs(self, prompt: str, model: str = "qwen3-32b"):
        """测试非流式输出模式下的logprobs（对比）"""
        logger.info(f"开始测试非流式输出模式下的logprobs...")
        logger.info(f"模型: {model}")
        logger.info(f"Prompt: {prompt[:100]}...")
        
        try:
            # 非流式调用LLM
            completion = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                logprobs=True,
                top_logprobs=5,
                stream=False,  # 非流式输出
                extra_body={"enable_thinking": False},
            )
            
            logger.info("非流式响应接收完成...")
            
            if completion.choices and len(completion.choices) > 0:
                choice = completion.choices[0]
                
                # 提取content
                content = ""
                if hasattr(choice, 'message') and choice.message:
                    content = choice.message.content or ""
                
                # 提取logprobs
                logprobs = None
                if hasattr(choice, 'message') and choice.message:
                    if hasattr(choice.message, 'logprobs'):
                        logprobs = choice.message.logprobs
                
                logger.info(f"非流式输出完成")
                logger.info(f"内容长度: {len(content)}")
                logger.info(f"Logprobs类型: {type(logprobs)}")
                
                return {
                    'content': content,
                    'logprobs': logprobs
                }
            else:
                logger.warning("非流式输出没有返回choices")
                return {'content': '', 'logprobs': None}
                
        except Exception as e:
            logger.error(f"非流式输出测试失败: {e}")
            raise
    
    def compare_stream_vs_non_stream(self, prompt: str, model: str = "qwen3-32b"):
        """比较流式和非流式输出的logprobs差异"""
        logger.info("开始比较流式和非流式输出的logprobs...")
        
        # 测试流式输出
        stream_result = self.test_stream_logprobs(prompt, model)
        
        # 等待一下，避免API限制
        time.sleep(2)
        
        # 测试非流式输出
        non_stream_result = self.test_non_stream_logprobs(prompt, model)
        
        # 比较结果
        logger.info("\n=== 比较结果 ===")
        logger.info(f"流式输出内容长度: {len(stream_result['full_content'])}")
        logger.info(f"非流式输出内容长度: {len(non_stream_result['content'])}")
        logger.info(f"内容是否相同: {stream_result['full_content'] == non_stream_result['content']}")
        
        logger.info(f"流式输出logprobs chunks数: {len(stream_result['all_logprobs'])}")
        logger.info(f"非流式输出logprobs类型: {type(non_stream_result['logprobs'])}")
        
        # 详细比较logprobs结构
        if stream_result['all_logprobs'] and non_stream_result['logprobs']:
            logger.info("Logprobs结构比较:")
            logger.info(f"  流式输出第一个chunk logprobs类型: {type(stream_result['all_logprobs'][0])}")
            logger.info(f"  非流式输出logprobs类型: {type(non_stream_result['logprobs'])}")
        
        return {
            'stream_result': stream_result,
            'non_stream_result': non_stream_result
        }

def test_stream_logprobs():
    """测试流式输出logprobs"""
    logger.info("开始测试流式输出模式下的logprobs...")
    
    try:
        # 创建测试器
        tester = StreamLogprobsTester()
        
        # 测试prompt
        test_prompt = "请分析这条推文的情感倾向：'I love this new feature! It works perfectly.'"
        
        # 比较流式和非流式输出
        results = tester.compare_stream_vs_non_stream(test_prompt)
        
        logger.info("流式输出logprobs测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise

if __name__ == "__main__":
    test_stream_logprobs()
