experiment:
  name: "semantic_entropy_test"
  description: "Testing semantic entropy uncertainty quantification on TrivialQA"

openai:
  api_key: "${OPENAI_API_KEY}"
  model: "gpt-3.5-turbo"
  parameters:
    temperature: 0.7
    max_tokens: 150

prompts:
  templates:
    - name: "qa_direct"
      template: "请回答：{{question}}"
    - name: "qa_detailed"
      template: "问题：{{question}}\n请提供详细的答案："
  n_samples: 10

datasets:
  - name: "trivialqa"
    type: "trivialqa"
    sample_size: 20
    random_seed: 42

uq_methods:
  - name: "semantic_entropy"
    type: "semantic_entropy"
    parameters:
      similarity_threshold: 0.85
      n_samples: 5

output:
  format: "csv"
  include_metadata: true
  dir: "results/csv"

progress:
  show_visual: true

checkpoint:
  dir: "checkpoints"

cache:
  dir: ".cache"
  max_size_mb: 100

parallel:
  max_workers: 4