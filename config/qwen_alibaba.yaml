experiment:
  name: "qwen_alibaba_uncertainty_test"
  description: "Testing uncertainty quantification on Alibaba Cloud Qwen models"

qwen:
  api_key: "${DASHSCOPE_API_KEY}"
  model: "qwen3-32b"           # 阿里云Qwen3-32B模型
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  nli_model: "qwen3-latest"    # 阿里云Qwen3-Latest用于NLI推理
  parameters:
    temperature: 0.7
    max_tokens: 512
    stream: true                # 启用流式输出

prompts:
  templates:
    - name: "basic_question"
      template: "问题：{{question}}\n请回答："
    - name: "uncertainty_prompt"
      template: "问题：{{question}}\n请给出多个可能的答案并说明不确定性："
    - name: "nli_prompt"
      template: "前提：{{premise}}\n假设：{{hypothesis}}\n关系："
  n_samples: 5

datasets:
  - name: "trivialqa"
    type: "trivialqa"
    sample_size: 20
    random_seed: 42
  - name: "nli_test"
    type: "nli_dataset"
    sample_size: 15
    random_seed: 42

uq_methods:
  - name: "semantic_entropy"
    type: "semantic_entropy"
    parameters:
      similarity_threshold: 0.85
      n_samples: 3
  - name: "semantic_entropy_nli"
    type: "semantic_entropy_nli"
    parameters:
      similarity_threshold: 0.80
      n_samples: 3

output:
  format: "csv"
  include_metadata: true
  dir: "results/csv"

progress:
  show_visual: true

checkpoint:
  dir: "checkpoints"

cache:
  dir: ".cache"
  max_size_mb: 100

parallel:
  max_workers: 2              # 阿里云API有速率限制，使用较少并发