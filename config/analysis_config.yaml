# UQ方法分析配置文件
# 该文件包含所有可配置的参数，提高系统的可维护性

# 数据配置
data:
  # 输入文件路径
  twitter_csv: "data/all_twitter_responses.csv"
  commit_csv: "data/all_commit_responses.csv"

  # 输出目录
  output_dir: "data/Uq_Evaluation_20250731"

  # 缓存目录
  cache_dir: "cache"

  # 样本大小配置
  sample_sizes:
    twitter: [10, 15, 20]
    commit: [10, 15, 20, 25, 30]

  # 数据验证配置
  validation:
    required_columns:
      twitter: ["tweet_index", "prompt_type", "response_text"]
      commit: ["commit_sha", "prompt_type", "response_text"]

    valid_prompt_types:
      twitter: ["sentiment", "sentiment_reason", "sentiment_reason_first"]
      commit: ["single_word", "module_reasoning"]

# NLI模型配置
nli:
  # 默认模型
  default_model: "microsoft/deberta-large-mnli"

  # 可用模型列表
  available_models:
    - "microsoft/deberta-large-mnli"
    - "cross-encoder/nli-deberta-v3-base"
    - "potsawee/deberta-v3-large-mnli"

  # 模型参数
  max_length: 256
  batch_size: 32

  # 缓存配置
  cache:
    enable_csv_cache: true
    enable_pickle_cache: true
    cache_save_interval: 5  # 每处理多少组保存一次缓存
    timestamped_backup: true

# UQ方法配置
uq_methods:
  # 启用的方法
  enabled_methods:
    - "deg_mat_nli"
    - "eccentricity_nli"
    - "eig_val_nli"
    - "deg_mat_jaccard"
    - "eccentricity_jaccard"
    - "eig_val_jaccard"
    - "num_sets"

  # 方法特定配置
  num_sets:
    use_unified_nli: true
    threshold: 0.5

# 性能和内存配置
performance:
  # 内存监控
  memory_monitoring: true
  memory_threshold_mb: 4000  # 内存使用阈值（MB）

  # 批处理配置
  batch_processing:
    enable: true
    intermediate_save_interval: 20  # 每处理多少组保存中间结果

  # 并行处理（未来扩展）
  parallel:
    enable: false
    max_workers: 4

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"

  # 详细日志选项
  detailed_logging:
    nli_computation: true
    cache_operations: true
    memory_usage: true
    validation_errors: true

# 检查点和恢复配置
checkpoint:
  enable: true
  checkpoint_file: "analysis_checkpoint.pkl"
  auto_save_interval: 10  # 每处理多少组自动保存检查点

# 输出配置
output:
  # 文件格式
  encoding: "utf-8-sig"

  # 报告生成
  generate_pivot_tables: true
  generate_summary_stats: true

  # 结果验证
  validate_results: true

# 错误处理配置
error_handling:
  # 错误恢复策略
  retry_failed_computations: true
  max_retries: 3

  # 错误记录
  log_detailed_errors: true
  save_error_results: true  # 将错误结果也保存到输出中

  # 容错模式
  continue_on_error: true
  skip_invalid_data: true

# 原有配置（向后兼容）
analysis:
  queries_per_commit: 30
  prompt_types:
    - single_word
    - module_reasoning
    - reasoning_module

# Repository Configuration
repositories:
  pytorch:
    name: "pytorch/pytorch"
    csv_file: "data/pytorch_commits_sample.csv"
  tensorflow:
    name: "tensorflow/tensorflow"
    csv_file: "data/tensorflow_commits_sample.csv"