experiment:
  name: "qwen_nli_uncertainty_test"
  description: "Testing uncertainty quantification on Qwen models with NLI reasoning"

qwen:
  api_key: "${QWEN_API_KEY}"
  model: "qwen3-32b"           # 主模型用于生成响应
  base_url: "http://localhost:8000/v1"  # Qwen本地服务地址
  nli_model: "qwen3-latest"    # NLI推理专用模型
  parameters:
    temperature: 0.7
    max_tokens: 512
    stream: true                # 启用流式输出

prompts:
  templates:
    - name: "nli_premise"
      template: "Premise: {{premise}}"
    - name: "nli_hypothesis"
      template: "Hypothesis: {{hypothesis}}"
    - name: "nli_question"
      template: "Question: {{question}}"
  n_samples: 5

datasets:
  - name: "nli_test"
    type: "nli_dataset"         # NLI数据集类型
    sample_size: 50
    random_seed: 42

uq_methods:
  - name: "semantic_entropy_nli"
    type: "semantic_entropy_nli"
    parameters:
      similarity_threshold: 0.85
      n_samples: 3

output:
  format: "csv"
  include_metadata: true
  dir: "results/csv"

progress:
 show_visual: true

checkpoint:
  dir: "checkpoints"

cache:
  dir: ".cache"
  max_size_mb: 100

parallel:
  max_workers: 2