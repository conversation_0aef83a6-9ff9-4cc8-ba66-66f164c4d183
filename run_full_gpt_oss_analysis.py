#!/usr/bin/env python3
"""
完整的GPT-OSS:20b UQ分析运行脚本
运行所有7个GPT-OSS变体方法并保存结果到MongoDB
"""

import sys
import os
import subprocess
import logging
from datetime import datetime
from pymongo import MongoClient

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def check_prerequisites():
    """检查运行前置条件"""
    print("🔍 检查运行前置条件...")
    
    # 1. 检查gpt-oss:20b模型
    try:
        result = subprocess.run(['curl', '-s', 'http://localhost:11434/api/tags'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and 'gpt-oss:20b' in result.stdout:
            print("✅ gpt-oss:20b模型可用")
        else:
            print("❌ gpt-oss:20b模型不可用")
            return False
    except Exception as e:
        print(f"❌ 检查Ollama服务失败: {e}")
        return False
    
    # 2. 检查MongoDB连接
    try:
        client = MongoClient('localhost', 27017, serverSelectionTimeoutMS=5000)
        db = client['LLM-UQ']
        
        # 检查源数据
        source_col = db['response_collections_grouped']
        source_count = source_col.count_documents({'dataset_source': 'counterfactual_data'})
        print(f"✅ MongoDB连接正常，源数据: {source_count} 个任务组")
        
        if source_count == 0:
            print("❌ 没有找到源数据")
            return False
        
        client.close()
        
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        return False
    
    # 3. 检查配置文件
    config_file = "configs/uq_analysis_gpt_oss.yaml"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    else:
        print(f"✅ 配置文件存在: {config_file}")
    
    return True


def run_gpt_oss_analysis():
    """运行完整的GPT-OSS分析"""
    print("\n🚀 开始完整的GPT-OSS UQ分析")
    print("="*60)
    
    config_file = "configs/uq_analysis_gpt_oss.yaml"
    
    print(f"📋 配置文件: {config_file}")
    print("🔄 开始运行分析...")
    print("⚠️ 注意: 这可能需要较长时间，因为GPT-OSS模型响应较慢")
    
    try:
        # 运行分析脚本
        cmd = [
            sys.executable, 
            "analyze_uq_from_mongo.py",
            "--config", config_file
        ]
        
        print(f"💻 执行命令: {' '.join(cmd)}")
        
        start_time = datetime.now()
        
        # 使用实时输出
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 universal_newlines=True, bufsize=1)
        
        print("\n📤 实时输出:")
        print("-" * 50)
        
        output_lines = []
        for line in process.stdout:
            print(line.rstrip())
            output_lines.append(line.rstrip())
        
        process.wait()
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        if process.returncode == 0:
            print(f"\n✅ 分析成功完成！")
            print(f"⏱️ 总耗时: {duration:.1f}秒 ({duration/60:.1f}分钟)")
            return True, output_lines
        else:
            print(f"\n❌ 分析失败！返回码: {process.returncode}")
            print(f"⏱️ 耗时: {duration:.1f}秒")
            return False, output_lines
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断分析")
        return False, []
    except Exception as e:
        print(f"\n❌ 运行分析时出错: {e}")
        return False, []


def analyze_results():
    """分析运行结果"""
    print("\n📊 分析运行结果")
    print("="*50)
    
    try:
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        col = db['UQ_results']
        
        # 查找所有GPT-OSS结果
        gpt_oss_results = list(col.find({
            'method.method_name': {'$regex': 'GPT-OSS-20B'}
        }))
        
        print(f"🎯 找到 {len(gpt_oss_results)} 个GPT-OSS结果")
        
        if len(gpt_oss_results) == 0:
            print("❌ 没有找到GPT-OSS结果")
            return False
        
        # 按方法分组统计
        method_stats = {}
        for result in gpt_oss_results:
            method_name = result.get('method', {}).get('method_name', 'Unknown')
            if method_name not in method_stats:
                method_stats[method_name] = {
                    'count': 0,
                    'successful': 0,
                    'errors': 0,
                    'avg_time': 0,
                    'total_time': 0
                }
            
            method_stats[method_name]['count'] += 1
            
            outputs = result.get('outputs', {})
            if 'error' in outputs:
                method_stats[method_name]['errors'] += 1
            else:
                method_stats[method_name]['successful'] += 1
            
            if 'execution_time' in outputs:
                method_stats[method_name]['total_time'] += outputs['execution_time']
        
        # 计算平均时间
        for method_name, stats in method_stats.items():
            if stats['successful'] > 0:
                stats['avg_time'] = stats['total_time'] / stats['successful']
        
        # 显示统计结果
        print(f"\n📈 方法统计:")
        print(f"{'方法名':<35} {'总数':<6} {'成功':<6} {'错误':<6} {'平均时间':<10}")
        print("-" * 80)
        
        total_results = 0
        total_successful = 0
        total_errors = 0
        
        for method_name, stats in method_stats.items():
            success_rate = stats['successful'] / stats['count'] * 100 if stats['count'] > 0 else 0
            avg_time_str = f"{stats['avg_time']:.1f}s" if stats['avg_time'] > 0 else "N/A"
            
            print(f"{method_name:<35} {stats['count']:<6} {stats['successful']:<6} {stats['errors']:<6} {avg_time_str:<10}")
            
            total_results += stats['count']
            total_successful += stats['successful']
            total_errors += stats['errors']
        
        print("-" * 80)
        print(f"{'总计':<35} {total_results:<6} {total_successful:<6} {total_errors:<6}")
        
        success_rate = total_successful / total_results * 100 if total_results > 0 else 0
        print(f"\n🎯 总体成功率: {success_rate:.1f}%")
        
        # 显示样本结果
        print(f"\n🔍 样本结果:")
        for i, result in enumerate(gpt_oss_results[:3], 1):
            method_name = result.get('method', {}).get('method_name', 'Unknown')
            outputs = result.get('outputs', {})
            uncertainty_score = outputs.get('uncertainty_score', 'N/A')
            
            print(f"  {i}. {method_name}: {uncertainty_score}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 分析结果时出错: {e}")
        return False


def main():
    """主函数"""
    print("🚀 完整GPT-OSS:20b UQ分析")
    print("="*60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，无法继续")
        return
    
    # 2. 运行分析
    success, output_lines = run_gpt_oss_analysis()
    
    if not success:
        print("\n❌ 分析运行失败")
        return
    
    # 3. 分析结果
    if not analyze_results():
        print("\n⚠️ 结果分析有问题，但分析可能已成功")
    
    print(f"\n✅ 完整GPT-OSS UQ分析完成！")
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n💡 后续步骤:")
    print(f"   1. 检查MongoDB中的UQ_results collection")
    print(f"   2. 对比GPT-OSS与原始方法的结果差异")
    print(f"   3. 生成详细的对比分析报告")


if __name__ == "__main__":
    main()
