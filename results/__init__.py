"""
Comprehensive Results Management for LLM Uncertainty Quantification

This package provides a complete framework for organizing, storing, and analyzing
uncertainty quantification results across different datasets, sample configurations,
estimators, and prompts.

The structure organizes results as:
results/
├── datasets/
│   └── {dataset_name}/
│       └── samples_{sample_config}/
│           └── {estimator_name}/
│               └── {prompt_type}/
│                   ├── task_config.json
│                   ├── individual_results.csv
│                   ├── aggregate_results.csv
│                   ├── raw_outputs.jsonl
│                   ├── uq_distribution.png
│                   └── summary_report.json
├── comparisons/
├── visualizations/
└── reports/
"""

from .results_manager import ResultsManager, TaskConfiguration
from .visualization_manager import VisualizationManager
from .report_generator import ReportGenerator

__all__ = [
    'ResultsManager',
    'TaskConfiguration',
    'VisualizationManager', 
    'ReportGenerator'
]