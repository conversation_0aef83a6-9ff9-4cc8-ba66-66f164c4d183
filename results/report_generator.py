"""
Report Generator for Uncertainty Quantification Results

Generates comprehensive reports and summaries for UQ analysis results.
"""

import json
import csv
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import os


class ReportGenerator:
    """Generate comprehensive reports for UQ analysis."""
    
    def __init__(self, base_dir: str = "./results"):
        self.base_dir = Path(base_dir)
        self.reports_dir = self.base_dir / "reports"
        self.reports_dir.mkdir(exist_ok=True)
    
    def generate_task_report(self, 
                           task_results: Dict[str, Any],
                           save_path: Optional[str] = None) -> str:
        """
        Generate comprehensive report for a single task.
        
        Args:
            task_results: Results dictionary from ResultsManager
            save_path: Optional custom save path
            
        Returns:
            Path to generated report
        """
        config = task_results.get("config", {})
        aggregate = task_results.get("aggregate_results", {})
        individual = task_results.get("individual_results", [])
        uq_analysis = task_results.get("uq_analysis", {})
        
        report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "report_type": "individual_task_report"
            },
            "task_configuration": config,
            "summary_statistics": self._generate_summary_stats(aggregate, individual),
            "uncertainty_analysis": self._analyze_uncertainty_levels(individual),
            "category_breakdown": self._analyze_by_category(individual),
            "quality_metrics": self._calculate_quality_metrics(individual),
            "recommendations": self._generate_recommendations(aggregate, individual),
            "raw_data_summary": {
                "total_questions": len(individual),
                "valid_results": len([i for i in individual if i.get("semantic_entropy") is not None]),
                "missing_data_count": len([i for i in individual if i.get("semantic_entropy") is None])
            }
        }
        
        if save_path is None:
            dataset = config.get("dataset", {}).get("name", "unknown")
            estimator = config.get("estimator", {}).get("name", "unknown")
            prompt = config.get("prompt", {}).get("type", "unknown")
            
            filename = f"report_{dataset}_{estimator}_{prompt}.json"
            save_path = self.reports_dir / filename
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=float)
        
        return str(save_path)
    
    def generate_comparison_report(self,
                                 task_results_list: List[Dict[str, Any]],
                                 save_path: Optional[str] = None) -> str:
        """
        Generate comparative report across multiple tasks.
        
        Args:
            task_results_list: List of task results to compare
            save_path: Optional custom save path
            
        Returns:
            Path to generated report
        """
        if not task_results_list:
            return ""
        
        comparison_data = []
        for task_results in task_results_list:
            config = task_results.get("config", {})
            aggregate = task_results.get("aggregate_results", {})
            individual = task_results.get("individual_results", [])
            
            comparison_data.append({
                "dataset": config.get("dataset", {}).get("name", "unknown"),
                "sample_config": config.get("sampling", {}).get("config", "unknown"),
                "estimator": config.get("estimator", {}).get("name", "unknown"),
                "prompt": config.get("prompt", {}).get("type", "unknown"),
                "sample_size": aggregate.get("sample_size", 0),
                "entropy_mean": aggregate.get("entropy_mean", 0),
                "entropy_std": aggregate.get("entropy_std", 0),
                "entropy_min": aggregate.get("entropy_min", 0),
                "entropy_max": aggregate.get("entropy_max", 0),
                "high_uncertainty_count": aggregate.get("high_uncertainty_count", 0),
                "medium_uncertainty_count": aggregate.get("medium_uncertainty_count", 0),
                "low_uncertainty_count": aggregate.get("low_uncertainty_count", 0),
                "valid_results_ratio": len([i for i in individual if i.get("semantic_entropy") is not None]) / len(individual) if individual else 0
            })
        
        report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "report_type": "comparative_analysis",
                "num_configurations": len(task_results_list)
            },
            "comparative_analysis": {
                "best_performing": self._identify_best_performing(comparison_data),
                "statistical_summary": self._generate_statistical_summary(comparison_data),
                "configuration_rankings": self._rank_configurations(comparison_data),
                "insights": self._generate_comparative_insights(comparison_data)
            },
            "detailed_comparison": comparison_data
        }
        
        if save_path is None:
            filename = f"comparison_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            save_path = self.reports_dir / filename
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=float)
        
        return str(save_path)
    
    def generate_summary_csv(self,
                           task_results_list: List[Dict[str, Any]],
                           save_path: Optional[str] = None) -> str:
        """
        Generate summary CSV for all tasks.
        
        Args:
            task_results_list: List of task results
            save_path: Optional custom save path
            
        Returns:
            Path to generated CSV
        """
        if not task_results_list:
            return ""
        
        summary_data = []
        for task_results in task_results_list:
            config = task_results.get("config", {})
            aggregate = task_results.get("aggregate_results", {})
            individual = task_results.get("individual_results", [])
            
            summary_data.append({
                "dataset": config.get("dataset", {}).get("name", "unknown"),
                "sample_config": config.get("sampling", {}).get("config", "unknown"),
                "estimator": config.get("estimator", {}).get("name", "unknown"),
                "prompt_type": config.get("prompt", {}).get("type", "unknown"),
                "task_id": config.get("task_id", "unknown"),
                "timestamp": config.get("timestamp", "unknown"),
                "sample_size": aggregate.get("sample_size", 0),
                "entropy_mean": aggregate.get("entropy_mean", 0),
                "entropy_std": aggregate.get("entropy_std", 0),
                "entropy_min": aggregate.get("entropy_min", 0),
                "entropy_max": aggregate.get("entropy_max", 0),
                "entropy_median": aggregate.get("entropy_median", 0),
                "entropy_q25": aggregate.get("entropy_q25", 0),
                "entropy_q75": aggregate.get("entropy_q75", 0),
                "high_uncertainty_count": aggregate.get("high_uncertainty_count", 0),
                "medium_uncertainty_count": aggregate.get("medium_uncertainty_count", 0),
                "low_uncertainty_count": aggregate.get("low_uncertainty_count", 0),
                "total_responses": aggregate.get("total_responses", 0),
                "avg_responses_per_question": aggregate.get("avg_responses_per_question", 0),
                "avg_cluster_count": aggregate.get("avg_cluster_count", 0),
                "coverage": aggregate.get("coverage", 0),
                "valid_results_ratio": len([i for i in individual if i.get("semantic_entropy") is not None]) / len(individual) if individual else 0
            })
        
        if save_path is None:
            filename = f"summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            save_path = self.reports_dir / filename
        
        with open(save_path, 'w', newline='', encoding='utf-8') as csvfile:
            if summary_data:
                writer = csv.DictWriter(csvfile, fieldnames=summary_data[0].keys())
                writer.writeheader()
                writer.writerows(summary_data)
        
        return str(save_path)
    
    def _generate_summary_stats(self, aggregate: Dict[str, Any], individual: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics."""
        return {
            "total_questions": aggregate.get("sample_size", 0),
            "mean_entropy": aggregate.get("entropy_mean", 0),
            "std_entropy": aggregate.get("entropy_std", 0),
            "min_entropy": aggregate.get("entropy_min", 0),
            "max_entropy": aggregate.get("entropy_max", 0),
            "uncertainty_distribution": {
                "high": aggregate.get("high_uncertainty_count", 0),
                "medium": aggregate.get("medium_uncertainty_count", 0),
                "low": aggregate.get("low_uncertainty_count", 0)
            },
            "response_statistics": {
                "total_responses": aggregate.get("total_responses", 0),
                "avg_responses_per_question": aggregate.get("avg_responses_per_question", 0),
                "avg_cluster_count": aggregate.get("avg_cluster_count", 0)
            }
        }
    
    def _analyze_uncertainty_levels(self, individual: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze uncertainty levels across questions."""
        if not individual:
            return {}
        
        levels = {}
        for level in ['high', 'medium', 'low']:
            level_questions = [i for i in individual if i.get("uncertainty_level") == level]
            levels[level] = {
                "count": len(level_questions),
                "avg_entropy": sum(i.get("semantic_entropy", 0) for i in level_questions) / len(level_questions) if level_questions else 0,
                "categories": list(set(i.get("category", "unknown") for i in level_questions))
            }
        
        return levels
    
    def _analyze_by_category(self, individual: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze results by category."""
        if not individual:
            return {}
        
        categories = {}
        for category in set(i.get("category", "unknown") for i in individual):
            category_questions = [i for i in individual if i.get("category") == category]
            entropies = [i.get("semantic_entropy", 0) for i in category_questions if i.get("semantic_entropy") is not None]
            
            categories[category] = {
                "count": len(category_questions),
                "avg_entropy": sum(entropies) / len(entropies) if entropies else 0,
                "min_entropy": min(entropies) if entropies else 0,
                "max_entropy": max(entropies) if entropies else 0,
                "uncertainty_levels": {
                    "high": len([i for i in category_questions if i.get("uncertainty_level") == "high"]),
                    "medium": len([i for i in category_questions if i.get("uncertainty_level") == "medium"]),
                    "low": len([i for i in category_questions if i.get("uncertainty_level") == "low"])
                }
            }
        
        return categories
    
    def _calculate_quality_metrics(self, individual: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate quality metrics."""
        if not individual:
            return {}
        
        total_questions = len(individual)
        valid_results = len([i for i in individual if i.get("semantic_entropy") is not None])
        
        return {
            "total_questions": total_questions,
            "valid_results": valid_results,
            "coverage_ratio": valid_results / total_questions if total_questions > 0 else 0,
            "missing_data_count": total_questions - valid_results,
            "response_quality": {
                "avg_response_length": sum(i.get("avg_response_length", 0) for i in individual) / len(individual) if individual else 0,
                "avg_cluster_count": sum(i.get("num_clusters", 0) for i in individual) / len(individual) if individual else 0
            }
        }
    
    def _generate_recommendations(self, aggregate: Dict[str, Any], individual: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on results."""
        recommendations = []
        
        # Check coverage
        total_questions = len(individual)
        valid_results = len([i for i in individual if i.get("semantic_entropy") is not None])
        coverage_ratio = valid_results / total_questions if total_questions > 0 else 0
        
        if coverage_ratio < 0.9:
            recommendations.append(f"Improve data coverage: currently {coverage_ratio:.1%} valid results")
        
        # Check uncertainty levels
        high_count = aggregate.get("high_uncertainty_count", 0)
        medium_count = aggregate.get("medium_uncertainty_count", 0)
        low_count = aggregate.get("low_uncertainty_count", 0)
        
        total_with_levels = high_count + medium_count + low_count
        if total_with_levels > 0:
            high_ratio = high_count / total_with_levels
            if high_ratio > 0.5:
                recommendations.append("High uncertainty detected in over 50% of questions - consider improving model or prompt design")
            elif high_ratio < 0.1:
                recommendations.append("Very low uncertainty - consider more challenging questions")
        
        # Check response quality
        avg_responses = aggregate.get("avg_responses_per_question", 0)
        if avg_responses < 5:
            recommendations.append("Consider increasing number of responses per question for better uncertainty estimation")
        
        if not recommendations:
            recommendations.append("Results appear satisfactory - no specific recommendations")
        
        return recommendations
    
    def _identify_best_performing(self, comparison_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Identify best performing configurations."""
        if not comparison_data:
            return {}
        
        df = pd.DataFrame(comparison_data)
        
        # Best by lowest entropy (indicating more consistent responses)
        best_entropy = df.loc[df['entropy_mean'].idxmin()]
        
        # Best by highest coverage
        best_coverage = df.loc[df['valid_results_ratio'].idxmax()]
        
        return {
            "lowest_uncertainty": {
                "configuration": f"{best_entropy['dataset']} - {best_entropy['estimator']} - {best_entropy['prompt']}",
                "entropy_mean": float(best_entropy['entropy_mean']),
                "sample_size": int(best_entropy['sample_size'])
            },
            "highest_coverage": {
                "configuration": f"{best_coverage['dataset']} - {best_coverage['estimator']} - {best_coverage['prompt']}",
                "coverage_ratio": float(best_coverage['valid_results_ratio']),
                "sample_size": int(best_coverage['sample_size'])
            }
        }
    
    def _generate_statistical_summary(self, comparison_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate statistical summary for comparison."""
        if not comparison_data:
            return {}
        
        df = pd.DataFrame(comparison_data)
        
        return {
            "entropy_statistics": {
                "mean": float(df['entropy_mean'].mean()),
                "std": float(df['entropy_mean'].std()),
                "min": float(df['entropy_mean'].min()),
                "max": float(df['entropy_mean'].max()),
                "median": float(df['entropy_mean'].median())
            },
            "sample_size_statistics": {
                "mean": float(df['sample_size'].mean()),
                "std": float(df['sample_size'].std()),
                "min": float(df['sample_size'].min()),
                "max": float(df['sample_size'].max())
            },
            "coverage_statistics": {
                "mean": float(df['valid_results_ratio'].mean()),
                "std": float(df['valid_results_ratio'].std()),
                "min": float(df['valid_results_ratio'].min()),
                "max": float(df['valid_results_ratio'].max())
            }
        }
    
    def _rank_configurations(self, comparison_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank configurations by performance."""
        if not comparison_data:
            return []
        
        df = pd.DataFrame(comparison_data)
        
        # Create composite score (lower entropy + higher coverage = better)
        df['composite_score'] = (
            (1 - (df['entropy_mean'] - df['entropy_mean'].min()) / 
                  (df['entropy_mean'].max() - df['entropy_mean'].min())) * 0.7 +
            df['valid_results_ratio'] * 0.3
        )
        
        df_sorted = df.sort_values('composite_score', ascending=False)
        
        rankings = []
        for idx, (_, row) in enumerate(df_sorted.iterrows(), 1):
            rankings.append({
                "rank": idx,
                "dataset": row['dataset'],
                "estimator": row['estimator'],
                "prompt": row['prompt'],
                "composite_score": float(row['composite_score']),
                "entropy_mean": float(row['entropy_mean']),
                "coverage_ratio": float(row['valid_results_ratio']),
                "sample_size": int(row['sample_size'])
            })
        
        return rankings
    
    def _generate_comparative_insights(self, comparison_data: List[Dict[str, Any]]) -> List[str]:
        """Generate insights from comparative analysis."""
        if not comparison_data:
            return []
        
        df = pd.DataFrame(comparison_data)
        insights = []
        
        # High vs low entropy patterns
        high_entropy_configs = df[df['entropy_mean'] > df['entropy_mean'].quantile(0.75)]
        low_entropy_configs = df[df['entropy_mean'] < df['entropy_mean'].quantile(0.25)]
        
        if not high_entropy_configs.empty and not low_entropy_configs.empty:
            insights.append(f"High entropy configurations ({len(high_entropy_configs)}) have mean entropy {high_entropy_configs['entropy_mean'].mean():.2f} vs low entropy {low_entropy_configs['entropy_mean'].mean():.2f}")
        
        # Dataset patterns
        dataset_stats = df.groupby('dataset')['entropy_mean'].agg(['mean', 'std'])
        most_consistent = dataset_stats['std'].idxmin()
        insights.append(f"Most consistent dataset: {most_consistent} (std: {dataset_stats.loc[most_consistent, 'std']:.2f})")
        
        # Estimator patterns
        estimator_stats = df.groupby('estimator')['entropy_mean'].mean()
        best_estimator = estimator_stats.idxmin()
        insights.append(f"Best performing estimator: {best_estimator} (mean entropy: {estimator_stats[best_estimator]:.2f})")
        
        # Sample size correlation
        correlation = df['sample_size'].corr(df['entropy_mean'])
        if abs(correlation) > 0.3:
            insights.append(f"Moderate correlation between sample size and entropy: {correlation:.2f}")
        
        return insights