"""
Visualization Manager for Uncertainty Quantification Results

Provides comprehensive visualization capabilities for UQ analysis including
distribution plots, uncertainty level analysis, and comparative visualizations.
"""

import json
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')


class VisualizationManager:
    """Comprehensive visualization manager for UQ results."""
    
    def __init__(self, base_dir: str = "./results"):
        self.base_dir = Path(base_dir)
        self.visualization_dir = self.base_dir / "visualizations"
        self.visualization_dir.mkdir(exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def create_uq_distribution_plot(self, 
                                  task_results: Dict[str, Any],
                                  save_path: Optional[str] = None) -> str:
        """
        Create comprehensive UQ distribution visualization.
        
        Args:
            task_results: Results dictionary from ResultsManager
            save_path: Optional custom save path
            
        Returns:
            Path to saved visualization
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Uncertainty Quantification Analysis', fontsize=16, fontweight='bold')
        
        individual_results = task_results.get("individual_results", [])
        if not individual_results:
            print("No individual results found")
            return ""
        
        df = pd.DataFrame(individual_results)
        
        # 1. Entropy Distribution
        self._plot_entropy_distribution(df, axes[0, 0])
        
        # 2. Uncertainty Level Distribution
        self._plot_uncertainty_levels(df, axes[0, 1])
        
        # 3. Category-wise Analysis
        self._plot_category_analysis(df, axes[1, 0])
        
        # 4. Response Count vs Uncertainty
        self._plot_response_analysis(df, axes[1, 1])
        
        plt.tight_layout()
        
        # Save plot
        if save_path is None:
            config = task_results.get("config", {})
            dataset = config.get("dataset", {}).get("name", "unknown")
            estimator = config.get("estimator", {}).get("name", "unknown")
            prompt = config.get("prompt", {}).get("type", "unknown")
            
            filename = f"uq_distribution_{dataset}_{estimator}_{prompt}.png"
            save_path = self.visualization_dir / filename
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def _plot_entropy_distribution(self, df: pd.DataFrame, ax):
        """Plot entropy distribution with KDE."""
        if 'semantic_entropy' not in df.columns:
            ax.text(0.5, 0.5, 'No entropy data', ha='center', va='center')
            return
            
        entropies = df['semantic_entropy'].dropna()
        
        # Histogram with KDE
        ax.hist(entropies, bins=20, alpha=0.7, density=True, color='skyblue', edgecolor='black')
        
        # Add KDE line
        try:
            from scipy.stats import gaussian_kde
            kde = gaussian_kde(entropies)
            x_range = np.linspace(entropies.min(), entropies.max(), 100)
            ax.plot(x_range, kde(x_range), 'r-', linewidth=2, label='KDE')
        except:
            pass
        
        # Add statistics
        mean_ent = entropies.mean()
        median_ent = entropies.median()
        ax.axvline(mean_ent, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_ent:.2f}')
        ax.axvline(median_ent, color='green', linestyle='--', alpha=0.7, label=f'Median: {median_ent:.2f}')
        
        ax.set_xlabel('Semantic Entropy')
        ax.set_ylabel('Density')
        ax.set_title('Entropy Distribution')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_uncertainty_levels(self, df: pd.DataFrame, ax):
        """Plot uncertainty level distribution."""
        if 'uncertainty_level' not in df.columns:
            ax.text(0.5, 0.5, 'No uncertainty level data', ha='center', va='center')
            return
            
        level_counts = df['uncertainty_level'].value_counts()
        
        # Create bar chart
        colors = {'high': 'red', 'medium': 'orange', 'low': 'green'}
        bars = ax.bar(level_counts.index, level_counts.values, 
                     color=[colors.get(level, 'gray') for level in level_counts.index])
        
        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')
        
        ax.set_xlabel('Uncertainty Level')
        ax.set_ylabel('Count')
        ax.set_title('Uncertainty Level Distribution')
        ax.grid(True, alpha=0.3)
    
    def _plot_category_analysis(self, df: pd.DataFrame, ax):
        """Plot category-wise uncertainty analysis."""
        if 'category' not in df.columns or 'semantic_entropy' not in df.columns:
            ax.text(0.5, 0.5, 'No category data', ha='center', va='center')
            return
            
        # Group by category
        category_stats = df.groupby('category')['semantic_entropy'].agg(['mean', 'std', 'count'])
        category_stats = category_stats.sort_values('mean', ascending=False)
        
        # Create bar chart with error bars
        bars = ax.bar(range(len(category_stats)), category_stats['mean'], 
                     yerr=category_stats['std'], capsize=5, alpha=0.7)
        
        ax.set_xlabel('Category')
        ax.set_ylabel('Mean Entropy')
        ax.set_title('Uncertainty by Category')
        ax.set_xticks(range(len(category_stats)))
        ax.set_xticklabels(category_stats.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
    
    def _plot_response_analysis(self, df: pd.DataFrame, ax):
        """Plot response count vs uncertainty relationship."""
        if 'num_responses' not in df.columns or 'semantic_entropy' not in df.columns:
            ax.text(0.5, 0.5, 'No response/entropy data', ha='center', va='center')
            return
            
        # Scatter plot with trend line
        ax.scatter(df['num_responses'], df['semantic_entropy'], alpha=0.6)
        
        # Add trend line
        try:
            z = np.polyfit(df['num_responses'], df['semantic_entropy'], 1)
            p = np.poly1d(z)
            ax.plot(df['num_responses'].sort_values(), p(df['num_responses'].sort_values()), 
                   "r--", alpha=0.8, label=f'Trend: r={np.corrcoef(df["num_responses"], df["semantic_entropy"])[0,1]:.2f}')
        except:
            pass
        
        ax.set_xlabel('Number of Responses')
        ax.set_ylabel('Semantic Entropy')
        ax.set_title('Response Count vs Uncertainty')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def create_comparison_plot(self, 
                             task_results_list: List[Dict[str, Any]],
                             comparison_metric: str = "entropy_mean",
                             save_path: Optional[str] = None) -> str:
        """
        Create comparison plot across different configurations.
        
        Args:
            task_results_list: List of task results to compare
            comparison_metric: Metric to compare ('entropy_mean', 'sample_size', etc.)
            save_path: Optional custom save path
            
        Returns:
            Path to saved visualization
        """
        if not task_results_list:
            print("No task results provided for comparison")
            return ""
        
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle('Configuration Comparison Analysis', fontsize=16, fontweight='bold')
        
        # Prepare comparison data
        comparison_data = []
        for task_results in task_results_list:
            config = task_results.get("config", {})
            aggregate = task_results.get("aggregate_results", {})
            
            comparison_data.append({
                "dataset": config.get("dataset", {}).get("name", "unknown"),
                "estimator": config.get("estimator", {}).get("name", "unknown"),
                "prompt": config.get("prompt", {}).get("type", "unknown"),
                "sample_size": aggregate.get("sample_size", 0),
                "entropy_mean": aggregate.get("entropy_mean", 0),
                "entropy_std": aggregate.get("entropy_std", 0),
                "high_uncertainty_count": aggregate.get("high_uncertainty_count", 0)
            })
        
        df_comparison = pd.DataFrame(comparison_data)
        
        # 1. Bar chart comparison
        self._plot_metric_comparison(df_comparison, comparison_metric, axes[0])
        
        # 2. Scatter plot with error bars
        self._plot_scatter_comparison(df_comparison, axes[1])
        
        plt.tight_layout()
        
        if save_path is None:
            filename = f"comparison_{comparison_metric}.png"
            save_path = self.visualization_dir / filename
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def _plot_metric_comparison(self, df: pd.DataFrame, metric: str, ax):
        """Plot bar chart comparison of specified metric."""
        if metric not in df.columns:
            ax.text(0.5, 0.5, f'Metric {metric} not found', ha='center', va='center')
            return
            
        # Create labels
        labels = [f"{row['dataset']}\n{row['estimator']}\n{row['prompt']}" 
                 for _, row in df.iterrows()]
        
        bars = ax.bar(range(len(df)), df[metric], alpha=0.7)
        
        # Color bars by value
        norm = plt.Normalize(df[metric].min(), df[metric].max())
        colors = plt.cm.RdYlBu_r(norm(df[metric]))
        for bar, color in zip(bars, colors):
            bar.set_color(color)
        
        ax.set_xlabel('Configuration')
        ax.set_ylabel(metric.replace('_', ' ').title())
        ax.set_title(f'{metric.replace("_", " ").title()} Comparison')
        ax.set_xticks(range(len(labels)))
        ax.set_xticklabels(labels, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
    
    def _plot_scatter_comparison(self, df: pd.DataFrame, ax):
        """Plot scatter comparison with error bars."""
        # Scatter plot of entropy mean vs sample size
        x = df['sample_size']
        y = df['entropy_mean']
        yerr = df['entropy_std']
        
        scatter = ax.errorbar(x, y, yerr=yerr, fmt='o', alpha=0.7, capsize=5)
        
        # Add labels for each point
        for i, row in df.iterrows():
            label = f"{row['dataset'][:8]}...\n{row['estimator'][:8]}..."
            ax.annotate(label, (x.iloc[i], y.iloc[i]), 
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=8, alpha=0.7)
        
        ax.set_xlabel('Sample Size')
        ax.set_ylabel('Mean Entropy')
        ax.set_title('Entropy vs Sample Size')
        ax.grid(True, alpha=0.3)
    
    def create_uncertainty_heatmap(self, 
                                 task_results: Dict[str, Any],
                                 save_path: Optional[str] = None) -> str:
        """
        Create uncertainty level heatmap.
        
        Args:
            task_results: Results dictionary from ResultsManager
            save_path: Optional custom save path
            
        Returns:
            Path to saved visualization
        """
        individual_results = task_results.get("individual_results", [])
        if not individual_results:
            print("No individual results found")
            return ""
        
        df = pd.DataFrame(individual_results)
        
        # Create pivot table for heatmap
        if 'category' in df.columns and 'uncertainty_level' in df.columns:
            heatmap_data = pd.crosstab(df['category'], df['uncertainty_level'])
        else:
            print("Insufficient data for heatmap")
            return ""
        
        plt.figure(figsize=(10, 8))
        
        # Create heatmap
        sns.heatmap(heatmap_data, annot=True, fmt='d', cmap='YlOrRd', 
                   cbar_kws={'label': 'Count'})
        
        plt.title('Uncertainty Level Distribution by Category')
        plt.xlabel('Uncertainty Level')
        plt.ylabel('Category')
        plt.tight_layout()
        
        if save_path is None:
            config = task_results.get("config", {})
            dataset = config.get("dataset", {}).get("name", "unknown")
            filename = f"uncertainty_heatmap_{dataset}.png"
            save_path = self.visualization_dir / filename
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_summary_dashboard(self, 
                               task_results_list: List[Dict[str, Any]],
                               save_path: Optional[str] = None) -> str:
        """
        Create comprehensive summary dashboard.
        
        Args:
            task_results_list: List of task results
            save_path: Optional custom save path
            
        Returns:
            Path to saved dashboard
        """
        if not task_results_list:
            print("No task results provided for dashboard")
            return ""
        
        fig = plt.figure(figsize=(20, 12))
        
        # Create grid layout
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # Summary statistics
        summary_data = []
        for task_results in task_results_list:
            config = task_results.get("config", {})
            aggregate = task_results.get("aggregate_results", {})
            
            summary_data.append({
                "dataset": config.get("dataset", {}).get("name", "unknown"),
                "estimator": config.get("estimator", {}).get("name", "unknown"),
                "prompt": config.get("prompt", {}).get("type", "unknown"),
                "sample_size": aggregate.get("sample_size", 0),
                "entropy_mean": aggregate.get("entropy_mean", 0),
                "entropy_std": aggregate.get("entropy_std", 0),
                "high_uncertainty_count": aggregate.get("high_uncertainty_count", 0),
                "medium_uncertainty_count": aggregate.get("medium_uncertainty_count", 0),
                "low_uncertainty_count": aggregate.get("low_uncertainty_count", 0)
            })
        
        df_summary = pd.DataFrame(summary_data)
        
        # 1. Overall performance metrics
        ax1 = fig.add_subplot(gs[0, :2])
        self._plot_overall_metrics(df_summary, ax1)
        
        # 2. Dataset comparison
        ax2 = fig.add_subplot(gs[0, 2])
        self._plot_dataset_comparison(df_summary, ax2)
        
        # 3. Estimator comparison
        ax3 = fig.add_subplot(gs[1, :2])
        self._plot_estimator_comparison(df_summary, ax3)
        
        # 4. Prompt comparison
        ax4 = fig.add_subplot(gs[1, 2])
        self._plot_prompt_comparison(df_summary, ax4)
        
        # 5. Uncertainty distribution summary
        ax5 = fig.add_subplot(gs[2, :])
        self._plot_uncertainty_summary(df_summary, ax5)
        
        fig.suptitle('Uncertainty Quantification Summary Dashboard', 
                    fontsize=18, fontweight='bold')
        
        if save_path is None:
            filename = "uq_summary_dashboard.png"
            save_path = self.visualization_dir / filename
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def _plot_overall_metrics(self, df: pd.DataFrame, ax):
        """Plot overall performance metrics."""
        metrics = ['entropy_mean', 'entropy_std', 'sample_size']
        df_metrics = df[metrics].mean()
        
        bars = ax.bar(range(len(df_metrics)), df_metrics.values, alpha=0.7)
        ax.set_xticks(range(len(df_metrics)))
        ax.set_xticklabels([m.replace('_', ' ').title() for m in metrics])
        ax.set_title('Overall Average Metrics')
        ax.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars, df_metrics.values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.2f}', ha='center', va='bottom')
    
    def _plot_dataset_comparison(self, df: pd.DataFrame, ax):
        """Plot dataset comparison."""
        dataset_stats = df.groupby('dataset')['entropy_mean'].mean()
        
        ax.pie(dataset_stats.values, labels=dataset_stats.index, autopct='%1.1f%%')
        ax.set_title('Entropy Distribution by Dataset')
    
    def _plot_estimator_comparison(self, df: pd.DataFrame, ax):
        """Plot estimator comparison."""
        estimator_stats = df.groupby('estimator')['entropy_mean'].mean().sort_values()
        
        bars = ax.barh(range(len(estimator_stats)), estimator_stats.values, alpha=0.7)
        ax.set_yticks(range(len(estimator_stats)))
        ax.set_yticklabels(estimator_stats.index)
        ax.set_xlabel('Mean Entropy')
        ax.set_title('Estimator Performance Comparison')
        ax.grid(True, alpha=0.3)
    
    def _plot_prompt_comparison(self, df: pd.DataFrame, ax):
        """Plot prompt comparison."""
        prompt_stats = df.groupby('prompt')['entropy_mean'].mean()
        
        ax.bar(range(len(prompt_stats)), prompt_stats.values, alpha=0.7)
        ax.set_xticks(range(len(prompt_stats)))
        ax.set_xticklabels(prompt_stats.index, rotation=45, ha='right')
        ax.set_ylabel('Mean Entropy')
        ax.set_title('Prompt Type Comparison')
        ax.grid(True, alpha=0.3)
    
    def _plot_uncertainty_summary(self, df: pd.DataFrame, ax):
        """Plot uncertainty level summary."""
        uncertainty_cols = ['high_uncertainty_count', 'medium_uncertainty_count', 'low_uncertainty_count']
        uncertainty_data = df[uncertainty_cols].sum()
        
        x_pos = np.arange(len(uncertainty_data))
        bars = ax.bar(x_pos, uncertainty_data.values, alpha=0.7,
                     color=['red', 'orange', 'green'])
        
        ax.set_xticks(x_pos)
        ax.set_xticklabels([col.replace('_', ' ').title() for col in uncertainty_data.index])
        ax.set_ylabel('Count')
        ax.set_title('Overall Uncertainty Level Distribution')
        ax.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars, uncertainty_data.values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(value)}', ha='center', va='bottom')