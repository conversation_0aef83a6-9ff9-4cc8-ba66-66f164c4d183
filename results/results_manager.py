"""
Enhanced Results Manager for LLM Uncertainty Quantification

Provides comprehensive organization of UQ results by:
- Dataset name
- Sample configuration 
- Estimator method
- Prompt type

Includes detailed UQ metrics, per-sample analysis, and distribution statistics.
"""

import csv
import json
import os
import uuid
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd
from pathlib import Path


class TaskConfiguration:
    """Complete configuration for uncertainty analysis task."""
    
    def __init__(self,
                 dataset_name: str,
                 sample_config: str,
                 estimator_name: str,
                 prompt_type: str,
                 dataset_config: Dict[str, Any],
                 estimator_config: Dict[str, Any],
                 prompt_config: Dict[str, Any],
                 environment_config: Dict[str, Any] = None):
        
        self.dataset_name = dataset_name
        self.sample_config = sample_config
        self.estimator_name = estimator_name
        self.prompt_type = prompt_type
        self.dataset_config = dataset_config
        self.estimator_config = estimator_config
        self.prompt_config = prompt_config
        self.environment_config = environment_config or {}
        
        self.task_id = self._generate_task_id()
        self.timestamp = datetime.now().isoformat()
        self.created_at = datetime.now()
    
    def _generate_task_id(self) -> str:
        """Generate unique task ID based on configuration."""
        config_str = f"{self.dataset_name}_{self.sample_config}_{self.estimator_name}_{self.prompt_type}"
        config_hash = hashlib.md5(config_str.encode()).hexdigest()[:8]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"{self.dataset_name}_{self.estimator_name}_{config_hash}_{timestamp}"
    
    def get_path_components(self) -> Tuple[str, str, str, str]:
        """Get directory path components."""
        return (
            self.dataset_name,
            f"samples_{self.sample_config}",
            self.estimator_name,
            self.prompt_type
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "task_id": self.task_id,
            "timestamp": self.timestamp,
            "dataset": {
                "name": self.dataset_name,
                "config": self.dataset_config
            },
            "sampling": {
                "config": self.sample_config,
                "details": self.dataset_config.get("sampling", {})
            },
            "estimator": {
                "name": self.estimator_name,
                "config": self.estimator_config
            },
            "prompt": {
                "type": self.prompt_type,
                "config": self.prompt_config
            },
            "environment": self.environment_config
        }


class ResultsManager:
    """Comprehensive results manager for UQ analysis."""
    
    def __init__(self, base_dir: str = "./results"):
        self.base_dir = Path(base_dir)
        self.ensure_directories()
    
    def ensure_directories(self):
        """Ensure all required directory structure exists."""
        dirs = [
            "datasets",
            "comparisons", 
            "visualizations",
            "reports",
            "summaries"
        ]
        for dir_name in dirs:
            (self.base_dir / dir_name).mkdir(parents=True, exist_ok=True)
    
    def get_task_directory(self, task_config: TaskConfiguration) -> Path:
        """Get full path for task-specific results."""
        dataset, samples, estimator, prompt = task_config.get_path_components()
        return self.base_dir / "datasets" / dataset / samples / estimator / prompt
    
    def save_task_results(self, 
                         task_config: TaskConfiguration,
                         results: List[Dict[str, Any]]) -> str:
        """
        Save complete results for a task.
        
        Args:
            task_config: TaskConfiguration object
            results: List of individual question results
            
        Returns:
            Path to saved results directory
        """
        task_dir = self.get_task_directory(task_config)
        task_dir.mkdir(parents=True, exist_ok=True)
        
        # Save all result formats
        self._save_task_config(task_config, task_dir)
        self._save_individual_results(task_config, results, task_dir)
        self._save_aggregate_results(task_config, results, task_dir)
        self._save_raw_outputs(task_config, results, task_dir)
        self._save_uq_analysis(task_config, results, task_dir)
        
        return str(task_dir)
    
    def _save_task_config(self, task_config: TaskConfiguration, task_dir: Path):
        """Save task configuration."""
        config_path = task_dir / "task_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(task_config.to_dict(), f, indent=2, ensure_ascii=False)
    
    def _save_individual_results(self, 
                               task_config: TaskConfiguration,
                               results: List[Dict[str, Any]],
                               task_dir: Path):
        """Save detailed individual question results."""
        csv_path = task_dir / "individual_results.csv"
        
        if not results:
            return
        
        fieldnames = [
            "task_id",
            "question_id",
            "question_text",
            "expected_answer",
            "category",
            "semantic_entropy",
            "uncertainty_score",
            "uncertainty_level",
            "confidence_interval_lower",
            "confidence_interval_upper",
            "num_clusters",
            "max_entropy_possible",
            "entropy_normalized",
            "num_responses",
            "avg_response_length",
            "response_diversity",
            "cluster_distribution",
            "timestamp"
        ]
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                row = {
                    "task_id": task_config.task_id,
                    "question_id": result.get("question_id", "unknown"),
                    "question_text": result.get("question", ""),
                    "expected_answer": result.get("expected_answer", ""),
                    "category": result.get("category", "unknown"),
                    "semantic_entropy": result.get("semantic_entropy", 0.0),
                    "uncertainty_score": result.get("uncertainty_score", 0.0),
                    "uncertainty_level": result.get("uncertainty_level", "unknown"),
                    "confidence_interval_lower": result.get("confidence_interval", [0, 0])[0],
                    "confidence_interval_upper": result.get("confidence_interval", [0, 0])[1],
                    "num_clusters": result.get("num_clusters", 0),
                    "max_entropy_possible": result.get("max_entropy_possible", 0.0),
                    "entropy_normalized": result.get("entropy_normalized", 0.0),
                    "num_responses": result.get("num_responses", 0),
                    "avg_response_length": result.get("avg_response_length", 0.0),
                    "response_diversity": result.get("response_diversity", 0.0),
                    "cluster_distribution": json.dumps(result.get("cluster_distribution", {})),
                    "timestamp": task_config.timestamp
                }
                writer.writerow(row)
    
    def _save_aggregate_results(self,
                              task_config: TaskConfiguration,
                              results: List[Dict[str, Any]],
                              task_dir: Path):
        """Save comprehensive aggregate statistics."""
        csv_path = task_dir / "aggregate_results.csv"
        
        if not results:
            return
        
        entropies = [r.get("semantic_entropy", 0.0) for r in results]
        uncertainty_scores = [r.get("uncertainty_score", 0.0) for r in results]
        
        # Calculate distribution statistics
        entropy_stats = self._calculate_distribution_stats(entropies)
        uncertainty_stats = self._calculate_distribution_stats(uncertainty_scores)
        
        aggregate = {
            "task_id": task_config.task_id,
            "dataset_name": task_config.dataset_name,
            "sample_config": task_config.sample_config,
            "estimator_name": task_config.estimator_name,
            "prompt_type": task_config.prompt_type,
            "timestamp": task_config.timestamp,
            "sample_size": len(results),
            
            # Entropy statistics
            "entropy_mean": entropy_stats["mean"],
            "entropy_std": entropy_stats["std"],
            "entropy_min": entropy_stats["min"],
            "entropy_max": entropy_stats["max"],
            "entropy_median": entropy_stats["median"],
            "entropy_q25": entropy_stats["q25"],
            "entropy_q75": entropy_stats["q75"],
            
            # Uncertainty score statistics
            "uncertainty_mean": uncertainty_stats["mean"],
            "uncertainty_std": uncertainty_stats["std"],
            "uncertainty_min": uncertainty_stats["min"],
            "uncertainty_max": uncertainty_stats["max"],
            "uncertainty_median": uncertainty_stats["median"],
            
            # Categorical analysis
            "high_uncertainty_count": sum(1 for e in entropies if e > 2.0),
            "medium_uncertainty_count": sum(1 for e in entropies if 1.0 <= e <= 2.0),
            "low_uncertainty_count": sum(1 for e in entropies if e < 1.0),
            
            # Response analysis
            "total_responses": sum(r.get("num_responses", 0) for r in results),
            "avg_responses_per_question": np.mean([r.get("num_responses", 0) for r in results]),
            "avg_cluster_count": np.mean([r.get("num_clusters", 0) for r in results]),
            
            # Quality metrics
            "coverage": len([r for r in results if r.get("semantic_entropy") is not None]) / len(results),
            "valid_responses_ratio": np.mean([r.get("valid_response_ratio", 1.0) for r in results])
        }
        
        fieldnames = list(aggregate.keys())
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerow(aggregate)
    
    def _save_raw_outputs(self,
                        task_config: TaskConfiguration,
                        results: List[Dict[str, Any]],
                        task_dir: Path):
        """Save raw model outputs and analysis."""
        jsonl_path = task_dir / "raw_outputs.jsonl"
        
        with open(jsonl_path, 'w', encoding='utf-8') as f:
            for result in results:
                output = {
                    "task_id": task_config.task_id,
                    "question_id": result.get("question_id", "unknown"),
                    "question": result.get("question", ""),
                    "expected_answer": result.get("expected_answer", ""),
                    "category": result.get("category", "unknown"),
                    "responses": result.get("responses", []),
                    "clusters": result.get("clusters", []),
                    "analysis": {
                        "semantic_entropy": result.get("semantic_entropy", 0.0),
                        "uncertainty_score": result.get("uncertainty_score", 0.0),
                        "uncertainty_level": result.get("uncertainty_level", "unknown"),
                        "confidence_interval": result.get("confidence_interval", [0, 0]),
                        "num_clusters": result.get("num_clusters", 0),
                        "cluster_distribution": result.get("cluster_distribution", {}),
                        "response_diversity": result.get("response_diversity", 0.0),
                        "valid_response_ratio": result.get("valid_response_ratio", 1.0)
                    },
                    "metadata": {
                        "timestamp": task_config.timestamp,
                        "estimator_config": task_config.estimator_config,
                        "prompt_config": task_config.prompt_config
                    }
                }
                f.write(json.dumps(output, ensure_ascii=False) + '\n')
    
    def _save_uq_analysis(self,
                        task_config: TaskConfiguration,
                        results: List[Dict[str, Any]],
                        task_dir: Path):
        """Save uncertainty quantification analysis."""
        analysis_path = task_dir / "uq_analysis.json"
        
        entropies = [r.get("semantic_entropy", 0.0) for r in results]
        uncertainty_scores = [r.get("uncertainty_score", 0.0) for r in results]
        
        analysis = {
            "task_id": task_config.task_id,
            "timestamp": task_config.timestamp,
            "distribution_analysis": {
                "entropy": {
                    "values": entropies,
                    "histogram": np.histogram(entropies, bins=20)[0].tolist() if entropies else [],
                    "bin_edges": np.histogram(entropies, bins=20)[1].tolist() if entropies else []
                },
                "uncertainty_score": {
                    "values": uncertainty_scores,
                    "histogram": np.histogram(uncertainty_scores, bins=20)[0].tolist() if uncertainty_scores else [],
                    "bin_edges": np.histogram(uncertainty_scores, bins=20)[1].tolist() if uncertainty_scores else []
                }
            },
            "categorical_breakdown": {
                "by_category": self._analyze_by_category(results),
                "by_uncertainty_level": self._analyze_by_uncertainty_level(results)
            },
            "quality_metrics": {
                "total_questions": len(results),
                "valid_results": len([r for r in results if r.get("semantic_entropy") is not None]),
                "missing_data_ratio": 1 - (len([r for r in results if r.get("semantic_entropy") is not None]) / len(results))
            }
        }
        
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False, default=float)
    
    def _calculate_distribution_stats(self, values: List[float]) -> Dict[str, float]:
        """Calculate comprehensive distribution statistics."""
        if not values:
            return {k: 0.0 for k in ["mean", "std", "min", "max", "median", "q25", "q75"]}
        
        values_array = np.array(values)
        return {
            "mean": float(np.mean(values_array)),
            "std": float(np.std(values_array)),
            "min": float(np.min(values_array)),
            "max": float(np.max(values_array)),
            "median": float(np.median(values_array)),
            "q25": float(np.percentile(values_array, 25)),
            "q75": float(np.percentile(values_array, 75))
        }
    
    def _analyze_by_category(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze results by category."""
        categories = {}
        for result in results:
            category = result.get("category", "unknown")
            if category not in categories:
                categories[category] = []
            categories[category].append(result.get("semantic_entropy", 0.0))
        
        analysis = {}
        for category, entropies in categories.items():
            analysis[category] = self._calculate_distribution_stats(entropies)
            analysis[category]["count"] = len(entropies)
        
        return analysis
    
    def _analyze_by_uncertainty_level(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze results by uncertainty level."""
        levels = {"high": [], "medium": [], "low": []}
        
        for result in results:
            level = result.get("uncertainty_level", "unknown")
            entropy = result.get("semantic_entropy", 0.0)
            
            if level in levels:
                levels[level].append(entropy)
        
        analysis = {}
        for level, entropies in levels.items():
            analysis[level] = self._calculate_distribution_stats(entropies)
            analysis[level]["count"] = len(entropies)
        
        return analysis
    
    def list_tasks(self) -> List[Dict[str, str]]:
        """List all available tasks across all configurations."""
        tasks = []
        
        datasets_dir = self.base_dir / "datasets"
        if not datasets_dir.exists():
            return tasks
        
        for dataset_name in datasets_dir.iterdir():
            if not dataset_name.is_dir():
                continue
                
            for sample_config in dataset_name.iterdir():
                if not sample_config.is_dir():
                    continue
                    
                for estimator_name in sample_config.iterdir():
                    if not estimator_name.is_dir():
                        continue
                        
                    for prompt_type in estimator_name.iterdir():
                        if not prompt_type.is_dir():
                            continue
                        
                        config_file = prompt_type / "task_config.json"
                        if config_file.exists():
                            tasks.append({
                                "dataset": dataset_name.name,
                                "sample_config": sample_config.name,
                                "estimator": estimator_name.name,
                                "prompt_type": prompt_type.name,
                                "path": str(prompt_type)
                            })
        
        return tasks
    
    def load_task_results(self, dataset: str, sample_config: str, 
                         estimator: str, prompt_type: str) -> Dict[str, Any]:
        """Load results for a specific task configuration."""
        task_dir = (self.base_dir / "datasets" / dataset / 
                   f"samples_{sample_config}" / estimator / prompt_type)
        
        if not task_dir.exists():
            raise FileNotFoundError(f"Task results not found: {task_dir}")
        
        results = {}
        
        # Load configuration
        config_path = task_dir / "task_config.json"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                results["config"] = json.load(f)
        
        # Load individual results
        individual_path = task_dir / "individual_results.csv"
        if individual_path.exists():
            results["individual_results"] = pd.read_csv(individual_path).to_dict('records')
        
        # Load aggregate results
        aggregate_path = task_dir / "aggregate_results.csv"
        if aggregate_path.exists():
            df = pd.read_csv(aggregate_path)
            results["aggregate_results"] = df.to_dict('records')[0] if not df.empty else {}
        
        # Load UQ analysis
        analysis_path = task_dir / "uq_analysis.json"
        if analysis_path.exists():
            with open(analysis_path, 'r', encoding='utf-8') as f:
                results["uq_analysis"] = json.load(f)
        
        return results