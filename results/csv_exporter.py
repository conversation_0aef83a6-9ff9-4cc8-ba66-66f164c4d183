import pandas as pd
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import datetime
import hashlib


class SimpleCSVExporter:
    """Simple CSV exporter for UQ method results"""
    
    def __init__(self, output_dir: str = "results/csv"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def export_uq_method_results(
        self,
        experiment_name: str,
        uq_method_name: str,
        results: List[Dict[str, Any]],
        include_metadata: bool = True
    ) -> Dict[str, str]:
        """
        Export UQ method results to CSV files
        
        Args:
            experiment_name: Name of the experiment
            uq_method_name: Name of the UQ method
            results: List of result dictionaries
            include_metadata: Whether to include metadata in the export
            
        Returns:
            Dictionary with paths to created files
        """
        if not results:
            return {}
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{experiment_name}_{uq_method_name}_{timestamp}"
        
        # Main results file
        results_file = self.output_dir / f"{base_filename}_results.csv"
        
        # Create DataFrame from results
        df_results = pd.DataFrame(results)
        
        # Add metadata columns if requested
        if include_metadata:
            df_results['experiment'] = experiment_name
            df_results['uq_method'] = uq_method_name
            df_results['export_timestamp'] = timestamp
        
        # Sort by uncertainty score if available
        if 'uncertainty_score' in df_results.columns:
            df_results = df_results.sort_values('uncertainty_score', ascending=False)
        
        # Save main results
        df_results.to_csv(results_file, index=False, encoding='utf-8')
        
        # Create responses file if responses are present
        responses_file = None
        if any('responses' in result for result in results):
            responses_file = self.output_dir / f"{base_filename}_responses.csv"
            self._export_responses(responses_file, results, experiment_name, uq_method_name)
        
        # Create summary file
        summary_file = self.output_dir / f"{base_filename}_summary.csv"
        self._export_summary(summary_file, results, experiment_name, uq_method_name)
        
        return {
            'results': str(results_file),
            'responses': str(responses_file) if responses_file else None,
            'summary': str(summary_file)
        }
    
    def export_dataset_results(
        self,
        experiment_name: str,
        dataset_name: str,
        results: List[Dict[str, Any]]
    ) -> str:
        """
        Export dataset-level results
        
        Args:
            experiment_name: Name of the experiment
            dataset_name: Name of the dataset
            results: List of result dictionaries
            
        Returns:
            Path to the created file
        """
        if not results:
            return ""
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_{dataset_name}_{timestamp}.csv"
        file_path = self.output_dir / filename
        
        df = pd.DataFrame(results)
        df['experiment'] = experiment_name
        df['dataset'] = dataset_name
        df['timestamp'] = timestamp
        
        df.to_csv(file_path, index=False, encoding='utf-8')
        return str(file_path)
    
    def export_experiment_summary(
        self,
        experiment_name: str,
        all_results: Dict[str, List[Dict[str, Any]]]
    ) -> str:
        """
        Export comprehensive experiment summary
        
        Args:
            experiment_name: Name of the experiment
            all_results: Dictionary mapping (dataset, uq_method) to results
            
        Returns:
            Path to the created summary file
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_summary_{timestamp}.csv"
        file_path = self.output_dir / filename
        
        summary_data = []
        
        for (dataset_name, uq_method_name), results in all_results.items():
            if not results:
                continue
                
            summary = {
                'experiment': experiment_name,
                'dataset': dataset_name,
                'uq_method': uq_method_name,
                'total_samples': len(results),
                'timestamp': timestamp
            }
            
            # Add uncertainty statistics if available
            if results and 'uncertainty_score' in results[0]:
                uncertainty_scores = [r['uncertainty_score'] for r in results]
                summary.update({
                    'mean_uncertainty': sum(uncertainty_scores) / len(uncertainty_scores),
                    'min_uncertainty': min(uncertainty_scores),
                    'max_uncertainty': max(uncertainty_scores),
                    'std_uncertainty': pd.Series(uncertainty_scores).std()
                })
            
            # Add accuracy statistics if available
            if results and 'accuracy' in results[0]:
                accuracies = [r['accuracy'] for r in results]
                summary.update({
                    'mean_accuracy': sum(accuracies) / len(accuracies),
                    'min_accuracy': min(accuracies),
                    'max_accuracy': max(accuracies),
                    'std_accuracy': pd.Series(accuracies).std()
                })
            
            summary_data.append(summary)
        
        if summary_data:
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_csv(file_path, index=False, encoding='utf-8')
            return str(file_path)
        
        return ""
    
    def export_comparison_table(
        self,
        experiment_name: str,
        comparison_data: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Export comparison table between different UQ methods
        
        Args:
            experiment_name: Name of the experiment
            comparison_data: Dictionary with comparison metrics
            
        Returns:
            Path to the created comparison file
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{experiment_name}_comparison_{timestamp}.csv"
        file_path = self.output_dir / filename
        
        # Flatten comparison data for CSV
        flattened_data = []
        for dataset_name, methods_data in comparison_data.items():
            for uq_method_name, metrics in methods_data.items():
                row = {
                    'experiment': experiment_name,
                    'dataset': dataset_name,
                    'uq_method': uq_method_name
                }
                row.update(metrics)
                flattened_data.append(row)
        
        if flattened_data:
            df_comparison = pd.DataFrame(flattened_data)
            df_comparison.to_csv(file_path, index=False, encoding='utf-8')
            return str(file_path)
        
        return ""
    
    def _export_responses(
        self,
        file_path: Path,
        results: List[Dict[str, Any]],
        experiment_name: str,
        uq_method_name: str
    ):
        """Export detailed responses to a separate CSV file"""
        responses_data = []
        
        for result in results:
            if 'responses' not in result:
                continue
                
            responses = result['responses']
            if isinstance(responses, list):
                for i, response in enumerate(responses):
                    responses_data.append({
                        'experiment': experiment_name,
                        'uq_method': uq_method_name,
                        'sample_id': result.get('sample_id', ''),
                        'prompt': result.get('prompt', ''),
                        'response_index': i,
                        'response': str(response),
                        'timestamp': datetime.datetime.now().isoformat()
                    })
            else:
                responses_data.append({
                    'experiment': experiment_name,
                    'uq_method': uq_method_name,
                    'sample_id': result.get('sample_id', ''),
                    'prompt': result.get('prompt', ''),
                    'response_index': 0,
                    'response': str(responses),
                    'timestamp': datetime.datetime.now().isoformat()
                })
        
        if responses_data:
            df_responses = pd.DataFrame(responses_data)
            df_responses.to_csv(file_path, index=False, encoding='utf-8')
    
    def _export_summary(
        self,
        file_path: Path,
        results: List[Dict[str, Any]],
        experiment_name: str,
        uq_method_name: str
    ):
        """Export summary statistics"""
        if not results:
            return
        
        summary = {
            'experiment': experiment_name,
            'uq_method': uq_method_name,
            'total_samples': len(results),
            'timestamp': datetime.datetime.now().isoformat()
        }
        
        # Collect all numeric metrics
        numeric_metrics = {}
        for result in results:
            for key, value in result.items():
                if isinstance(value, (int, float)) and key not in ['sample_id', 'prompt']:
                    if key not in numeric_metrics:
                        numeric_metrics[key] = []
                    numeric_metrics[key].append(value)
        
        # Calculate statistics for each metric
        for metric_name, values in numeric_metrics.items():
            if values:
                summary[f"{metric_name}_mean"] = sum(values) / len(values)
                summary[f"{metric_name}_min"] = min(values)
                summary[f"{metric_name}_max"] = max(values)
                summary[f"{metric_name}_std"] = pd.Series(values).std()
        
        df_summary = pd.DataFrame([summary])
        df_summary.to_csv(file_path, index=False, encoding='utf-8')
    
    def get_exported_files(self, experiment_name: str = None) -> List[str]:
        """
        Get list of all exported files
        
        Args:
            experiment_name: Optional filter by experiment name
            
        Returns:
            List of file paths
        """
        files = []
        
        for file_path in self.output_dir.glob("*.csv"):
            if experiment_name is None or experiment_name in file_path.name:
                files.append(str(file_path))
        
        return sorted(files)
    
    def cleanup_old_files(self, experiment_name: str, keep_days: int = 7) -> int:
        """
        Clean up old export files for an experiment
        
        Args:
            experiment_name: Name of the experiment
            keep_days: Number of days to keep files
            
        Returns:
            Number of files deleted
        """
        cutoff_time = datetime.datetime.now() - datetime.timedelta(days=keep_days)
        deleted_count = 0
        
        for file_path in self.output_dir.glob(f"{experiment_name}_*.csv"):
            if datetime.datetime.fromtimestamp(file_path.stat().st_mtime) < cutoff_time:
                try:
                    file_path.unlink()
                    deleted_count += 1
                except Exception:
                    pass
        
        return deleted_count


class ExportFormatError(Exception):
    """Custom exception for export-related errors"""
    pass