"""
Example Usage for the New Results System

This file demonstrates how to use the comprehensive results management system
for LLM uncertainty quantification.
"""

import os
import sys
import json
import numpy as np
from pathlib import Path

# Add the current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from results_manager import ResultsManager, TaskConfiguration
from visualization_manager import VisualizationManager
from report_generator import ReportGenerator


def generate_sample_data():
    """Generate sample uncertainty quantification results for demonstration."""
    np.random.seed(42)
    
    sample_results = []
    
    # Generate 20 sample questions with varying uncertainty
    for i in range(20):
        # Generate different uncertainty patterns based on category
        category = np.random.choice(['science', 'history', 'math', 'literature'])
        
        if category == 'math':
            entropy = np.random.exponential(0.5)  # Low uncertainty for math
        elif category == 'science':
            entropy = np.random.exponential(1.2)  # Medium uncertainty for science
        else:
            entropy = np.random.exponential(2.0)  # High uncertainty for others
            
        # Determine uncertainty level
        if entropy < 1.0:
            uncertainty_level = "low"
        elif entropy < 2.0:
            uncertainty_level = "medium"
        else:
            uncertainty_level = "high"
        
        result = {
            "question_id": f"q_{i+1:03d}",
            "question": f"Sample question about {category} topic {i+1}?",
            "expected_answer": f"Expected answer for question {i+1}",
            "category": category,
            "semantic_entropy": float(entropy),
            "uncertainty_score": float(entropy / 3.0),  # Normalized
            "uncertainty_level": uncertainty_level,
            "confidence_interval": [float(entropy * 0.8), float(entropy * 1.2)],
            "num_clusters": int(np.random.poisson(3) + 1),
            "max_entropy_possible": 3.0,
            "entropy_normalized": float(entropy / 3.0),
            "num_responses": int(np.random.poisson(8) + 5),
            "avg_response_length": float(np.random.normal(50, 20)),
            "response_diversity": float(np.random.beta(2, 5)),
            "cluster_distribution": {"cluster_1": 0.4, "cluster_2": 0.3, "cluster_3": 0.3},
            "valid_response_ratio": float(np.random.beta(8, 2)),
            "responses": [f"Sample response {j+1} for question {i+1}" for j in range(5)],
            "clusters": [{"cluster_id": j+1, "size": np.random.randint(1, 5)} for j in range(3)]
        }
        
        sample_results.append(result)
    
    return sample_results


def example_single_task_analysis():
    """Example: Analyze a single task configuration."""
    print("=== Example: Single Task Analysis ===")
    
    # Initialize managers
    results_manager = ResultsManager()
    visualization_manager = VisualizationManager()
    report_generator = ReportGenerator()
    
    # Create task configuration
    task_config = TaskConfiguration(
        dataset_name="trivialqa",
        sample_config="subset_100",
        estimator_name="semantic_entropy",
        prompt_type="question_answering",
        dataset_config={
            "size": 100,
            "split": "test",
            "sampling": {"method": "random", "seed": 42}
        },
        estimator_config={
            "method": "semantic_entropy",
            "num_samples": 10,
            "temperature": 0.7
        },
        prompt_config={
            "type": "question_answering",
            "template": "Answer the following question: {question}",
            "max_tokens": 150
        },
        environment_config={
            "model": "gpt-3.5-turbo",
            "timestamp": "2024-01-01T00:00:00"
        }
    )
    
    # Generate sample results
    sample_results = generate_sample_data()
    
    # Save results
    save_path = results_manager.save_task_results(task_config, sample_results)
    print(f"Results saved to: {save_path}")
    
    # Load results for visualization
    loaded_results = results_manager.load_task_results(
        "trivialqa", "subset_100", "semantic_entropy", "question_answering"
    )
    
    # Create visualizations
    viz_path = visualization_manager.create_uq_distribution_plot(loaded_results)
    print(f"Visualization saved to: {viz_path}")
    
    # Generate report
    report_path = report_generator.generate_task_report(loaded_results)
    print(f"Report saved to: {report_path}")
    
    return save_path, viz_path, report_path


def example_multiple_configurations():
    """Example: Compare multiple configurations."""
    print("\n=== Example: Multiple Configuration Comparison ===")
    
    results_manager = ResultsManager()
    visualization_manager = VisualizationManager()
    report_generator = ReportGenerator()
    
    configurations = [
        {
            "dataset": "trivialqa",
            "sample_config": "subset_100",
            "estimator": "semantic_entropy",
            "prompt": "question_answering"
        },
        {
            "dataset": "trivialqa", 
            "sample_config": "subset_100",
            "estimator": "kernel_semantic_entropy",
            "prompt": "question_answering"
        },
        {
            "dataset": "trivialqa",
            "sample_config": "subset_50", 
            "estimator": "semantic_entropy",
            "prompt": "question_answering_with_reason"
        }
    ]
    
    all_results = []
    
    for i, config in enumerate(configurations):
        # Create task configuration
        task_config = TaskConfiguration(
            dataset_name=config["dataset"],
            sample_config=config["sample_config"],
            estimator_name=config["estimator"],
            prompt_type=config["prompt"],
            dataset_config={"size": 100 if "100" in config["sample_config"] else 50},
            estimator_config={"method": config["estimator"]},
            prompt_config={"type": config["prompt"]},
            environment_config={"run_id": f"run_{i+1}"}
        )
        
        # Generate sample results with slight variations
        np.random.seed(42 + i)
        sample_results = generate_sample_data()
        
        # Add variation based on estimator
        for result in sample_results:
            if config["estimator"] == "kernel_semantic_entropy":
                result["semantic_entropy"] *= 0.9  # Slightly lower uncertainty
            elif "reason" in config["prompt"]:
                result["semantic_entropy"] *= 1.1  # Slightly higher uncertainty
        
        # Save results
        save_path = results_manager.save_task_results(task_config, sample_results)
        print(f"Configuration {i+1} saved to: {save_path}")
        
        # Load and collect results
        loaded_results = results_manager.load_task_results(
            config["dataset"], config["sample_config"], config["estimator"], config["prompt"]
        )
        all_results.append(loaded_results)
    
    # Create comparison visualizations
    comparison_viz = visualization_manager.create_comparison_plot(all_results)
    print(f"Comparison visualization saved to: {comparison_viz}")
    
    # Create summary dashboard
    dashboard = visualization_manager.create_summary_dashboard(all_results)
    print(f"Summary dashboard saved to: {dashboard}")
    
    # Generate comparison report
    comparison_report = report_generator.generate_comparison_report(all_results)
    print(f"Comparison report saved to: {comparison_report}")
    
    # Generate summary CSV
    summary_csv = report_generator.generate_summary_csv(all_results)
    print(f"Summary CSV saved to: {summary_csv}")
    
    return all_results, comparison_viz, comparison_report, summary_csv


def example_file_structure_exploration():
    """Example: Explore the results file structure."""
    print("\n=== Example: File Structure Exploration ===")
    
    results_manager = ResultsManager()
    
    # List all available tasks
    tasks = results_manager.list_tasks()
    print(f"Found {len(tasks)} task configurations:")
    
    for task in tasks:
        print(f"  - Dataset: {task['dataset']}")
        print(f"    Sample: {task['sample_config']}")
        print(f"    Estimator: {task['estimator']}")
        print(f"    Prompt: {task['prompt_type']}")
        print(f"    Path: {task['path']}")
        print()
    
    return tasks


def main():
    """Main example execution."""
    print("LLM Uncertainty Quantification Results System - Example Usage")
    print("=" * 60)
    
    try:
        # Example 1: Single task analysis
        single_results = example_single_task_analysis()
        
        # Example 2: Multiple configuration comparison
        multi_results = example_multiple_configurations()
        
        # Example 3: File structure exploration
        tasks = example_file_structure_exploration()
        
        print("\n" + "=" * 60)
        print("All examples completed successfully!")
        print("\nResults are organized in the following structure:")
        print("results/")
        print("├── datasets/")
        print("│   └── {dataset_name}/")
        print("│       └── samples_{sample_config}/")
        print("│           └── {estimator_name}/")
        print("│               └── {prompt_type}/")
        print("├── visualizations/")
        print("├── reports/")
        print("└── comparisons/")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()