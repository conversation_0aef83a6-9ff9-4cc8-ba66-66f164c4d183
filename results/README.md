# LLM Uncertainty Quantification Results System

## Overview

This comprehensive results management system provides a structured framework for organizing, analyzing, and visualizing uncertainty quantification (UQ) results for LLM evaluations. The system organizes results by dataset, sample configuration, estimator method, and prompt type, providing detailed UQ metrics and visualizations.

## Directory Structure

```
results/
├── datasets/
│   └── {dataset_name}/
│       └── samples_{sample_config}/
│           └── {estimator_name}/
│               └── {prompt_type}/
│                   ├── task_config.json          # Complete task configuration
│                   ├── individual_results.csv     # Per-question results
│                   ├── aggregate_results.csv      # Summary statistics
│                   ├── raw_outputs.jsonl         # Raw model outputs
│                   ├── uq_analysis.json          # UQ distribution analysis
│                   └── uq_distribution.png       # Visualization
├── visualizations/
│   ├── uq_distribution_{dataset}_{estimator}_{prompt}.png
│   ├── comparison_{metric}.png
│   ├── uncertainty_heatmap_{dataset}.png
│   └── uq_summary_dashboard.png
├── reports/
│   ├── report_{dataset}_{estimator}_{prompt}.json
│   ├── comparison_report_{timestamp}.json
│   └── summary_{timestamp}.csv
├── comparisons/
└── summaries/
```

## Key Features

### 1. Comprehensive Results Organization
- **Dataset-based**: Organize by dataset name
- **Sample Configuration**: Track different sampling strategies
- **Estimator Methods**: Support multiple UQ estimation approaches
- **Prompt Types**: Compare different prompt strategies

### 2. Detailed UQ Metrics
- **Total UQ**: Overall uncertainty across dataset
- **Per-sample UQ**: Individual question uncertainty scores
- **UQ Distribution**: Statistical analysis of uncertainty patterns
- **Uncertainty Levels**: Categorization (high/medium/low)

### 3. Rich Visualizations
- **Distribution plots**: Entropy and uncertainty score distributions
- **Category analysis**: Uncertainty by question category
- **Comparative plots**: Across different configurations
- **Summary dashboards**: Comprehensive overview plots

### 4. Comprehensive Reporting
- **Individual task reports**: Detailed analysis per configuration
- **Comparative reports**: Across multiple configurations
- **Statistical summaries**: CSV exports for further analysis
- **Quality metrics**: Coverage, validity, and reliability measures

## Quick Start

### 1. Basic Usage

```python
from results import ResultsManager, TaskConfiguration, VisualizationManager, ReportGenerator

# Initialize managers
results_manager = ResultsManager()
viz_manager = VisualizationManager()
report_gen = ReportGenerator()

# Create task configuration
task_config = TaskConfiguration(
    dataset_name="trivialqa",
    sample_config="full_data",
    estimator_name="semantic_entropy",
    prompt_type="question_answering",
    dataset_config={"size": 1000, "split": "test"},
    estimator_config={"method": "semantic_entropy", "num_samples": 10},
    prompt_config={"type": "question_answering", "max_tokens": 150}
)

# Save results
results = [...]  # Your UQ results
save_path = results_manager.save_task_results(task_config, results)

# Generate visualizations
loaded_results = results_manager.load_task_results(
    "trivialqa", "full_data", "semantic_entropy", "question_answering"
)
viz_path = viz_manager.create_uq_distribution_plot(loaded_results)

# Generate report
report_path = report_gen.generate_task_report(loaded_results)
```

### 2. Multiple Configuration Comparison

```python
# Compare different configurations
configurations = [
    {"dataset": "trivialqa", "estimator": "semantic_entropy", "prompt": "basic"},
    {"dataset": "trivialqa", "estimator": "kernel_semantic_entropy", "prompt": "basic"},
    {"dataset": "trivialqa", "estimator": "semantic_entropy", "prompt": "with_reasoning"}
]

all_results = []
for config in configurations:
    # ... process each configuration ...
    all_results.append(results)

# Generate comparison visualizations
comparison_viz = viz_manager.create_comparison_plot(all_results)
dashboard = viz_manager.create_summary_dashboard(all_results)
comparison_report = report_gen.generate_comparison_report(all_results)
```

## API Reference

### ResultsManager

#### Methods

- `save_task_results(task_config, results)` - Save complete results for a task
- `load_task_results(dataset, sample_config, estimator, prompt)` - Load specific task results
- `list_tasks()` - List all available task configurations

#### TaskConfiguration Parameters

- `dataset_name`: Name of the dataset
- `sample_config`: Sampling configuration identifier
- `estimator_name`: Name of the UQ estimation method
- `prompt_type`: Type of prompt used
- `dataset_config`: Dataset-specific configuration
- `estimator_config`: Estimator-specific parameters
- `prompt_config`: Prompt-specific settings
- `environment_config`: Environment and model information

### VisualizationManager

#### Methods

- `create_uq_distribution_plot(task_results)` - UQ distribution analysis
- `create_comparison_plot(task_results_list)` - Compare multiple configurations
- `create_uncertainty_heatmap(task_results)` - Uncertainty level heatmap
- `create_summary_dashboard(task_results_list)` - Comprehensive dashboard

### ReportGenerator

#### Methods

- `generate_task_report(task_results)` - Individual task report
- `generate_comparison_report(task_results_list)` - Comparative analysis
- `generate_summary_csv(task_results_list)` - CSV summary export

## File Formats

### task_config.json
```json
{
  "task_id": "unique_task_identifier",
  "timestamp": "2024-01-01T00:00:00",
  "dataset": {"name": "dataset_name", "config": {...}},
  "sampling": {"config": "sample_config", "details": {...}},
  "estimator": {"name": "estimator_name", "config": {...}},
  "prompt": {"type": "prompt_type", "config": {...}},
  "environment": {...}
}
```

### individual_results.csv
Contains per-question results with columns:
- `task_id`, `question_id`, `question_text`, `expected_answer`
- `category`, `semantic_entropy`, `uncertainty_score`
- `uncertainty_level`, `confidence_interval`, `num_clusters`
- `num_responses`, `avg_response_length`, `response_diversity`

### aggregate_results.csv
Contains summary statistics:
- `task_id`, `dataset_name`, `sample_config`, `estimator_name`, `prompt_type`
- `sample_size`, `entropy_mean`, `entropy_std`, `entropy_min`, `entropy_max`
- `high/medium/low_uncertainty_count`, `coverage`, `quality_metrics`

### uq_analysis.json
Contains detailed uncertainty distribution analysis:
- Distribution histograms and statistics
- Categorical breakdowns
- Quality metrics and recommendations

## Integration Examples

### With Existing Estimators

```python
# Integrate with semantic entropy estimator
from estimators import SemanticEntropyCalculator
from results import ResultsManager

calculator = SemanticEntropyCalculator()
results = calculator.calculate_entropy_batch(questions)

# Convert to results format
formatted_results = [
    {
        "question_id": qid,
        "question": question,
        "semantic_entropy": entropy,
        "uncertainty_level": "high" if entropy > 2.0 else "medium" if entropy > 1.0 else "low",
        # ... other required fields
    }
    for qid, question, entropy in results
]

# Save using results manager
results_manager.save_task_results(task_config, formatted_results)
```

### Batch Processing

```python
# Process multiple datasets/configurations
configs = [
    ("trivialqa", "semantic_entropy", "basic"),
    ("trivialqa", "kernel_semantic_entropy", "basic"),
    ("nq", "semantic_entropy", "basic"),
    ("nq", "semantic_entropy", "with_reasoning")
]

for dataset, estimator, prompt in configs:
    # Process dataset
    results = process_dataset(dataset, estimator, prompt)
    
    # Create task configuration
    task_config = TaskConfiguration(
        dataset_name=dataset,
        sample_config="full_data",
        estimator_name=estimator,
        prompt_type=prompt,
        dataset_config={"size": len(results)},
        estimator_config={"method": estimator},
        prompt_config={"type": prompt}
    )
    
    # Save results
    results_manager.save_task_results(task_config, results)
```

## Advanced Usage

### Custom Visualization

```python
# Create custom visualizations using the data
import matplotlib.pyplot as plt
import seaborn as sns

# Load results
task_results = results_manager.load_task_results("trivialqa", "full_data", "semantic_entropy", "basic")
df = pd.DataFrame(task_results["individual_results"])

# Custom analysis
plt.figure(figsize=(12, 8))
sns.boxplot(data=df, x="category", y="semantic_entropy")
plt.title("Uncertainty by Category")
plt.savefig("custom_analysis.png")
```

### Automated Quality Checks

```python
# Implement automated quality checking
def check_result_quality(results):
    issues = []
    
    # Check for missing entropy values
    missing_entropy = [r for r in results if r.get("semantic_entropy") is None]
    if missing_entropy:
        issues.append(f"Missing entropy for {len(missing_entropy)} questions")
    
    # Check entropy range
    entropies = [r["semantic_entropy"] for r in results if r.get("semantic_entropy")]
    if entropies and (max(entropies) > 10 or min(entropies) < 0):
        issues.append("Entropy values outside expected range")
    
    return issues

# Use in processing
quality_issues = check_result_quality(results)
if quality_issues:
    print("Quality issues found:", quality_issues)
```

## Best Practices

1. **Consistent Naming**: Use consistent dataset names and configuration identifiers
2. **Detailed Configuration**: Include all relevant parameters in configuration objects
3. **Regular Backups**: Copy important results to backup locations
4. **Version Control**: Tag results with meaningful version identifiers
5. **Documentation**: Include detailed descriptions in configuration objects
6. **Quality Checks**: Validate results before saving

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Install required packages
   ```bash
   pip install matplotlib seaborn pandas numpy
   ```

2. **File Permission Errors**: Ensure write permissions for results directory

3. **Large File Sizes**: Consider compressing raw outputs for large datasets

4. **Memory Issues**: Process large datasets in batches

### Error Handling

```python
try:
    results_manager.save_task_results(task_config, results)
except Exception as e:
    print(f"Error saving results: {e}")
    # Log error and continue with next task
```

## Support

For issues or questions about the results system, please refer to the example usage in `example_usage.py` or check the individual module documentation.