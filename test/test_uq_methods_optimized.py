import pandas as pd
import argparse
import re
from typing import List, Tu<PERSON>, Dict, Any
from tqdm import tqdm
import logging
import hashlib
import pickle
import os

# 导入UQ方法实现 - NLI版本
from uq_methods.implementations.deg_mat_nli_entail import DegMatNLIEntailUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIEntailUQ

# 导入UQ方法实现 - Jaccard版本
from uq_methods.implementations.deg_mat_jaccard import DegMatJaccardUQ
from uq_methods.implementations.eccentricity_jaccard import EccentricityJaccardUQ
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ

# 导入NumSets方法
from uq_methods.implementations.num_sets import NumSetsUQ

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局缓存
SIMILARITY_CACHE = {}
CACHE_FILE = "data/similarity_cache.pkl"

def load_cache():
    """加载缓存文件"""
    global SIMILARITY_CACHE
    if os.path.exists(CACHE_FILE):
        try:
            with open(CACHE_FILE, 'rb') as f:
                SIMILARITY_CACHE = pickle.load(f)
            logger.info(f"Loaded similarity cache with {len(SIMILARITY_CACHE)} entries")
        except Exception as e:
            logger.warning(f"Failed to load cache: {e}")
            SIMILARITY_CACHE = {}
    else:
        SIMILARITY_CACHE = {}

def save_cache():
    """保存缓存文件"""
    try:
        os.makedirs(os.path.dirname(CACHE_FILE), exist_ok=True)
        with open(CACHE_FILE, 'wb') as f:
            pickle.dump(SIMILARITY_CACHE, f)
        logger.info(f"Saved similarity cache with {len(SIMILARITY_CACHE)} entries")
    except Exception as e:
        logger.warning(f"Failed to save cache: {e}")

def get_cache_key(text1: str, text2: str, method_type: str) -> str:
    """生成缓存键"""
    # 使用哈希来处理长文本，确保键的唯一性
    combined = f"{text1}||{text2}||{method_type}"
    return hashlib.md5(combined.encode()).hexdigest()

def cached_jaccard_similarity(text1: str, text2: str) -> float:
    """带缓存的Jaccard相似度计算"""
    cache_key = get_cache_key(text1, text2, "jaccard")
    
    if cache_key in SIMILARITY_CACHE:
        return SIMILARITY_CACHE[cache_key]
    
    # 计算Jaccard相似度
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    similarity = intersection / union if union > 0 else 0.0
    
    # 缓存结果
    SIMILARITY_CACHE[cache_key] = similarity
    return similarity

def cached_nli_similarity(text1: str, text2: str, nli_model) -> float:
    """带缓存的NLI相似度计算"""
    cache_key = get_cache_key(text1, text2, f"nli_{nli_model.model_name}")
    
    if cache_key in SIMILARITY_CACHE:
        return SIMILARITY_CACHE[cache_key]
    
    # 计算NLI相似度
    similarity = nli_model._compute_nli_entail_score(text1, text2)
    
    # 缓存结果
    SIMILARITY_CACHE[cache_key] = similarity
    return similarity

class CachedUQMethods:
    """包装UQ方法，使用缓存的相似度计算"""
    
    def __init__(self, nli_model_name: str = "microsoft/deberta-large-mnli"):
        self.nli_model_name = nli_model_name
        
        # 创建一个NLI模型实例用于缓存计算
        self.nli_model = DegMatNLIEntailUQ(model_name=nli_model_name, verbose=False)
        
        # 初始化所有UQ方法
        self.methods = {
            # NLI版本 - 使用缓存
            'deg_mat_nli': self._create_cached_nli_method(DegMatNLIEntailUQ, nli_model_name),
            'eccentricity_nli': self._create_cached_nli_method(EccentricityNLIEntailUQ, nli_model_name),
            'eig_val_nli': self._create_cached_nli_method(EigValLaplacianNLIEntailUQ, nli_model_name),
            
            # Jaccard版本 - 使用缓存
            'deg_mat_jaccard': self._create_cached_jaccard_method(DegMatJaccardUQ),
            'eccentricity_jaccard': self._create_cached_jaccard_method(EccentricityJaccardUQ),
            'eig_val_jaccard': self._create_cached_jaccard_method(EigValLaplacianJaccardUQ),
            
            # NumSets方法
            'num_sets': NumSetsUQ(model_name=nli_model_name)
        }
    
    def _create_cached_nli_method(self, method_class, model_name):
        """创建使用缓存NLI计算的方法实例"""
        method = method_class(model_name=model_name, verbose=False)
        # 替换原来的相似度计算方法
        original_compute_similarity = method._compute_similarity_matrix
        
        def cached_compute_similarity(responses: List[str]):
            import numpy as np
            n = len(responses)
            W = np.zeros((n, n))
            
            for i in range(n):
                for j in range(n):
                    if i == j:
                        W[i, j] = 1.0
                    else:
                        # 使用缓存的NLI计算
                        score_ij = cached_nli_similarity(responses[i], responses[j], self.nli_model)
                        score_ji = cached_nli_similarity(responses[j], responses[i], self.nli_model)
                        W[i, j] = (score_ij + score_ji) / 2
            
            W = (W + np.transpose(W)) / 2
            return W
        
        method._compute_similarity_matrix = cached_compute_similarity
        return method
    
    def _create_cached_jaccard_method(self, method_class):
        """创建使用缓存Jaccard计算的方法实例"""
        method = method_class(verbose=False)
        # 替换原来的相似度计算方法
        
        def cached_compute_similarity(responses: List[str]):
            import numpy as np
            n = len(responses)
            W = np.zeros((n, n))
            
            for i in range(n):
                for j in range(n):
                    if i == j:
                        W[i, j] = 1.0
                    else:
                        W[i, j] = cached_jaccard_similarity(responses[i], responses[j])
            
            W = (W + np.transpose(W)) / 2
            return W
        
        method._compute_similarity_matrix = cached_compute_similarity
        return method

def split_twitter_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """从Twitter响应中提取label和reasoning部分"""
    response = response.replace('\r\n', '\n').replace('\r', '\n')
    if prompt_type == "sentiment":
        return response.strip(), ""
    elif prompt_type in ("sentiment_reason", "sentiment_reason_first"):
        label_match = re.search(r"\[Label\]:\s*([^\n]+)", response)
        reasoning_match = re.search(r"\[Reasoning\]:\s*((?:.|\n)*?)(?=\n\[|$)", response)
        label = label_match.group(1).strip() if label_match else ""
        reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
        return label, reasoning
    else:
        return response.strip(), ""

def split_commit_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """从Commit响应中提取module和reasoning部分"""
    if prompt_type == 'single_word':
        return response.strip(), ""
    else:
        module, reasoning = '', ''
        module_match = re.search(r'Module:\s*(.*?)(?:\n|$)', response, re.DOTALL)
        reasoning_match = re.search(r'Reasoning:\s*(.*?)(?:\nModule:|$)', response, re.DOTALL)
        if module_match:
            module = module_match.group(1).strip()
        if reasoning_match:
            reasoning = reasoning_match.group(1).strip()
        return module, reasoning

def calculate_validation_accuracy(predicted_labels: List[str], validation_labels: List[str]) -> Dict[str, float]:
    """计算预测标签与validation标签的准确率"""
    if len(predicted_labels) != len(validation_labels):
        logger.warning(f"Length mismatch: predicted={len(predicted_labels)}, validation={len(validation_labels)}")
        return {'accuracy': 0.0, 'total_pairs': 0}
    
    correct = 0
    total = len(predicted_labels)
    
    for pred, val in zip(predicted_labels, validation_labels):
        # 简单的字符串匹配（可能需要根据具体情况调整）
        if pred.lower().strip() == val.lower().strip():
            correct += 1
    
    accuracy = correct / total if total > 0 else 0.0
    return {
        'accuracy': accuracy,
        'correct_predictions': correct,
        'total_pairs': total
    }

def analyze_all_uq_methods(csv_file: str, data_type: str, nli_model: str = "microsoft/deberta-large-mnli", 
                          max_identifiers: int = None):
    """分析所有数据的UQ方法"""
    print(f"\n=== Analyzing ALL {data_type} data with UQ methods ===")
    print(f"Using NLI model: {nli_model}")
    
    # 加载缓存
    load_cache()
    
    # 读取数据
    df = pd.read_csv(csv_file)
    print(f"Loaded {len(df)} records from {csv_file}")
    
    if data_type == 'twitter':
        identifier_col = 'tweet_index'
        sample_sizes = [10, 15, 20]  # 针对Twitter数据的sample sizes
    else:  # commit
        identifier_col = 'commit_sha'
        sample_sizes = [10, 15, 20, 25, 30]  # 针对Commit数据的sample sizes
    
    # 获取unique identifiers，并可能限制数量
    unique_identifiers = df[identifier_col].unique()
    if max_identifiers:
        unique_identifiers = unique_identifiers[:max_identifiers]
        logger.info(f"Limited to first {max_identifiers} identifiers")
    
    print(f"Found {len(unique_identifiers)} unique {identifier_col}s to process")
    
    # 初始化缓存的UQ方法
    logger.info("Initializing cached UQ methods...")
    cached_methods = CachedUQMethods(nli_model)
    logger.info(f"Initialized {len(cached_methods.methods)} UQ methods (NLI + Jaccard + NumSets versions)")
    
    results = []
    
    # 按identifier和prompt_type分组
    grouped = df.groupby([identifier_col, 'prompt_type'])
    logger.info(f"Processing {len(grouped)} groups ({identifier_col}, prompt_type combinations)")
    
    processed_count = 0
    for (identifier, prompt_type), group in tqdm(grouped, desc=f"Processing {data_type} groups"):
        # 如果设置了max_identifiers限制，跳过不在列表中的identifier
        if max_identifiers and identifier not in unique_identifiers:
            continue
            
        logger.info(f"Processing {identifier_col}={str(identifier)[:8]}..., prompt_type={prompt_type}")
        
        responses = group['response_text'].dropna().tolist()
        logger.info(f"Found {len(responses)} responses")
        
        if len(responses) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(responses)} responses, need at least {max(sample_sizes)}")
            continue
        
        # 提取labels/modules和validation数据
        if data_type == 'twitter':
            content_items = []
            validation_items = []
            for idx, resp in enumerate(responses):
                label, _ = split_twitter_response(resp, prompt_type)
                if label:
                    content_items.append(label)
                    # 获取对应的validation标签
                    if 'validation' in group.columns:
                        validation_label = group.iloc[idx]['validation'] if idx < len(group) else ""
                        validation_items.append(str(validation_label))
            content_type = 'label'
        else:  # commit
            validation_items = []  # Commit数据没有validation
            if prompt_type == 'single_word':
                content_items = responses
                content_type = 'response'
            else:
                content_items = []
                for resp in responses:
                    module, _ = split_commit_response(resp, prompt_type)
                    if module:
                        content_items.append(module)
                content_type = 'module'
        
        logger.info(f"Extracted {len(content_items)} {content_type}s")
        
        if len(content_items) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(content_items)} {content_type}s available")
            continue
        
        # 对每个sample size进行测试
        for sample_size in sample_sizes:
            if len(content_items) < sample_size:
                logger.warning(f"Skipping sample_size={sample_size}: only {len(content_items)} items available")
                continue
                
            sample_items = content_items[:sample_size]
            sample_validation = validation_items[:sample_size] if validation_items else []
            logger.info(f"Testing with sample_size={sample_size}")
            
            # 计算validation准确率（仅对Twitter数据）
            validation_accuracy = {}
            if data_type == 'twitter' and validation_items:
                validation_accuracy = calculate_validation_accuracy(sample_items, sample_validation)
                logger.info(f"Validation accuracy: {validation_accuracy['accuracy']:.4f} ({validation_accuracy['correct_predictions']}/{validation_accuracy['total_pairs']})")
            
            # 对每个UQ方法进行测试
            for method_name, uq_method in cached_methods.methods.items():
                try:
                    logger.info(f"Computing uncertainty for {len(sample_items)} {content_type}s using {method_name}")
                    result = uq_method.compute_uncertainty(sample_items)
                    
                    # 处理不同方法的返回格式
                    if method_name == 'num_sets':
                        uncertainty_score = result.get('set_entropy', 0)
                        mean_similarity = 0  # NumSets不返回mean_similarity
                        additional_metrics = {
                            'num_sets': result.get('num_sets', 0),
                            'set_sizes': result.get('set_sizes', [])
                        }
                    else:
                        uncertainty_score = result.get('uncertainty_score', 0)
                        mean_similarity = result.get('mean_similarity', 0)
                        additional_metrics = {}
                    
                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                    
                    result_dict = {
                        'data_type': data_type,
                        'identifier': str(identifier)[:12] if len(str(identifier)) > 12 else str(identifier),
                        'prompt_type': prompt_type,
                        'content_type': content_type,
                        'sample_size': sample_size,
                        'uq_method': method_name,
                        'uncertainty_score': uncertainty_score,
                        'mean_similarity': mean_similarity,
                        'method_details': result.get('method', method_name),
                        'sample_responses': sample_items[:3]
                    }
                    
                    # 添加validation准确率信息
                    if validation_accuracy:
                        result_dict.update({
                            'validation_accuracy': validation_accuracy['accuracy'],
                            'correct_predictions': validation_accuracy['correct_predictions'],
                            'total_validation_pairs': validation_accuracy['total_pairs']
                        })
                    
                    # 添加额外的指标
                    result_dict.update(additional_metrics)
                    results.append(result_dict)
                    
                except Exception as e:
                    logger.error(f"Error processing {method_name} with sample_size={sample_size}: {str(e)}")
        
        processed_count += 1
        if processed_count % 100 == 0:
            logger.info(f"Processed {processed_count} groups so far...")
            # 定期保存缓存
            save_cache()
    
    # 保存缓存
    save_cache()
    
    return results

def create_summary_tables(results_df: pd.DataFrame):
    """创建汇总表格，包括validation准确率"""
    print("\n=== Results Summary Tables ===")
    
    for data_type in results_df['data_type'].unique():
        type_df = results_df[results_df['data_type'] == data_type]
        print(f"\n--- {data_type.upper()} Data Summary ---")
        
        # 按prompt_type, sample_size, uq_method展示uncertainty_score
        pivot_uncertainty = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='uncertainty_score',
            aggfunc='mean'
        ).round(4)
        print("\nUncertainty Scores:")
        print(pivot_uncertainty)
        
        # 按prompt_type, sample_size, uq_method展示mean_similarity
        pivot_similarity = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='mean_similarity',
            aggfunc='mean'
        ).round(4)
        print("\nMean Similarity Scores:")
        print(pivot_similarity)
        
        # 对于Twitter数据，展示validation准确率
        if data_type == 'twitter' and 'validation_accuracy' in type_df.columns:
            pivot_validation = type_df.pivot_table(
                index=['prompt_type', 'sample_size'], 
                columns='uq_method', 
                values='validation_accuracy',
                aggfunc='mean'
            ).round(4)
            print("\nValidation Accuracy:")
            print(pivot_validation)
            
            # 保存validation准确率表
            pivot_validation.to_csv(f'data/{data_type}_validation_accuracy_pivot.csv')
            print(f"Validation accuracy table saved to data/{data_type}_validation_accuracy_pivot.csv")
        
        # 保存详细的pivot表到CSV
        pivot_uncertainty.to_csv(f'data/{data_type}_uncertainty_pivot.csv')
        pivot_similarity.to_csv(f'data/{data_type}_similarity_pivot.csv')
        print(f"\nDetailed pivot tables saved to data/{data_type}_*_pivot.csv")
        
        # 创建方法比较表（NLI vs Jaccard）
        print(f"\n--- Method Comparison ({data_type.upper()}) ---")
        for method_base in ['deg_mat', 'eccentricity', 'eig_val']:
            nli_method = f'{method_base}_nli'
            jaccard_method = f'{method_base}_jaccard'
            
            if nli_method in type_df['uq_method'].values and jaccard_method in type_df['uq_method'].values:
                comparison_df = type_df[type_df['uq_method'].isin([nli_method, jaccard_method])]
                
                # 基本指标比较
                comparison_pivot = comparison_df.pivot_table(
                    index=['prompt_type', 'sample_size'],
                    columns='uq_method',
                    values=['uncertainty_score', 'mean_similarity'],
                    aggfunc='mean'
                ).round(4)
                print(f"\n{method_base.upper()} Comparison (NLI vs Jaccard):")
                print(comparison_pivot)
                
                # 如果有validation数据，也进行比较
                if data_type == 'twitter' and 'validation_accuracy' in comparison_df.columns:
                    validation_comparison = comparison_df.pivot_table(
                        index=['prompt_type', 'sample_size'],
                        columns='uq_method',
                        values='validation_accuracy',
                        aggfunc='mean'
                    ).round(4)
                    print(f"\n{method_base.upper()} Validation Accuracy Comparison:")
                    print(validation_comparison)

def main():
    parser = argparse.ArgumentParser(description="Analyze UQ methods on ALL Twitter and Commit data with validation accuracy")
    parser.add_argument('--twitter_csv', type=str, default='data/all_twitter_responses.csv')
    parser.add_argument('--commit_csv', type=str, default='data/all_commit_responses.csv')
    parser.add_argument('--output_file', type=str, default='data/uq_methods_complete_analysis.csv')
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'])
    parser.add_argument('--max_twitter_identifiers', type=int, default=None,
                       help='Limit number of Twitter identifiers to process (for testing)')
    parser.add_argument('--max_commit_identifiers', type=int, default=50,
                       help='Limit number of Commit identifiers to process (default: 50)')
    
    args = parser.parse_args()
    
    all_results = []
    
    # 分析Twitter数据
    try:
        print(f"Starting Twitter data analysis...")
        twitter_results = analyze_all_uq_methods(
            args.twitter_csv, 
            'twitter', 
            args.nli_model,
            max_identifiers=args.max_twitter_identifiers
        )
        all_results.extend(twitter_results)
        print(f"Completed Twitter analysis: {len(twitter_results)} results")
    except Exception as e:
        logger.error(f"Error analyzing Twitter data: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 分析Commit数据（由于数据量大，限制处理数量）
    try:
        print(f"Starting Commit data analysis (limited to {args.max_commit_identifiers} identifiers)...")
        commit_results = analyze_all_uq_methods(
            args.commit_csv, 
            'commit', 
            args.nli_model,
            max_identifiers=args.max_commit_identifiers
        )
        all_results.extend(commit_results)
        print(f"Completed Commit analysis: {len(commit_results)} results")
    except Exception as e:
        logger.error(f"Error analyzing Commit data: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 保存结果
    if all_results:
        results_df = pd.DataFrame(all_results)
        results_df.to_csv(args.output_file, index=False, encoding='utf-8-sig')
        print(f"\nAll results saved to {args.output_file}")
        print(f"Total results: {len(results_df)}")
        
        # 创建汇总表格
        create_summary_tables(results_df)
        
        # 显示基本统计信息
        print("\n=== Basic Statistics ===")
        print("Results by data type and method:")
        print(results_df.groupby(['data_type', 'uq_method']).size())
        
        print("\nSample sizes tested:")
        print(results_df.groupby(['data_type', 'sample_size']).size())
        
        print("\nMethod types:")
        results_df['method_type'] = results_df['uq_method'].apply(
            lambda x: 'NLI' if 'nli' in x else ('Jaccard' if 'jaccard' in x else 'NumSets')
        )
        results_df['method_base'] = results_df['uq_method'].apply(
            lambda x: x.replace('_nli', '').replace('_jaccard', '') if x != 'num_sets' else 'num_sets'
        )
        print(results_df.groupby(['data_type', 'method_type', 'method_base']).size())
        
        # 显示Twitter validation准确率统计
        if 'validation_accuracy' in results_df.columns:
            twitter_results = results_df[results_df['data_type'] == 'twitter']
            if not twitter_results.empty:
                print("\n=== Twitter Validation Accuracy Statistics ===")
                print("Average validation accuracy by method:")
                avg_accuracy = twitter_results.groupby('uq_method')['validation_accuracy'].mean().sort_values(ascending=False)
                print(avg_accuracy.round(4))
                
                print("\nValidation accuracy by prompt type:")
                prompt_accuracy = twitter_results.groupby(['prompt_type', 'uq_method'])['validation_accuracy'].mean()
                print(prompt_accuracy.round(4))
        
        # 显示缓存统计
        print(f"\nCache statistics: {len(SIMILARITY_CACHE)} similarity calculations cached")
    else:
        print("No results generated")

if __name__ == "__main__":
    main()