import pandas as pd
import argparse
import re
from typing import List, <PERSON><PERSON>
from tqdm import tqdm
import logging

# 导入UQ方法实现 - NLI版本
from uq_methods.implementations.deg_mat_nli_entail import DegMatNLIEntailUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIEntailUQ

# 导入UQ方法实现 - Jaccard版本
from uq_methods.implementations.deg_mat_jaccard import DegMatJaccardUQ
from uq_methods.implementations.eccentricity_jaccard import EccentricityJaccardUQ
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def split_twitter_response(response: str, prompt_type: str) -> <PERSON><PERSON>[str, str]:
    """
    从Twitter响应中提取label和reasoning部分
    """
    # 统一换行符
    response = response.replace('\r\n', '\n').replace('\r', '\n')
    if prompt_type == "sentiment":
        return response.strip(), ""
    elif prompt_type in ("sentiment_reason", "sentiment_reason_first"):
        # 提取 [Label]: ... 和 [Reasoning]: ...，允许顺序任意，reasoning支持多行
        label_match = re.search(r"\[Label\]:\s*([^\n]+)", response)
        reasoning_match = re.search(r"\[Reasoning\]:\s*((?:.|\n)*?)(?=\n\[|$)", response)
        label = label_match.group(1).strip() if label_match else ""
        reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
        return label, reasoning
    else:
        return response.strip(), ""

def split_commit_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """
    从Commit响应中提取module和reasoning部分
    """
    if prompt_type == 'single_word':
        return response.strip(), ""
    else:
        # 提取Module和Reasoning部分，兼容顺序和缺失
        module, reasoning = '', ''
        module_match = re.search(r'Module:\s*(.*?)(?:\n|$)', response, re.DOTALL)
        reasoning_match = re.search(r'Reasoning:\s*(.*?)(?:\nModule:|$)', response, re.DOTALL)
        if module_match:
            module = module_match.group(1).strip()
        if reasoning_match:
            reasoning = reasoning_match.group(1).strip()
        return module, reasoning

def test_uq_methods_with_sample_sizes(csv_file: str, data_type: str, nli_model: str = "microsoft/deberta-large-mnli"):
    """
    使用不同sample size测试UQ方法
    """
    print(f"\n=== Testing UQ methods on {data_type} data with different sample sizes ===")
    print(f"Using NLI model: {nli_model}")
    
    # 读取数据
    df = pd.read_csv(csv_file)
    print(f"Loaded {len(df)} records from {csv_file}")
    
    if data_type == 'twitter':
        # 只取第一个tweet的数据
        first_identifier = df['tweet_index'].iloc[0]
        df = df[df['tweet_index'] == first_identifier]
        logger.info(f"Filtering to first tweet (index={first_identifier}): {len(df)} records")
        identifier_col = 'tweet_index'
        sample_sizes = [10, 15, 20]  # Twitter有20个responses
    else:  # commit
        # 只取第一个commit的数据
        first_identifier = df['commit_sha'].iloc[0]
        df = df[df['commit_sha'] == first_identifier]
        logger.info(f"Filtering to first commit (sha={first_identifier[:8]}...): {len(df)} records")
        identifier_col = 'commit_sha'
        sample_sizes = [10, 15, 20, 25, 30]  # Commit有30个responses
    
    # 初始化UQ方法 - 包括NLI和Jaccard版本
    logger.info("Initializing UQ methods...")
    uq_methods = {
        # NLI版本
        'deg_mat_nli': DegMatNLIEntailUQ(model_name=nli_model, verbose=False),
        'eccentricity_nli': EccentricityNLIEntailUQ(model_name=nli_model, verbose=False),
        'eig_val_nli': EigValLaplacianNLIEntailUQ(model_name=nli_model, verbose=False),
        # Jaccard版本
        'deg_mat_jaccard': DegMatJaccardUQ(verbose=False),
        'eccentricity_jaccard': EccentricityJaccardUQ(verbose=False),
        'eig_val_jaccard': EigValLaplacianJaccardUQ(verbose=False)
    }
    logger.info(f"Initialized {len(uq_methods)} UQ methods (NLI + Jaccard versions)")
    
    results = []
    
    # 按identifier和prompt_type分组
    grouped = df.groupby([identifier_col, 'prompt_type'])
    logger.info(f"Processing {len(grouped)} groups ({identifier_col}, prompt_type combinations)")
    
    for (identifier, prompt_type), group in tqdm(grouped, desc=f"Processing {data_type} groups"):
        logger.info(f"Processing {identifier_col}={str(identifier)[:8]}..., prompt_type={prompt_type}")
        
        responses = group['response_text'].dropna().tolist()
        logger.info(f"Found {len(responses)} responses")
        
        if len(responses) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(responses)} responses, need at least {max(sample_sizes)}")
            continue
        
        # 提取labels/modules (只关注这个，不分析reasoning)
        if data_type == 'twitter':
            content_items = []
            for resp in responses:
                label, _ = split_twitter_response(resp, prompt_type)
                if label:
                    content_items.append(label)
            content_type = 'label'
        else:  # commit
            if prompt_type == 'single_word':
                content_items = responses
                content_type = 'response'
            else:
                content_items = []
                for resp in responses:
                    module, _ = split_commit_response(resp, prompt_type)
                    if module:
                        content_items.append(module)
                content_type = 'module'
        
        logger.info(f"Extracted {len(content_items)} {content_type}s")
        
        if len(content_items) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(content_items)} {content_type}s available")
            continue
        
        # 对每个sample size进行测试
        for sample_size in sample_sizes:
            if len(content_items) < sample_size:
                logger.warning(f"Skipping sample_size={sample_size}: only {len(content_items)} items available")
                continue
                
            # 取前sample_size个items
            sample_items = content_items[:sample_size]
            logger.info(f"Testing with sample_size={sample_size}")
            
            # 对每个UQ方法进行测试
            for method_name, uq_method in uq_methods.items():
                try:
                    logger.info(f"Computing uncertainty for {len(sample_items)} {content_type}s using {method_name}")
                    result = uq_method.compute_uncertainty(sample_items)
                    uncertainty_score = result.get('uncertainty_score', 0)
                    mean_similarity = result.get('mean_similarity', 0)
                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                    
                    results.append({
                        'data_type': data_type,
                        'identifier': str(identifier)[:12] if len(str(identifier)) > 12 else str(identifier),
                        'prompt_type': prompt_type,
                        'content_type': content_type,
                        'sample_size': sample_size,
                        'uq_method': method_name,
                        'uncertainty_score': uncertainty_score,
                        'mean_similarity': mean_similarity,
                        'method_details': result.get('method', ''),
                        'sample_responses': sample_items[:3]  # 保存前3个作为示例
                    })
                except Exception as e:
                    logger.error(f"Error processing {method_name} with sample_size={sample_size}: {str(e)}")
    
    return results

def create_summary_tables(results_df: pd.DataFrame):
    """
    创建汇总表格
    """
    print("\n=== Results Summary Tables ===")
    
    # 按数据类型分别创建表格
    for data_type in results_df['data_type'].unique():
        type_df = results_df[results_df['data_type'] == data_type]
        print(f"\n--- {data_type.upper()} Data Summary ---")
        
        # 按prompt_type, sample_size, uq_method展示uncertainty_score
        pivot_uncertainty = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='uncertainty_score',
            aggfunc='mean'
        ).round(4)
        print("\nUncertainty Scores:")
        print(pivot_uncertainty)
        
        # 按prompt_type, sample_size, uq_method展示mean_similarity
        pivot_similarity = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='mean_similarity',
            aggfunc='mean'
        ).round(4)
        print("\nMean Similarity Scores:")
        print(pivot_similarity)
        
        # 保存详细的pivot表到CSV
        pivot_uncertainty.to_csv(f'data/{data_type}_uncertainty_pivot.csv')
        pivot_similarity.to_csv(f'data/{data_type}_similarity_pivot.csv')
        print(f"\nDetailed pivot tables saved to data/{data_type}_*_pivot.csv")
        
        # 创建方法比较表（NLI vs Jaccard）
        print(f"\n--- Method Comparison ({data_type.upper()}) ---")
        for method_base in ['deg_mat', 'eccentricity', 'eig_val']:
            nli_method = f'{method_base}_nli'
            jaccard_method = f'{method_base}_jaccard'
            
            if nli_method in type_df['uq_method'].values and jaccard_method in type_df['uq_method'].values:
                comparison_df = type_df[type_df['uq_method'].isin([nli_method, jaccard_method])]
                comparison_pivot = comparison_df.pivot_table(
                    index=['prompt_type', 'sample_size'],
                    columns='uq_method',
                    values=['uncertainty_score', 'mean_similarity'],
                    aggfunc='mean'
                ).round(4)
                print(f"\n{method_base.upper()} Comparison (NLI vs Jaccard):")
                print(comparison_pivot)

def main():
    parser = argparse.ArgumentParser(description="Test UQ methods with different sample sizes")
    parser.add_argument('--twitter_csv', type=str, default='data/twitter_responses.csv', 
                       help='Twitter responses CSV file')
    parser.add_argument('--commit_csv', type=str, default='data/commit_responses_first5.csv', 
                       help='Commit responses CSV file')
    parser.add_argument('--output_file', type=str, default='data/uq_methods_nli_jaccard_sample_sizes_results.csv', 
                       help='Output CSV file for results')
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'],
                       help='NLI model to use')
    
    args = parser.parse_args()
    
    all_results = []
    
    # 测试Twitter数据
    try:
        twitter_results = test_uq_methods_with_sample_sizes(args.twitter_csv, 'twitter', args.nli_model)
        all_results.extend(twitter_results)
        print(f"Completed Twitter testing: {len(twitter_results)} results")
    except Exception as e:
        logger.error(f"Error testing Twitter data: {str(e)}")
    
    # 测试Commit数据
    try:
        commit_results = test_uq_methods_with_sample_sizes(args.commit_csv, 'commit', args.nli_model)
        all_results.extend(commit_results)
        print(f"Completed Commit testing: {len(commit_results)} results")
    except Exception as e:
        logger.error(f"Error testing Commit data: {str(e)}")
    
    # 保存结果
    if all_results:
        results_df = pd.DataFrame(all_results)
        results_df.to_csv(args.output_file, index=False, encoding='utf-8-sig')
        print(f"\nAll results saved to {args.output_file}")
        print(f"Total results: {len(results_df)}")
        
        # 创建汇总表格
        create_summary_tables(results_df)
        
        # 显示基本统计信息
        print("\n=== Basic Statistics ===")
        print("Results by data type and method:")
        print(results_df.groupby(['data_type', 'uq_method']).size())
        
        print("\nSample sizes tested:")
        print(results_df.groupby(['data_type', 'sample_size']).size())
        
        print("\nMethod types (NLI vs Jaccard):")
        results_df['method_type'] = results_df['uq_method'].apply(lambda x: 'NLI' if 'nli' in x else 'Jaccard')
        results_df['method_base'] = results_df['uq_method'].apply(lambda x: x.replace('_nli', '').replace('_jaccard', ''))
        print(results_df.groupby(['data_type', 'method_type', 'method_base']).size())
        
    else:
        print("No results generated")

if __name__ == "__main__":
    main()