import pandas as pd
import argparse
import re
from typing import List, <PERSON><PERSON>
from tqdm import tqdm
import logging

# 导入UQ方法实现
from uq_methods.implementations.deg_mat_nli_entail import DegMatNLIEntailUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIEntailUQ

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def split_twitter_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """
    从Twitter响应中提取label和reasoning部分
    """
    # 统一换行符
    response = response.replace('\r\n', '\n').replace('\r', '\n')
    if prompt_type == "sentiment":
        return response.strip(), ""
    elif prompt_type in ("sentiment_reason", "sentiment_reason_first"):
        # 提取 [Label]: ... 和 [Reasoning]: ...，允许顺序任意，reasoning支持多行
        label_match = re.search(r"\[Label\]:\s*([^\n]+)", response)
        reasoning_match = re.search(r"\[Reasoning\]:\s*((?:.|\n)*?)(?=\n\[|$)", response)
        label = label_match.group(1).strip() if label_match else ""
        reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
        return label, reasoning
    else:
        return response.strip(), ""

def split_commit_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """
    从Commit响应中提取module和reasoning部分
    """
    if prompt_type == 'single_word':
        return response.strip(), ""
    else:
        # 提取Module和Reasoning部分，兼容顺序和缺失
        module, reasoning = '', ''
        module_match = re.search(r'Module:\s*(.*?)(?:\n|$)', response, re.DOTALL)
        reasoning_match = re.search(r'Reasoning:\s*(.*?)(?:\nModule:|$)', response, re.DOTALL)
        if module_match:
            module = module_match.group(1).strip()
        if reasoning_match:
            reasoning = reasoning_match.group(1).strip()
        return module, reasoning

def test_uq_methods_on_twitter_data(csv_file: str, nli_model: str = "potsawee/deberta-v3-large-mnli"):
    """
    在Twitter数据上测试UQ方法
    """
    print(f"\n=== Testing UQ methods on Twitter data ===")
    print(f"Using NLI model: {nli_model}")
    
    # 读取数据
    df = pd.read_csv(csv_file)
    print(f"Loaded {len(df)} records from {csv_file}")
    
    # 只取第一个tweet的数据
    first_tweet_index = df['tweet_index'].iloc[0]
    df = df[df['tweet_index'] == first_tweet_index]
    logger.info(f"Filtering to first tweet (index={first_tweet_index}): {len(df)} records")
    
    # 初始化UQ方法
    logger.info("Initializing UQ methods...")
    uq_methods = {
        'deg_mat': DegMatNLIEntailUQ(model_name=nli_model, verbose=False),
        'eccentricity': EccentricityNLIEntailUQ(model_name=nli_model, verbose=False),
        'eig_val': EigValLaplacianNLIEntailUQ(model_name=nli_model, verbose=False)
    }
    logger.info(f"Initialized {len(uq_methods)} UQ methods")
    
    results = []
    
    # 按tweet_index和prompt_type分组
    grouped = df.groupby(['tweet_index', 'prompt_type'])
    logger.info(f"Processing {len(grouped)} groups (tweet_index, prompt_type combinations)")
    
    for (tweet_index, prompt_type), group in tqdm(grouped, desc="Processing Twitter groups"):
        logger.info(f"Processing tweet {tweet_index}, prompt_type {prompt_type}: {len(group)} responses")
        responses = group['response_text'].dropna().tolist()
        logger.info(f"Found {len(responses)} responses for tweet {tweet_index}, prompt_type {prompt_type}")
        
        if len(responses) < 2:
            logger.warning(f"Skipping tweet {tweet_index}, prompt_type {prompt_type}: only {len(responses)} responses")
            continue
            
        # 提取labels和reasonings
        labels, reasonings = [], []
        for resp in responses:
            label, reasoning = split_twitter_response(resp, prompt_type)
            if label: 
                labels.append(label)
            if reasoning: 
                reasonings.append(reasoning)
        
        logger.info(f"Extracted {len(labels)} labels and {len(reasonings)} reasonings")
        
        # 对每个UQ方法进行测试
        for method_name, uq_method in uq_methods.items():
            logger.info(f"Testing {method_name} method...")
            # 测试labels
            if labels and len(labels) >= 2:
                try:
                    logger.info(f"Computing uncertainty for {len(labels)} labels using {method_name}")
                    label_result = uq_method.compute_uncertainty(labels)
                    uncertainty_score = label_result.get('uncertainty_score', 0)
                    mean_similarity = label_result.get('mean_similarity', 0)
                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                    
                    results.append({
                        'data_type': 'twitter',
                        'identifier': tweet_index,
                        'prompt_type': prompt_type,
                        'content_type': 'label',
                        'uq_method': method_name,
                        'uncertainty_score': uncertainty_score,
                        'num_responses': len(labels),
                        'mean_similarity': mean_similarity,
                        'method_details': label_result.get('method', ''),
                        'responses': labels[:5]  # 只保存前5个响应作为示例
                    })
                except Exception as e:
                    logger.error(f"Error processing {method_name} on labels for tweet {tweet_index}: {str(e)}")
            else:
                logger.warning(f"Skipping {method_name} on labels: only {len(labels)} labels available")
            
            # 测试reasonings
            if reasonings and len(reasonings) >= 2:
                try:
                    logger.info(f"Computing uncertainty for {len(reasonings)} reasonings using {method_name}")
                    reasoning_result = uq_method.compute_uncertainty(reasonings)
                    uncertainty_score = reasoning_result.get('uncertainty_score', 0)
                    mean_similarity = reasoning_result.get('mean_similarity', 0)
                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                    
                    results.append({
                        'data_type': 'twitter',
                        'identifier': tweet_index,
                        'prompt_type': prompt_type,
                        'content_type': 'reasoning',
                        'uq_method': method_name,
                        'uncertainty_score': uncertainty_score,
                        'num_responses': len(reasonings),
                        'mean_similarity': mean_similarity,
                        'method_details': reasoning_result.get('method', ''),
                        'responses': reasonings[:5]  # 只保存前5个响应作为示例
                    })
                except Exception as e:
                    logger.error(f"Error processing {method_name} on reasonings for tweet {tweet_index}: {str(e)}")
            else:
                logger.warning(f"Skipping {method_name} on reasonings: only {len(reasonings)} reasonings available")
    
    return results

def test_uq_methods_on_commit_data(csv_file: str, nli_model: str = "potsawee/deberta-v3-large-mnli"):
    """
    在Commit数据上测试UQ方法
    """
    print(f"\n=== Testing UQ methods on Commit data ===")
    print(f"Using NLI model: {nli_model}")
    
    # 读取数据
    df = pd.read_csv(csv_file)
    print(f"Loaded {len(df)} records from {csv_file}")
    
    # 只取第一个commit的数据
    first_commit_sha = df['commit_sha'].iloc[0]
    df = df[df['commit_sha'] == first_commit_sha]
    logger.info(f"Filtering to first commit (sha={first_commit_sha[:8]}...): {len(df)} records")
    
    # 初始化UQ方法
    logger.info("Initializing UQ methods...")
    uq_methods = {
        'deg_mat': DegMatNLIEntailUQ(model_name=nli_model, verbose=False),
        'eccentricity': EccentricityNLIEntailUQ(model_name=nli_model, verbose=False),
        'eig_val': EigValLaplacianNLIEntailUQ(model_name=nli_model, verbose=False)
    }
    logger.info(f"Initialized {len(uq_methods)} UQ methods")
    
    results = []
    
    # 按commit_sha和prompt_type分组
    grouped = df.groupby(['commit_sha', 'prompt_type'])
    logger.info(f"Processing {len(grouped)} groups (commit_sha, prompt_type combinations)")
    
    for (commit_sha, prompt_type), group in tqdm(grouped, desc="Processing Commit groups"):
        logger.info(f"Processing commit {commit_sha[:8]}..., prompt_type {prompt_type}: {len(group)} responses")
        responses = group['response_text'].dropna().tolist()
        logger.info(f"Found {len(responses)} responses for commit {commit_sha[:8]}..., prompt_type {prompt_type}")
        
        if len(responses) < 2:
            logger.warning(f"Skipping commit {commit_sha[:8]}..., prompt_type {prompt_type}: only {len(responses)} responses")
            continue
            
        # 对每个UQ方法进行测试
        for method_name, uq_method in uq_methods.items():
            logger.info(f"Testing {method_name} method...")
            if prompt_type == 'single_word':
                # 直接对整个响应进行UQ分析
                try:
                    logger.info(f"Computing uncertainty for {len(responses)} single-word responses using {method_name}")
                    result = uq_method.compute_uncertainty(responses)
                    uncertainty_score = result.get('uncertainty_score', 0)
                    mean_similarity = result.get('mean_similarity', 0)
                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                    
                    results.append({
                        'data_type': 'commit',
                        'identifier': commit_sha,
                        'prompt_type': prompt_type,
                        'content_type': 'response',
                        'uq_method': method_name,
                        'uncertainty_score': uncertainty_score,
                        'num_responses': len(responses),
                        'mean_similarity': mean_similarity,
                        'method_details': result.get('method', ''),
                        'responses': responses[:5]  # 只保存前5个响应作为示例
                    })
                except Exception as e:
                    logger.error(f"Error processing {method_name} on responses for commit {commit_sha}: {str(e)}")
            else:
                # 提取modules和reasonings
                modules, reasonings = [], []
                for resp in responses:
                    module, reasoning = split_commit_response(resp, prompt_type)
                    if module: 
                        modules.append(module)
                    if reasoning: 
                        reasonings.append(reasoning)
                
                logger.info(f"Extracted {len(modules)} modules and {len(reasonings)} reasonings")
                
                # 测试modules
                if modules and len(modules) >= 2:
                    try:
                        logger.info(f"Computing uncertainty for {len(modules)} modules using {method_name}")
                        module_result = uq_method.compute_uncertainty(modules)
                        uncertainty_score = module_result.get('uncertainty_score', 0)
                        mean_similarity = module_result.get('mean_similarity', 0)
                        logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                        
                        results.append({
                            'data_type': 'commit',
                            'identifier': commit_sha,
                            'prompt_type': prompt_type,
                            'content_type': 'module',
                            'uq_method': method_name,
                            'uncertainty_score': uncertainty_score,
                            'num_responses': len(modules),
                            'mean_similarity': mean_similarity,
                            'method_details': module_result.get('method', ''),
                            'responses': modules[:5]  # 只保存前5个响应作为示例
                        })
                    except Exception as e:
                        logger.error(f"Error processing {method_name} on modules for commit {commit_sha}: {str(e)}")
                else:
                    logger.warning(f"Skipping {method_name} on modules: only {len(modules)} modules available")
                
                # 测试reasonings
                if reasonings and len(reasonings) >= 2:
                    try:
                        logger.info(f"Computing uncertainty for {len(reasonings)} reasonings using {method_name}")
                        reasoning_result = uq_method.compute_uncertainty(reasonings)
                        uncertainty_score = reasoning_result.get('uncertainty_score', 0)
                        mean_similarity = reasoning_result.get('mean_similarity', 0)
                        logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")
                        
                        results.append({
                            'data_type': 'commit',
                            'identifier': commit_sha,
                            'prompt_type': prompt_type,
                            'content_type': 'reasoning',
                            'uq_method': method_name,
                            'uncertainty_score': uncertainty_score,
                            'num_responses': len(reasonings),
                            'mean_similarity': mean_similarity,
                            'method_details': reasoning_result.get('method', ''),
                            'responses': reasonings[:5]  # 只保存前5个响应作为示例
                        })
                    except Exception as e:
                        logger.error(f"Error processing {method_name} on reasonings for commit {commit_sha}: {str(e)}")
                else:
                    logger.warning(f"Skipping {method_name} on reasonings: only {len(reasonings)} reasonings available")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Test UQ methods on Twitter and Commit data")
    parser.add_argument('--twitter_csv', type=str, default='data/twitter_responses.csv', 
                       help='Twitter responses CSV file')
    parser.add_argument('--commit_csv', type=str, default='data/commit_responses_first5.csv', 
                       help='Commit responses CSV file')
    parser.add_argument('--output_file', type=str, default='data/uq_methods_test_results.csv', 
                       help='Output CSV file for results')
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'],
                       help='NLI model to use')
    
    args = parser.parse_args()
    
    all_results = []
    
    # 测试Twitter数据
    try:
        twitter_results = test_uq_methods_on_twitter_data(args.twitter_csv, args.nli_model)
        all_results.extend(twitter_results)
        print(f"Completed Twitter testing: {len(twitter_results)} results")
    except Exception as e:
        logger.error(f"Error testing Twitter data: {str(e)}")
    
    # 测试Commit数据
    try:
        commit_results = test_uq_methods_on_commit_data(args.commit_csv, args.nli_model)
        all_results.extend(commit_results)
        print(f"Completed Commit testing: {len(commit_results)} results")
    except Exception as e:
        logger.error(f"Error testing Commit data: {str(e)}")
    
    # 保存结果
    if all_results:
        results_df = pd.DataFrame(all_results)
        results_df.to_csv(args.output_file, index=False, encoding='utf-8-sig')
        print(f"\nAll results saved to {args.output_file}")
        print(f"Total results: {len(results_df)}")
        
        # 显示结果摘要
        print("\n=== Results Summary ===")
        summary = results_df.groupby(['data_type', 'uq_method', 'content_type']).agg({
            'uncertainty_score': ['mean', 'std'],
            'mean_similarity': ['mean', 'std'],
            'identifier': 'count'
        }).round(4)
        print(summary)
    else:
        print("No results generated")

if __name__ == "__main__":
    main()