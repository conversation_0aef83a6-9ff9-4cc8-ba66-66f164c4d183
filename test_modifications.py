#!/usr/bin/env python3
"""
测试修改后的LLM响应生成器功能
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_prompt_loading():
    """测试prompt加载功能"""
    logger.info("测试prompt加载功能...")
    
    try:
        # 模拟LLMResponseGenerator的prompt加载逻辑
        templates = {}
        
        # 加载Twitter情感分析prompt模板（10个同义句）
        templates["twitter_sentiment"] = []
        sentiment_dir = "prompts/1_sentiment_analysis"
        for i in range(1, 11):
            file_path = f"{sentiment_dir}/prompt_{i:02d}.txt"
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    templates["twitter_sentiment"].append(content)
                logger.info(f"Loaded sentiment analysis prompt {i}")
            except FileNotFoundError:
                logger.warning(f"Sentiment analysis prompt file not found: {file_path}")
        
        # 加载Commits模块分析prompt模板（10个同义句）
        templates["pytorch_commits"] = []
        commits_dir = "prompts/2_explorative_coding_commits"
        for i in range(1, 11):
            file_path = f"{commits_dir}/prompt_{i:02d}.txt"
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    templates["pytorch_commits"].append(content)
                logger.info(f"Loaded explorative coding prompt {i}")
            except FileNotFoundError:
                logger.warning(f"Explorative coding prompt file not found: {file_path}")
        
        logger.info(f"Loaded {len(templates['twitter_sentiment'])} sentiment analysis prompts")
        logger.info(f"Loaded {len(templates['pytorch_commits'])} explorative coding prompts")
        
        return templates
        
    except Exception as e:
        logger.error(f"Error loading prompts: {e}")
        return None

def test_prompt_sampling():
    """测试prompt采样功能"""
    logger.info("测试prompt采样功能...")
    
    import random
    
    def sample_prompts_for_message(dataset_source: str, message_index: int):
        """为指定message采样5个prompt索引"""
        # 使用message索引作为随机种子，确保可重现性
        random.seed(message_index)
        
        # 从10个prompt中随机选择5个
        available_prompts = list(range(10))  # 0-9对应prompt_01.txt到prompt_10.txt
        selected_prompts = random.sample(available_prompts, 5)
        
        logger.info(f"Message {message_index} ({dataset_source}) selected prompts: {[i+1 for i in selected_prompts]}")
        return selected_prompts
    
    # 测试几个不同的message索引
    for i in range(5):
        sentiment_prompts = sample_prompts_for_message("twitter_sentiment", i)
        commits_prompts = sample_prompts_for_message("pytorch_commits", i)
        
        logger.info(f"Message {i}: sentiment={[p+1 for p in sentiment_prompts]}, commits={[p+1 for p in commits_prompts]}")

def test_thinking_extraction():
    """测试thinking内容提取功能"""
    logger.info("测试thinking内容提取功能...")
    
    import re
    
    def extract_thinking_content(raw_response: str):
        """从qwen响应中提取thinking部分和实际回答部分"""
        result = {
            'thinking': '',
            'response': raw_response or ''
        }
        
        if not raw_response:
            return result
        
        # qwen thinking模式的响应格式通常是：
        # <thinking>...</thinking>
        # 实际回答内容
        thinking_pattern = r'<thinking>(.*?)</thinking>'
        thinking_match = re.search(thinking_pattern, raw_response, re.DOTALL)
        
        if thinking_match:
            result['thinking'] = thinking_match.group(1).strip()
            # 移除thinking部分，获取实际回答
            result['response'] = re.sub(thinking_pattern, '', raw_response, flags=re.DOTALL).strip()
        else:
            result['response'] = raw_response.strip()
        
        return result
    
    # 测试用例
    test_cases = [
        "<thinking>This tweet seems positive because it mentions good things.</thinking>Positive",
        "<thinking>\nLet me analyze this commit message.\nIt seems to be related to the optimizer module.\n</thinking>\noptimizer",
        "Just a regular response without thinking tags",
        "<thinking>Some thinking</thinking>\n\n[Label]: Positive\n[Reasoning]: This is positive because...",
    ]
    
    for i, test_case in enumerate(test_cases):
        result = extract_thinking_content(test_case)
        logger.info(f"Test case {i+1}:")
        logger.info(f"  Thinking: {result['thinking'][:50]}..." if result['thinking'] else "  Thinking: (empty)")
        logger.info(f"  Response: {result['response'][:50]}..." if result['response'] else "  Response: (empty)")

def main():
    """主函数"""
    logger.info("开始测试修改后的功能...")
    
    # 测试prompt加载
    templates = test_prompt_loading()
    if not templates:
        logger.error("Prompt加载测试失败")
        return False
    
    # 测试prompt采样
    test_prompt_sampling()
    
    # 测试thinking内容提取
    test_thinking_extraction()
    
    logger.info("所有测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
