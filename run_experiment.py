import asyncio
import yaml
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
import uuid
from dotenv import load_dotenv

from core.llm_client import QwenClient
from core.cache import SmartCache
from core.parallel import ParallelProcessor
from core.progress import ProgressTracker, SimpleProgressTracker
from core.checkpoint import CheckpointManager
from results.csv_exporter import SimpleCSVExporter
from datasets.base import Dataset
from uq_methods.base import UQMethod
from prompts.manager import PromptManager


class SimpleExperimentRunner:
    """Main experiment runner for LLM uncertainty evaluation"""
    
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
        # Initialize components
        self.llm_client = None
        self.cache = None
        self.parallel_processor = None
        self.progress_tracker = None
        self.checkpoint_manager = None
        self.csv_exporter = None
        self.prompt_manager = None
        
        # Experiment state
        self.experiment_id = str(uuid.uuid4())[:8]
        self.completed_prompts: Set[str] = set()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load experiment configuration from YAML file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Load environment variables
        load_dotenv()
        
        # Replace environment variables in config
        self._replace_env_vars(config)
        
        return config
    
    def _replace_env_vars(self, config: Dict[str, Any]):
        """Replace environment variable placeholders in config"""
        if isinstance(config, dict):
            for key, value in config.items():
                if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                    env_var = value[2:-1]
                    config[key] = os.getenv(env_var, value)
                elif isinstance(value, (dict, list)):
                    self._replace_env_vars(value)
        elif isinstance(config, list):
            for item in config:
                if isinstance(item, (dict, list)):
                    self._replace_env_vars(item)
    
    async def run(self, resume: bool = False) -> Dict[str, Any]:
        """
        Run the complete experiment
        
        Args:
            resume: Whether to resume from a checkpoint
            
        Returns:
            Dictionary with experiment results
        """
        try:
            # Initialize components
            await self._initialize_components()
            
            experiment_name = self.config.get('experiment', {}).get('name', 'default')
            
            # Start experiment tracking
            self.progress_tracker.start_experiment(experiment_name, self.config)
            
            # Run experiment
            results = await self._run_experiment(resume=resume)
            
            # Display summary
            self.progress_tracker.display_summary(results)
            
            return results
            
        except Exception as e:
            self.progress_tracker.log_error(f"实验失败: {str(e)}")
            raise e
        finally:
            await self._cleanup()
    
    async def _initialize_components(self):
        """Initialize all experiment components"""
        openai_config = self.config.get('qwen', {})
        
        # Initialize Qwen client
        self.llm_client = QwenClient(
            api_key=openai_config.get('api_key') or os.getenv('QWEN_API_KEY'),
            model=openai_config.get('model', 'qwen3-32b'),
            base_url=openai_config.get('base_url', 'http://localhost:8000/v1'),
            nli_model=openai_config.get('nli_model', 'qwen3-latest'),
            **openai_config.get('parameters', {})
        )
        
        # Initialize cache
        self.cache = SmartCache(
            cache_dir=self.config.get('cache', {}).get('dir', '.cache'),
            max_size_mb=self.config.get('cache', {}).get('max_size_mb', 100)
        )
        
        # Initialize parallel processor
        self.parallel_processor = ParallelProcessor(
            max_workers=self.config.get('parallel', {}).get('max_workers', 4)
        )
        
        # Initialize progress tracker
        progress_config = self.config.get('progress', {})
        if progress_config.get('show_visual', True):
            self.progress_tracker = ProgressTracker(
                show_visual=progress_config.get('show_visual', True)
            )
        else:
            self.progress_tracker = SimpleProgressTracker()
        
        # Initialize checkpoint manager
        self.checkpoint_manager = CheckpointManager(
            checkpoint_dir=self.config.get('checkpoint', {}).get('dir', 'checkpoints')
        )
        
        # Initialize CSV exporter
        self.csv_exporter = SimpleCSVExporter(
            output_dir=self.config.get('output', {}).get('dir', 'results/csv')
        )
        
        # Initialize prompt manager
        self.prompt_manager = PromptManager()
        
        # Load prompt templates
        for template_config in self.config.get('prompts', {}).get('templates', []):
            self.prompt_manager.add_template(
                template_config['name'],
                template_config['template']
            )
    
    async def _run_experiment(self, resume: bool = False) -> Dict[str, Any]:
        """Run the actual experiment"""
        all_results = {}
        
        # Process each dataset
        for dataset_config in self.config.get('datasets', []):
            dataset_name = dataset_config['name']
            dataset_type = dataset_config['type']
            
            # Load dataset
            dataset = await self._load_dataset(dataset_name, dataset_type, dataset_config)
            
            # Process each UQ method
            for uq_method_config in self.config.get('uq_methods', []):
                uq_method_name = uq_method_config['name']
                uq_method_type = uq_method_config['type']
                
                # Load UQ method
                uq_method = await self._load_uq_method(uq_method_name, uq_method_type, uq_method_config)
                
                # Process dataset with UQ method
                results = await self._process_dataset_with_uq_method(
                    dataset, uq_method, dataset_config, uq_method_config, resume
                )
                
                key = f"{dataset_name}_{uq_method_name}"
                all_results[key] = results
                
                # Export results
                export_paths = self.csv_exporter.export_uq_method_results(
                    experiment_name=self.config.get('experiment', {}).get('name', 'default'),
                    uq_method_name=uq_method_name,
                    results=results,
                    include_metadata=self.config.get('output', {}).get('include_metadata', True)
                )
                
                self.progress_tracker.log_success(
                    f"结果已导出到: {export_paths['results']}"
                )
        
        # Export experiment summary
        summary_path = self.csv_exporter.export_experiment_summary(
            experiment_name=self.config.get('experiment', {}).get('name', 'default'),
            all_results=all_results
        )
        
        if summary_path:
            self.progress_tracker.log_success(f"实验摘要已导出到: {summary_path}")
        
        return {
            'experiment_id': self.experiment_id,
            'total_results': sum(len(results) for results in all_results.values()),
            'datasets_processed': len(self.config.get('datasets', [])),
            'uq_methods_processed': len(self.config.get('uq_methods', [])),
            'results': all_results
        }
    
    async def _load_dataset(self, name: str, dataset_type: str, config: Dict[str, Any]) -> Dataset:
        """Load a dataset by type"""
        # This is a placeholder - implement actual dataset loading
        from datasets.implementations.trivialqa import TrivialQADataset
        
        if dataset_type == 'trivialqa':
            return TrivialQADataset(
                name=name,
                sample_size=config.get('sample_size', 100),
                random_seed=config.get('random_seed', 42)
            )
        else:
            raise ValueError(f"Unknown dataset type: {dataset_type}")
    
    async def _load_uq_method(self, name: str, uq_method_type: str, config: Dict[str, Any]) -> UQMethod:
        """Load a UQ method by type"""
        # This is a placeholder - implement actual UQ method loading
        from uq_methods.implementations.semantic_entropy import SemanticEntropyMethod
        
        if uq_method_type == 'semantic_entropy':
            return SemanticEntropyMethod(
                name=name,
                similarity_threshold=config.get('parameters', {}).get('similarity_threshold', 0.85),
                n_samples=self.config.get('prompts', {}).get('n_samples', 10)
            )
        else:
            raise ValueError(f"Unknown UQ method type: {uq_method_type}")
    
    async def _process_dataset_with_uq_method(
        self,
        dataset: Dataset,
        uq_method: UQMethod,
        dataset_config: Dict[str, Any],
        uq_method_config: Dict[str, Any],
        resume: bool = False
    ) -> List[Dict[str, Any]]:
        """Process a dataset with a specific UQ method"""
        dataset_name = dataset_config['name']
        uq_method_name = uq_method_config['name']
        
        # Load samples from dataset
        samples = await dataset.load_samples()
        
        # Check for checkpoint
        if resume:
            checkpoint_data = self.checkpoint_manager.resume_experiment(
                experiment_id=self.experiment_id,
                dataset_name=dataset_name,
                uq_method_name=uq_method_name
            )
            
            if checkpoint_data:
                self.completed_prompts = checkpoint_data.get('completed_prompts', set())
                self.progress_tracker.log_success(
                    f"从检查点恢复: {len(self.completed_prompts)} 个样本已处理"
                )
        
        # Create progress task
        task_id = self.progress_tracker.add_uq_method_task(uq_method_name, len(samples))
        
        # Process samples
        results = []
        
        for sample in samples:
            sample_id = str(hash(str(sample)))[:8]
            prompt_id = f"{dataset_name}_{uq_method_name}_{sample_id}"
            
            # Skip if already processed (from checkpoint)
            if prompt_id in self.completed_prompts:
                self.progress_tracker.update_progress(task_id)
                continue
            
            try:
                # Generate prompts for this sample
                prompts = []
                for template_name in self.config.get('prompts', {}).get('templates', []):
                    if isinstance(template_name, dict):
                        template_name = template_name['name']
                    
                    prompt = self.prompt_manager.generate_prompt(
                        template_name,
                        sample
                    )
                    prompts.append(prompt)
                
                # Apply UQ method
                uq_result = await uq_method.evaluate_uncertainty(
                    prompts=prompts,
                    llm_client=self.llm_client,
                    sample=sample
                )
                
                # Add metadata
                uq_result.update({
                    'sample_id': sample_id,
                    'dataset': dataset_name,
                    'uq_method': uq_method_name,
                    'prompts': prompts
                })
                
                results.append(uq_result)
                
                # Mark as completed
                self.completed_prompts.add(prompt_id)
                
                # Update progress
                self.progress_tracker.update_progress(task_id)
                
                # Create checkpoint periodically
                if len(results) % 10 == 0:
                    self.checkpoint_manager.create_checkpoint(
                        experiment_id=self.experiment_id,
                        completed_prompts=self.completed_prompts,
                        config=self.config,
                        dataset_name=dataset_name,
                        uq_method_name=uq_method_name
                    )
                
            except Exception as e:
                self.progress_tracker.log_error(
                    f"处理样本 {sample_id} 失败: {str(e)}"
                )
        
        # Final checkpoint
        self.checkpoint_manager.create_checkpoint(
            experiment_id=self.experiment_id,
            completed_prompts=self.completed_prompts,
            config=self.config,
            dataset_name=dataset_name,
            uq_method_name=uq_method_name
        )
        
        return results
    
    async def _cleanup(self):
        """Clean up resources"""
        if self.llm_client:
            await self.llm_client.close()
        if self.cache:
            await self.cache.close()
        if self.parallel_processor:
            await self.parallel_processor.close()


def main():
    """Main entry point for the experiment runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run LLM uncertainty evaluation experiment")
    parser.add_argument("--config", required=True, help="Path to configuration file")
    parser.add_argument("--resume", action="store_true", help="Resume from checkpoint")
    
    args = parser.parse_args()
    
    async def run_experiment():
        runner = SimpleExperimentRunner(args.config)
        results = await runner.run(resume=args.resume)
        
        print(f"\n实验完成!")
        print(f"实验ID: {results['experiment_id']}")
        print(f"总结果数: {results['total_results']}")
        print(f"处理数据集: {results['datasets_processed']}")
        print(f"处理UQ方法: {results['uq_methods_processed']}")
    
    asyncio.run(run_experiment())


if __name__ == "__main__":
    main()