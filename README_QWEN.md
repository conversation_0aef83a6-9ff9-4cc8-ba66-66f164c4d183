# LLM不确定性评估框架 - Qwen阿里云版本

一个完整的LLM不确定性量化评估框架，支持Qwen模型（阿里云DashScope API）和流式输出，专门用于NLI推理任务。

## 🚀 阿里云快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置阿里云环境变量

创建一个 `.env` 文件并添加你的阿里云DashScope API密钥：

```bash
echo "DASHSCOPE_API_KEY=your_dashscope_api_key_here" > .env
```

### 3. 运行Qwen实验

```bash
# 运行Qwen不确定性实验
python run_experiment.py --config config/qwen_alibaba.yaml

# 测试Qwen集成
python test_qwen.py
```

## 🔧 阿里云配置示例

### config/qwen_alibaba.yaml

```yaml
experiment:
  name: "qwen_alibaba_uncertainty_test"
  description: "Testing uncertainty quantification on Alibaba Cloud Qwen models"

qwen:
  api_key: "${DASHSCOPE_API_KEY}"
  model: "qwen3-32b"           # 阿里云Qwen3-32B模型
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  nli_model: "qwen3-latest"    # 阿里云Qwen3-Latest用于NLI推理
  parameters:
    temperature: 0.7
    max_tokens: 512
    stream: true                # 启用流式输出
```