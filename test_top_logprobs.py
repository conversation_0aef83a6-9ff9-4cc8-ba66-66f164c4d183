#!/usr/bin/env python3
"""
测试top_logprobs=5参数的脚本
"""

import logging
from llm_response_generator import LLMResponseGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_top_logprobs():
    """测试top_logprobs=5参数"""
    logger.info("开始测试top_logprobs=5参数...")
    
    try:
        # 创建生成器实例
        generator = LLMResponseGenerator()
        
        # 运行测试模式
        run_id = generator.run(
            attempts_per_variant=1,  # 每个prompt变体只尝试1次
            resume=False,  # 不恢复，创建新的运行
            test_mode=True  # 启用测试模式
        )
        
        logger.info(f"测试完成！运行ID: {run_id}")
        logger.info("结果已保存到test_response collection中")
        
        # 显示测试结果
        show_test_results(generator)
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise

def show_test_results(generator):
    """显示测试结果"""
    logger.info("\n=== 测试结果 ===")
    
    # 查询test_response collection中的所有结果
    results = list(generator.db["test_response"].find({}, sort=[("execution_timestamp", 1)]))
    
    if not results:
        logger.info("没有找到测试结果")
        return
    
    logger.info(f"找到 {len(results)} 条测试结果")
    
    for i, result in enumerate(results, 1):
        logger.info(f"\n--- 结果 {i} ---")
        logger.info(f"数据集: {result.get('dataset_source')}")
        logger.info(f"Prompt变体: {result.get('prompt_variant')}")
        logger.info(f"输入文本: {result.get('input_text', '')[:100]}...")
        logger.info(f"原始响应: {result.get('raw_response', '')[:200]}...")
        logger.info(f"解析答案: {result.get('parsed_answer', '')}")
        logger.info(f"解析原因: {result.get('parsed_reason', '')[:100] if result.get('parsed_reason') else 'None'}...")
        
        # 检查logprobs
        logprobs = result.get('response_logprobs')
        if logprobs:
            logger.info(f"Logprobs可用: 是")
            if isinstance(logprobs, list):
                logger.info(f"Logprobs列表长度: {len(logprobs)}")
            elif isinstance(logprobs, dict):
                logger.info(f"Logprobs字典键: {list(logprobs.keys())}")
        else:
            logger.info(f"Logprobs可用: 否")

if __name__ == "__main__":
    test_top_logprobs()
