import os
import csv
import uuid
import logging
import re
import yaml
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from openai import OpenAI
from pymongo import MongoClient
from prompts.json_prompt_manager import JSONPromptManager

# ================================
# 配置加载
# ================================

def load_config(config_file: str = "config.yaml") -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        print(f"Warning: Config file {config_file} not found, using default settings")
        return {}
    except Exception as e:
        print(f"Error loading config file {config_file}: {e}")
        return {}

# 加载全局配置
CONFIG = load_config()

# ================================
# 日志配置
# ================================

log_level = CONFIG.get('logging', {}).get('level', 'INFO')
log_file = CONFIG.get('logging', {}).get('file', 'llm_response_generator.log')

logging.basicConfig(
    level=getattr(logging, log_level),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LLMResponseGenerator:
    """LLM响应生成器，负责调用LLM和保存响应"""

    def __init__(self, config_file: str = "config.yaml"):
        """初始化LLM响应生成器"""
        logger.info("Initializing LLMResponseGenerator...")

        # 加载配置
        self.config = load_config(config_file) if config_file != "config.yaml" else CONFIG

        # 初始化OpenAI客户端
        model_config = self.config.get('model', {})
        self.client = OpenAI(
            api_key=os.getenv(model_config.get('api_key_env', 'DASHSCOPE_API_KEY'), 'null'),
            base_url=model_config.get('base_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1'),
        )

        # 初始化MongoDB连接
        output_config = self.config.get('output', {})
        if output_config.get('format') == 'mongodb':
            mongo_config = output_config.get('mongo', {})
            self.mongo_client = MongoClient(f"mongodb://{mongo_config.get('host', 'localhost')}:{mongo_config.get('port', 27017)}/")
            self.db = self.mongo_client[mongo_config.get('database', 'LLM-UQ')]
            self.collection = self.db[mongo_config.get('collection', 'response_collection')]
            self._create_indexes()
        else:
            self.mongo_client = None
            self.db = None
            self.collection = None

        # 初始化JSON prompt管理器
        self.prompt_manager = JSONPromptManager()

        # 验证所有启用任务的prompt配置
        tasks_config = self.config.get('tasks', {})
        for task_name, task_config in tasks_config.items():
            if task_config.get('enabled', True):
                if not self.prompt_manager.validate_task_config(task_name):
                    logger.warning(f"Task '{task_name}' has invalid prompt configuration")

        logger.info("LLMResponseGenerator initialized successfully")

    def _create_indexes(self):
        """创建MongoDB索引"""
        if self.collection is None:
            return

        try:
            self.collection.create_index([("run_id", 1)])
            self.collection.create_index([("task_id", 1)])
            self.collection.create_index([("dataset_source", 1)])
            self.collection.create_index([("task_name", 1)])
            self.collection.create_index([("execution_timestamp", -1)])
            self.collection.create_index([("prompt_seed", 1)])
            self.collection.create_index([("prompt_index", 1)])
            logger.info(f"MongoDB indexes created successfully")
        except Exception as e:
            logger.error(f"Error creating MongoDB indexes: {e}")
        

    
    def get_task_prompts_json(self, task_name: str, sample_count: int = None, seed: int = None) -> List[Dict[str, Any]]:
        """从JSON配置获取任务的prompt模板，支持固定种子确保可重现性"""
        task_config = self.config.get('tasks', {}).get(task_name, {})
        sample_prompts = sample_count or task_config.get('sample_prompts', 5)

        # 获取所有可用的prompts
        all_prompts = self.prompt_manager.get_task_prompts(task_name)

        if not all_prompts:
            logger.warning(f"No prompts found for task '{task_name}'")
            return []

        # 如果请求的数量大于等于可用数量，返回所有
        if sample_prompts >= len(all_prompts):
            logger.info(f"Using all {len(all_prompts)} prompts for task '{task_name}'")
            return all_prompts

        # 使用种子随机选择指定数量的prompts
        selected_prompts = self.prompt_manager.get_random_prompts(task_name, sample_prompts, seed=seed)
        if seed is not None:
            logger.info(f"Selected {len(selected_prompts)} prompts from {len(all_prompts)} available for task '{task_name}' with seed {seed}")
        else:
            logger.info(f"Selected {len(selected_prompts)} prompts from {len(all_prompts)} available for task '{task_name}'")

        return selected_prompts
    
    def call_llm(self, prompt: str, model_name: str = None) -> Dict[str, Any]:
        """调用LLM API"""
        model_config = self.config.get('model', {})
        model_name = model_name or model_config.get('name', 'qwen3-32b')

        logger.info(f"Calling LLM with model: {model_name}")
        logger.debug(f"Prompt length: {len(prompt)} characters")

        try:
            # 构建API调用参数
            api_params = {
                "model": model_name,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": model_config.get('temperature', 0.7),
                "top_p": model_config.get('top_p', 0.95),
            }

            if model_config.get('max_tokens'):
                api_params["max_tokens"] = model_config['max_tokens']

            # 检查模型是否支持logprobs
            if model_config.get('enable_logprobs', False) and not model_name.startswith('deepseek'):
                api_params["logprobs"] = True
                api_params["top_logprobs"] = model_config.get('top_logprobs', 5)
            elif model_name.startswith('deepseek') and model_config.get('enable_logprobs', False):
                logger.warning(f"Model {model_name} does not support logprobs, skipping logprobs parameters")

            # 对于qwen模型，总是需要设置enable_thinking参数
            if model_name.startswith('qwen'):
                enable_thinking = model_config.get('enable_thinking', False)
                api_params["extra_body"] = {"enable_thinking": enable_thinking}

            # 检查是否启用流式调用
            use_stream = model_config.get('stream', False)
            if use_stream:
                api_params["stream"] = True

            if use_stream:
                # 流式调用
                stream = self.client.chat.completions.create(**api_params)
                return self._handle_stream_response(stream, model_name)
            else:
                # 非流式调用
                completion = self.client.chat.completions.create(**api_params)
                return self._handle_non_stream_response(completion, model_name)
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            return {'content': '', 'logprobs': None, 'model': model_name, 'finish_reason': None}

    def _handle_stream_response(self, stream, model_name: str) -> Dict[str, Any]:
        """处理流式响应"""
        logger.info("Processing stream response...")

        content_parts = []
        finish_reason = None

        try:
            for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]

                    # 提取内容
                    if hasattr(choice, 'delta') and choice.delta:
                        if hasattr(choice.delta, 'content') and choice.delta.content:
                            content_parts.append(choice.delta.content)

                    # 提取结束原因
                    if hasattr(choice, 'finish_reason') and choice.finish_reason:
                        finish_reason = choice.finish_reason

            # 合并所有内容
            full_content = ''.join(content_parts)

            logger.info(f"Stream response completed, content length: {len(full_content)}")

            return {
                'content': full_content,
                'logprobs': None,  # 流式调用通常不返回logprobs
                'model': model_name,
                'finish_reason': finish_reason
            }

        except Exception as e:
            logger.error(f"Error processing stream response: {e}")
            return {'content': '', 'logprobs': None, 'model': model_name, 'finish_reason': None}

    def _handle_non_stream_response(self, completion, model_name: str) -> Dict[str, Any]:
        """处理非流式响应"""
        logger.info(f"LLM API call successful, received {len(completion.choices)} choices")

        if completion.choices and len(completion.choices) > 0:
            choice = completion.choices[0]

            # 安全地提取content
            content = ""
            if hasattr(choice, 'message') and choice.message:
                content = choice.message.content or ""
            else:
                logger.warning("No message content found in LLM response")

            # 安全地提取logprobs
            logprobs = None
            try:
                if hasattr(choice, 'message') and choice.message:
                    if hasattr(choice.message, 'logprobs'):
                        logprobs = choice.message.logprobs
                        if logprobs:
                            logger.info(f"Logprobs extracted successfully")
            except Exception as e:
                logger.warning(f"Error extracting logprobs: {e}")

            result = {
                'content': content,
                'logprobs': logprobs,
                'model': model_name,
                'finish_reason': getattr(choice, 'finish_reason', None)
            }

            logger.info(f"Successfully extracted response content (length: {len(content)})")
            return result
        else:
            logger.warning("No choices returned from LLM API")
            return {'content': '', 'logprobs': None, 'model': model_name, 'finish_reason': None}

    def extract_thinking_content(self, raw_response: str) -> Dict[str, str]:
        """从qwen响应中提取thinking部分和实际回答部分"""
        logger.debug(f"Extracting thinking content from response: {raw_response[:200] if raw_response else 'None'}...")

        result = {
            'thinking': '',
            'response': raw_response or ''
        }

        if not raw_response:
            return result

        # qwen thinking模式的响应格式通常是：
        # <thinking>...</thinking>
        # 实际回答内容
        thinking_pattern = r'<thinking>(.*?)</thinking>'
        thinking_match = re.search(thinking_pattern, raw_response, re.DOTALL)

        if thinking_match:
            result['thinking'] = thinking_match.group(1).strip()
            # 移除thinking部分，获取实际回答
            result['response'] = re.sub(thinking_pattern, '', raw_response, flags=re.DOTALL).strip()
            logger.debug(f"Extracted thinking content (length: {len(result['thinking'])})")
            logger.debug(f"Extracted response content (length: {len(result['response'])})")
        else:
            logger.debug("No thinking tags found in response")
            result['response'] = raw_response.strip()

        return result



    def parse_response(self, raw_response: str, prompt_variant: str, dataset_source: str) -> Dict[str, Any]:
        """解析LLM响应"""
        logger.debug(f"Parsing response for {dataset_source} with variant {prompt_variant}")
        logger.debug(f"Raw response: {raw_response[:200] if raw_response else 'None'}...")
        
        parsed = {
            'raw_answer': raw_response,
            'parsed_answer': None,
            'parsed_reason': None
        }
        
        # Handle None or empty responses
        if raw_response is None or raw_response.strip() == "":
            logger.warning("Empty or None raw response received")
            parsed['raw_answer'] = "" if raw_response is None else raw_response
            return parsed
        
        if dataset_source == "twitter_sentiment":
            # Twitter情感分析响应解析
            if prompt_variant == "only_answer":
                # 只返回答案，直接使用原始响应
                parsed['parsed_answer'] = raw_response.strip()
                logger.debug(f"Only answer mode - parsed answer: {parsed['parsed_answer']}")

            elif prompt_variant == "sampled":
                # 采样模式：优先提取 [Label]: <label>，否则用情感词兜底
                try:
                    text = raw_response.strip()
                    label = None
                    if '[Label]:' in text:
                        # 提取 [Label]: 之后到行尾
                        try:
                            label = text.split('[Label]:', 1)[1].strip().splitlines()[0].strip()
                        except Exception:
                            label = text.split('[Label]:', 1)[1].strip()
                    if not label:
                        m = re.search(r"(?i)\b(positive|negative|neutral)\b", text)
                        if m:
                            label = m.group(1).lower()
                    if label:
                        parsed['parsed_answer'] = label
                    else:
                        parsed['parsed_answer'] = text
                except Exception as e:
                    logger.warning(f"Error parsing sampled sentiment format: {e}")
                    parsed['parsed_answer'] = raw_response.strip()

            elif prompt_variant == "answer_then_reason":
                # 格式: [Label]: <label> [Reasoning]: <reasoning>
                try:
                    if '[Label]:' in raw_response and '[Reasoning]:' in raw_response:
                        label_part = raw_response.split('[Reasoning]:')[0]
                        reason_part = raw_response.split('[Reasoning]:')[1]
                        
                        label = label_part.split('[Label]:')[1].strip()
                        reason = reason_part.strip()
                        
                        parsed['parsed_answer'] = label
                        parsed['parsed_reason'] = reason
                        logger.debug(f"Answer then reason mode - parsed: label='{label}', reason='{reason[:50]}...'")
                    else:
                        parsed['parsed_answer'] = raw_response.strip()
                        logger.warning("Expected format not found in answer_then_reason mode")
                except Exception as e:
                    logger.warning(f"Error parsing answer_then_reason format: {e}")
                    parsed['parsed_answer'] = raw_response.strip()
                    
            elif prompt_variant == "reasoning_then_answer":
                # 格式: [Reasoning]: <reasoning> [Label]: <label>
                try:
                    if '[Reasoning]:' in raw_response and '[Label]:' in raw_response:
                        reason_part = raw_response.split('[Label]:')[0]
                        label_part = raw_response.split('[Label]:')[1]
                        
                        reason = reason_part.split('[Reasoning]:')[1].strip()
                        label = label_part.strip()
                        
                        parsed['parsed_answer'] = label
                        parsed['parsed_reason'] = reason
                        logger.debug(f"Reasoning then answer mode - parsed: label='{label}', reason='{reason[:50]}...'")
                    else:
                        parsed['parsed_answer'] = raw_response.strip()
                        logger.warning("Expected format not found in reasoning_then_answer mode")
                except Exception as e:
                    logger.warning(f"Error parsing reasoning_then_answer format: {e}")
                    parsed['parsed_answer'] = raw_response.strip()

        elif dataset_source == "topic_model_data":
            # Topic modeling标签响应解析
            # 对于topic labeling，我们期望简单的标签响应，直接使用原始响应
            parsed['parsed_answer'] = raw_response.strip()
            logger.debug(f"Topic labeling mode - parsed answer: {parsed['parsed_answer']}")

        elif dataset_source == "counterfactual_data":
            # Counterfactual QA响应解析
            # 对于counterfactual问答，我们期望详细的分析响应，直接使用原始响应
            parsed['parsed_answer'] = raw_response.strip()
            logger.debug(f"Counterfactual QA mode - parsed answer length: {len(parsed['parsed_answer'])}")

        elif dataset_source == "pytorch_commits":
            # Commits模块分析响应解析
            if prompt_variant == "only_answer":
                # 只返回答案，直接使用原始响应
                parsed['parsed_answer'] = raw_response.strip()
                logger.debug(f"Only answer mode - parsed answer: {parsed['parsed_answer']}")
                
            elif prompt_variant == "answer_then_reason":
                # 格式: Module: <module> Reasoning: <reasoning>
                try:
                    if 'Module:' in raw_response and 'Reasoning:' in raw_response:
                        module_part = raw_response.split('Reasoning:')[0]
                        reason_part = raw_response.split('Reasoning:')[1]
                        
                        module = module_part.split('Module:')[1].strip()
                        reason = reason_part.strip()
                        
                        parsed['parsed_answer'] = module
                        parsed['parsed_reason'] = reason
                        logger.debug(f"Answer then reason mode - parsed: module='{module}', reason='{reason[:50]}...'")
                    else:
                        parsed['parsed_answer'] = raw_response.strip()
                        logger.warning("Expected format not found in answer_then_reason mode")
                except Exception as e:
                    logger.warning(f"Error parsing answer_then_reason format: {e}")
                    parsed['parsed_answer'] = raw_response.strip()
                    
            elif prompt_variant == "reasoning_then_answer":
                # 格式: Reasoning: <reasoning> Module: <module>
                try:
                    if 'Reasoning:' in raw_response and 'Module:' in raw_response:
                        reason_part = raw_response.split('Module:')[0]
                        module_part = raw_response.split('Module:')[1]
                        
                        reason = reason_part.split('Reasoning:')[1].strip()
                        module = module_part.strip()
                        
                        parsed['parsed_answer'] = module
                        parsed['parsed_reason'] = reason
                        logger.debug(f"Reasoning then answer mode - parsed: module='{module}', reason='{reason[:50]}...'")
                    else:
                        parsed['parsed_answer'] = raw_response.strip()
                        logger.warning("Expected format not found in reasoning_then_answer mode")
                except Exception as e:
                    logger.warning(f"Error parsing reasoning_then_answer format: {e}")
                    parsed['parsed_answer'] = raw_response.strip()
        
        logger.info(f"Response parsing completed for {dataset_source} - {prompt_variant}")
        return parsed
    
    def generate_task_id(self, dataset_source: str, input_id: str, prompt_variant: str) -> str:
        """生成任务ID"""
        task_id = f"task_{dataset_source}_{input_id}_{prompt_variant}"
        logger.debug(f"Generated task ID: {task_id}")
        return task_id
    
    def save_to_mongodb(self, data: Dict[str, Any]):
        """保存数据到MongoDB"""
        if self.collection is None:
            logger.error("MongoDB collection not initialized")
            return None

        try:
            result = self.collection.insert_one(data)
            logger.debug(f"Successfully saved document to MongoDB with ID: {result.inserted_id}")
            return result.inserted_id
        except Exception as e:
            logger.error(f"Error saving to MongoDB: {e}")
            return None
    
    def load_semeval_data_from_csv(self) -> List[Dict[str, Any]]:
        """从CSV文件加载SemEval采样数据"""
        task_config = self.config.get('tasks', {}).get('sentiment_analysis', {})
        data_file = task_config.get('data_file', 'sampled_semeval.csv')

        logger.info(f"Loading SemEval data from {data_file}...")
        data = []
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(dict(row))  # 保留所有字段
            logger.info(f"Loaded {len(data)} SemEval records from CSV")
        except FileNotFoundError:
            logger.warning(f"Warning: {data_file} not found")
        except Exception as e:
            logger.error(f"Error loading SemEval data: {e}")
        return data
    
    def load_commits_data_from_csv(self) -> List[Dict[str, Any]]:
        """从CSV文件加载Commits采样数据"""
        task_config = self.config.get('tasks', {}).get('explorative_coding', {})
        data_file = task_config.get('data_file', 'sampled_commits.csv')

        logger.info(f"Loading Commits data from {data_file}...")
        data = []
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(dict(row))  # 保留所有字段
            logger.info(f"Loaded {len(data)} Commits records from CSV")
        except FileNotFoundError:
            logger.warning(f"Warning: {data_file} not found")
        except Exception as e:
            logger.error(f"Error loading Commits data: {e}")
        return data

    def load_topic_modeling_data_from_csv(self) -> List[Dict[str, Any]]:
        """从CSV文件加载Topic Modeling数据"""
        task_config = self.config.get('tasks', {}).get('topic_labeling', {})
        data_file = task_config.get('data_file', 'data/topic_modeling/topic-model-topwords-label.csv')

        logger.info(f"Loading Topic Modeling data from {data_file}...")
        data = []
        try:
            with open(data_file, 'r', encoding='utf-8-sig') as f:  # 使用utf-8-sig处理BOM
                reader = csv.DictReader(f)
                for row in reader:
                    # 清理可能的BOM字符
                    cleaned_row = {}
                    for key, value in row.items():
                        clean_key = key.lstrip('\ufeff')  # 移除BOM字符
                        cleaned_row[clean_key] = value
                    data.append(cleaned_row)
            logger.info(f"Loaded {len(data)} Topic Modeling records from CSV")
        except FileNotFoundError:
            logger.warning(f"Warning: {data_file} not found")
        except Exception as e:
            logger.error(f"Error loading Topic Modeling data: {e}")
        return data

    def load_counterfactual_data_from_csv(self) -> List[Dict[str, Any]]:
        """从CSV文件加载Counterfactual QA数据"""
        task_config = self.config.get('tasks', {}).get('counterfactual_qa', {})
        data_file = task_config.get('data_file', 'data/conterfactual/counterfactual_prompts.csv')

        logger.info(f"Loading Counterfactual QA data from {data_file}...")
        data = []
        try:
            with open(data_file, 'r', encoding='utf-8-sig') as f:  # 使用utf-8-sig处理BOM
                reader = csv.DictReader(f)
                for row_idx, row in enumerate(reader):
                    # 清理可能的BOM字符
                    cleaned_row = {}
                    for key, value in row.items():
                        clean_key = key.lstrip('\ufeff')  # 移除BOM字符
                        cleaned_row[clean_key] = value

                    # 跳过空行
                    if not cleaned_row.get('Prompt', '').strip():
                        continue

                    # 添加行索引作为唯一标识符
                    cleaned_row['row_index'] = row_idx
                    data.append(cleaned_row)
            logger.info(f"Loaded {len(data)} Counterfactual QA records from CSV")
        except FileNotFoundError:
            logger.warning(f"Warning: {data_file} not found")
        except Exception as e:
            logger.error(f"Error loading Counterfactual QA data: {e}")
        return data

    def process_semeval_data(self, data: List[Dict[str, Any]], run_id: str = None,
                           processed_items: Dict[str, Any] = None):
        """处理SemEval数据 - 使用配置驱动的方式"""
        task_name = "sentiment_analysis"
        task_config = self.config.get('tasks', {}).get(task_name, {})

        if not run_id:
            run_id = str(uuid.uuid4())

        task_attempt_total = 0
        dataset_source = task_config.get('dataset_source', 'twitter_sentiment')
        attempts_per_prompt = task_config.get('attempts_per_prompt', 8)
        sample_prompts = task_config.get('sample_prompts', 5)

        logger.info(f"开始处理SemEval数据，共 {len(data)} 个项目")
        logger.info(f"每个项目随机选择{sample_prompts}个prompt，每个prompt尝试 {attempts_per_prompt} 次")
        logger.info(f"运行ID: {run_id}")

        # 统计跳过和需要处理的项目
        skipped_items = 0
        to_process_items = 0

        for item_idx, item in enumerate(data):
            # 从配置中获取字段名
            id_field = task_config.get('id_field', 'id')
            text_field = task_config.get('text_field', 'text')
            label_field = task_config.get('label_field', 'label')

            input_text = item[text_field]
            reference_answer = item.get(label_field) if label_field else None
            item_id = item[id_field]

            logger.info(f"处理SemEval项目 {item_idx + 1}/{len(data)}: {item_id}")

            # 获取当前任务的prompt模板，使用数据项索引作为随机种子确保可重现性
            selected_prompts = self.get_task_prompts_json(task_name, seed=item_idx)

            # 处理每个选中的prompt
            for prompt_idx, prompt_dict in enumerate(selected_prompts):
                prompt_id = prompt_dict.get('id', f'prompt_{prompt_idx+1}')
                prompt_number = prompt_idx + 1  # 转换为1-N的编号

                # 检查是否应该跳过
                prompt_key = f"{item_id}_prompt_{prompt_number}"
                if processed_items and self.should_skip_item(
                    dataset_source, prompt_key, "sampled", processed_items, attempts_per_prompt):
                    skipped_items += 1
                    continue

                to_process_items += 1
                logger.info(f"处理SemEval项目 {item_id} 使用 {prompt_id}...")

                # 计算需要处理的尝试次数
                if processed_items:
                    next_attempt = self.get_next_attempt_number(
                        dataset_source, prompt_key, "sampled", processed_items)
                    attempts_to_process = attempts_per_prompt - (next_attempt - 1)
                else:
                    next_attempt = 1
                    attempts_to_process = attempts_per_prompt

                logger.info(f"  需要处理 {attempts_to_process} 次尝试，从第 {next_attempt} 次开始")

                # 并发处理每个prompt的多次尝试
                from concurrent.futures import ThreadPoolExecutor, as_completed
                system_cfg = self.config.get('system', {})
                max_workers = max(1, min(system_cfg.get('max_concurrent_requests', 10), attempts_to_process))

                def process_single_attempt(current_attempt_local: int):
                    task_id_local = self.generate_task_id(dataset_source, prompt_key, "sampled")
                    template_var_local = task_config.get('template_variable', 'tweet')
                    # 使用JSON prompt管理器格式化prompt
                    final_prompt_local = self.prompt_manager.format_prompt(
                        prompt_dict, **{template_var_local: input_text}
                    )
                    logger.info(f"  尝试 {current_attempt_local}/{attempts_per_prompt}...")
                    llm_response_local = self.call_llm(final_prompt_local)
                    if not llm_response_local['content']:
                        logger.warning(f"    第 {current_attempt_local} 次尝试收到空响应")
                        return None
                    thinking_response_local = self.extract_thinking_content(llm_response_local['content'])
                    parsed_response_local = self.parse_response(thinking_response_local['response'], "sampled", dataset_source)
                    model_config_local = self.config.get('model', {})
                    document_local = {
                        "run_id": run_id,
                        "task_id": task_id_local,
                        "task_name": task_name,
                        "dataset_source": dataset_source,
                        "task_category": task_config.get('task_category', 'sentiment_analysis'),
                        "input_text": input_text,
                        "reference_answer": reference_answer,
                        "model_identifier": llm_response_local['model'],
                        "prompt_variant": "sampled",
                        "prompt_seed": item_idx,
                        "prompt_index": prompt_number,
                        "prompt_raw_text": final_prompt_local,
                        "generation_config": {
                            "temperature": model_config_local.get('temperature', 0.7),
                            "top_p": model_config_local.get('top_p', 0.95),
                            "enable_thinking": model_config_local.get('enable_thinking', True),
                            "enable_logprobs": model_config_local.get('enable_logprobs', True),
                            "top_logprobs": model_config_local.get('top_logprobs', 5)
                        },
                        "task_attempt_prompt": current_attempt_local,
                        "task_attempt_total": None,
                        "raw_response": llm_response_local['content'],
                        "thinking_content": thinking_response_local['thinking'],
                        "actual_response": thinking_response_local['response'],
                        "response_logprobs": llm_response_local['logprobs'],
                        "finish_reason": llm_response_local.get('finish_reason'),
                        "raw_answer": parsed_response_local['raw_answer'],
                        "parsed_answer": parsed_response_local['parsed_answer'],
                        "parsed_reason": parsed_response_local['parsed_reason'],
                        "execution_timestamp": datetime.now(timezone.utc)
                    }
                    return document_local

                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = []
                    for attempt_offset in range(attempts_to_process):
                        current_attempt = next_attempt + attempt_offset
                        futures.append(executor.submit(process_single_attempt, current_attempt))
                    for future in as_completed(futures):
                        doc = future.result()
                        if doc is None:
                            continue
                        inserted_id = self.save_to_mongodb(doc)
                        if inserted_id:
                            logger.info(f"    保存到MongoDB，ID: {inserted_id}")
                        else:
                            logger.error(f"    并发尝试保存到MongoDB失败")

        logger.info(f"SemEval数据处理完成。跳过 {skipped_items} 个项目，处理 {to_process_items} 个项目，总计 {task_attempt_total} 次尝试")
    
    def process_commits_data(self, data: List[Dict[str, Any]], attempts_per_prompt: int = 8,
                           run_id: str = None, processed_items: Dict[str, Any] = None):
        """处理Commits数据 - 新版本使用随机采样的prompt"""
        if not run_id:
            run_id = str(uuid.uuid4())

        task_attempt_total = 0
        task_name = "explorative_coding"
        dataset_source = "pytorch_commits"

        logger.info(f"开始处理Commits数据，共 {len(data)} 个项目")
        logger.info(f"每个项目随机选择5个prompt，每个prompt尝试 {attempts_per_prompt} 次")
        logger.info(f"运行ID: {run_id}")

        # 统计跳过和需要处理的项目
        skipped_items = 0
        to_process_items = 0

        for item_idx, item in enumerate(data):
            # 准备commits数据
            commit_data = {
                'repo_name': 'pytorch/pytorch',
                'sha': item['sha'],
                'author': item['author'],
                'date': item['date'],
                'message': item['message']
            }
            reference_answer = None  # Commits没有预定义的答案

            logger.info(f"处理Commits项目 {item_idx + 1}/{len(data)}: {item['sha'][:8]}")

            # 获取当前任务的prompt模板，使用数据项索引作为随机种子确保可重现性
            selected_prompts = self.get_task_prompts_json(task_name, seed=item_idx)

            # 处理每个选中的prompt
            for prompt_idx, prompt_dict in enumerate(selected_prompts):
                prompt_id = prompt_dict.get('id', f'prompt_{prompt_idx+1}')
                prompt_number = prompt_idx + 1  # 转换为1-N的编号

                # 检查是否应该跳过
                prompt_key = f"{item['sha']}_prompt_{prompt_number}"
                if processed_items and self.should_skip_item(
                    dataset_source, prompt_key, "sampled", processed_items, attempts_per_prompt):
                    skipped_items += 1
                    continue

                to_process_items += 1
                logger.info(f"处理commit {item['sha'][:8]} 使用 {prompt_id}...")

                # 计算需要处理的尝试次数
                if processed_items:
                    next_attempt = self.get_next_attempt_number(
                        dataset_source, prompt_key, "sampled", processed_items)
                    attempts_to_process = attempts_per_prompt - (next_attempt - 1)
                else:
                    next_attempt = 1
                    attempts_to_process = attempts_per_prompt

                logger.info(f"  需要处理 {attempts_to_process} 次尝试，从第 {next_attempt} 次开始")

                # 并发处理每个prompt的多次尝试（Commits）
                from concurrent.futures import ThreadPoolExecutor, as_completed
                system_cfg = self.config.get('system', {})
                max_workers = max(1, min(system_cfg.get('max_concurrent_requests', 10), attempts_to_process))

                def process_single_attempt(current_attempt_local: int):
                    task_id_local = self.generate_task_id(dataset_source, prompt_key, "sampled")
                    # 使用JSON prompt管理器格式化prompt
                    final_prompt_local = self.prompt_manager.format_prompt(prompt_dict, **commit_data)
                    logger.info(f"  尝试 {current_attempt_local}/{attempts_per_prompt}...")
                    llm_response_local = self.call_llm(final_prompt_local)
                    if not llm_response_local['content']:
                        logger.warning(f"    第 {current_attempt_local} 次尝试收到空响应")
                        return None
                    thinking_response_local = self.extract_thinking_content(llm_response_local['content'])
                    parsed_response_local = self.parse_response(thinking_response_local['response'], "sampled", dataset_source)
                    document_local = {
                        "run_id": run_id,
                        "task_id": task_id_local,
                        "dataset_source": dataset_source,
                        "task_name": task_name,
                        "task_category": "open_explorative_coding",
                        "input_text": item['message'],
                        "reference_answer": reference_answer,
                        "model_identifier": llm_response_local['model'],
                        "prompt_variant": "sampled",
                        "prompt_seed": item_idx,
                        "prompt_index": prompt_number,
                        "prompt_raw_text": final_prompt_local,
                        "generation_config": {
                            "temperature": 0.7,
                            "top_p": 0.95,
                            "enable_thinking": True
                        },
                        "task_attempt_prompt": current_attempt_local,
                        "task_attempt_total": None,
                        "raw_response": llm_response_local['content'],
                        "thinking_content": thinking_response_local['thinking'],
                        "actual_response": thinking_response_local['response'],
                        "response_logprobs": llm_response_local['logprobs'],
                        "finish_reason": llm_response_local.get('finish_reason'),
                        "raw_answer": parsed_response_local['raw_answer'],
                        "parsed_answer": parsed_response_local['parsed_answer'],
                        "parsed_reason": parsed_response_local['parsed_reason'],
                        "execution_timestamp": datetime.now(timezone.utc)
                    }
                    return document_local

                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = []
                    for attempt_offset in range(attempts_to_process):
                        current_attempt = next_attempt + attempt_offset
                        futures.append(executor.submit(process_single_attempt, current_attempt))
                    for future in as_completed(futures):
                        doc = future.result()
                        if doc is None:
                            continue
                        inserted_id = self.save_to_mongodb(doc)
                        if inserted_id:
                            logger.info(f"    保存到MongoDB，ID: {inserted_id}")
                        else:
                            logger.error(f"    并发尝试保存到MongoDB失败")

        logger.info(f"Commits数据处理完成。跳过 {skipped_items} 个项目，处理 {to_process_items} 个项目，总计 {task_attempt_total} 次尝试")

    def process_topic_labeling_data(self, data: List[Dict[str, Any]], run_id: str = None,
                                  processed_items: Dict[str, Any] = None):
        """处理Topic Labeling数据 - 使用配置驱动的方式"""
        task_name = "topic_labeling"
        task_config = self.config.get('tasks', {}).get(task_name, {})

        if not run_id:
            run_id = str(uuid.uuid4())

        task_attempt_total = 0
        dataset_source = task_config.get('dataset_source', 'topic_model_data')
        attempts_per_prompt = task_config.get('attempts_per_prompt', 6)

        logger.info(f"开始处理Topic Labeling数据，共 {len(data)} 个项目")
        logger.info(f"每个项目使用多个prompt，每个prompt尝试 {attempts_per_prompt} 次")
        logger.info(f"运行ID: {run_id}")

        # 统计跳过和需要处理的项目
        skipped_items = 0
        to_process_items = 0

        for item_idx, item in enumerate(data):
            # 从配置中获取字段名
            id_field = task_config.get('id_field', 'topic_number')
            text_field = task_config.get('text_field', 'key_terms')
            label_field = task_config.get('label_field', 'original_label')

            input_text = item[text_field]
            reference_answer = item.get(label_field) if label_field else None
            item_id = f"{item.get('paper_name', 'unknown')}_{item.get('table_number', 'unknown')}_{item[id_field]}"

            logger.info(f"处理Topic项目 {item_idx + 1}/{len(data)}: {item_id}")

            # 获取当前任务的prompt模板，使用数据项索引作为随机种子确保可重现性
            selected_prompts = self.get_task_prompts_json(task_name, seed=item_idx)

            # 处理每个选中的prompt
            for prompt_idx, prompt_dict in enumerate(selected_prompts):
                prompt_id = prompt_dict.get('id', f'prompt_{prompt_idx+1}')
                prompt_number = prompt_idx + 1  # 转换为1-N的编号

                # 检查是否应该跳过
                prompt_key = f"{item_id}_prompt_{prompt_number}"
                if processed_items and self.should_skip_item(
                    dataset_source, prompt_key, "sampled", processed_items, attempts_per_prompt):
                    skipped_items += 1
                    continue

                to_process_items += 1
                logger.info(f"处理Topic项目 {item_id} 使用 {prompt_id}...")

                # 计算需要处理的尝试次数
                if processed_items:
                    next_attempt = self.get_next_attempt_number(
                        dataset_source, prompt_key, "sampled", processed_items)
                    attempts_to_process = attempts_per_prompt - (next_attempt - 1)
                else:
                    next_attempt = 1
                    attempts_to_process = attempts_per_prompt

                logger.info(f"  需要处理 {attempts_to_process} 次尝试，从第 {next_attempt} 次开始")

                # 处理每次尝试
                for current_attempt in range(next_attempt, next_attempt + attempts_to_process):
                    task_id_local = self.generate_task_id(dataset_source, prompt_key, "sampled")
                    template_var_local = task_config.get('template_variable', 'key_terms')
                    # 使用JSON prompt管理器格式化prompt
                    final_prompt_local = self.prompt_manager.format_prompt(
                        prompt_dict, **{template_var_local: input_text}
                    )
                    logger.info(f"  尝试 {current_attempt}/{attempts_per_prompt}...")

                    # 记录prompt信息
                    prompt_tokens = len(final_prompt_local.split())
                    logger.info(f"    发送Prompt (约{prompt_tokens}词):")
                    logger.info(f"    完整Prompt内容:\n{final_prompt_local}")

                    llm_response_local = self.call_llm(final_prompt_local)
                    if not llm_response_local['content']:
                        logger.warning(f"    第 {current_attempt} 次尝试收到空响应")
                        continue

                    # 记录响应信息
                    response_tokens = len(llm_response_local['content'].split())
                    logger.info(f"    收到响应 (约{response_tokens}词):")
                    logger.info(f"    完整响应内容:\n{llm_response_local['content']}")
                    logger.info(f"    Token统计: Prompt≈{prompt_tokens}, Response≈{response_tokens}, Total≈{prompt_tokens + response_tokens}")
                    thinking_response_local = self.extract_thinking_content(llm_response_local['content'])
                    parsed_response_local = self.parse_response(thinking_response_local['response'], "sampled", dataset_source)
                    model_config_local = self.config.get('model', {})
                    document_local = {
                        "run_id": run_id,
                        "task_id": task_id_local,
                        "task_name": task_name,
                        "dataset_source": dataset_source,
                        "task_category": task_config.get('task_category', 'topic_modeling_labeling'),
                        "input_text": input_text,
                        "reference_answer": reference_answer,
                        "model_identifier": llm_response_local['model'],
                        "prompt_variant": "sampled",
                        "prompt_seed": item_idx,
                        "prompt_index": prompt_number,
                        "prompt_id": prompt_id,
                        "prompt_raw_text": final_prompt_local,
                        "generation_config": {
                            "temperature": model_config_local.get('temperature', 0.7),
                            "top_p": model_config_local.get('top_p', 0.95),
                            "enable_thinking": model_config_local.get('enable_thinking', True),
                            "enable_logprobs": model_config_local.get('enable_logprobs', True),
                            "top_logprobs": model_config_local.get('top_logprobs', 5)
                        },
                        "task_attempt_prompt": current_attempt,
                        "task_attempt_total": None,
                        "raw_response": llm_response_local['content'],
                        "thinking_content": thinking_response_local['thinking'],
                        "actual_response": thinking_response_local['response'],
                        "response_logprobs": llm_response_local['logprobs'],
                        "finish_reason": llm_response_local.get('finish_reason'),
                        "raw_answer": parsed_response_local['raw_answer'],
                        "parsed_answer": parsed_response_local['parsed_answer'],
                        "parsed_reason": parsed_response_local.get('parsed_reason'),
                        "execution_timestamp": datetime.now(timezone.utc).isoformat(),
                        # Topic modeling specific fields
                        "paper_name": item.get('paper_name'),
                        "doi": item.get('doi'),
                        "table_number": item.get('table_number'),
                        "topic_number": item.get('topic_number'),
                        "original_label": item.get('original_label'),  # 保存原始标签
                        # 构建完整的topic标识符
                        "topic_identifier": f"{item.get('paper_name', 'unknown')}_{item.get('table_number', 'unknown')}_{item.get('topic_number', 'unknown')}"
                    }

                    # 保存到MongoDB
                    result_id = self.save_to_mongodb(document_local)
                    if result_id:
                        logger.info(f"    第 {current_attempt} 次尝试成功保存到MongoDB")
                        task_attempt_total += 1
                    else:
                        logger.error(f"    第 {current_attempt} 次尝试保存到MongoDB失败")

        logger.info(f"Topic Labeling数据处理完成！")
        logger.info(f"  跳过的项目: {skipped_items}")
        logger.info(f"  处理的项目: {to_process_items}")
        logger.info(f"  总尝试次数: {task_attempt_total}")

    def process_counterfactual_data(self, data: List[Dict[str, Any]], run_id: str = None,
                                  processed_items: Dict[str, Any] = None):
        """处理Counterfactual QA数据 - 使用配置驱动的方式"""
        task_name = "counterfactual_qa"
        task_config = self.config.get('tasks', {}).get(task_name, {})

        if not run_id:
            run_id = str(uuid.uuid4())

        task_attempt_total = 0
        dataset_source = task_config.get('dataset_source', 'counterfactual_data')
        attempts_per_prompt = task_config.get('attempts_per_prompt', 2)

        logger.info(f"开始处理Counterfactual QA数据，共 {len(data)} 个项目")
        logger.info(f"每个项目使用多个prompt，每个prompt尝试 {attempts_per_prompt} 次")
        logger.info(f"运行ID: {run_id}")

        # 统计跳过和需要处理的项目
        skipped_items = 0
        to_process_items = 0

        for item_idx, item in enumerate(data):
            # 从配置中获取字段名
            id_field = task_config.get('id_field', 'Category')
            text_field = task_config.get('text_field', 'Prompt')
            label_field = task_config.get('label_field', None)

            input_text = item[text_field]
            reference_answer = item.get(label_field) if label_field else None
            category = item.get(id_field, 'Unknown')
            row_index = item.get('row_index', item_idx)
            item_id = f"{category}_{row_index}"

            logger.info(f"处理Counterfactual项目 {item_idx + 1}/{len(data)}: {item_id}")

            # 获取当前任务的prompt模板，使用数据项索引作为随机种子确保可重现性
            selected_prompts = self.get_task_prompts_json(task_name, seed=item_idx)

            # 处理每个选中的prompt
            for prompt_idx, prompt_dict in enumerate(selected_prompts):
                prompt_id = prompt_dict.get('id', f'prompt_{prompt_idx+1}')
                prompt_number = prompt_idx + 1  # 转换为1-N的编号

                # 检查是否应该跳过
                prompt_key = f"{item_id}_prompt_{prompt_number}"
                if processed_items and self.should_skip_item(
                    dataset_source, prompt_key, "sampled", processed_items, attempts_per_prompt):
                    skipped_items += 1
                    continue

                to_process_items += 1
                logger.info(f"处理Counterfactual项目 {item_id} 使用 {prompt_id}...")

                # 计算需要处理的尝试次数
                if processed_items:
                    next_attempt = self.get_next_attempt_number(
                        dataset_source, prompt_key, "sampled", processed_items)
                    attempts_to_process = attempts_per_prompt - (next_attempt - 1)
                else:
                    next_attempt = 1
                    attempts_to_process = attempts_per_prompt

                logger.info(f"  需要处理 {attempts_to_process} 次尝试，从第 {next_attempt} 次开始")

                # 并发处理每个prompt的多次尝试
                from concurrent.futures import ThreadPoolExecutor, as_completed
                system_cfg = self.config.get('system', {})
                max_workers = max(1, min(system_cfg.get('max_concurrent_requests', 10), attempts_to_process))

                def process_single_attempt(current_attempt_local: int):
                    task_id_local = self.generate_task_id(dataset_source, prompt_key, "sampled")
                    template_var_local = task_config.get('template_variable', 'question')
                    # 使用JSON prompt管理器格式化prompt
                    final_prompt_local = self.prompt_manager.format_prompt(
                        prompt_dict, **{template_var_local: input_text}
                    )
                    logger.info(f"  尝试 {current_attempt_local}/{attempts_per_prompt}...")

                    # 记录prompt信息
                    prompt_tokens = len(final_prompt_local.split())
                    logger.info(f"    发送Prompt (约{prompt_tokens}词):")
                    logger.info(f"    完整Prompt内容:\n{final_prompt_local}")

                    llm_response_local = self.call_llm(final_prompt_local)
                    if not llm_response_local['content']:
                        logger.warning(f"    第 {current_attempt_local} 次尝试收到空响应")
                        return None

                    # 记录响应信息
                    response_tokens = len(llm_response_local['content'].split())
                    logger.info(f"    收到响应 (约{response_tokens}词):")
                    logger.info(f"    完整响应内容:\n{llm_response_local['content']}")
                    logger.info(f"    Token统计: Prompt≈{prompt_tokens}, Response≈{response_tokens}, Total≈{prompt_tokens + response_tokens}")
                    thinking_response_local = self.extract_thinking_content(llm_response_local['content'])
                    parsed_response_local = self.parse_response(thinking_response_local['response'], "sampled", dataset_source)
                    model_config_local = self.config.get('model', {})
                    document_local = {
                        "run_id": run_id,
                        "task_id": task_id_local,
                        "task_name": task_name,
                        "dataset_source": dataset_source,
                        "task_category": task_config.get('task_category', 'historical_counterfactual_qa'),
                        "input_text": input_text,
                        "reference_answer": reference_answer,
                        "model_identifier": llm_response_local['model'],
                        "prompt_variant": "sampled",
                        "prompt_seed": item_idx,
                        "prompt_index": prompt_number,
                        "prompt_id": prompt_id,
                        "prompt_raw_text": final_prompt_local,
                        "generation_config": {
                            "temperature": model_config_local.get('temperature', 0.7),
                            "top_p": model_config_local.get('top_p', 0.95),
                            "enable_thinking": model_config_local.get('enable_thinking', True),
                            "enable_logprobs": model_config_local.get('enable_logprobs', True),
                            "top_logprobs": model_config_local.get('top_logprobs', 5)
                        },
                        "task_attempt_prompt": current_attempt_local,
                        "task_attempt_total": None,
                        "raw_response": llm_response_local['content'],
                        "thinking_content": thinking_response_local['thinking'],
                        "actual_response": thinking_response_local['response'],
                        "response_logprobs": llm_response_local['logprobs'],
                        "finish_reason": llm_response_local.get('finish_reason'),
                        "raw_answer": parsed_response_local['raw_answer'],
                        "parsed_answer": parsed_response_local['parsed_answer'],
                        "parsed_reason": parsed_response_local.get('parsed_reason'),
                        "execution_timestamp": datetime.now(timezone.utc).isoformat(),
                        # Counterfactual specific fields
                        "category": category,
                        "row_index": row_index,
                        "question_type": "counterfactual_historical"
                    }

                    # 保存到MongoDB
                    result_id = self.save_to_mongodb(document_local)
                    if result_id:
                        logger.info(f"    第 {current_attempt_local} 次尝试成功保存到MongoDB")
                        return current_attempt_local
                    else:
                        logger.error(f"    第 {current_attempt_local} 次尝试保存到MongoDB失败")
                        return None

                # 执行并发处理
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = []
                    for attempt_num in range(next_attempt, next_attempt + attempts_to_process):
                        future = executor.submit(process_single_attempt, attempt_num)
                        futures.append(future)

                    # 等待所有任务完成
                    for future in as_completed(futures):
                        try:
                            result = future.result()
                            if result is not None:
                                task_attempt_total += 1
                        except Exception as e:
                            logger.error(f"处理尝试时出错: {e}")

        logger.info(f"Counterfactual QA数据处理完成！")
        logger.info(f"  跳过的项目: {skipped_items}")
        logger.info(f"  处理的项目: {to_process_items}")
        logger.info(f"  总尝试次数: {task_attempt_total}")

    def run(self, resume: bool = True, test_mode: bool = False):
        """运行完整的响应生成流程"""
        logger.info("开始LLM响应生成...")

        # 1. 从CSV加载采样数据
        semeval_data = self.load_semeval_data_from_csv()
        commits_data = self.load_commits_data_from_csv()
        topic_data = self.load_topic_modeling_data_from_csv()
        counterfactual_data = self.load_counterfactual_data_from_csv()

        if not semeval_data and not commits_data and not topic_data and not counterfactual_data:
            logger.error("错误: 在CSV文件中未找到数据。请先运行数据采样或确保数据文件存在。")
            return

        # 测试模式：只处理每个数据集的第一条数据
        if test_mode:
            system_config = self.config.get('system', {})
            test_sample_size = system_config.get('test_mode_sample_size', 1)

            logger.info(f"测试模式：只处理每个数据集的前{test_sample_size}条数据")
            semeval_data = semeval_data[:test_sample_size] if semeval_data else []
            commits_data = commits_data[:test_sample_size] if commits_data else []
            topic_data = topic_data[:test_sample_size] if topic_data else []
            counterfactual_data = counterfactual_data[:test_sample_size] if counterfactual_data else []
        else:
            # 生产模式：使用配置中的max_samples限制counterfactual数据
            counterfactual_task = self.config.get('tasks', {}).get('counterfactual_qa', {})
            if counterfactual_data:
                max_samples = counterfactual_task.get('max_samples', None)
                if max_samples is not None and len(counterfactual_data) > max_samples:
                    logger.info(f"限制Counterfactual数据为前{max_samples}个问题（原始数据: {len(counterfactual_data)} 个）")
                    counterfactual_data = counterfactual_data[:max_samples]
                else:
                    logger.info(f"处理所有Counterfactual数据: {len(counterfactual_data)} 个问题")

        # 根据模式选择正确的collection
        if test_mode:
            # 测试模式：使用测试collection
            if self.collection is not None:
                output_config = self.config.get('output', {}).get('mongo', {})
                test_collection_name = output_config.get('test_collection', 'test_response')
                self.collection = self.db[test_collection_name]
                logger.info(f"测试模式：使用测试collection: {test_collection_name}")
        else:
            # 生产模式：使用正式collection
            if self.collection is not None:
                output_config = self.config.get('output', {}).get('mongo', {})
                prod_collection_name = output_config.get('collection', 'response_collections')
                self.collection = self.db[prod_collection_name]
                logger.info(f"生产模式：使用正式collection: {prod_collection_name}")
        
        # 2. 检查现有进度
        if resume:
            logger.info("检查现有进度...")
            existing_progress = self.check_existing_progress()
            run_id = existing_progress.get("run_id")
            processed_items = existing_progress.get("processed_items", {})
            
            if run_id:
                logger.info(f"发现现有运行ID: {run_id}")
                logger.info(f"已处理项目数: {len(processed_items)}")
                logger.info(f"总尝试次数: {existing_progress.get('total_attempts', 0)}")
            else:
                logger.info("未发现现有进度，将创建新的运行")
                run_id = str(uuid.uuid4())
                processed_items = {}
        else:
            logger.info("跳过进度检查，创建新的运行")
            run_id = str(uuid.uuid4())
            processed_items = {}
        
        # 3. 处理SemEval数据
        sentiment_task = self.config.get('tasks', {}).get('sentiment_analysis', {})
        if semeval_data and sentiment_task.get('enabled', False):
            logger.info("\n处理SemEval数据...")
            # 使用配置中的max_samples，如果没有则处理所有数据
            max_samples = sentiment_task.get('max_samples', None)
            if max_samples is not None:
                limited_semeval_data = semeval_data[:max_samples] if len(semeval_data) > max_samples else semeval_data
                logger.info(f"原始数据: {len(semeval_data)} 个样本，限制处理: {len(limited_semeval_data)} 个样本 (max_samples: {max_samples})")
            else:
                limited_semeval_data = semeval_data
                logger.info(f"处理所有SemEval数据: {len(limited_semeval_data)} 个样本")
            
            self.process_semeval_data(
                limited_semeval_data,
                run_id=run_id,
                processed_items=processed_items
            )
        else:
            logger.info("\n跳过SemEval数据处理（任务已禁用）...")

        # 4. 处理Commits数据
        coding_task = self.config.get('tasks', {}).get('explorative_coding', {})
        if commits_data and coding_task.get('enabled', False):
            logger.info(f"\n处理Commits数据...")
            # 使用配置中的max_samples，如果没有则处理所有数据
            max_samples = coding_task.get('max_samples', None)
            if max_samples is not None:
                limited_commits_data = commits_data[:max_samples] if len(commits_data) > max_samples else commits_data
                logger.info(f"原始数据: {len(commits_data)} 个commit，限制处理: {len(limited_commits_data)} 个commit (max_samples: {max_samples})")
            else:
                limited_commits_data = commits_data
                logger.info(f"处理所有Commits数据: {len(limited_commits_data)} 个commit")

            self.process_commits_data(
                limited_commits_data,
                run_id=run_id,
                processed_items=processed_items
            )
        else:
            logger.info("\n跳过Commits数据处理（任务已禁用）...")

        # 5. 处理Topic Labeling数据
        topic_task = self.config.get('tasks', {}).get('topic_labeling', {})
        if topic_data and topic_task.get('enabled', True):
            logger.info(f"\n处理Topic Labeling数据...")
            logger.info(f"数据量: {len(topic_data)} 个topic")

            self.process_topic_labeling_data(
                topic_data,
                run_id=run_id,
                processed_items=processed_items
            )

        # 6. 处理Counterfactual QA数据
        counterfactual_task = self.config.get('tasks', {}).get('counterfactual_qa', {})
        if counterfactual_data and counterfactual_task.get('enabled', True):
            logger.info(f"\n处理Counterfactual QA数据...")
            logger.info(f"数据量: {len(counterfactual_data)} 个问题")

            self.process_counterfactual_data(
                counterfactual_data,
                run_id=run_id,
                processed_items=processed_items
            )

        logger.info(f"\nLLM响应生成完成！运行ID: {run_id}")
        
        # 5. 最终统计
        final_progress = self.check_existing_progress(run_id)
        logger.info(f"最终统计:")
        logger.info(f"  总项目数: {len(final_progress.get('processed_items', {}))}")
        logger.info(f"  总尝试次数: {final_progress.get('total_attempts', 0)}")
        logger.info(f"  运行ID: {run_id}")
        
        return run_id

    def check_existing_progress(self, run_id: str = None) -> Dict[str, Any]:
        """检查现有进度，返回已处理的项目信息"""
        logger.info("检查现有进度...")
        
        # 如果没有指定run_id，查找最近的run_id
        if not run_id:
            recent_run = self.collection.find_one(
                {},
                sort=[("execution_timestamp", -1)]
            )
            if recent_run:
                run_id = recent_run.get("run_id")
                logger.info(f"使用最近的run_id: {run_id}")
            else:
                logger.info("没有找到现有的运行记录")
                return {"run_id": None, "processed_items": {}, "total_attempts": 0}
        
        # 统计已处理的项目
        pipeline = [
            {"$match": {"run_id": run_id}},
            {"$group": {
                "_id": {
                    "dataset_source": "$dataset_source",
                    "task_id": "$task_id",
                    "prompt_variant": "$prompt_variant"
                },
                "attempts": {"$sum": 1},
                "last_attempt": {"$max": "$task_attempt_prompt"}
            }}
        ]
        
        processed_items = {}
        total_attempts = 0
        
        for result in self.collection.aggregate(pipeline):
            key = f"{result['_id']['dataset_source']}_{result['_id']['task_id']}_{result['_id']['prompt_variant']}"
            processed_items[key] = {
                "dataset_source": result['_id']['dataset_source'],
                "task_id": result['_id']['task_id'],
                "prompt_variant": result['_id']['prompt_variant'],
                "attempts": result['attempts'],
                "last_attempt": result['last_attempt']
            }
            total_attempts += result['attempts']
        
        logger.info(f"找到 {len(processed_items)} 个已处理的项目，总计 {total_attempts} 次尝试")
        return {
            "run_id": run_id,
            "processed_items": processed_items,
            "total_attempts": total_attempts
        }
    
    def should_skip_item(self, dataset_source: str, item_id: str, prompt_variant: str, 
                        processed_items: Dict[str, Any], attempts_per_variant: int) -> bool:
        """检查是否应该跳过某个项目"""
        key = f"{dataset_source}_{item_id}_{prompt_variant}"
        
        if key in processed_items:
            item_info = processed_items[key]
            if item_info['attempts'] >= attempts_per_variant:
                logger.debug(f"跳过 {key} - 已完成 {item_info['attempts']}/{attempts_per_variant} 次尝试")
                return True
            else:
                logger.info(f"继续 {key} - 已完成 {item_info['attempts']}/{attempts_per_variant} 次尝试")
                return False
        
        return False
    
    def get_next_attempt_number(self, dataset_source: str, item_id: str, prompt_variant: str, 
                               processed_items: Dict[str, Any]) -> int:
        """获取下一个尝试编号"""
        key = f"{dataset_source}_{item_id}_{prompt_variant}"
        
        if key in processed_items:
            return processed_items[key]['attempts'] + 1
        else:
            return 1

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LLM响应生成器")
    parser.add_argument("--attempts", type=int, default=8, 
                       help="每个prompt变体的尝试次数 (默认: 8)")
    parser.add_argument("--no-resume", action="store_true", 
                       help="不恢复现有进度，创建新的运行")
    parser.add_argument("--check-progress", action="store_true", 
                       help="只检查进度，不运行生成")
    parser.add_argument("--test-mode", action="store_true", 
                       help="测试模式：只处理每个数据集的第一条数据，使用test_response collection")
    
    args = parser.parse_args()
    
    logger.info("启动LLM响应生成器...")
    try:
        generator = LLMResponseGenerator()
        
        if args.check_progress:
            # 只检查进度
            logger.info("检查现有进度...")
            progress = generator.check_existing_progress()
            if progress["run_id"]:
                logger.info(f"发现运行ID: {progress['run_id']}")
                logger.info(f"已处理项目数: {len(progress['processed_items'])}")
                logger.info(f"总尝试次数: {progress['total_attempts']}")
            else:
                logger.info("未发现现有进度")
        else:
            # 运行生成
            run_id = generator.run(
                resume=not args.no_resume,
                test_mode=args.test_mode
            )
            logger.info(f"LLM响应生成器成功完成，运行ID: {run_id}")
            
    except Exception as e:
        logger.error(f"主函数出错: {e}")
        raise

if __name__ == "__main__":
    main()
