# 网络监控和自动重连服务

这是一个自动化的网络监控和WiFi重连服务，能够在网络异常时自动重连WiFi并重启natapp服务。

## 功能特性

1. **网络连接监控**: 每30秒检测一次网络连接状态
2. **自动WiFi重连**: 检测到网络异常时自动重启WiFi接口
3. **natapp服务管理**: 网络重连成功后自动启动natapp服务
4. **系统服务集成**: 作为systemd服务运行，支持开机自启动
5. **详细日志记录**: 记录所有操作和状态变化

## 文件说明

- `network_monitor.sh` - 主要的网络监控脚本
- `network-monitor.service` - systemd服务配置文件
- `install_network_monitor.sh` - 自动安装脚本
- `NETWORK_MONITOR_README.md` - 本说明文件

## 安装步骤

### 1. 确保natapp已安装

确保 `/etc/natapp` 文件存在且可执行：

```bash
ls -la /etc/natapp
```

如果不存在，请先安装natapp到该目录。

### 2. 运行安装脚本

```bash
sudo ./install_network_monitor.sh
```

安装脚本会自动：
- 设置脚本权限
- 安装systemd服务
- 启用开机自启动
- 启动服务

### 3. 验证安装

检查服务状态：

```bash
sudo systemctl status network-monitor.service
```

## 使用方法

### 启动服务

```bash
sudo systemctl start network-monitor.service
```

### 停止服务

```bash
sudo systemctl stop network-monitor.service
```

### 重启服务

```bash
sudo systemctl restart network-monitor.service
```

### 查看服务状态

```bash
sudo systemctl status network-monitor.service
```

### 查看实时日志

```bash
# 查看服务日志
sudo journalctl -u network-monitor.service -f

# 查看脚本日志
sudo tail -f /var/log/network_monitor.log

# 查看natapp日志
sudo tail -f /var/log/natapp.log
```

## 配置参数

可以在 `network_monitor.sh` 文件中修改以下配置：

```bash
LOG_FILE="/var/log/network_monitor.log"  # 日志文件路径
CHECK_INTERVAL=30                        # 检查间隔（秒）
MAX_RETRIES=3                           # 最大重试次数
NATAPP_DIR="/etc"                       # natapp所在目录
NATAPP_TOKEN="67e46cb2ad41cf9f"        # natapp认证token
```

## 工作原理

1. **网络检测**: 通过ping *******检测网络连接
2. **WiFi重连**: 使用`ip link`命令重启WiFi接口
3. **服务管理**: 自动管理natapp进程的启动和停止
4. **错误处理**: 包含重试机制和详细的错误日志

## 故障排除

### 服务无法启动

1. 检查脚本权限：
   ```bash
   ls -la network_monitor.sh
   ```

2. 检查natapp文件是否存在：
   ```bash
   ls -la /etc/natapp
   ```

3. 查看服务日志：
   ```bash
   sudo journalctl -u network-monitor.service -n 50
   ```

### 网络重连失败

1. 检查WiFi接口名称：
   ```bash
   iw dev
   ```

2. 检查网络配置：
   ```bash
   ip addr show
   ```

### 权限问题

确保以root权限运行安装脚本：

```bash
sudo ./install_network_monitor.sh
```

## 安全注意事项

- 脚本需要root权限运行
- 服务文件包含安全限制（NoNewPrivileges, PrivateTmp等）
- 日志文件存储在系统日志目录中

## 卸载服务

如果需要卸载服务：

```bash
sudo systemctl stop network-monitor.service
sudo systemctl disable network-monitor.service
sudo rm /etc/systemd/system/network-monitor.service
sudo systemctl daemon-reload
```

## 技术支持

如果遇到问题，请检查：
1. 系统日志：`sudo journalctl -u network-monitor.service`
2. 脚本日志：`sudo tail -f /var/log/network_monitor.log`
3. 服务状态：`sudo systemctl status network-monitor.service`
