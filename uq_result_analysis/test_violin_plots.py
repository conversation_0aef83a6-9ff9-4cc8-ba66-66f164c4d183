#!/usr/bin/env python3
"""
测试violin plot的简化版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def setup_paper_style():
    """设置论文发表风格"""
    plt.rcParams.update({
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10,
        'figure.titlesize': 16,
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("加载数据...")
    data_dir = Path('uq_result_analysis/data')
    df = pd.read_csv(data_dir / 'combined_uq_results.csv')
    
    # 清理数据
    df_clean = df.dropna(subset=['uq_value']).copy()
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()
    
    print(f"清理后数据量: {len(df_analysis)}")
    return df_analysis

def test_individual_violin_plot(df_analysis, output_dir):
    """测试单个model-task-uq method的violin plot"""
    print("测试单个violin plot...")
    
    setup_paper_style()
    
    # 选择一个有足够数据的组合
    combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
    combinations = combinations[combinations['count'] >= 20]  # 至少20个样本
    
    if len(combinations) == 0:
        print("没有找到足够数据的组合")
        return
    
    # 选择第一个组合进行测试
    row = combinations.iloc[0]
    model = row['llm_model']
    task = row['task_name']
    method = row['uq_method']
    
    print(f"测试组合: {model} - {task} - {method}")
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task) & 
        (df_analysis['uq_method'] == method)
    ]
    
    # 创建图表
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'Test: {model} - {task} - {method}', fontsize=14, fontweight='bold')
    
    # 1. 直方图
    axes[0].hist(subset['uq_value'], bins=min(15, len(subset)//3), alpha=0.7, 
                color='skyblue', edgecolor='black')
    axes[0].axvline(subset['uq_value'].mean(), color='red', linestyle='--', 
                   label=f'Mean: {subset["uq_value"].mean():.3f}')
    axes[0].set_xlabel('UQ Value')
    axes[0].set_ylabel('Frequency')
    axes[0].set_title('Distribution')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 2. 小提琴图
    parts = axes[1].violinplot([subset['uq_value']], positions=[1], showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_facecolor('lightgreen')
        pc.set_alpha(0.7)
    axes[1].set_ylabel('UQ Value')
    axes[1].set_title('Violin Plot')
    axes[1].set_xticks([1])
    axes[1].set_xticklabels([method])
    axes[1].grid(True, alpha=0.3)
    
    # 3. 统计信息
    stats_text = f"""Statistics:
Count: {len(subset)}
Mean: {subset['uq_value'].mean():.4f}
Std: {subset['uq_value'].std():.4f}
Median: {subset['uq_value'].median():.4f}
Min: {subset['uq_value'].min():.4f}
Max: {subset['uq_value'].max():.4f}"""
    
    axes[2].text(0.1, 0.5, stats_text, transform=axes[2].transAxes, 
                fontsize=10, verticalalignment='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
    axes[2].set_xlim(0, 1)
    axes[2].set_ylim(0, 1)
    axes[2].axis('off')
    axes[2].set_title('Statistics')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_individual_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    print("单个violin plot测试完成")

def test_multiple_violin_plots(df_analysis, output_dir):
    """测试多个UQ方法的violin plot比较"""
    print("测试多个violin plot比较...")
    
    setup_paper_style()
    
    # 选择一个model-task组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index(name='count')
    model_task_combinations = model_task_combinations[model_task_combinations['count'] >= 100]
    
    if len(model_task_combinations) == 0:
        print("没有找到足够数据的model-task组合")
        return
    
    row = model_task_combinations.iloc[0]
    model = row['llm_model']
    task = row['task_name']
    
    print(f"测试组合: {model} - {task}")
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    # 创建图表
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle(f'Test Multiple Violin: {model} - {task}', fontsize=14, fontweight='bold')
    
    # 1. 使用seaborn的violin plot
    sns.violinplot(data=subset, x='uq_method', y='uq_value', ax=axes[0])
    axes[0].set_title('Seaborn Violin Plot')
    axes[0].set_xlabel('UQ Method')
    axes[0].set_ylabel('UQ Value')
    axes[0].tick_params(axis='x', rotation=45)
    
    # 2. 使用matplotlib的violin plot
    methods = subset['uq_method'].unique()
    method_data = [subset[subset['uq_method'] == method]['uq_value'].values for method in methods]
    parts = axes[1].violinplot(method_data, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    axes[1].set_title('Matplotlib Violin Plot')
    axes[1].set_xlabel('UQ Method')
    axes[1].set_ylabel('UQ Value')
    axes[1].set_xticks(range(len(methods)))
    axes[1].set_xticklabels(methods, rotation=45)
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_multiple_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    print("多个violin plot测试完成")

def test_overall_violin_plots(df_analysis, output_dir):
    """测试整体violin plot"""
    print("测试整体violin plot...")
    
    setup_paper_style()
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Overall Violin Plot Tests', fontsize=16, fontweight='bold')
    
    # 1. 按模型的violin plot
    models = df_analysis['llm_model'].unique()
    model_data = [df_analysis[df_analysis['llm_model'] == model]['uq_value'].values for model in models]
    parts = axes[0, 0].violinplot(model_data, positions=range(len(models)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    axes[0, 0].set_title('By Model')
    axes[0, 0].set_xlabel('Model')
    axes[0, 0].set_ylabel('UQ Value')
    axes[0, 0].set_xticks(range(len(models)))
    axes[0, 0].set_xticklabels(models)
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 按任务的violin plot
    tasks = df_analysis['task_name'].unique()
    task_data = [df_analysis[df_analysis['task_name'] == task]['uq_value'].values for task in tasks]
    parts = axes[0, 1].violinplot(task_data, positions=range(len(tasks)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    axes[0, 1].set_title('By Task')
    axes[0, 1].set_xlabel('Task')
    axes[0, 1].set_ylabel('UQ Value')
    axes[0, 1].set_xticks(range(len(tasks)))
    axes[0, 1].set_xticklabels(tasks, rotation=45)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 按UQ方法的violin plot (选择前6个)
    top_methods = df_analysis['uq_method'].value_counts().head(6).index
    method_data = [df_analysis[df_analysis['uq_method'] == method]['uq_value'].values for method in top_methods]
    parts = axes[1, 0].violinplot(method_data, positions=range(len(top_methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    axes[1, 0].set_title('By Top UQ Methods')
    axes[1, 0].set_xlabel('UQ Method')
    axes[1, 0].set_ylabel('UQ Value')
    axes[1, 0].set_xticks(range(len(top_methods)))
    axes[1, 0].set_xticklabels(top_methods, rotation=45)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 整体分布
    axes[1, 1].hist(df_analysis['uq_value'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(df_analysis['uq_value'].mean(), color='red', linestyle='--', 
                       label=f'Mean: {df_analysis["uq_value"].mean():.3f}')
    axes[1, 1].set_xlabel('UQ Value')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Overall Distribution')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_overall_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    print("整体violin plot测试完成")

def main():
    """主函数"""
    print("=== Violin Plot 测试开始 ===")
    
    # 创建输出目录
    figures_dir = Path('uq_result_analysis/figures')
    figures_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    df_analysis = load_and_preprocess_data()
    
    # 运行测试
    test_individual_violin_plot(df_analysis, figures_dir)
    test_multiple_violin_plots(df_analysis, figures_dir)
    test_overall_violin_plots(df_analysis, figures_dir)
    
    print("\n=== 测试完成 ===")
    print(f"测试图表已保存到: {figures_dir}")
    
    # 列出生成的测试文件
    test_files = list(figures_dir.glob("test_*.pdf"))
    print(f"\n生成的测试文件:")
    for test_file in sorted(test_files):
        print(f"  - {test_file.name}")

if __name__ == "__main__":
    main()
