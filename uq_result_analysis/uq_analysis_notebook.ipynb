{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UQ结果分析与可视化\n", "\n", "本notebook用于分析不同模型、任务和UQ方法的不确定性量化结果。\n", "\n", "## 分析目标\n", "1. 确认UQ方法的适用程度\n", "2. 确认模型在执行不同任务上的有效度\n", "3. 以model为基本单位，以不同task为单位，再以uq method为单位，group相应的分数\n", "4. 绘制数值的distribution，以及平均分，标准差，方差等统计指标"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境设置完成\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体和样式\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置图表样式\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# 设置图表大小\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['figure.dpi'] = 100\n", "\n", "print(\"环境设置完成\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据加载完成\n", "总记录数: 8689\n", "数据形状: (8689, 19)\n", "\n", "列名: ['document_id', 'llm_model', 'task_name', 'dataset_source', 'prompt_seed', 'input_text', 'n_responses', 'successful_methods', 'failed_methods', 'created_at', 'uq_method', 'uq_value', 'status', 'computed_at', 'uncertainty_score', 'mean_similarity', 'num_responses', 'method_type', 'collection_source']\n"]}], "source": ["# 加载数据\n", "data_dir = Path('uq_result_analysis/data')\n", "df = pd.read_csv(data_dir / 'combined_uq_results.csv')\n", "\n", "# 加载数据摘要\n", "with open(data_dir / 'data_summary.json', 'r', encoding='utf-8') as f:\n", "    summary = json.load(f)\n", "\n", "print(f\"数据加载完成\")\n", "print(f\"总记录数: {len(df)}\")\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"\\n列名: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据概览 ===\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 8689 entries, 0 to 8688\n", "Data columns (total 19 columns):\n", " #   Column              Non-Null Count  Dtype  \n", "---  ------              --------------  -----  \n", " 0   document_id         8689 non-null   object \n", " 1   llm_model           8689 non-null   object \n", " 2   task_name           8689 non-null   object \n", " 3   dataset_source      8689 non-null   object \n", " 4   prompt_seed         8689 non-null   object \n", " 5   input_text          8689 non-null   object \n", " 6   n_responses         8689 non-null   int64  \n", " 7   successful_methods  8689 non-null   int64  \n", " 8   failed_methods      8689 non-null   int64  \n", " 9   created_at          8689 non-null   object \n", " 10  uq_method           8689 non-null   object \n", " 11  uq_value            8689 non-null   float64\n", " 12  status              8689 non-null   object \n", " 13  computed_at         8689 non-null   object \n", " 14  uncertainty_score   8689 non-null   float64\n", " 15  mean_similarity     3156 non-null   float64\n", " 16  num_responses       7900 non-null   float64\n", " 17  method_type         8689 non-null   object \n", " 18  collection_source   8689 non-null   object \n", "dtypes: float64(4), int64(3), object(12)\n", "memory usage: 1.3+ MB\n", "None\n", "\n", "=== 缺失值统计 ===\n", "document_id              0\n", "llm_model                0\n", "task_name                0\n", "dataset_source           0\n", "prompt_seed              0\n", "input_text               0\n", "n_responses              0\n", "successful_methods       0\n", "failed_methods           0\n", "created_at               0\n", "uq_method                0\n", "uq_value                 0\n", "status                   0\n", "computed_at              0\n", "uncertainty_score        0\n", "mean_similarity       5533\n", "num_responses          789\n", "method_type              0\n", "collection_source        0\n", "dtype: int64\n", "\n", "清理后数据量: 8689\n", "\n", "=== 基本统计 ===\n", "模型数量: 3\n", "任务数量: 4\n", "UQ方法数量: 11\n", "\n", "模型分布:\n", "llm_model\n", "qwen3-32b      4520\n", "phi4_latest    3949\n", "unknown         220\n", "Name: count, dtype: int64\n", "\n", "任务分布:\n", "task_name\n", "topic_labeling        3949\n", "sentiment_analysis    2343\n", "explorative_coding    2277\n", "counterfactual_qa      120\n", "Name: count, dtype: int64\n", "\n", "UQ方法分布:\n", "uq_method\n", "LUQUQ                       799\n", "EigValLaplacianJaccardUQ    789\n", "EigValLaplacianNLIUQ        789\n", "EccentricityJaccardUQ       789\n", "EccentricityNLIEntailUQ     789\n", "SemanticEntropyNLIUQ        789\n", "EmbeddingQwenUQ             789\n", "EmbeddingE5UQ               789\n", "LofreeCPUQ                  789\n", "NumSetsUQ                   789\n", "KernelLanguageEntropyUQ     789\n", "Name: count, dtype: int64\n"]}], "source": ["# 数据预处理和清洗\n", "print(\"=== 数据概览 ===\")\n", "print(df.info())\n", "print(\"\\n=== 缺失值统计 ===\")\n", "print(df.isnull().sum())\n", "\n", "# 清理数据\n", "# 移除uq_value为空的记录\n", "df_clean = df.dropna(subset=['uq_value']).copy()\n", "print(f\"\\n清理后数据量: {len(df_clean)}\")\n", "\n", "# 基本统计信息\n", "print(\"\\n=== 基本统计 ===\")\n", "print(f\"模型数量: {df_clean['llm_model'].nunique()}\")\n", "print(f\"任务数量: {df_clean['task_name'].nunique()}\")\n", "print(f\"UQ方法数量: {df_clean['uq_method'].nunique()}\")\n", "\n", "print(\"\\n模型分布:\")\n", "print(df_clean['llm_model'].value_counts())\n", "\n", "print(\"\\n任务分布:\")\n", "print(df_clean['task_name'].value_counts())\n", "\n", "print(\"\\nUQ方法分布:\")\n", "print(df_clean['uq_method'].value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 整体UQ值分布分析"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["=== UQ值统计信息 ===\n", "均值: 1.3402\n", "标准差: 1.4571\n", "方差: 2.1230\n", "中位数: 0.9828\n", "最小值: -0.0000\n", "最大值: 11.0087\n"]}], "source": ["# 创建论文风格的可视化函数\n", "def setup_paper_style():\n", "    \"\"\"设置论文发表风格\"\"\"\n", "    plt.rcParams.update({\n", "        'font.size': 12,\n", "        'axes.titlesize': 14,\n", "        'axes.labelsize': 12,\n", "        'xtick.labelsize': 10,\n", "        'ytick.labelsize': 10,\n", "        'legend.fontsize': 10,\n", "        'figure.titlesize': 16,\n", "        'lines.linewidth': 2,\n", "        'grid.alpha': 0.3,\n", "        'axes.spines.top': <PERSON><PERSON><PERSON>,\n", "        'axes.spines.right': <PERSON><PERSON><PERSON>,\n", "    })\n", "\n", "setup_paper_style()\n", "\n", "# 整体UQ值分布\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Uncertainty Quantification Results Overview', fontsize=16, fontweight='bold')\n", "\n", "# 1. 整体分布直方图\n", "axes[0, 0].hist(df_clean['uq_value'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0, 0].axvline(df_clean['uq_value'].mean(), color='red', linestyle='--', \n", "                   label=f'Mean: {df_clean[\"uq_value\"].mean():.3f}')\n", "axes[0, 0].axvline(df_clean['uq_value'].median(), color='orange', linestyle='--', \n", "                   label=f'Median: {df_clean[\"uq_value\"].median():.3f}')\n", "axes[0, 0].set_xlabel('UQ Value')\n", "axes[0, 0].set_ylabel('Frequency')\n", "axes[0, 0].set_title('Overall UQ Value Distribution')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 2. 箱线图\n", "axes[0, 1].boxplot(df_clean['uq_value'], patch_artist=True, \n", "                   boxprops=dict(facecolor='lightblue', alpha=0.7))\n", "axes[0, 1].set_ylabel('UQ Value')\n", "axes[0, 1].set_title('UQ Value Box Plot')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. 按模型分布\n", "for i, model in enumerate(df_clean['llm_model'].unique()):\n", "    if model != 'unknown':  # 跳过unknown模型\n", "        model_data = df_clean[df_clean['llm_model'] == model]['uq_value']\n", "        axes[1, 0].hist(model_data, bins=30, alpha=0.6, label=model)\n", "axes[1, 0].set_xlabel('UQ Value')\n", "axes[1, 0].set_ylabel('Frequency')\n", "axes[1, 0].set_title('UQ Value Distribution by Model')\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# 4. 按任务分布\n", "for i, task in enumerate(df_clean['task_name'].unique()):\n", "    task_data = df_clean[df_clean['task_name'] == task]['uq_value']\n", "    axes[1, 1].hist(task_data, bins=30, alpha=0.6, label=task)\n", "axes[1, 1].set_xlabel('UQ Value')\n", "axes[1, 1].set_ylabel('Frequency')\n", "axes[1, 1].set_title('UQ Value Distribution by Task')\n", "axes[1, 1].legend()\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('uq_result_analysis/figures/overall_distribution.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "# 打印统计信息\n", "print(\"=== UQ值统计信息 ===\")\n", "print(f\"均值: {df_clean['uq_value'].mean():.4f}\")\n", "print(f\"标准差: {df_clean['uq_value'].std():.4f}\")\n", "print(f\"方差: {df_clean['uq_value'].var():.4f}\")\n", "print(f\"中位数: {df_clean['uq_value'].median():.4f}\")\n", "print(f\"最小值: {df_clean['uq_value'].min():.4f}\")\n", "print(f\"最大值: {df_clean['uq_value'].max():.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 按模型-任务-方法分组分析"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 分组统计结果 (前20行) ===\n", "                                                        count    mean     std  \\\n", "llm_model   task_name         uq_method                                         \n", "phi4_latest topic_labeling    EccentricityJaccardUQ       359  3.4408  0.7753   \n", "                              EccentricityNLIEntailUQ     359  2.4462  0.7159   \n", "                              EigValLaplacianJaccardUQ    359  4.6249  1.5869   \n", "                              EigValLaplacianNLIUQ        359  2.8635  0.9699   \n", "                              EmbeddingE5UQ               359  0.0807  0.0228   \n", "                              EmbeddingQwenUQ             359  0.2475  0.0738   \n", "                              KernelLanguageEntropyUQ     359  0.9817  0.0089   \n", "                              LUQUQ                       359  0.3663  0.1137   \n", "                              LofreeCPUQ                  359  0.8104  0.1071   \n", "                              NumSetsUQ                   359  1.2284  0.9234   \n", "                              SemanticEntropyNLIUQ        359  0.8350  0.1508   \n", "qwen3-32b   counterfactual_qa EccentricityJaccardUQ        10  4.4711  1.1592   \n", "                              EccentricityNLIEntailUQ      10  2.6089  0.4706   \n", "                              EigValLaplacianJaccardUQ     10  4.2628  0.2438   \n", "                              EigValLaplacianNLIUQ         10  3.0044  0.4706   \n", "                              EmbeddingE5UQ                10  0.0366  0.0046   \n", "                              EmbeddingQwenUQ              10  0.0887  0.0185   \n", "                              KernelLanguageEntropyUQ      10  0.9869  0.0031   \n", "                              LUQUQ                        20  0.4309  0.0663   \n", "                              LofreeCPUQ                   10  0.9232  0.0066   \n", "\n", "                                                           var  median  \\\n", "llm_model   task_name         uq_method                                  \n", "phi4_latest topic_labeling    EccentricityJaccardUQ     0.6010  3.4643   \n", "                              EccentricityNLIEntailUQ   0.5126  2.4502   \n", "                              EigValLaplacianJaccardUQ  2.5182  4.4407   \n", "                              EigValLaplacianNLIUQ      0.9407  2.5866   \n", "                              EmbeddingE5UQ             0.0005  0.0823   \n", "                              EmbeddingQwenUQ           0.0055  0.2493   \n", "                              KernelLanguageEntropyUQ   0.0001  0.9841   \n", "                              LUQUQ                     0.0129  0.3514   \n", "                              LofreeCPUQ                0.0115  0.8302   \n", "                              NumSetsUQ                 0.8527  1.0000   \n", "                              SemanticEntropyNLIUQ      0.0227  0.8764   \n", "qwen3-32b   counterfactual_qa EccentricityJaccardUQ     1.3438  4.8452   \n", "                              EccentricityNLIEntailUQ   0.2215  2.4507   \n", "                              EigValLaplacianJaccardUQ  0.0594  4.3086   \n", "                              EigValLaplacianNLIUQ      0.2215  2.9139   \n", "                              EmbeddingE5UQ             0.0000  0.0373   \n", "                              EmbeddingQwenUQ           0.0003  0.0848   \n", "                              KernelLanguageEntropyUQ   0.0000  0.9884   \n", "                              LUQUQ                     0.0044  0.4535   \n", "                              LofreeCPUQ                0.0000  0.9242   \n", "\n", "                                                           min      max  \n", "llm_model   task_name         uq_method                                  \n", "phi4_latest topic_labeling    EccentricityJaccardUQ     0.0000   5.2915  \n", "                              EccentricityNLIEntailUQ   0.0000   4.4766  \n", "                              EigValLaplacianJaccardUQ  1.0000  11.0087  \n", "                              EigValLaplacianNLIUQ      1.0410   6.7810  \n", "                              EmbeddingE5UQ            -0.0000   0.1448  \n", "                              EmbeddingQwenUQ          -0.0000   0.4649  \n", "                              KernelLanguageEntropyUQ   0.9001   0.9900  \n", "                              LUQUQ                     0.0167   0.7390  \n", "                              LofreeCPUQ                0.0000   0.9242  \n", "                              NumSetsUQ                 1.0000  11.0000  \n", "                              SemanticEntropyNLIUQ      0.0000   0.9872  \n", "qwen3-32b   counterfactual_qa EccentricityJaccardUQ     1.4143   5.2915  \n", "                              EccentricityNLIEntailUQ   2.0009   3.7431  \n", "                              EigValLaplacianJaccardUQ  3.7304   4.5717  \n", "                              EigValLaplacianNLIUQ      2.4565   4.1404  \n", "                              EmbeddingE5UQ             0.0260   0.0413  \n", "                              EmbeddingQwenUQ           0.0659   0.1265  \n", "                              KernelLanguageEntropyUQ   0.9803   0.9893  \n", "                              LUQUQ                     0.2216   0.4875  \n", "                              LofreeCPUQ                0.9067   0.9339  \n", "\n", "详细统计结果已保存到: uq_result_analysis/data/grouped_statistics.csv\n"]}], "source": ["# 创建分组统计\n", "# 过滤掉unknown模型\n", "df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()\n", "\n", "# 按模型、任务、方法分组统计\n", "grouped_stats = df_analysis.groupby(['llm_model', 'task_name', 'uq_method'])['uq_value'].agg([\n", "    'count', 'mean', 'std', 'var', 'median', 'min', 'max'\n", "]).round(4)\n", "\n", "print(\"=== 分组统计结果 (前20行) ===\")\n", "print(grouped_stats.head(20))\n", "\n", "# 保存详细统计结果\n", "grouped_stats.to_csv('uq_result_analysis/data/grouped_statistics.csv')\n", "print(\"\\n详细统计结果已保存到: uq_result_analysis/data/grouped_statistics.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 模型效果比较分析"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 模型-任务统计 ===\n", "                                  mean     std  count\n", "llm_model   task_name                                \n", "phi4_latest topic_labeling      1.6296  1.5869   3949\n", "qwen3-32b   counterfactual_qa   1.5962  1.5645    120\n", "            explorative_coding  1.5092  1.5476   2167\n", "            sentiment_analysis  0.6719  0.7482   2233\n"]}, {"ename": "ValueError", "evalue": "Unknown format code 'd' for object of type 'float'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 33\u001b[0m\n\u001b[1;32m     30\u001b[0m \u001b[38;5;66;03m# 3. 样本数量热力图\u001b[39;00m\n\u001b[1;32m     31\u001b[0m pivot_count \u001b[38;5;241m=\u001b[39m df_analysis\u001b[38;5;241m.\u001b[39mpivot_table(values\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124muq_value\u001b[39m\u001b[38;5;124m'\u001b[39m, index\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mllm_model\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[1;32m     32\u001b[0m                                      columns\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtask_name\u001b[39m\u001b[38;5;124m'\u001b[39m, aggfunc\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcount\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m---> 33\u001b[0m \u001b[43msns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheatmap\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpivot_count\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mannot\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfmt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43md\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcmap\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mBlues\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m     34\u001b[0m \u001b[43m            \u001b[49m\u001b[43max\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxes\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcbar_kws\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mlabel\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSample Count\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     35\u001b[0m axes[\u001b[38;5;241m2\u001b[39m]\u001b[38;5;241m.\u001b[39mset_title(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSample Count by Model and Task\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     36\u001b[0m axes[\u001b[38;5;241m2\u001b[39m]\u001b[38;5;241m.\u001b[39mset_xlabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mTask\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/seaborn/matrix.py:459\u001b[0m, in \u001b[0;36mheatmap\u001b[0;34m(data, vmin, vmax, cmap, center, robust, annot, fmt, annot_kws, linewidths, linecolor, cbar, cbar_kws, cbar_ax, square, xticklabels, yticklabels, mask, ax, **kwargs)\u001b[0m\n\u001b[1;32m    457\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m square:\n\u001b[1;32m    458\u001b[0m     ax\u001b[38;5;241m.\u001b[39mset_aspect(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mequal\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 459\u001b[0m \u001b[43mplotter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mplot\u001b[49m\u001b[43m(\u001b[49m\u001b[43max\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcbar_ax\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    460\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ax\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/seaborn/matrix.py:352\u001b[0m, in \u001b[0;36m_HeatMapper.plot\u001b[0;34m(self, ax, cax, kws)\u001b[0m\n\u001b[1;32m    350\u001b[0m \u001b[38;5;66;03m# Annotate the cells with the formatted values\u001b[39;00m\n\u001b[1;32m    351\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mannot:\n\u001b[0;32m--> 352\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_annotate_heatmap\u001b[49m\u001b[43m(\u001b[49m\u001b[43max\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmesh\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/seaborn/matrix.py:260\u001b[0m, in \u001b[0;36m_HeatMapper._annotate_heatmap\u001b[0;34m(self, ax, mesh)\u001b[0m\n\u001b[1;32m    258\u001b[0m lum \u001b[38;5;241m=\u001b[39m relative_luminance(color)\n\u001b[1;32m    259\u001b[0m text_color \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.15\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m lum \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m.408\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 260\u001b[0m annotation \u001b[38;5;241m=\u001b[39m \u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m{\u001b[39;49m\u001b[38;5;124;43m:\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfmt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m}\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mformat\u001b[49m\u001b[43m(\u001b[49m\u001b[43mval\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    261\u001b[0m text_kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m(color\u001b[38;5;241m=\u001b[39mtext_color, ha\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcenter\u001b[39m\u001b[38;5;124m\"\u001b[39m, va\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcenter\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    262\u001b[0m text_kwargs\u001b[38;5;241m.\u001b[39mupdate(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mannot_kws)\n", "\u001b[0;31mValueError\u001b[0m: Unknown format code 'd' for object of type 'float'"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x600 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 按模型和任务分组的平均UQ值比较\n", "model_task_stats = df_analysis.groupby(['llm_model', 'task_name'])['uq_value'].agg([\n", "    'mean', 'std', 'count'\n", "]).round(4)\n", "\n", "print(\"=== 模型-任务统计 ===\")\n", "print(model_task_stats)\n", "\n", "# 创建热力图\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "# 1. 平均UQ值热力图\n", "pivot_mean = df_analysis.pivot_table(values='uq_value', index='llm_model', \n", "                                     columns='task_name', aggfunc='mean')\n", "sns.heatmap(pivot_mean, annot=True, fmt='.3f', cmap='RdYlBu_r', \n", "            ax=axes[0], cbar_kws={'label': 'Mean UQ Value'})\n", "axes[0].set_title('Mean UQ Value by Model and Task')\n", "axes[0].set_xlabel('Task')\n", "axes[0].set_ylabel('Model')\n", "\n", "# 2. 标准差热力图\n", "pivot_std = df_analysis.pivot_table(values='uq_value', index='llm_model', \n", "                                   columns='task_name', aggfunc='std')\n", "sns.heatmap(pivot_std, annot=True, fmt='.3f', cmap='Reds', \n", "            ax=axes[1], cbar_kws={'label': 'Std UQ Value'})\n", "axes[1].set_title('UQ Value Standard Deviation by Model and Task')\n", "axes[1].set_xlabel('Task')\n", "axes[1].set_ylabel('Model')\n", "\n", "# 3. 样本数量热力图\n", "pivot_count = df_analysis.pivot_table(values='uq_value', index='llm_model', \n", "                                     columns='task_name', aggfunc='count')\n", "sns.heatmap(pivot_count, annot=True, fmt='d', cmap='Blues', \n", "            ax=axes[2], cbar_kws={'label': 'Sample Count'})\n", "axes[2].set_title('Sample Count by Model and Task')\n", "axes[2].set_xlabel('Task')\n", "axes[2].set_ylabel('Model')\n", "\n", "plt.tight_layout()\n", "plt.savefig('uq_result_analysis/figures/model_task_heatmaps.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. UQ方法效果比较"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== UQ方法统计 ===\n", "                          count    mean     std  median     min      max\n", "uq_method                                                               \n", "EigValLaplacianJaccardUQ    769  3.9512  1.8485  4.2938  1.0000  11.0087\n", "EccentricityJaccardUQ       769  2.7428  1.5124  3.1625  0.0000   5.2915\n", "EigValLaplacianNLIUQ        769  2.3205  0.9530  2.1568  1.0250   6.7810\n", "EccentricityNLIEntailUQ     769  1.7171  1.0261  1.7340  0.0000   4.4766\n", "NumSetsUQ                   769  1.2536  0.7444  1.0000  1.0000  11.0000\n", "KernelLanguageEntropyUQ     769  0.9855  0.0079  0.9866  0.9001   0.9931\n", "SemanticEntropyNLIUQ        769  0.6517  0.3242  0.7879  0.0000   1.0000\n", "LofreeCPUQ                  769  0.6142  0.3361  0.7902  0.0000   0.9339\n", "LUQUQ                       779  0.3289  0.1998  0.3206  0.0000   1.0000\n", "EmbeddingQwenUQ             769  0.1865  0.1105  0.2139 -0.0000   0.4649\n", "EmbeddingE5UQ               769  0.0652  0.0299  0.0696 -0.0000   0.1448\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# UQ方法效果比较\n", "method_stats = df_analysis.groupby('uq_method')['uq_value'].agg([\n", "    'count', 'mean', 'std', 'median', 'min', 'max'\n", "]).round(4)\n", "\n", "print(\"=== UQ方法统计 ===\")\n", "print(method_stats.sort_values('mean', ascending=False))\n", "\n", "# 创建UQ方法比较图\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# 1. 箱线图比较\n", "df_analysis.boxplot(column='uq_value', by='uq_method', ax=axes[0, 0], rot=45)\n", "axes[0, 0].set_title('UQ Value Distribution by Method')\n", "axes[0, 0].set_xlabel('UQ Method')\n", "axes[0, 0].set_ylabel('UQ Value')\n", "\n", "# 2. 平均值条形图\n", "method_means = method_stats['mean'].sort_values(ascending=True)\n", "axes[0, 1].barh(range(len(method_means)), method_means.values, \n", "                color=plt.cm.viridis(np.linspace(0, 1, len(method_means))))\n", "axes[0, 1].set_yticks(range(len(method_means)))\n", "axes[0, 1].set_yticklabels(method_means.index)\n", "axes[0, 1].set_xlabel('Mean UQ Value')\n", "axes[0, 1].set_title('Mean UQ Value by Method')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. 标准差条形图\n", "method_stds = method_stats['std'].sort_values(ascending=True)\n", "axes[1, 0].barh(range(len(method_stds)), method_stds.values, \n", "                color=plt.cm.plasma(np.linspace(0, 1, len(method_stds))))\n", "axes[1, 0].set_yticks(range(len(method_stds)))\n", "axes[1, 0].set_yticklabels(method_stds.index)\n", "axes[1, 0].set_xlabel('Standard Deviation')\n", "axes[1, 0].set_title('UQ Value Std by Method')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# 4. 变异系数 (CV = std/mean)\n", "method_cv = (method_stats['std'] / method_stats['mean']).sort_values(ascending=True)\n", "axes[1, 1].barh(range(len(method_cv)), method_cv.values, \n", "                color=plt.cm.coolwarm(np.linspace(0, 1, len(method_cv))))\n", "axes[1, 1].set_yticks(range(len(method_cv)))\n", "axes[1, 1].set_yticklabels(method_cv.index)\n", "axes[1, 1].set_xlabel('Coefficient of Variation (CV)')\n", "axes[1, 1].set_title('UQ Value CV by Method')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('uq_result_analysis/figures/method_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 详细分组分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建详细的分组分析\n", "def create_detailed_analysis(model, task):\n", "    \"\"\"为特定模型和任务创建详细分析\"\"\"\n", "    subset = df_analysis[(df_analysis['llm_model'] == model) & \n", "                        (df_analysis['task_name'] == task)]\n", "    \n", "    if subset.empty:\n", "        print(f\"No data for {model} - {task}\")\n", "        return\n", "    \n", "    print(f\"\\n=== {model} - {task} 详细分析 ===\")\n", "    \n", "    # 按方法统计\n", "    method_stats = subset.groupby('uq_method')['uq_value'].agg([\n", "        'count', 'mean', 'std', 'median', 'min', 'max'\n", "    ]).round(4)\n", "    \n", "    print(method_stats)\n", "    \n", "    # 创建可视化\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 分布图\n", "    for method in subset['uq_method'].unique():\n", "        method_data = subset[subset['uq_method'] == method]['uq_value']\n", "        axes[0].hist(method_data, bins=20, alpha=0.6, label=method)\n", "    \n", "    axes[0].set_xlabel('UQ Value')\n", "    axes[0].set_ylabel('Frequency')\n", "    axes[0].set_title(f'{model} - {task}: UQ Distribution by Method')\n", "    axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    axes[0].grid(True, alpha=0.3)\n", "    \n", "    # 箱线图\n", "    subset.boxplot(column='uq_value', by='uq_method', ax=axes[1], rot=45)\n", "    axes[1].set_title(f'{model} - {task}: UQ Value by Method')\n", "    axes[1].set_xlabel('UQ Method')\n", "    axes[1].set_ylabel('UQ Value')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(f'uq_result_analysis/figures/{model}_{task}_analysis.png', \n", "                dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    return method_stats\n", "\n", "# 为每个模型-任务组合创建分析\n", "model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index()\n", "print(\"Available model-task combinations:\")\n", "print(model_task_combinations)\n", "\n", "# 创建目录\n", "Path('uq_result_analysis/figures').mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析主要的模型-任务组合\n", "detailed_results = {}\n", "\n", "for _, row in model_task_combinations.iterrows():\n", "    model = row['llm_model']\n", "    task = row['task_name']\n", "    if row[0] > 50:  # 只分析样本数量大于50的组合\n", "        detailed_results[f\"{model}_{task}\"] = create_detailed_analysis(model, task)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 总结报告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成总结报告\n", "print(\"=== UQ分析总结报告 ===\")\n", "print(f\"\\n1. 数据概况:\")\n", "print(f\"   - 总样本数: {len(df_analysis)}\")\n", "print(f\"   - 模型数量: {df_analysis['llm_model'].nunique()}\")\n", "print(f\"   - 任务数量: {df_analysis['task_name'].nunique()}\")\n", "print(f\"   - UQ方法数量: {df_analysis['uq_method'].nunique()}\")\n", "\n", "print(f\"\\n2. UQ值整体统计:\")\n", "print(f\"   - 均值: {df_analysis['uq_value'].mean():.4f}\")\n", "print(f\"   - 标准差: {df_analysis['uq_value'].std():.4f}\")\n", "print(f\"   - 中位数: {df_analysis['uq_value'].median():.4f}\")\n", "print(f\"   - 范围: [{df_analysis['uq_value'].min():.4f}, {df_analysis['uq_value'].max():.4f}]\")\n", "\n", "print(f\"\\n3. 模型表现 (按平均UQ值排序):\")\n", "model_performance = df_analysis.groupby('llm_model')['uq_value'].agg(['mean', 'std', 'count']).round(4)\n", "model_performance = model_performance.sort_values('mean')\n", "for model, stats in model_performance.iterrows():\n", "    print(f\"   - {model}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 样本数={stats['count']}\")\n", "\n", "print(f\"\\n4. 任务难度 (按平均UQ值排序):\")\n", "task_difficulty = df_analysis.groupby('task_name')['uq_value'].agg(['mean', 'std', 'count']).round(4)\n", "task_difficulty = task_difficulty.sort_values('mean', ascending=False)\n", "for task, stats in task_difficulty.iterrows():\n", "    print(f\"   - {task}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 样本数={stats['count']}\")\n", "\n", "print(f\"\\n5. UQ方法效果 (按平均UQ值排序):\")\n", "method_effectiveness = df_analysis.groupby('uq_method')['uq_value'].agg(['mean', 'std', 'count']).round(4)\n", "method_effectiveness = method_effectiveness.sort_values('mean', ascending=False)\n", "for method, stats in method_effectiveness.iterrows():\n", "    print(f\"   - {method}: 均值={stats['mean']:.4f}, 标准差={stats['std']:.4f}, 样本数={stats['count']}\")\n", "\n", "# 保存报告\n", "report = {\n", "    'data_overview': {\n", "        'total_samples': len(df_analysis),\n", "        'num_models': df_analysis['llm_model'].nunique(),\n", "        'num_tasks': df_analysis['task_name'].nunique(),\n", "        'num_methods': df_analysis['uq_method'].nunique()\n", "    },\n", "    'uq_value_stats': {\n", "        'mean': float(df_analysis['uq_value'].mean()),\n", "        'std': float(df_analysis['uq_value'].std()),\n", "        'median': float(df_analysis['uq_value'].median()),\n", "        'min': float(df_analysis['uq_value'].min()),\n", "        'max': float(df_analysis['uq_value'].max())\n", "    },\n", "    'model_performance': model_performance.to_dict('index'),\n", "    'task_difficulty': task_difficulty.to_dict('index'),\n", "    'method_effectiveness': method_effectiveness.to_dict('index')\n", "}\n", "\n", "with open('uq_result_analysis/data/analysis_report.json', 'w', encoding='utf-8') as f:\n", "    json.dump(report, f, indent=2, ensure_ascii=False)\n", "\n", "print(\"\\n分析报告已保存到: uq_result_analysis/data/analysis_report.json\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}