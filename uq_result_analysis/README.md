# UQ结果分析实现方案

## 概述

本项目实现了对不确定性量化(UQ)结果的全面分析，按照以下层次结构进行：
1. **单独分析**: model-task-uq method 逐一分析
2. **合并比较**: model-task 层面的UQ方法比较（归一化）
3. **整体分析**: 全局视角的UQ效果评估

## 核心功能

### 1. 数据提取 (`data_extractor.py`)
- 从MongoDB提取UQ结果数据
- 支持多个collection的数据合并
- 生成CSV格式的结构化数据
- 提供数据摘要统计

### 2. 数据归一化处理
**问题**: 不同UQ方法的绝对值无法直接比较
- `LUQUQ`: [0.0000, 1.0000]
- `EigValLaplacianJaccardUQ`: [1.0000, 11.0087]
- `KernelLanguageEntropyUQ`: [0.9001, 0.9931]

**解决方案**: 实现两种归一化方法
- **Min-Max归一化**: 将每个方法的值归一化到[0,1]区间
- **Z-score标准化**: 标准化为均值0、标准差1的分布

### 3. 可视化分析

#### 3.1 单独分析 (Individual Analysis)
为每个 model-task-uq method 组合生成：
- 分布直方图
- Violin plot（替代box plot）
- 详细统计信息

**输出**: `{model}_{task}_{method}_individual.pdf`

#### 3.2 合并比较 (Comparison Analysis)
为每个 model-task 组合生成UQ方法对比：
- 原始值 vs 归一化值对比
- 多种归一化方法的violin plot
- 统计比较表格

**输出**: `{model}_{task}_methods_comparison_normalized.pdf`

#### 3.3 整体分析 (Overall Analysis)
- 全局分布概览
- 模型-任务热力图
- UQ方法效果排名（归一化后）
- 综合统计报告

**输出**: 
- `overall_distribution.pdf`
- `model_task_heatmaps.pdf`
- `uq_methods_heatmaps.pdf`
- `overall_method_comparison_normalized.pdf`

## 文件结构

```
uq_result_analysis/
├── data_extractor.py          # 数据提取脚本
├── run_analysis.py            # 主分析脚本
├── test_violin_plots.py       # Violin plot测试
├── test_normalization.py      # 归一化测试
├── uq_analysis_notebook.ipynb # Jupyter notebook版本
├── data/                      # 数据文件
│   ├── combined_uq_results.csv
│   ├── grouped_statistics.csv
│   ├── normalization_stats.json
│   └── analysis_report_with_normalization.json
└── figures/                   # 生成的PDF图表
    ├── test_*.pdf            # 测试图表
    ├── *_individual.pdf      # 单独分析图表
    ├── *_comparison_normalized.pdf  # 比较分析图表
    └── overall_*.pdf         # 整体分析图表
```

## 使用方法

### 1. 数据提取
```bash
python data_extractor.py
```

### 2. 完整分析
```bash
python run_analysis.py
```

### 3. 测试功能
```bash
# 测试violin plot
python test_violin_plots.py

# 测试归一化
python test_normalization.py
```

## 关键特性

### 1. 归一化处理
- **必要性**: 不同UQ方法的值域差异巨大，直接比较无意义
- **方法**: Min-Max和Z-score两种归一化
- **效果**: 使不同方法的结果具有可比性

### 2. Violin Plot
- **优势**: 比box plot提供更丰富的分布信息
- **应用**: 所有分布比较都使用violin plot
- **参数**: 显示均值和中位数线

### 3. 论文级可视化
- **格式**: 所有图表输出为PDF格式
- **样式**: 统一的学术论文风格
- **布局**: 清晰的多子图布局

### 4. 分层分析
- **Level 1**: 单个组合的详细分析
- **Level 2**: 同一model-task下的方法比较
- **Level 3**: 全局视角的整体分析

## 数据统计

当前数据集包含：
- **总样本数**: 8,469
- **模型数量**: 2 (qwen3-32b, phi4_latest)
- **任务数量**: 4 (sentiment_analysis, topic_labeling, explorative_coding, counterfactual_qa)
- **UQ方法数量**: 11

## 输出说明

### 1. 归一化统计 (`normalization_stats.json`)
记录每个UQ方法的原始统计信息：
- 最小值、最大值、均值、标准差
- 变异系数
- 归一化参数

### 2. 分析报告 (`analysis_report_with_normalization.json`)
包含：
- 数据概览
- 模型性能比较
- 任务难度分析
- UQ方法效果评估
- 归一化信息

### 3. PDF图表
- **单独分析**: 每个model-task-method组合的详细分析
- **比较分析**: 归一化前后的方法比较
- **整体分析**: 全局视角的综合评估

## 技术要点

### 1. 数据处理
- 自动处理缺失值和异常值
- 支持大规模数据的内存优化
- 灵活的数据过滤和分组

### 2. 可视化优化
- 自适应的图表布局
- 颜色编码的一致性
- 清晰的标签和图例

### 3. 统计分析
- 多种描述性统计指标
- 归一化效果验证
- 方法间的定量比较

## 扩展性

该框架支持：
- 新增UQ方法的自动处理
- 不同数据源的集成
- 自定义分析维度
- 批量处理和自动化

## 注意事项

1. **归一化的重要性**: 所有横向比较都必须使用归一化值
2. **样本数量要求**: 单独分析要求≥5个样本，比较分析要求≥50个样本
3. **内存管理**: 大量图表生成时注意内存使用，及时关闭图表对象
4. **PDF格式**: 所有输出图表均为PDF格式，适合论文使用
