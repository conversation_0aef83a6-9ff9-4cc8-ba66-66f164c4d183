#!/usr/bin/env python3
"""
测试归一化功能的简化版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def setup_paper_style():
    """设置论文发表风格"""
    plt.rcParams.update({
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10,
        'figure.titlesize': 16,
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("加载数据...")
    data_dir = Path('uq_result_analysis/data')
    df = pd.read_csv(data_dir / 'combined_uq_results.csv')
    
    # 清理数据
    df_clean = df.dropna(subset=['uq_value']).copy()
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()
    
    print(f"清理后数据量: {len(df_analysis)}")
    return df_analysis

def normalize_uq_values(df_analysis):
    """对每个UQ方法的值进行归一化处理"""
    print("对UQ值进行归一化处理...")
    
    df_normalized = df_analysis.copy()
    normalization_stats = {}
    
    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']
        
        # 计算归一化参数
        min_val = method_values.min()
        max_val = method_values.max()
        mean_val = method_values.mean()
        std_val = method_values.std()
        
        # Min-Max归一化到[0,1]
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0
        
        df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values
        
        # Z-score归一化
        if std_val != 0:
            z_score_values = (method_values - mean_val) / std_val
        else:
            z_score_values = method_values * 0
        
        df_normalized.loc[method_mask, 'uq_value_zscore'] = z_score_values
        
        # 保存统计信息
        normalization_stats[method] = {
            'original_min': float(min_val),
            'original_max': float(max_val),
            'original_mean': float(mean_val),
            'original_std': float(std_val),
            'range': float(max_val - min_val)
        }
    
    print("归一化完成")
    return df_normalized, normalization_stats

def test_normalization_visualization(df_analysis, output_dir):
    """测试归一化可视化"""
    print("创建归一化测试可视化...")
    
    setup_paper_style()
    
    # 选择一个有多个方法的model-task组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).agg({
        'uq_method': 'nunique',
        'uq_value': 'count'
    }).reset_index()
    model_task_combinations = model_task_combinations[
        (model_task_combinations['uq_method'] >= 5) & 
        (model_task_combinations['uq_value'] >= 100)
    ]
    
    if len(model_task_combinations) == 0:
        print("没有找到合适的测试数据")
        return
    
    # 选择第一个组合
    row = model_task_combinations.iloc[0]
    model = row['llm_model']
    task = row['task_name']
    
    print(f"测试组合: {model} - {task}")
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    methods = subset['uq_method'].unique()[:6]  # 最多6个方法
    
    # 创建对比图
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    fig.suptitle(f'Normalization Test: {model} - {task}', fontsize=16, fontweight='bold')
    
    # 1. 原始值分布
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value']
        axes[0, 0].hist(method_data, bins=15, alpha=0.6, label=method)
    axes[0, 0].set_xlabel('Original UQ Value')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Original Values Distribution')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 原始值violin plot
    method_data_orig = [subset[subset['uq_method'] == method]['uq_value'].values for method in methods]
    parts = axes[0, 1].violinplot(method_data_orig, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    axes[0, 1].set_title('Original Values Violin Plot')
    axes[0, 1].set_xlabel('UQ Method')
    axes[0, 1].set_ylabel('Original UQ Value')
    axes[0, 1].set_xticks(range(len(methods)))
    axes[0, 1].set_xticklabels(methods, rotation=45)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 归一化值分布
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value_normalized']
        axes[1, 0].hist(method_data, bins=15, alpha=0.6, label=method)
    axes[1, 0].set_xlabel('Normalized UQ Value [0,1]')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('Normalized Values Distribution')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 归一化值violin plot
    method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]
    parts = axes[1, 1].violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    axes[1, 1].set_title('Normalized Values Violin Plot')
    axes[1, 1].set_xlabel('UQ Method')
    axes[1, 1].set_ylabel('Normalized UQ Value [0,1]')
    axes[1, 1].set_xticks(range(len(methods)))
    axes[1, 1].set_xticklabels(methods, rotation=45)
    axes[1, 1].grid(True, alpha=0.3)
    
    # 5. Z-score值分布
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value_zscore']
        axes[2, 0].hist(method_data, bins=15, alpha=0.6, label=method)
    axes[2, 0].set_xlabel('Z-score UQ Value')
    axes[2, 0].set_ylabel('Frequency')
    axes[2, 0].set_title('Z-score Values Distribution')
    axes[2, 0].legend()
    axes[2, 0].grid(True, alpha=0.3)
    
    # 6. 统计比较表
    stats_data = []
    for method in methods:
        method_subset = subset[subset['uq_method'] == method]
        stats_data.append([
            method,
            f"{len(method_subset)}",
            f"{method_subset['uq_value'].mean():.3f}",
            f"{method_subset['uq_value'].std():.3f}",
            f"{method_subset['uq_value_normalized'].mean():.3f}",
            f"{method_subset['uq_value_normalized'].std():.3f}"
        ])
    
    table = axes[2, 1].table(cellText=stats_data,
                           colLabels=['Method', 'Count', 'Orig Mean', 'Orig Std', 'Norm Mean', 'Norm Std'],
                           cellLoc='center',
                           loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1.2, 1.5)
    axes[2, 1].axis('off')
    axes[2, 1].set_title('Statistics Comparison')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_normalization_comparison.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    
    print("归一化测试可视化完成")

def print_normalization_summary(normalization_stats):
    """打印归一化摘要"""
    print("\n=== 归一化摘要 ===")
    print(f"处理的UQ方法数量: {len(normalization_stats)}")
    
    print("\n各方法原始值范围:")
    for method, stats in normalization_stats.items():
        print(f"  {method}:")
        print(f"    范围: [{stats['original_min']:.4f}, {stats['original_max']:.4f}]")
        print(f"    均值: {stats['original_mean']:.4f}")
        print(f"    标准差: {stats['original_std']:.4f}")
        print(f"    变异系数: {stats['original_std']/stats['original_mean']:.4f}" if stats['original_mean'] != 0 else "    变异系数: N/A")

def main():
    """主函数"""
    print("=== 归一化测试开始 ===")
    
    # 创建输出目录
    output_dir = Path('uq_result_analysis/figures')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    df_analysis = load_and_preprocess_data()
    
    # 进行归一化
    df_normalized, normalization_stats = normalize_uq_values(df_analysis)
    
    # 保存归一化统计
    with open(output_dir / 'test_normalization_stats.json', 'w', encoding='utf-8') as f:
        json.dump(normalization_stats, f, indent=2, ensure_ascii=False)
    
    # 创建可视化
    test_normalization_visualization(df_normalized, output_dir)
    
    # 打印摘要
    print_normalization_summary(normalization_stats)
    
    print(f"\n=== 测试完成 ===")
    print(f"归一化测试图表已保存到: {output_dir}")
    print(f"归一化统计已保存到: {output_dir / 'test_normalization_stats.json'}")

if __name__ == "__main__":
    main()
