#!/usr/bin/env python3
"""
为sentiment_analysis和topic_labeling任务计算embedding reference距离。

该脚本：
1. 从MongoDB读取原始数据和responses
2. 为每个document推断或定义reference text
3. 使用embedding方法重新计算reference距离
4. 更新CSV数据文件

对于sentiment_analysis：
- 使用最常见的sentiment label作为reference
- 或者使用预定义的sentiment标准答案

对于topic_labeling：
- 使用最常见的topic label作为reference
- 或者使用预定义的topic标准答案
"""

import os
import sys
import argparse
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter
import pandas as pd
import numpy as np
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from pymongo import MongoClient
from uq_methods.implementations.embedding_e5 import EmbeddingE5UQ
from uq_methods.implementations.embedding_qwen import EmbeddingQwenUQ

# 配置
DATA_DIR = Path('uq_result_analysis/data')
MONGO_HOST = 'localhost'
MONGO_PORT = 27017
DB_NAME = 'LLM-UQ'

# 预定义的reference texts
SENTIMENT_REFERENCES = {
    'positive': 'This is positive',
    'negative': 'This is negative', 
    'neutral': 'This is neutral'
}

TOPIC_REFERENCES = {
    'technology': 'This is about technology',
    'politics': 'This is about politics',
    'sports': 'This is about sports',
    'entertainment': 'This is about entertainment',
    'business': 'This is about business',
    'science': 'This is about science',
    'health': 'This is about health',
    'education': 'This is about education'
}

def connect_mongo():
    """连接MongoDB"""
    client = MongoClient(MONGO_HOST, MONGO_PORT)
    db = client[DB_NAME]
    return db

def get_responses_from_mongo(db, uq_collection_name: str, document_id: str) -> List[str]:
    """从MongoDB获取指定document的responses"""
    try:
        from bson import ObjectId

        # 首先从UQ collection获取group_key
        uq_doc = db[uq_collection_name].find_one({'_id': ObjectId(document_id)})
        if not uq_doc or 'group_key' not in uq_doc:
            return []

        group_key = uq_doc['group_key']
        task_name = group_key['task_name']

        # 根据任务类型选择不同的response collection
        if task_name == 'topic_labeling':
            response_collection_name = 'topic_modeling_responses'
        else:
            response_collection_name = 'response_collections'

        # 使用group_key在对应的response collection中查找匹配的responses
        query = {
            'task_name': group_key['task_name'],
            'dataset_source': group_key['dataset_source'],
            'prompt_seed': group_key['prompt_seed'],
            'input_text': group_key['input_text']
        }

        response_docs = list(db[response_collection_name].find(query))

        # 提取responses
        responses = []
        for doc in response_docs:
            # 优先使用actual_response，然后是parsed_answer，最后是raw_answer
            response_text = doc.get('actual_response') or doc.get('parsed_answer') or doc.get('raw_answer')
            if response_text:
                responses.append(str(response_text))

        return responses

    except Exception as e:
        print(f"Error getting responses for {document_id}: {e}")
        return []

def infer_reference_sentiment(responses: List[str]) -> str:
    """从responses推断sentiment reference"""
    # 简单的关键词匹配来推断sentiment
    positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'positive']
    negative_words = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'sad', 'angry', 'negative', 'worst']
    
    sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
    
    for response in responses:
        response_lower = response.lower()
        pos_count = sum(1 for word in positive_words if word in response_lower)
        neg_count = sum(1 for word in negative_words if word in response_lower)
        
        if pos_count > neg_count:
            sentiment_counts['positive'] += 1
        elif neg_count > pos_count:
            sentiment_counts['negative'] += 1
        else:
            sentiment_counts['neutral'] += 1
    
    # 返回最常见的sentiment
    most_common = max(sentiment_counts.items(), key=lambda x: x[1])
    return SENTIMENT_REFERENCES[most_common[0]]

def infer_reference_topic(responses: List[str], input_text: str = "") -> str:
    """从responses和input推断topic reference"""
    # 简单的关键词匹配来推断topic
    topic_keywords = {
        'technology': ['tech', 'computer', 'software', 'ai', 'digital', 'internet', 'app', 'data', 'algorithm'],
        'politics': ['government', 'election', 'vote', 'policy', 'political', 'president', 'congress', 'law'],
        'sports': ['game', 'team', 'player', 'score', 'match', 'championship', 'football', 'basketball', 'soccer'],
        'entertainment': ['movie', 'music', 'celebrity', 'show', 'film', 'actor', 'singer', 'entertainment'],
        'business': ['company', 'market', 'business', 'economy', 'financial', 'money', 'profit', 'investment'],
        'science': ['research', 'study', 'scientific', 'experiment', 'discovery', 'theory', 'analysis'],
        'health': ['health', 'medical', 'doctor', 'hospital', 'medicine', 'treatment', 'disease', 'patient'],
        'education': ['school', 'student', 'teacher', 'education', 'learning', 'university', 'study', 'academic']
    }
    
    # 合并input_text和responses进行分析
    all_text = (input_text + " " + " ".join(responses)).lower()
    
    topic_scores = {}
    for topic, keywords in topic_keywords.items():
        score = sum(1 for keyword in keywords if keyword in all_text)
        topic_scores[topic] = score
    
    # 返回得分最高的topic，如果都是0则返回默认
    if max(topic_scores.values()) == 0:
        return TOPIC_REFERENCES['technology']  # 默认
    
    best_topic = max(topic_scores.items(), key=lambda x: x[1])[0]
    return TOPIC_REFERENCES[best_topic]

def compute_embedding_reference_distance(responses: List[str], reference_text: str, embedding_method: str = 'e5') -> Optional[float]:
    """计算responses与reference的平均embedding距离"""
    try:
        if embedding_method.lower() == 'e5':
            method = EmbeddingE5UQ(reference_text=reference_text)
        else:
            method = EmbeddingQwenUQ(reference_text=reference_text)
        
        result = method.compute_uncertainty(responses)
        return result.get('avg_distance_to_reference')
    except Exception as e:
        print(f"Error computing embedding distance: {e}")
        return None

def process_task_data(task: str, limit: int = 0) -> pd.DataFrame:
    """处理指定任务的数据，计算embedding reference距离"""
    print(f"Processing task: {task}")
    
    # 读取现有CSV数据
    csv_path = DATA_DIR / f"UQ_result_{task}.csv"
    if not csv_path.exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows from CSV")
    
    # 连接MongoDB
    db = connect_mongo()
    collection_name = f"UQ_result_{task}"
    
    # 获取需要处理的embedding方法行
    embedding_df = df[df['uq_method'].str.contains('Embedding', case=False, na=False)].copy()
    if embedding_df.empty:
        print("No embedding methods found in data")
        return df
    
    print(f"Found {len(embedding_df)} embedding method rows")
    
    # 按document_id分组处理
    unique_docs = embedding_df['document_id'].unique()
    if limit > 0:
        unique_docs = unique_docs[:limit]
    
    print(f"Processing {len(unique_docs)} unique documents")
    
    # 存储计算结果
    reference_distances = {}
    
    for doc_id in tqdm(unique_docs, desc="Computing reference distances"):
        # 获取responses
        responses = get_responses_from_mongo(db, collection_name, doc_id)
        if not responses:
            print(f"No responses found for document {doc_id}")
            continue
        
        # 获取input_text用于topic推断
        doc_rows = embedding_df[embedding_df['document_id'] == doc_id]
        input_text = doc_rows.iloc[0]['input_text'] if not doc_rows.empty else ""
        
        # 推断reference text
        if task == 'sentiment_analysis':
            reference_text = infer_reference_sentiment(responses)
        elif task == 'topic_labeling':
            reference_text = infer_reference_topic(responses, input_text)
        else:
            print(f"Unknown task: {task}")
            continue
        
        # 计算E5和Qwen的reference距离
        e5_distance = compute_embedding_reference_distance(responses, reference_text, 'e5')
        qwen_distance = compute_embedding_reference_distance(responses, reference_text, 'qwen')
        
        reference_distances[doc_id] = {
            'reference_text': reference_text,
            'e5_distance': e5_distance,
            'qwen_distance': qwen_distance
        }
    
    # 更新DataFrame
    print("Updating DataFrame with reference distances...")
    
    def update_reference_distance(row):
        doc_id = row['document_id']
        method = row['uq_method']
        
        if doc_id not in reference_distances:
            return row['avg_distance_to_reference'] if 'avg_distance_to_reference' in row else None
        
        distances = reference_distances[doc_id]
        
        if 'E5' in method:
            return distances['e5_distance']
        elif 'Qwen' in method:
            return distances['qwen_distance']
        else:
            return row['avg_distance_to_reference'] if 'avg_distance_to_reference' in row else None
    
    # 更新embedding方法行的reference距离
    mask = df['uq_method'].str.contains('Embedding', case=False, na=False)
    df.loc[mask, 'avg_distance_to_reference'] = df[mask].apply(update_reference_distance, axis=1)
    
    print(f"Updated {mask.sum()} embedding method rows")
    
    return df

def main():
    parser = argparse.ArgumentParser(description="计算embedding reference距离")
    parser.add_argument('--task', required=True, choices=['sentiment_analysis', 'topic_labeling'], 
                       help='任务类型')
    parser.add_argument('--limit', type=int, default=0, 
                       help='限制处理的document数量（0表示处理全部）')
    parser.add_argument('--output-suffix', default='_with_reference', 
                       help='输出文件后缀')
    
    args = parser.parse_args()
    
    try:
        # 处理数据
        updated_df = process_task_data(args.task, args.limit)
        
        # 保存更新后的数据
        output_path = DATA_DIR / f"UQ_result_{args.task}{args.output_suffix}.csv"
        updated_df.to_csv(output_path, index=False)
        print(f"Updated data saved to: {output_path}")
        
        # 显示统计信息
        embedding_rows = updated_df[updated_df['uq_method'].str.contains('Embedding', case=False, na=False)]
        non_null_ref = embedding_rows['avg_distance_to_reference'].notna().sum()
        total_embedding = len(embedding_rows)
        
        print(f"\nStatistics:")
        print(f"Total embedding method rows: {total_embedding}")
        print(f"Rows with reference distance: {non_null_ref}")
        print(f"Coverage: {non_null_ref/total_embedding*100:.1f}%")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
