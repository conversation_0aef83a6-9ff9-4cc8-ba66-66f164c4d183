#!/usr/bin/env python3
"""
测试大字体和分离图表功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def setup_paper_style():
    """设置论文发表风格 - 大字体版本"""
    plt.rcParams.update({
        'font.size': 16,           # 增大基础字体
        'axes.titlesize': 20,      # 增大标题字体
        'axes.labelsize': 18,      # 增大坐标轴标签字体
        'xtick.labelsize': 16,     # 增大x轴刻度字体
        'ytick.labelsize': 16,     # 增大y轴刻度字体
        'legend.fontsize': 14,     # 增大图例字体
        'figure.titlesize': 22,    # 增大图形标题字体
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("加载数据...")
    data_dir = Path('uq_result_analysis/data')
    df = pd.read_csv(data_dir / 'combined_uq_results.csv')
    
    # 清理数据
    df_clean = df.dropna(subset=['uq_value']).copy()
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()
    
    # 归一化处理
    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']
        
        # Min-Max归一化
        min_val = method_values.min()
        max_val = method_values.max()
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0
        
        df_analysis.loc[method_mask, 'uq_value_normalized'] = normalized_values
    
    print(f"清理后数据量: {len(df_analysis)}")
    return df_analysis

def test_individual_plots(df_analysis, output_dir):
    """测试单独图表（大字体）"""
    print("测试单独图表...")
    
    setup_paper_style()
    
    # 选择一个测试组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
    test_combo = model_task_combinations[model_task_combinations['count'] >= 20].iloc[0]
    
    model = test_combo['llm_model']
    task = test_combo['task_name']
    method = test_combo['uq_method']
    
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task) & 
        (df_analysis['uq_method'] == method)
    ]
    
    print(f"测试组合: {model} - {task} - {method}")
    
    # 1. 分布图
    fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
    ax1.hist(subset['uq_value'], bins=15, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {subset["uq_value"].mean():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title(f'{model} - {task} - {method}\nDistribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_distribution.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    
    # 2. Violin图
    fig2, ax2 = plt.subplots(1, 1, figsize=(8, 10))
    parts = ax2.violinplot([subset['uq_value']], positions=[1], showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_facecolor('lightgreen')
        pc.set_alpha(0.7)
    ax2.set_ylabel('UQ Value')
    ax2.set_title(f'{model} - {task} - {method}\nViolin Plot')
    ax2.set_xticks([1])
    ax2.set_xticklabels([method], rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    
    # 3. 统计表格
    fig3, ax3 = plt.subplots(1, 1, figsize=(10, 8))
    stats_data = [
        ['Count', f"{len(subset)}"],
        ['Mean', f"{subset['uq_value'].mean():.4f}"],
        ['Std', f"{subset['uq_value'].std():.4f}"],
        ['Median', f"{subset['uq_value'].median():.4f}"],
        ['Min', f"{subset['uq_value'].min():.4f}"],
        ['Max', f"{subset['uq_value'].max():.4f}"]
    ]
    
    table = ax3.table(cellText=stats_data,
                     colLabels=['Statistic', 'Value'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(16)
    table.scale(1.5, 2.0)
    ax3.axis('off')
    ax3.set_title(f'{model} - {task} - {method}\nStatistics Summary')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_statistics.pdf', format='pdf', bbox_inches='tight')
    plt.close()

def test_comparison_plots(df_analysis, output_dir):
    """测试比较图表（大字体）"""
    print("测试比较图表...")
    
    setup_paper_style()
    
    # 选择一个model-task组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index(name='count')
    test_combo = model_task_combinations[model_task_combinations['count'] >= 100].iloc[0]
    
    model = test_combo['llm_model']
    task = test_combo['task_name']
    
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    methods = subset['uq_method'].unique()[:6]  # 最多6个方法
    colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
    
    print(f"测试组合: {model} - {task}")
    
    # 1. 原始值分布
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value']
        ax1.hist(method_data, bins=15, alpha=0.6, label=method, color=colors[i])
    
    ax1.set_xlabel('Original UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title(f'{model} - {task}\nOriginal Values Distribution')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_comparison_original.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    
    # 2. 归一化值violin plot
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]
    parts = ax2.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax2.set_title(f'{model} - {task}\nNormalized Values Violin Plot')
    ax2.set_xlabel('UQ Method')
    ax2.set_ylabel('Normalized UQ Value [0,1]')
    ax2.set_xticks(range(len(methods)))
    ax2.set_xticklabels(methods, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_comparison_normalized.pdf', format='pdf', bbox_inches='tight')
    plt.close()

def test_overall_plots(df_analysis, output_dir):
    """测试整体图表（大字体）"""
    print("测试整体图表...")
    
    setup_paper_style()
    
    # 1. 整体分布
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    ax1.hist(df_analysis['uq_value'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(df_analysis['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {df_analysis["uq_value"].mean():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Overall UQ Value Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_overall_distribution.pdf', format='pdf', bbox_inches='tight')
    plt.close()
    
    # 2. 热力图
    fig2, ax2 = plt.subplots(1, 1, figsize=(10, 8))
    pivot_mean = df_analysis.pivot_table(values='uq_value', index='llm_model', 
                                         columns='task_name', aggfunc='mean')
    sns.heatmap(pivot_mean, annot=True, fmt='.3f', cmap='RdYlBu_r', 
                ax=ax2, cbar_kws={'label': 'Mean UQ Value'})
    ax2.set_title('Mean UQ Value by Model and Task')
    ax2.set_xlabel('Task')
    ax2.set_ylabel('Model')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_large_font_heatmap.pdf', format='pdf', bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("=== 大字体和分离图表测试开始 ===")
    
    # 创建输出目录
    output_dir = Path('uq_result_analysis/figures')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    df_analysis = load_and_preprocess_data()
    
    # 运行测试
    test_individual_plots(df_analysis, output_dir)
    test_comparison_plots(df_analysis, output_dir)
    test_overall_plots(df_analysis, output_dir)
    
    print("\n=== 测试完成 ===")
    print(f"大字体测试图表已保存到: {output_dir}")
    
    # 列出生成的测试文件
    test_files = list(output_dir.glob("test_large_font_*.pdf"))
    print(f"\n生成的测试文件:")
    for test_file in sorted(test_files):
        print(f"  - {test_file.name}")
    
    print("\n字体设置:")
    print(f"  - 基础字体: 16pt")
    print(f"  - 标题字体: 20pt")
    print(f"  - 坐标轴标签: 18pt")
    print(f"  - 刻度标签: 16pt")
    print(f"  - 图例字体: 14pt")

if __name__ == "__main__":
    main()
