# UQ分析Jupyter Notebook使用说明

## 概述

`uq_analysis_updated.ipynb` 是一个完整的UQ结果分析notebook，包含了所有最新的功能和改进。

## 主要特性

### 1. 大字体设置
- **基础字体**: 16pt
- **标题字体**: 20pt  
- **坐标轴标签**: 18pt
- **刻度标签**: 16pt
- **图例字体**: 14pt

所有图表都使用大字体，适合论文展示和打印。

### 2. 分离图表
- 每个图表都单独保存为PDF文件
- 不再使用多子图合并的方式
- 便于在论文中单独使用每个图表

### 3. 归一化处理
- **问题**: 不同UQ方法的绝对值无法直接比较
- **解决**: 对每个UQ方法单独进行归一化
- **方法**: Min-Max归一化[0,1] 和 Z-score标准化
- **效果**: 确保横向比较的有效性

### 4. Violin Plot
- 使用violin plot替代box plot
- 提供更丰富的分布信息
- 显示均值和中位数线

### 5. 密度曲线
- **改进**: 使用密度曲线替代柱状图
- **优势**: 更平滑的分布展示，便于多个分布的对比
- **效果**: 突出分布的形状特征，减少bins选择的影响

## 分析流程

### 第1步: 环境设置和数据加载
```python
# 设置大字体风格
setup_paper_style()

# 加载数据
df = pd.read_csv('uq_result_analysis/data/combined_uq_results.csv')
```

### 第2步: 数据预处理和归一化
```python
# 清理数据
df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()

# 归一化处理
df_analysis, normalization_stats = normalize_uq_values(df_analysis)
```

### 第3步: 单独分析 (Model-Task-UQ Method)
```python
# 为每个组合创建分析图表
create_individual_analysis(model, task, method, subset)
```
**输出文件**:
- `{model}_{task}_{method}_distribution.pdf`
- `{model}_{task}_{method}_violin.pdf`
- `{model}_{task}_{method}_statistics.pdf`

### 第4步: 合并比较 (Model-Task层面)
```python
# 比较同一model-task下的不同UQ方法
create_model_task_comparison(model, task, subset)
```
**输出文件**:
- `{model}_{task}_original_distribution.pdf`
- `{model}_{task}_original_violin.pdf`
- `{model}_{task}_normalized_distribution.pdf`
- `{model}_{task}_normalized_violin.pdf`
- `{model}_{task}_comparison_statistics.pdf`

### 第5步: 整体分析
```python
# 全局视角的分析
```
**输出文件**:
- `overall_distribution.pdf`
- `distribution_by_model.pdf`
- `distribution_by_task.pdf`
- `distribution_by_method.pdf`

### 第6步: 热力图分析
```python
# 不同维度的关系热力图
```
**输出文件**:
- `heatmap_mean_uq_value.pdf`
- `heatmap_std_uq_value.pdf`
- `heatmap_model_method_normalized.pdf`
- `heatmap_task_method_normalized.pdf`

### 第7步: UQ方法整体比较
```python
# 使用归一化值比较不同UQ方法
```
**输出文件**:
- `overall_original_violin.pdf`
- `overall_normalized_violin.pdf`
- `overall_normalization_comparison.pdf`

## 使用方法

### 1. 启动Jupyter Notebook
```bash
cd uq_result_analysis
jupyter notebook uq_analysis_updated.ipynb
```

### 2. 逐步执行
- 按顺序执行每个cell
- 每个cell都有详细的说明
- 可以根据需要修改参数

### 3. 自定义分析
```python
# 分析特定组合
model = "qwen3-32b"
task = "sentiment_analysis"
subset = df_analysis[
    (df_analysis['llm_model'] == model) & 
    (df_analysis['task_name'] == task)
]
create_model_task_comparison(model, task, subset)
```

### 4. 批量处理
```python
# 分析所有有效组合
for idx, row in valid_combinations.iterrows():
    model = row['llm_model']
    task = row['task_name']
    method = row['uq_method']
    
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task) & 
        (df_analysis['uq_method'] == method)
    ]
    
    create_individual_analysis(model, task, method, subset)
```

## 输出文件

### 图表文件
所有图表保存在 `figures/` 目录下，格式为PDF：
- 单独分析: `{model}_{task}_{method}_{type}.pdf`
- 比较分析: `{model}_{task}_{type}.pdf`
- 整体分析: `overall_{type}.pdf`
- 热力图: `heatmap_{type}.pdf`

### 数据文件
- `normalization_stats.json`: 归一化统计信息
- `grouped_statistics.csv`: 分组统计数据

## 重要说明

### 1. 归一化的必要性
不同UQ方法的原始值范围差异巨大：
- `LUQUQ`: [0.0000, 1.0000]
- `EigValLaplacianJaccardUQ`: [1.0000, 11.0087]
- `KernelLanguageEntropyUQ`: [0.9001, 0.9931]

**必须使用归一化值进行横向比较！**

### 2. 样本数量要求
- 单独分析: ≥5个样本
- 比较分析: ≥50个样本

### 3. 内存管理
- 每个图表执行后会自动关闭
- 避免内存泄漏
- 适合大规模数据处理

### 4. 自定义设置
```python
# 修改字体大小
plt.rcParams['font.size'] = 18  # 更大的字体

# 修改图表大小
fig, ax = plt.subplots(1, 1, figsize=(14, 10))

# 修改保存格式
plt.savefig('filename.png', dpi=300)  # PNG格式
```

## 故障排除

### 1. 数据文件不存在
确保已运行 `data_extractor.py` 提取数据：
```bash
python data_extractor.py
```

### 2. 字体显示问题
```python
# 检查可用字体
import matplotlib.font_manager as fm
print([f.name for f in fm.fontManager.ttflist])
```

### 3. 内存不足
```python
# 减少处理的数据量
df_sample = df_analysis.sample(n=1000)  # 随机采样
```

### 4. 图表不显示
```python
# 确保matplotlib后端设置正确
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
```

## 扩展功能

### 1. 添加新的分析维度
```python
# 按数据集分析
for dataset in df_analysis['dataset_source'].unique():
    subset = df_analysis[df_analysis['dataset_source'] == dataset]
    # 进行分析...
```

### 2. 自定义归一化方法
```python
# 使用其他归一化方法
from sklearn.preprocessing import RobustScaler
scaler = RobustScaler()
df_analysis['uq_value_robust'] = scaler.fit_transform(df_analysis[['uq_value']])
```

### 3. 交互式图表
```python
import plotly.express as px
fig = px.violin(df_analysis, x='uq_method', y='uq_value_normalized')
fig.show()
```

这个notebook提供了完整的UQ分析流程，所有图表都使用大字体并单独保存，确保了论文展示的质量和便利性。
