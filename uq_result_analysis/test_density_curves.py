#!/usr/bin/env python3
"""
测试密度曲线替代柱状图的效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def setup_paper_style():
    """设置论文发表风格 - 大字体版本"""
    plt.rcParams.update({
        'font.size': 16,           # 增大基础字体
        'axes.titlesize': 20,      # 增大标题字体
        'axes.labelsize': 18,      # 增大坐标轴标签字体
        'xtick.labelsize': 16,     # 增大x轴刻度字体
        'ytick.labelsize': 16,     # 增大y轴刻度字体
        'legend.fontsize': 14,     # 增大图例字体
        'figure.titlesize': 22,    # 增大图形标题字体
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("加载数据...")
    data_dir = Path('uq_result_analysis/data')
    df = pd.read_csv(data_dir / 'combined_uq_results.csv')
    
    # 清理数据
    df_clean = df.dropna(subset=['uq_value']).copy()
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()
    
    # 归一化处理
    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']
        
        # Min-Max归一化
        min_val = method_values.min()
        max_val = method_values.max()
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0
        
        df_analysis.loc[method_mask, 'uq_value_normalized'] = normalized_values
    
    print(f"清理后数据量: {len(df_analysis)}")
    return df_analysis

def test_single_distribution_comparison(df_analysis, output_dir):
    """测试单个分布的柱状图vs密度曲线对比"""
    print("测试单个分布对比...")
    
    setup_paper_style()
    
    # 选择一个测试组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
    test_combo = model_task_combinations[model_task_combinations['count'] >= 50].iloc[0]
    
    model = test_combo['llm_model']
    task = test_combo['task_name']
    method = test_combo['uq_method']
    
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task) & 
        (df_analysis['uq_method'] == method)
    ]
    
    print(f"测试组合: {model} - {task} - {method}")
    
    # 1. 柱状图版本
    fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
    ax1.hist(subset['uq_value'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {subset["uq_value"].mean():.3f}')
    ax1.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
               label=f'Median: {subset["uq_value"].median():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title(f'{model} - {task} - {method}\\nHistogram Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_histogram_single.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 2. 密度曲线版本
    fig2, ax2 = plt.subplots(1, 1, figsize=(10, 8))
    subset['uq_value'].plot.density(ax=ax2, color='skyblue', linewidth=4, alpha=0.8)
    ax2.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {subset["uq_value"].mean():.3f}')
    ax2.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
               label=f'Median: {subset["uq_value"].median():.3f}')
    ax2.set_xlabel('UQ Value')
    ax2.set_ylabel('Density')
    ax2.set_title(f'{model} - {task} - {method}\\nDensity Curve Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_density_single.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

def test_multiple_distribution_comparison(df_analysis, output_dir):
    """测试多个分布的柱状图vs密度曲线对比"""
    print("测试多个分布对比...")
    
    setup_paper_style()
    
    # 选择一个model-task组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index(name='count')
    test_combo = model_task_combinations[model_task_combinations['count'] >= 200].iloc[0]
    
    model = test_combo['llm_model']
    task = test_combo['task_name']
    
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    methods = subset['uq_method'].unique()[:5]  # 最多5个方法
    colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
    
    print(f"测试组合: {model} - {task} (方法数: {len(methods)})")
    
    # 1. 柱状图版本
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value_normalized']
        ax1.hist(method_data, bins=15, alpha=0.6, label=method, color=colors[i])
    
    ax1.set_xlabel('Normalized UQ Value [0,1]')
    ax1.set_ylabel('Frequency')
    ax1.set_title(f'{model} - {task}\\nHistogram Comparison')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_histogram_multiple.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 2. 密度曲线版本
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    for i, method in enumerate(methods):
        method_data = subset[subset['uq_method'] == method]['uq_value_normalized']
        method_data.plot.density(ax=ax2, alpha=0.7, label=method, color=colors[i], linewidth=3)
    
    ax2.set_xlabel('Normalized UQ Value [0,1]')
    ax2.set_ylabel('Density')
    ax2.set_title(f'{model} - {task}\\nDensity Curve Comparison')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_density_multiple.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

def test_overall_comparison(df_analysis, output_dir):
    """测试整体分布对比"""
    print("测试整体分布对比...")
    
    setup_paper_style()
    
    # 1. 整体分布 - 柱状图版本
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    ax1.hist(df_analysis['uq_value'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(df_analysis['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {df_analysis["uq_value"].mean():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Overall UQ Value Distribution - Histogram')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_overall_histogram.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 2. 整体分布 - 密度曲线版本
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    df_analysis['uq_value'].plot.density(ax=ax2, color='skyblue', linewidth=4, alpha=0.8)
    ax2.axvline(df_analysis['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {df_analysis["uq_value"].mean():.3f}')
    ax2.set_xlabel('UQ Value')
    ax2.set_ylabel('Density')
    ax2.set_title('Overall UQ Value Distribution - Density Curve')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_overall_density.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 3. 按方法对比 - 密度曲线版本
    fig3, ax3 = plt.subplots(1, 1, figsize=(14, 8))
    top_methods = df_analysis['uq_method'].value_counts().head(5).index
    for method in top_methods:
        method_data = df_analysis[df_analysis['uq_method'] == method]['uq_value_normalized']
        method_data.plot.density(ax=ax3, alpha=0.7, label=method, linewidth=3)
    
    ax3.set_xlabel('Normalized UQ Value [0,1]')
    ax3.set_ylabel('Density')
    ax3.set_title('UQ Methods Comparison - Density Curves')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_dir / 'test_methods_density.pdf', format='pdf', bbox_inches='tight')
    plt.show()
    plt.close()

def main():
    """主函数"""
    print("=== 密度曲线测试开始 ===")
    
    # 创建输出目录
    output_dir = Path('uq_result_analysis/figures')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载数据
    df_analysis = load_and_preprocess_data()
    
    # 运行测试
    test_single_distribution_comparison(df_analysis, output_dir)
    test_multiple_distribution_comparison(df_analysis, output_dir)
    test_overall_comparison(df_analysis, output_dir)
    
    print("\n=== 测试完成 ===")
    print(f"对比图表已保存到: {output_dir}")
    
    # 列出生成的测试文件
    test_files = list(output_dir.glob("test_*.pdf"))
    print(f"\n生成的对比文件:")
    for test_file in sorted(test_files):
        print(f"  - {test_file.name}")
    
    print("\n密度曲线的优势:")
    print("  ✅ 更平滑的分布展示")
    print("  ✅ 便于多个分布的对比")
    print("  ✅ 突出分布的形状特征")
    print("  ✅ 减少bins选择的影响")
    print("  ✅ 更适合连续数据的可视化")

if __name__ == "__main__":
    main()
