#!/bin/bash
# Installation script for LLM Uncertainty Quantification Framework

set -e  # Exit on any error

echo "🚀 Installing LLM Uncertainty Quantification Framework..."

# Check Python version
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python 3.8+ is required. Found: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Check if MongoDB is installed
if ! command -v mongod &> /dev/null; then
    echo "⚠️ Warning: MongoDB is not installed."
    echo "Please install MongoDB:"
    echo "  Ubuntu/Debian: sudo apt install mongodb"
    echo "  macOS: brew install mongodb-community"
    echo "  Or use Docker: docker run -d -p 27017:27017 mongo"
else
    echo "✅ MongoDB found"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p cache logs data results

# Download required models (optional)
echo "🤖 Downloading required models..."
python3 -c "
try:
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    from sentence_transformers import SentenceTransformer
    
    print('Downloading NLI model...')
    AutoTokenizer.from_pretrained('microsoft/deberta-large-mnli')
    AutoModelForSequenceClassification.from_pretrained('microsoft/deberta-large-mnli')
    
    print('Downloading embedding models...')
    SentenceTransformer('intfloat/multilingual-e5-large-instruct')
    
    print('✅ Models downloaded successfully')
except Exception as e:
    print(f'⚠️ Warning: Could not download models: {e}')
    print('Models will be downloaded automatically when first used.')
"

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Activate the virtual environment: source .venv/bin/activate"
echo "2. Set your API keys:"
echo "   export DEEPSEEK_API_KEY='your_deepseek_key'"
echo "   export DASHSCOPE_API_KEY='your_qwen_key'"
echo "3. Start MongoDB: sudo systemctl start mongodb"
echo "4. Run a test: python generate_llm_responses.py --test"
echo ""
echo "For more information, see README.md"
