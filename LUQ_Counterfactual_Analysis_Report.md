# LUQ Counterfactual Analysis Report

## 📊 **执行概览**

**分析时间**: 2025-08-15  
**分析方法**: LUQ (Linguistic Uncertainty Quantification)  
**数据集**: Counterfactual QA  
**模型**: Qwen3-32B  

---

## 🎯 **核心结果**

### **整体统计**
- **分析组数**: 10个counterfactual问题
- **总响应数**: 301个响应 (平均每组30个)
- **总句子数**: 13,247个句子
- **总NLI计算**: 401,307次NLI推理
- **分析时间**: 35.9秒
- **成功率**: 100% (10/10)

### **不确定性分数分布**
```
平均不确定性: 0.3930 ± 0.0724
最低不确定性: 0.2216 (高一致性)
最高不确定性: 0.4662 (低一致性)
```

### **一致性分数分布**
```
平均一致性: 0.6070 ± 0.0724
最低一致性: 0.5338
最高一致性: 0.7784
```

---

## 📈 **详细分析结果**

### **按不确定性排序的Top 5问题**

| 排名 | 不确定性分数 | 一致性分数 | 响应数 | 句子数 | NLI计算数 |
|------|-------------|-----------|--------|--------|----------|
| 1    | 0.4662      | 0.5338    | 30     | 1,226  | 35,554   |
| 2    | 0.4615      | 0.5385    | 30     | 1,491  | 43,239   |
| 3    | 0.4335      | 0.5665    | 44     | 1,993  | 85,699   |
| 4    | 0.4280      | 0.5720    | 30     | 1,287  | 34,749   |
| 5    | 0.4270      | 0.5730    | 19     | 744    | 13,392   |

### **样本问题示例**
**问题**: "If the Confederacy had been allowed to peacefully secede from the United States in 1861, how would this have affected: a) the development of democracy globally, b) the economic power of both nations, c) the abolition of slavery, and d) America's role in the 20th century?"

**NLI计算示例**:
- 句子0与上下文0: entailment=0.947, luq_score=0.991 (高一致性)
- 句子0与上下文3: entailment=0.232, luq_score=0.589 (中等一致性)

---

## 🔍 **技术细节**

### **LUQ算法实现**
1. **句子分割**: 使用sentence-splitter将每个响应分割为句子
2. **NLI计算**: 使用microsoft/deberta-large-mnli模型
3. **一致性评分**: `entailment_prob / (entailment_prob + contradiction_prob)`
4. **不确定性转换**: `uncertainty = 1 - consistency`

### **数据存储优化**
- **MongoDB Collection**: `UQ_result_LUQ_counterfactual`
- **存储格式**: 索引化NLI详情，避免16MB限制
- **字段压缩**: 使用缩写字段名 (s=sentence_idx, c=context_idx, e=entailment, etc.)

### **NLI详情结构**
```json
{
  "s": 0,        // 句子索引
  "c": 1,        // 上下文索引  
  "e": 0.947,    // entailment分数
  "n": 0.045,    // neutral分数
  "d": 0.008,    // contradiction分数
  "l": 0.991     // LUQ一致性分数
}
```

---

## 📋 **关键发现**

### **1. 不确定性水平**
- **中等不确定性**: 平均0.39，表明counterfactual问题存在适度的观点分歧
- **合理范围**: 0.22-0.47，没有极端的高不确定性或低不确定性

### **2. 一致性模式**
- **中等一致性**: 平均60%的句子与其他响应一致
- **变异性**: 标准差0.07，显示不同问题间存在一致性差异

### **3. 计算效率**
- **高效处理**: 平均每组3.3秒，每秒处理约12,000次NLI计算
- **可扩展性**: 成功处理大规模数据集(40万+NLI计算)

### **4. 数据质量**
- **完整覆盖**: 所有10个问题组都成功分析
- **详细追踪**: 保存了所有NLI计算的索引信息

---

## 🎯 **结论与意义**

### **方法验证**
- ✅ **LUQ算法成功实现**: 完整遵循论文算法
- ✅ **大规模应用**: 处理了13,000+句子的复杂分析
- ✅ **技术优化**: 解决了MongoDB存储限制问题

### **结果解读**
- **Counterfactual问题的不确定性**: 中等水平(0.39)符合预期
- **模型行为**: Qwen3-32B在假设性问题上表现出合理的不确定性
- **一致性分析**: 60%的平均一致性表明模型输出有一定稳定性

### **应用价值**
- **不确定性量化**: 为counterfactual推理提供了量化的不确定性指标
- **模型评估**: 可用于评估LLM在假设性推理任务上的可靠性
- **方法扩展**: 技术框架可应用于其他长文本不确定性分析

---

## 📁 **输出文件**

1. **`luq_counterfactual_summary.csv`** - 汇总统计表
2. **`luq_counterfactual_detailed_sample.json`** - 详细结果样本
3. **`luq_nli_computation_sample.json`** - NLI计算示例
4. **`LUQ_NLI_Details_Schema.md`** - 数据结构说明
5. **MongoDB Collection**: `UQ_result_LUQ_counterfactual` - 完整结果

---

## 🚀 **后续工作建议**

1. **比较分析**: 与其他UQ方法(SE, NumSets等)进行对比
2. **阈值研究**: 探索不同一致性阈值对结果的影响
3. **类别分析**: 按counterfactual问题类型进行细分分析
4. **模型对比**: 在不同LLM上重复实验进行横向比较

---

*报告生成时间: 2025-08-15*  
*分析工具: LUQ Implementation with NLI Details*
