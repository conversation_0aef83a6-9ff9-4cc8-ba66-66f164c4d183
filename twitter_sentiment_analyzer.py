import csv
import yaml
import time
from pathlib import Path
import copy
from mongodb_client import MongoDBConfig, save_response_to_mongodb, create_indexes
from core.llm_client_sync import QwenClientSync, Qwen25ClientSync
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

class TwitterSentimentAnalyzer:
    def __init__(self, config_path="config/twitter_mongodb_config.yaml", model_name="default"):
        # 支持多个config文件合并
        if isinstance(config_path, str):
            config_paths = [p.strip() for p in config_path.split(",") if p.strip()]
        else:
            config_paths = list(config_path)
        merged_config = {}
        for path in config_paths:
            with open(path, 'r') as f:
                cfg = yaml.safe_load(f)
                if cfg:
                    self.deep_merge_dicts(merged_config, cfg)
        self.config = merged_config
        self.model_name = model_name
        self.mongo_config = MongoDBConfig(
            host=self.config['mongodb']['host'],
            port=self.config['mongodb']['port'],
            database=self.config['mongodb']['database'],
            collection=self.config['mongodb']['collection']
        )
        self.mongo_client = None
        self.collection = None
        # Token统计
        self.total_tokens_used = 0
        self.prompt_tokens_used = 0
        self.completion_tokens_used = 0
        self.total_requests = 0
        # MongoDB连接
        try:
            from pymongo import MongoClient
            self.mongo_client = MongoClient(self.mongo_config.get_connection_string())
            self.collection = self.mongo_client[self.mongo_config.database][self.mongo_config.collection]
            self.mongo_client.admin.command('ping')
            print("✅ MongoDB connection established successfully")
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            self.mongo_client = None
            self.collection = None
        # LLM client
        api_key = self.config.get('llm_api_key') or os.getenv('DASHSCOPE_API_KEY')
        if model_name == "qwen2.5":
            self.llm_client = Qwen25ClientSync(api_key=api_key)
        else:
            self.llm_client = QwenClientSync(api_key=api_key)
        self.queries_per_tweet = self.config['analysis']['queries_per_tweet']
        # prompt文件路径
        self.prompt_files = [
            "prompts/twitter_sentiment.txt",
            "prompts/twitter_sentiment_reason.txt",
            "prompts/twitter_sentiment_reason_first.txt"
        ]
        self.prompt_types = ["sentiment", "sentiment_reason", "sentiment_reason_first"]
        self.prompts = self.load_prompts()

    @staticmethod
    def deep_merge_dicts(a, b):
        for k, v in b.items():
            if k in a and isinstance(a[k], dict) and isinstance(v, dict):
                TwitterSentimentAnalyzer.deep_merge_dicts(a[k], v)
            else:
                a[k] = copy.deepcopy(v)
        return a

    def load_prompts(self):
        prompts = []
        for file in self.prompt_files:
            with open(file, 'r', encoding='utf-8') as f:
                prompts.append(f.read())
        return prompts

    def load_tweets_from_csv(self, csv_file, max_count=100):
        tweets = []
        csv_path = Path(csv_file)
        if not csv_path.exists():
            print(f"CSV file {csv_file} not found")
            return tweets
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i >= max_count:
                    break
                tweets.append({
                    'label': row['label'],
                    'text': row['text']
                })
        return tweets

    def analyze_tweets(self):
        if self.collection is None:
            print("❌ Cannot start analysis: MongoDB connection failed")
            return
        create_indexes()
        tweets = self.load_tweets_from_csv(self.config['analysis']['csv_file'], max_count=100)
        print(f"Loaded {len(tweets)} tweets")
        skipped_queries = 0
        new_queries = 0
        for idx, tweet in enumerate(tweets):
            print(f"Processing tweet {idx+1}/{len(tweets)}")
            for prompt_idx, (prompt_type, prompt_template) in enumerate(zip(self.prompt_types, self.prompts)):
                for repeat in range(1, self.queries_per_tweet + 1):
                    query_index = repeat
                    prompt_text = prompt_template.replace("{tweet}", tweet['text'])
                    # 检查是否已存在
                    existing = self.collection.find_one({
                        "tweet_index": idx,
                        "prompt_type": prompt_type,
                        "query_index": query_index
                    })
                    if existing:
                        skipped_queries += 1
                        continue
                    try:
                        prompt_tokens = self._estimate_tokens(prompt_text)
                        response = self.llm_client.generate_single_response(prompt_text)
                        completion_tokens = self._estimate_tokens(response)
                        total_tokens = prompt_tokens + completion_tokens
                        self.total_tokens_used += total_tokens
                        self.prompt_tokens_used += prompt_tokens
                        self.completion_tokens_used += completion_tokens
                        self.total_requests += 1
                        tweet_data_with_tokens = {
                            'tweet_index': idx,
                            'text': tweet['text'],
                            'validation': tweet['label'],
                            'token_usage': {
                                'prompt_tokens': prompt_tokens,
                                'completion_tokens': completion_tokens,
                                'total_tokens': total_tokens
                            }
                        }
                        # 保存到MongoDB
                        self.save_response(
                            tweet_data=tweet_data_with_tokens,
                            prompt_type=prompt_type,
                            prompt_text=prompt_text,
                            response_text=response,
                            query_index=query_index,
                            model_name=self.model_name
                        )
                        new_queries += 1
                        print(f"    Saved query {query_index} for tweet {idx+1} (tokens: {total_tokens})")
                    except Exception as e:
                        print(f"    Error on tweet {idx+1} prompt {prompt_type} query {query_index}: {e}")
                        continue
        print(f"Summary:")
        print(f"  Total queries expected: {len(tweets) * len(self.prompt_types) * self.queries_per_tweet}")
        print(f"  Skipped (existing): {skipped_queries}")
        print(f"  New queries processed: {new_queries}")
        print(f"  Token Statistics:")
        print(f"    Total requests: {self.total_requests}")
        print(f"    Total tokens: {self.total_tokens_used:,}")
        print(f"    Prompt tokens: {self.prompt_tokens_used:,}")
        print(f"    Completion tokens: {self.completion_tokens_used:,}")
        print(f"    Avg tokens/request: {self.total_tokens_used//max(1,self.total_requests)}")

    def _estimate_tokens(self, text: str) -> int:
        if not text:
            return 0
        return max(1, len(text) // 4)

    def save_response(self, tweet_data, prompt_type, prompt_text, response_text, query_index, model_name):
        # 自定义保存，包含validation字段
        document = {
            "tweet_index": tweet_data.get('tweet_index'),
            "text": tweet_data.get('text'),
            "validation": tweet_data.get('validation'),
            "prompt_type": prompt_type,
            "prompt_text": prompt_text,
            "response_text": response_text,
            "model_name": model_name,
            "query_index": query_index,
            "token_usage": tweet_data.get('token_usage'),
            "timestamp": time.time()
        }
        self.collection.insert_one(document)

def main():
    import argparse
    parser = argparse.ArgumentParser(description='Analyze twitter sentiment with LLM prompts')
    parser.add_argument('--model', default='default', help='Model name to use for analysis')
    parser.add_argument('--config', default='config/twitter_mongodb_config.yaml', help='Configuration file path(s), comma-separated for multiple')
    args = parser.parse_args()
    analyzer = TwitterSentimentAnalyzer(config_path=args.config, model_name=args.model)
    analyzer.analyze_tweets()

if __name__ == "__main__":
    main() 