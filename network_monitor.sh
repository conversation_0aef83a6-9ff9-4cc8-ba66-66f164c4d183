#!/bin/bash

# 网络监控和自动重连脚本
# 功能：
# 1. 定时监测网络连接是否正常
# 2. 如果不正常，自动重新连接wifi
# 3. 网络重连后自动重启natapp服务

# 配置参数
LOG_FILE="/var/log/network_monitor.log"
CHECK_INTERVAL=30  # 检查间隔（秒）
MAX_RETRIES=3      # 最大重试次数
NATAPP_DIR="/etc/natapp"  # natapp所在目录
NATAPP_TOKEN="67e46cb2ad41cf9f"  # natapp认证token
WIFI_CONNECTION="ZJUWLAN-Secure"  # WiFi连接名称

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 检查网络连接
check_network() {
    # 获取WiFi接口名称
    local wifi_interface=$(ip link show | grep -E "wlx|wlan|wifi" | awk -F: '{print $2}' | tr -d ' ' | head -1)
    
    if [ -z "$wifi_interface" ]; then
        log_message "未找到WiFi接口"
        return 1
    fi
    
    # 检查ping命令是否支持-I参数
    if ping -h 2>&1 | grep -q "\-I"; then
        # 支持-I参数，强制通过指定WiFi接口ping测试
        if ping -c 3 -W 1 -I ${wifi_interface} ******* > /dev/null 2>&1; then
            return 0  # 网络正常
        else
            return 1  # 网络异常
        fi
    else
        # 不支持-I参数，使用普通ping测试
        if ping -c 3 -W 1 ******* > /dev/null 2>&1; then
            return 0  # 网络正常
        else
            return 1  # 网络异常
        fi
    fi
}

# 重启WiFi
restart_wifi() {
    log_message "检测到网络异常，正在重启WiFi..."
    
    # 获取WiFi接口名称
    WIFI_INTERFACE=$(ip link show | grep -E "wlx|wlan|wifi" | awk -F: '{print $2}' | tr -d ' ' | head -1)
    
    if [ -z "$WIFI_INTERFACE" ]; then
        log_message "未找到WiFi接口"
        return 1
    fi
    
    log_message "找到WiFi接口: $WIFI_INTERFACE"
    
    # 方法1: 尝试使用NetworkManager重新连接
    log_message "尝试使用NetworkManager重新连接WiFi到 $WIFI_CONNECTION..."
    if command -v nmcli > /dev/null 2>&1; then
        # 先断开当前连接
        nmcli device disconnect "$WIFI_INTERFACE" 2>/dev/null
        sleep 3
        
        # 连接到指定的WiFi网络
        if nmcli connection up "$WIFI_CONNECTION" 2>/dev/null; then
            log_message "成功连接到 $WIFI_CONNECTION"
            sleep 8
            
            # 检查是否重连成功
            if check_network; then
                log_message "NetworkManager重连成功"
                return 0
            fi
        else
            log_message "无法连接到 $WIFI_CONNECTION，尝试使用设备连接..."
            nmcli device connect "$WIFI_INTERFACE" 2>/dev/null
            sleep 8
            
            # 检查是否重连成功
            if check_network; then
                log_message "NetworkManager重连成功"
                return 0
            fi
        fi
    fi
    
    # 方法2: 如果NetworkManager重连失败，重启WiFi接口
    log_message "NetworkManager重连失败，正在重启WiFi接口 $WIFI_INTERFACE..."
    
    # 使用ip命令重启网卡
    sudo ip link set ${WIFI_INTERFACE} down
    sleep 5  # 等待5秒确保网卡已关闭
    sudo ip link set ${WIFI_INTERFACE} up
    sleep 3  # 等待网卡启动
    
    log_message "WiFi接口 $WIFI_INTERFACE 重启完成"
    
    # 如果NetworkManager可用，尝试重新连接
    if command -v nmcli > /dev/null 2>&1; then
        log_message "尝试重新连接到 $WIFI_CONNECTION..."
        if nmcli connection up "$WIFI_CONNECTION" 2>/dev/null; then
            log_message "成功连接到 $WIFI_CONNECTION"
        else
            log_message "无法连接到 $WIFI_CONNECTION，尝试使用设备连接..."
            nmcli device connect "$WIFI_INTERFACE" 2>/dev/null
        fi
        sleep 5
    fi
    
    # 等待网络重新连接
    local retry_count=0
    while [ $retry_count -lt $MAX_RETRIES ]; do
        log_message "等待网络重连... (尝试 $((retry_count + 1))/$MAX_RETRIES)"
        sleep 10
        if check_network; then
            log_message "WiFi重连成功"
            return 0
        fi
        retry_count=$((retry_count + 1))
    done
    
    log_message "WiFi重连失败"
    return 1
}

# 检查natapp服务状态
check_natapp_status() {
    local natapp_pid=$(pgrep -f "natapp" | head -1)
    
    if [ -z "$natapp_pid" ]; then
        return 1  # 没有进程
    fi
    
    # 检查进程是否真的在运行
    if kill -0 "$natapp_pid" 2>/dev/null; then
        # 检查进程状态（不是停止状态）
        local process_state=$(ps -o state= -p "$natapp_pid" 2>/dev/null)
        if [ "$process_state" != "T" ]; then
            return 0  # 正常运行
        else
            return 2  # 进程停止状态
        fi
    else
        return 1  # 进程不存在
    fi
}

# 启动natapp服务
start_natapp() {
    log_message "正在启动natapp服务..."
    
    # 检查natapp进程是否已运行（包括停止状态的进程）
    local natapp_pids=$(pgrep -f "natapp")
    if [ -n "$natapp_pids" ]; then
        log_message "检测到natapp进程，正在停止所有相关进程..."
        # 停止所有natapp相关进程
        sudo pkill -f "natapp" 2>/dev/null
        sleep 3
        
        # 强制杀死可能残留的进程
        for pid in $natapp_pids; do
            if kill -0 "$pid" 2>/dev/null; then
                log_message "强制停止进程 $pid"
                sudo kill -9 "$pid" 2>/dev/null
            fi
        done
        sleep 2
    fi
    
    # 切换到natapp目录并启动服务
    cd "$NATAPP_DIR" || {
        log_message "错误：无法切换到目录 $NATAPP_DIR"
        return 1
    }
    
    log_message "切换到目录: $(pwd)"
    log_message "启动natapp服务，使用token: $NATAPP_TOKEN"
    
    # 启动natapp服务
    sudo nohup ./natapp -authtoken=$NATAPP_TOKEN -log=stdout > /var/log/natapp.log 2>&1 &
    local natapp_pid=$!
    
    # 等待服务启动
    sleep 5
    
    # 检查服务是否成功启动
    if pgrep -f "natapp" > /dev/null; then
        local running_pid=$(pgrep -f "natapp" | head -1)
        log_message "natapp服务启动成功，进程ID: $running_pid"
        
        # 检查进程状态
        if kill -0 "$running_pid" 2>/dev/null; then
            log_message "natapp进程运行正常"
            return 0
        else
            log_message "natapp进程状态异常"
            return 1
        fi
    else
        log_message "natapp服务启动失败"
        # 检查日志文件
        if [ -f "/var/log/natapp.log" ]; then
            log_message "natapp启动日志:"
            tail -10 /var/log/natapp.log | while read line; do
                log_message "  $line"
            done
        fi
        return 1
    fi
}

# 主监控循环
main_monitor() {
    log_message "网络监控服务启动"
    
    while true; do
        if check_network; then
            # 网络正常，检查natapp服务
            check_natapp_status
            local natapp_status=$?
            
            case $natapp_status in
                0)
                    # natapp正常运行，无需操作
                    ;;
                1)
                    log_message "natapp服务未运行，正在启动..."
                    start_natapp
                    ;;
                2)
                    log_message "natapp进程处于停止状态，正在重启..."
                    start_natapp
                    ;;
            esac
        else
            # 网络异常，尝试重连
            log_message "检测到网络异常"
            if restart_wifi; then
                # WiFi重连成功，启动natapp服务
                log_message "网络重连成功，正在启动natapp服务..."
                start_natapp
            else
                log_message "网络重连失败，等待下次检查..."
            fi
        fi
        
        # 等待下次检查
        sleep $CHECK_INTERVAL
    done
}

# 信号处理
cleanup() {
    log_message "收到退出信号，正在清理..."
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "错误：此脚本需要root权限运行，请使用 sudo 执行"
    echo "使用方法: sudo ./network_monitor.sh"
    exit 1
fi

# 检查必要文件
if [ ! -f "$NATAPP_DIR/natapp" ]; then
    echo "错误：在 $NATAPP_DIR 目录下未找到 natapp 文件"
    exit 1
fi

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# 启动主监控
main_monitor
