#!/bin/bash

# 网络监控服务安装脚本

set -e

echo "=== 网络监控服务安装脚本 ==="

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "错误：此脚本需要root权限运行，请使用 sudo 执行"
    echo "使用方法: sudo ./install_network_monitor.sh"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NETWORK_MONITOR_SCRIPT="$SCRIPT_DIR/network_monitor.sh"
SERVICE_FILE="$SCRIPT_DIR/network-monitor.service"

echo "脚本目录: $SCRIPT_DIR"

# 检查必要文件
if [ ! -f "$NETWORK_MONITOR_SCRIPT" ]; then
    echo "错误：未找到网络监控脚本 $NETWORK_MONITOR_SCRIPT"
    exit 1
fi

if [ ! -f "$SERVICE_FILE" ]; then
    echo "错误：未找到服务文件 $SERVICE_FILE"
    exit 1
fi

# 设置脚本权限
echo "设置脚本权限..."
chmod +x "$NETWORK_MONITOR_SCRIPT"

# 检查natapp文件是否存在
NATAPP_DIR="/etc/natapp"
if [ ! -f "$NATAPP_DIR/natapp" ]; then
    echo "警告：在 $NATAPP_DIR 目录下未找到 natapp 文件"
    echo "请确保 natapp 已正确安装在该目录下"
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 1
    fi
fi

# 复制服务文件到systemd目录
echo "安装systemd服务..."
cp "$SERVICE_FILE" /etc/systemd/system/

# 重新加载systemd配置
echo "重新加载systemd配置..."
systemctl daemon-reload

# 启用服务
echo "启用网络监控服务..."
systemctl enable network-monitor.service

# 启动服务
echo "启动网络监控服务..."
systemctl start network-monitor.service

# 检查服务状态
echo "检查服务状态..."
if systemctl is-active --quiet network-monitor.service; then
    echo "✓ 网络监控服务已成功启动"
else
    echo "✗ 网络监控服务启动失败"
    echo "查看服务状态:"
    systemctl status network-monitor.service
    exit 1
fi

# 显示服务信息
echo ""
echo "=== 安装完成 ==="
echo "服务名称: network-monitor.service"
echo "状态: $(systemctl is-active network-monitor.service)"
echo "开机自启: $(systemctl is-enabled network-monitor.service)"
echo ""
echo "常用命令:"
echo "  查看服务状态: sudo systemctl status network-monitor.service"
echo "  启动服务: sudo systemctl start network-monitor.service"
echo "  停止服务: sudo systemctl stop network-monitor.service"
echo "  重启服务: sudo systemctl restart network-monitor.service"
echo "  查看日志: sudo journalctl -u network-monitor.service -f"
echo "  查看脚本日志: sudo tail -f /var/log/network_monitor.log"
echo "  查看natapp日志: sudo tail -f /var/log/natapp.log"
echo ""
echo "服务将在系统启动时自动运行，监控网络连接并在需要时自动重连WiFi和重启natapp服务。"
