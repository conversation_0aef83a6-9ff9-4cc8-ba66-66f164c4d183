#!/bin/bash

# LUQSENTENCE Analysis Tmux Launcher
# 在tmux会话中启动LUQSENTENCE分析

set -e

# Configuration
SESSION_NAME="luqsentence_analysis"
LOG_DIR="logs"
CHECKPOINT_DIR="checkpoints"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create directories
mkdir -p "$LOG_DIR"
mkdir -p "$CHECKPOINT_DIR"

echo -e "${BLUE}🧠 LUQSENTENCE Counterfactual Analysis Launcher${NC}"
echo -e "${BLUE}=================================================${NC}"

# Check if session already exists
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Session '$SESSION_NAME' already exists${NC}"
    echo ""
    echo "Options:"
    echo "1. Attach to existing session: tmux attach -t $SESSION_NAME"
    echo "2. Kill existing session: tmux kill-session -t $SESSION_NAME"
    echo "3. Use different session name"
    echo ""
    read -p "Kill existing session and start new one? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Killing existing session...${NC}"
        tmux kill-session -t "$SESSION_NAME"
    else
        echo "Cancelled."
        exit 0
    fi
fi

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Python environment
if ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python not found${NC}"
    exit 1
fi

# Check MongoDB
if ! pgrep mongod > /dev/null; then
    echo -e "${YELLOW}⚠️  MongoDB not running. Starting MongoDB...${NC}"
    sudo systemctl start mongod || {
        echo -e "${RED}❌ Failed to start MongoDB${NC}"
        exit 1
    }
fi

# Check if counterfactual data exists
echo -e "${BLUE}📊 Checking counterfactual data...${NC}"
python3 -c "
from pymongo import MongoClient
try:
    client = MongoClient('localhost', 27017)
    db = client['LLM-UQ']
    count = db['response_collections'].count_documents({
        'task_name': 'counterfactual_qa', 
        'dataset_source': 'counterfactual_data'
    })
    print(f'Found {count} counterfactual responses')
    if count == 0:
        print('❌ No counterfactual data found!')
        exit(1)
    client.close()
except Exception as e:
    print(f'❌ MongoDB check failed: {e}')
    exit(1)
" || exit 1

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Ask for analysis mode
echo ""
echo -e "${BLUE}📋 Analysis Mode Selection:${NC}"
echo "1. Test mode (5 questions, ~5-10 minutes)"
echo "2. Full analysis (all data, ~2-4 hours)"
echo ""
read -p "Select mode (1/2): " -n 1 -r
echo

case $REPLY in
    1)
        MODE="test"
        ANALYSIS_CMD="python run_luqsentence_analysis.py --test"
        echo -e "${GREEN}Selected: Test mode${NC}"
        ;;
    2)
        MODE="full"
        ANALYSIS_CMD="python run_luqsentence_analysis.py"
        echo -e "${GREEN}Selected: Full analysis${NC}"
        ;;
    *)
        echo -e "${RED}Invalid selection${NC}"
        exit 1
        ;;
esac

# Create tmux session
echo ""
echo -e "${BLUE}🚀 Creating tmux session '$SESSION_NAME'...${NC}"

# Create new session in detached mode
tmux new-session -d -s "$SESSION_NAME"

# Set up the session
tmux send-keys -t "$SESSION_NAME" "cd $(pwd)" Enter
tmux send-keys -t "$SESSION_NAME" "echo '🧠 LUQSENTENCE Analysis Session Started'" Enter
tmux send-keys -t "$SESSION_NAME" "echo 'Session: $SESSION_NAME'" Enter
tmux send-keys -t "$SESSION_NAME" "echo 'Mode: $MODE'" Enter
tmux send-keys -t "$SESSION_NAME" "echo ''" Enter

# Show session info
echo -e "${GREEN}✅ Tmux session created successfully${NC}"
echo ""
echo -e "${BLUE}📋 Session Information:${NC}"
echo "   Session name: $SESSION_NAME"
echo "   Analysis mode: $MODE"
echo "   Log directory: $LOG_DIR"
echo "   Checkpoint directory: $CHECKPOINT_DIR"
echo ""

echo -e "${BLUE}🎮 Tmux Commands:${NC}"
echo "   Attach to session: ${GREEN}tmux attach -t $SESSION_NAME${NC}"
echo "   Detach from session: ${YELLOW}Ctrl+B, then D${NC}"
echo "   Kill session: ${RED}tmux kill-session -t $SESSION_NAME${NC}"
echo "   List sessions: tmux list-sessions"
echo ""

echo -e "${BLUE}🚀 Starting Analysis:${NC}"
echo "   Command: $ANALYSIS_CMD"
echo ""

# Ask for confirmation
if [ "$MODE" = "full" ]; then
    echo -e "${YELLOW}⚠️  Full analysis will take 2-4 hours and use significant computational resources.${NC}"
    read -p "Continue with full analysis? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Analysis cancelled. Session is still available for manual use."
        echo "Attach with: tmux attach -t $SESSION_NAME"
        exit 0
    fi
fi

# Start the analysis
echo -e "${GREEN}🎯 Launching LUQSENTENCE analysis...${NC}"
tmux send-keys -t "$SESSION_NAME" "$ANALYSIS_CMD" Enter

echo ""
echo -e "${GREEN}🎉 Analysis started successfully!${NC}"
echo ""
echo -e "${BLUE}📊 Monitoring Options:${NC}"
echo "1. Attach to session: ${GREEN}tmux attach -t $SESSION_NAME${NC}"
echo "2. View logs: ${GREEN}tail -f logs/luqsentence_analysis_*.log${NC}"
echo "3. Check progress in MongoDB:"
echo "   ${GREEN}python -c \"from pymongo import MongoClient; c=MongoClient(); print(f'Results: {c['LLM-UQ']['UQ_result_LUQSENTENCE_counterfactual${MODE:+_test}'].count_documents({})} records')\"${NC}"
echo ""
echo -e "${BLUE}💡 Tips:${NC}"
echo "   - The session will continue running even if you disconnect"
echo "   - Use 'Ctrl+B, then D' to detach without stopping the analysis"
echo "   - Analysis progress is automatically saved and can be resumed"
echo ""

if [ "$MODE" = "full" ]; then
    echo -e "${YELLOW}⏱️  Estimated completion time: 2-4 hours${NC}"
else
    echo -e "${YELLOW}⏱️  Estimated completion time: 5-10 minutes${NC}"
fi

echo ""
echo -e "${GREEN}Happy analyzing! 🧠✨${NC}"
