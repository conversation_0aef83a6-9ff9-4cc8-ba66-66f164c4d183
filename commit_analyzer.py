import json
import csv
import yaml
import time
from pathlib import Path
import copy
from mongodb_client import MongoDBConfig, save_response_to_mongodb, create_indexes
from core.llm_client_sync import QwenClientSync, Qwen25ClientSync
from prompts.manager import PromptManager
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import os

def deep_merge_dicts(a, b):
    """Recursively merge dict b into dict a (a is modified in-place)."""
    for k, v in b.items():
        if (
            k in a and isinstance(a[k], dict) and isinstance(v, dict)
        ):
            deep_merge_dicts(a[k], v)
        else:
            a[k] = copy.deepcopy(v)
    return a

class CommitAnalyzer:
    def __init__(self, config_path="config/mongodb_config.yaml", model_name="default"):
        # Support multiple config files separated by comma
        if isinstance(config_path, str):
            config_paths = [p.strip() for p in config_path.split(",") if p.strip()]
        else:
            config_paths = list(config_path)
        merged_config = {}
        for path in config_paths:
            with open(path, 'r') as f:
                cfg = yaml.safe_load(f)
                if cfg:
                    deep_merge_dicts(merged_config, cfg)
        self.config = merged_config
        self.model_name = model_name
        self.mongo_config = MongoDBConfig(
            host=self.config['mongodb']['host'],
            port=self.config['mongodb']['port'],
            database=self.config['mongodb']['database'],
            collection=self.config['mongodb']['collection']
        )
        self.mongo_client = None
        self.collection = None
        
        # Token statistics
        self.total_tokens_used = 0
        self.prompt_tokens_used = 0
        self.completion_tokens_used = 0
        self.total_requests = 0
        
        # Initialize MongoDB connection
        try:
            from pymongo import MongoClient
            self.mongo_client = MongoClient(self.mongo_config.get_connection_string())
            self.collection = self.mongo_client[self.mongo_config.database][self.mongo_config.collection]
            
            # Test connection
            self.mongo_client.admin.command('ping')
            print("✅ MongoDB connection established successfully")
            
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            self.mongo_client = None
            self.collection = None
        
        # Prefer config value, fallback to environment variable DASHSCOPE_API_KEY
        api_key = self.config.get('llm_api_key') or os.getenv('DASHSCOPE_API_KEY')
        if model_name == "qwen2.5":
            self.llm_client = Qwen25ClientSync(api_key=api_key)
        else:
            self.llm_client = QwenClientSync(api_key=api_key)
        self.queries_per_commit = self.config['analysis']['queries_per_commit']
        self.prompt_manager = PromptManager()
    
    def load_commits_from_csv(self, csv_file):
        """Load commits from CSV file."""
        commits = []
        csv_path = Path(csv_file)
        
        if not csv_path.exists():
            print(f"CSV file {csv_file} not found")
            return commits
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                commits.append({
                    'sha': row['sha'],
                    'author': row['author'],
                    'date': row['date'],
                    'message': row['message'],
                    'repo_name': 'pytorch' if 'pytorch' in csv_file else 'tensorflow'
                })
        return commits
    
    def get_prompt_templates(self):
        """Get all prompt templates from PromptManager."""
        return {
            'single_word': "single_word",
            'module_reasoning': "module_reasoning", 
            'reasoning_module': "reasoning_module"
        }
    
    def get_existing_queries(self, commit_sha, prompt_type):
        """Check MongoDB for existing queries for this commit and prompt type."""
        if self.collection is None:
            return set()
            
        client = None
        try:
            from pymongo import MongoClient
            client = MongoClient(self.mongo_config.get_connection_string())
            collection = client[self.mongo_config.database][self.mongo_config.collection]
            existing = collection.find({
                "commit_sha": commit_sha,
                "prompt_type": prompt_type
            })
            return {doc['query_index'] for doc in existing}
        finally:
            if client:
                client.close()
    
    def analyze_commits(self):
        """Analyze commits with MongoDB-based progress tracking and resume."""
        if self.collection is None:
            print("❌ Cannot start analysis: MongoDB connection failed")
            return
            
        create_indexes()
        prompt_templates = self.get_prompt_templates()
        
        for repo_key, repo_config in self.config['repositories'].items():
            print(f"Processing {repo_key} commits with model {self.model_name}...")
            commits = self.load_commits_from_csv(repo_config['csv_file'])
            print(f"Loaded {len(commits)} commits for {repo_key}")
            
            skipped_queries = 0
            new_queries = 0
            
            # Process commits in batches of 5
            batch_size = 5
            for batch_start in range(0, len(commits), batch_size):
                batch_commits = commits[batch_start:batch_start + batch_size]
                print(f"Processing batch {batch_start//batch_size + 1}/{(len(commits) + batch_size - 1)//batch_size}")
                
                for commit in batch_commits:
                    for prompt_key, template_name in prompt_templates.items():
                        try:
                            # Check existing queries in MongoDB
                            existing_indices = self.get_existing_queries(commit['sha'], prompt_key)
                            total_expected = 30  # 30 queries per prompt type
                            completed_count = len(existing_indices)
                            remaining_count = total_expected - completed_count
                            
                            print(f"Commit {commit['sha'][:8]}_{prompt_key}: {completed_count}/30 completed, {remaining_count} remaining")
                            
                            if completed_count >= total_expected:
                                print(f"✅ Skipping complete {commit['sha'][:8]}_{prompt_key}")
                                skipped_queries += total_expected
                                continue
                            
                            prompt_variations = self.prompt_manager.create_variations(template_name, commit)
                            if len(prompt_variations) * self.queries_per_commit != total_expected:
                                print(f"Warning: Expected {total_expected} queries, got {len(prompt_variations) * self.queries_per_commit}")
                            
                            # Process only missing queries
                            with ThreadPoolExecutor(max_workers=min(10, remaining_count)) as executor:
                                future_to_query = {}
                                
                                for variation_idx, prompt_text in enumerate(prompt_variations, 1):
                                    query_index = (variation_idx - 1) * self.queries_per_commit + 1
                                    
                                    for repeat_query in range(1, self.queries_per_commit + 1):
                                        actual_query_index = query_index + repeat_query - 1
                                        
                                        if actual_query_index in existing_indices:
                                            skipped_queries += 1
                                            continue
                                        
                                        future = executor.submit(
                                            self._process_single_query,
                                            commit, prompt_key, prompt_text, actual_query_index
                                        )
                                        future_to_query[future] = (commit['sha'][:8], prompt_key, actual_query_index)
                                        new_queries += 1
                                
                                # Collect results
                                for future in as_completed(future_to_query):
                                    sha, prompt_key, query_index = future_to_query[future]
                                    try:
                                        future.result()
                                        print(f"    Query {query_index} completed for commit {sha}")
                                    except Exception as e:
                                        print(f"    Error on query {query_index} for commit {sha}: {e}")
                        except ValueError as e:
                            print(f"    Error generating prompts: {e}")
                            continue
            
            print(f"Summary for {repo_key}:")
            print(f"  Total queries expected: {len(commits) * 3 * 30}")
            print(f"  Skipped (existing): {skipped_queries}")
            print(f"  New queries processed: {new_queries}")
            
            # 显示token统计信息
            print(f"  Token Statistics:")
            print(f"    Total requests: {self.total_requests}")
            print(f"    Total tokens: {self.total_tokens_used:,}")
            print(f"    Prompt tokens: {self.prompt_tokens_used:,}")
            print(f"    Completion tokens: {self.completion_tokens_used:,}")
            print(f"    Avg tokens/request: {self.total_tokens_used//max(1,self.total_requests)}")
    
    def _estimate_tokens(self, text: str) -> int:
        """估算文本的token数量（简化版，按4字符≈1token）"""
        if not text:
            return 0
        # 简化的token估算：每个token约4个字符
        return max(1, len(text) // 4)

    def _process_single_query(self, commit, prompt_key, prompt_text, query_index):
        """Process a single query with MongoDB skip check and token tracking."""
        try:
            # Check if MongoDB connection is available
            if self.collection is None:
                print(f"❌ MongoDB not available, cannot save commit {commit['sha'][:8]} query {query_index}")
                return
                
            # Double-check in case of concurrent processing
            existing = self.collection.find_one({
                "commit_sha": commit['sha'],
                "prompt_type": prompt_key,
                "query_index": query_index
            })
            if existing:
                print(f"    Skipped duplicate query {query_index} for commit {commit['sha'][:8]} (concurrent check)")
                return
            
            # 估算prompt token数
            prompt_tokens = self._estimate_tokens(prompt_text)
            
            if self.model_name == "qwen2.5-32b-instruct":
                # Qwen2.5: always non-streaming
                response = self.llm_client.generate_single_response(prompt_text)
            else:
                response = self.llm_client.generate_single_response(prompt_text)
            
            # 估算completion token数
            completion_tokens = self._estimate_tokens(response)
            total_tokens = prompt_tokens + completion_tokens
            
            # 更新统计信息
            self.total_tokens_used += total_tokens
            self.prompt_tokens_used += prompt_tokens
            self.completion_tokens_used += completion_tokens
            self.total_requests += 1
            
            # 将token信息保存到MongoDB
            commit_data_with_tokens = {**commit}
            commit_data_with_tokens['token_usage'] = {
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens,
                'total_tokens': total_tokens
            }
            
            save_response_to_mongodb(
                commit_data=commit_data_with_tokens,
                prompt_type=prompt_key,
                prompt_text=prompt_text,
                response_text=response,
                query_index=query_index,
                model_name=self.model_name,
                config=self.mongo_config
            )
            print(f"    Saved query {query_index} for commit {commit['sha'][:8]} (tokens: {total_tokens})")
            
        except Exception as e:
            print(f"Error processing commit {commit['sha'][:8]} query {query_index}: {e}")
            raise e

def main():
    import argparse
    parser = argparse.ArgumentParser(description='Analyze commits with LLM prompts')
    parser.add_argument('--model', default='default', help='Model name to use for analysis')
    parser.add_argument('--config', default='config/mongodb_config.yaml', help='Configuration file path(s), comma-separated for multiple')
    args = parser.parse_args()
    analyzer = CommitAnalyzer(config_path=args.config, model_name=args.model)
    analyzer.analyze_commits()

if __name__ == "__main__":
    main()