# LofreeCP Implementation

## Overview

This implementation provides the **LofreeCP (Logit-free Conformal Prediction)** uncertainty quantification method for Large Language Models without requiring access to model logits. The method is based on the paper "API Is Enough: Conformal Prediction for Large Language Models Without Logit-Access" (https://arxiv.org/abs/2403.01216).

## Key Features

LofreeCP addresses the challenge of uncertainty quantification in black-box LLMs by combining three uncertainty measures:

1. **Frequency (F)**: Coarse-grained uncertainty based on response frequency
2. **Normalized Entropy (NE)**: Fine-grained prompt-wise uncertainty measuring response diversity
3. **Semantic Similarity (SS)**: Fine-grained response-wise uncertainty measuring similarity to the most frequent response

## Algorithm

The nonconformity score is computed as:
```
N = -F + λ1 * NE - λ2 * SS
```

Where:
- **F**: Frequency of the response divided by total samples
- **NE**: Normalized entropy of the response distribution  
- **SS**: Semantic similarity to the most frequent response
- **λ1, λ2**: Hyperparameters controlling the balance between components

## Implementation Details

### Core Components

1. **Response Preprocessing**: 
   - Removes punctuation, articles (a, an, the)
   - Normalizes whitespace and converts to lowercase
   - Follows the reference implementation style

2. **Frequency Computation**:
   - Converts responses to frequency dictionary
   - Sorts by frequency (descending order)

3. **Normalized Entropy**:
   - Computes entropy of response distribution
   - Normalizes by log of total frequency

4. **Semantic Similarity**:
   - Uses embedding-based cosine similarity when available
   - Falls back to word overlap similarity (Jaccard index)

### Usage Example

```python
from uq_methods.implementations.LofreeCP import LofreeCPUQ

# Initialize with custom parameters
lofreecp = LofreeCPUQ(lambda1=1.0, lambda2=1.0, verbose=True)

# Compute uncertainty for responses
responses = [
    "The answer is Paris",
    "Paris", 
    "Paris, France",
    "London",
    "I think it's Paris"
]

result = lofreecp.compute_uncertainty(responses)
print(f"Uncertainty Score: {result['uncertainty_score']:.3f}")
```

### Output Format

The method returns a comprehensive dictionary containing:

```python
{
    "uncertainty_score": float,           # Main uncertainty score (0-1)
    "method": "LofreeCP",
    "num_responses": int,                 # Total number of responses
    "unique_responses": int,              # Number of unique responses
    "response_diversity": float,          # Ratio of unique to total responses
    "normalized_entropy": float,          # Entropy of response distribution
    "most_frequent_response": str,        # Most common response
    "most_frequent_score": float,         # LofreeCP score of most frequent response
    "frequency_distribution": dict,       # Response frequency mapping
    "lofreecp_scores": dict,             # LofreeCP scores for all responses
    "lambda1": float,                    # Entropy weight parameter
    "lambda2": float,                    # Similarity weight parameter
    "original_responses": list,          # Original input responses
    "metadata": dict                     # Additional method information
}
```

## Parameter Tuning

### λ1 (Entropy Weight)
- Controls the contribution of normalized entropy
- Higher values increase sensitivity to response diversity
- Typical range: 0.0 - 2.0

### λ2 (Similarity Weight)  
- Controls the contribution of semantic similarity
- Higher values reduce uncertainty for semantically similar responses
- Typical range: 0.0 - 2.0

### Recommended Settings
- **Frequency Only**: λ1=0.0, λ2=0.0 (baseline)
- **Balanced**: λ1=1.0, λ2=1.0 (default)
- **High Sensitivity**: λ1=2.0, λ2=2.0

## Performance Characteristics

### Strengths
- ✅ Works without logit access (black-box LLMs)
- ✅ Combines multiple uncertainty signals
- ✅ Handles response preprocessing robustly
- ✅ Provides detailed uncertainty breakdown
- ✅ Configurable via hyperparameters

### Limitations
- ⚠️ Requires multiple response samples (recommended: 10+)
- ⚠️ Semantic similarity depends on embedding quality
- ⚠️ Computational cost scales with number of responses
- ⚠️ May need parameter tuning for specific domains

## Integration

The implementation follows the project's `BaseUQMethod` interface:

```python
class LofreeCPUQ(BaseUQMethod):
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]
    def get_required_samples(self) -> int  # Returns 10
    def get_method_name(self) -> str       # Returns "LofreeCP"
```

## Testing

Run the test suite to verify the implementation:

```bash
python test_lofreecp_implementation.py
python lofreecp_example.py
```

## References

1. Su, J., Luo, J., Wang, H., & Cheng, L. (2024). API Is Enough: Conformal Prediction for Large Language Models Without Logit-Access. arXiv preprint arXiv:2403.01216.

2. Original reference implementation concepts adapted from the provided code sample.

## Future Improvements

- [ ] Optimize semantic similarity computation
- [ ] Add support for different embedding models
- [ ] Implement adaptive parameter selection
- [ ] Add confidence intervals for uncertainty estimates
- [ ] Support for streaming/online uncertainty computation
