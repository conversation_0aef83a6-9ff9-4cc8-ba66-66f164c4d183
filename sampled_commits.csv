sha,message,date,author
70fb673e51decdd8bf4e55244d910a8e5680d12f,"Use software approach to catch overflow ( `c10/utils/safe_numerics.h` ) on ARM devices (#89042)

Fixes #89040

Pull Request resolved: https://github.com/pytorch/pytorch/pull/89042
Approved by: https://github.com/malfet",2022-11-17T05:55:25Z,Rachel030219
cd4c32691d45c86b030e97f99d03fe4f693918e1,"Add complex32, complex64 and complex128 dtypes (#11173)

Summary:
We don't generate a corresponding Type implementations for them,
so this doesn't do anything at the moment.

We don't plan on supporting complex32 in the near future, but
it is added to reserve the name and number in case we do at
some point in the future.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/11173

Reviewed By: SsnL

Differential Revision: D9627477

Pulled By: ezyang

fbshipit-source-id: f49a44ab1c92d8a33130c249ac7b234f210a65e6",2018-09-04T02:00:47Z,<PERSON>
fea7a79e0bc622628fdcecbd9121af05d6d42249,"[special] Add ndtr (#58126)

Summary:
Reference: https://github.com/pytorch/pytorch/issues/50345

Plot:
![image](https://user-images.githubusercontent.com/19503980/117942099-54efd680-b328-11eb-8948-c3080779ce19.png)
https://colab.research.google.com/drive/1Of67A042rOImj8wrLF_fUTgoy_wVEOZS?usp=sharing

TODO:
* [x] Add docs (https://13385714-65600975-gh.circle-artifacts.com/0/docs/special.html#torch.special.ndtr)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/58126

Reviewed By: anjali411

Differential Revision: D28700957

Pulled By: mruberry

fbshipit-source-id: 5b9991e97ec1e8fd01518cc9d9849108d35fe406",2021-05-31T04:08:41Z,kshitij12345
a5fbd3ef8ae5e96d7ff8584069f0f137a0cb42fb,"[vulkan][build_fix] Fix Vulkan Build; Prepacking uses new register api (#39771)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/39771

Vulkan build was not integrated with CI, it fails without this change.
There were 2 separate problems
1.  Recently added aten/src/ATen/templates/Functions.cpp missed VulkanType in header

2. Applying the new registration api, similar to xnnpack change
https://github.com/pytorch/pytorch/pull/36800

Test Plan:
`ANDROID_ABI=x86 ./scripts/build_android.sh -DUSE_VULKAN=ON` builds ok

CI integration for it is in the next PR in this stack ( https://github.com/pytorch/pytorch/pull/39767 )
job `ci/circleci: pytorch_linux_xenial_py3_clang5_android_ndk_r19c_x86_32_vulkan_build`

Differential Revision: D21975992

Pulled By: IvanKobzarev

fbshipit-source-id: b0400a9cb0ae90d7763ebeb5b8f7ee932a2148e1",2020-06-10T20:46:12Z,Ivan Kobzarev
fddfb81dd062b0770271af1537934a3062b000fa,"Add BF16 type to _autocast_to_full_precision (#67707)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/67707

https://github.com/pytorch/pytorch/pull/63939/files has added FP16 support to torchscript.

This is to add BF16 device type when doing full conversion.

Test Plan: Unit test. Also tested BF16 locally on A100 using MLP model.

Reviewed By: idning

Differential Revision: D32027152

fbshipit-source-id: b2a5ff2b22ea1e02306b0399f2b39b8493be4f45",2021-11-03T21:05:23Z,Yusuo Hu
d1f0823d2333a822e8912add1ce4ae2d551c8932,"fix clang-tidy failing all the time on random lines (#25078)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/25078

Our script is set up to only run on lines generated by diffing your branch against the base branch.

But we were using `$TRAVIS_BRANCH` to refer to the target branch, which was causing the script to diff against master, generating many spurious lines of diff output to be clang-tidy'd

Test Plan: Imported from OSS

Differential Revision: D16993054

Pulled By: suo

fbshipit-source-id: 7bffa890f6a1a2d5566ef01b9798c4eb86d8169f",2019-08-23T19:47:42Z,Michael Suo
ec611aca88939f2ddfd735e5a089e4b3d438035e,"[Pytorch Mobile] Expose _export_operator_list to python (#51312)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/51312

Follow up to D24690094 (https://github.com/pytorch/pytorch/commit/4a870f6518d3028293f48570ab57a8a4ad5c90e6) exposing the api in python. Created matching unit test.
ghstack-source-id: 120611452

Test Plan: Ran unit test

Reviewed By: dhruvbird

Differential Revision: D26112765

fbshipit-source-id: ffe3bb97de0a4f08b31719b4b47dcebd7d2fd42a",2021-02-01T20:04:12Z,Jacob Szwejbka
e5235fb62cc0708e364054903eb3f4ab59866db9,"Convert GuardOnDataDependentSymNode into graph break (#93373)

Extracted from https://github.com/pytorch/pytorch/pull/93150 because
I need it earlier in trunk.

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/93373
Approved by: https://github.com/Skylion007",2023-01-31T16:03:33Z,Edward Z. Yang
a6aa336cc2e75ae8d3dc29084d6d3e256501ae9e,"[quant][graph] Fix bug in replaceConvolutionWithConv2d (#37635)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37635

replaceConvolutionWithConv2d incorrectly assumes that the size of padding is 2. For Conv1d it is 1, in which case we cannot replace with aten::conv2d

Test Plan: Imported from OSS

Differential Revision: D21354930

fbshipit-source-id: a2dbad856666b4bbb2d9015ade8e1704774f20dd",2020-05-04T19:33:20Z,Supriya Rao
bbb6e36495f7133a328cf19ab821b07210956073,"[FSDP2] Fixed `set_requires_gradient_sync`'s `recurse` arg (#124318)

The `recurse` argument was not being respected for `set_requires_gradient_sync`. This PR fixes that.

The previous unit test did not have nested FSDP modules with managed parameters, so the `recurse=False` was not being exercised. We augment the unit test to try only disabling gradient sync for the root module and not children.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/124318
Approved by: https://github.com/weifengpy
ghstack dependencies: #120952, #124293",2024-04-17T20:44:47Z,Andrew Gu
8dee7b7a16ab76c2a82b4032879ab35790e04a68,"Add TORCHDYNAMO_EXTENDED_DEBUG_GUARD_ADDED (#118750)

This allows us to request extended (including C++ backtrace) information
whenever a specific guard occurs.

Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/118750
Approved by: https://github.com/aakhundov",2024-01-31T18:24:47Z,Edward Z. Yang
62963125f558e27ecfd1838d8acfafcb6a397bf0,"[functorch] Fix parameter declarator cannot be qualified error (pytorch/functorch#361)

Before this change `include_guard` declaration can be interpreted as both local variable declaration and function prototype
Disambiguate it by using curly bracket",2021-12-21T18:20:25Z,Nikita Shulga
9e73e89029d950f3cf9ad4ee7c98ad1fb12ea502,"[quant][core][gpu][improvement] Converted reinterpret_cast<T *>(some_int8_tensor.data_ptr()) calls to some_int8_tensor.data_ptr<int8_t> in quantized cudnn operator files (#75980)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/75980

Support for data_ptr<T> for quantized tensor was enabled in
https://github.com/pytorch/pytorch/pull/75643. Rather than using
reinterpret_cast, we can use this overload directly. The change is
currently made in /aten/src/ATen/native/quantized/cudnn.

(Note: this ignores all push blocking failures!)

Test Plan:
```
python test/test_quantization.py -k test_qlinear_cudnn
python test/test_quantization.py -k test_qconv2d_cudnn
python test/test_quantization.py -k test_qadd_relu_cudnn
```

Reviewed By: jerryzh168

Differential Revision: D35720654

Pulled By: dzdang

fbshipit-source-id: 5ba4b99f6cfaf1b482a0a3f5208c94e53cb05eba
(cherry picked from commit 92e2480fa0862261bff42761d5eab8ee0bb3b075)",2022-04-28T03:22:35Z,dzdang
6dea9927a88aee8ee4db6fd42fb14156c9acc3e4,"Don't use thrust::log(complex) in CUDA as it takes a FOREVER to compile (#107559)

As per title
Pull Request resolved: https://github.com/pytorch/pytorch/pull/107559
Approved by: https://github.com/peterbell10",2023-08-21T00:50:04Z,lezcano
973371139475834c44d118ba0256d97b070980a0,"[JIT] Support calling Tensor.element_size() in TorchScript (#33808)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/33808

# Problem

https://github.com/pytorch/pytorch/issues/33620
ghstack-source-id: 99073701

Test Plan:
```
buck test mode/dev-nosan //caffe2/test:jit -- test_numel

buck test mode/dev-nosan //caffe2/test:jit -- test_element_size

buck build mode/dev-nosan //caffe2/test:jit \
&& buck-out/gen/caffe2/test/jit\#binary.par -r test_numel

buck build mode/dev-nosan //caffe2/test:jit \
&& buck-out/gen/caffe2/test/jit\#binary.par -r test_element_size
```

Compile error

P126667043

Generated code,
```
buck-out/dev/gen/caffe2/generate-code=register_aten_ops_0.cpp/register_aten_ops_0.cpp

buck-out/dev/gen/caffe2/generate-code=register_aten_ops_2.cpp/register_aten_ops_2.cpp
```
P126667064

Differential Revision: D7050644

fbshipit-source-id: 20dbdb9c500b6d7683c23e3049d43ed0ca06d831",2020-02-27T06:22:52Z,Shihao Xu
dc17fb68e4482b1afdf68a4ecf8a6968159d7ad9,Fix minor bug in parallel_apply (#2193),2017-07-24T22:15:00Z,Adam Paszke
61c96811be8051e39b1d5663b2c3538cafb93fd0,"[c10d] NCCL python binding and CI test, with bug fixes (#8357)

* [c10d] NCCL python binding and CI test, with bug fixes

* Addressed comments and further bug fix

* Made NCCL build optional, made C10D libc10d.a only

* Fixed tests so that NCCL pg won't run when not neeeded

* Addressed comments",2018-06-19T20:02:39Z,Teng Li
9cb4bce847af688ca344caaa876d3c47a36f28bf,"Open-source Caffe2 Int8 ops (#13065)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/13065

- Open-source Caffe2 Int8 (quantized) operators

Reviewed By: Yangqing

Differential Revision: D10524381

fbshipit-source-id: 6daa153dc247572900c91e37262d033c368b382d",2018-10-25T19:38:35Z,Marat Dukhan
4b00bce156869ba71475aa3fff80eec553e9da67,"[Gradient Compression] Introduce fp16_compress_wrapper in ddp_comm_hooks.rst (#54052)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54052

Introduce `fp16_compress_wrapper`, which can give some speedup on top of some gradient compression algorithms like PowerSGD.

ghstack-source-id: 124001805

Test Plan: {F509205173}

Reviewed By: iseessel

Differential Revision: D27076064

fbshipit-source-id: 4845a14854cafe2112c0caefc1e2532efe9d3ed8",2021-03-16T22:38:17Z,Yi Wang
f96bd52841275f8d94a29f3e80c1b4153d55a331,"aot autograd: dont allow symint outputs to get tangents in the bw graph (#96219)

Previously, if dynamic shapes were turned on and we had a forward graph that returns a symint, then we would generate a backward graph that takes in a tangent input for that symint fwd output. This causes problems for downstream - inductor will see an input that it expects to be a symint, but it gets a `None` from autograd.

Confirmed that this repro now passes:
```
benchmarks/dynamo/torchbench.py --devices cuda --inductor --dynamic-shapes --unspecialize-int --accuracy --training --only drq
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96219
Approved by: https://github.com/ezyang",2023-03-07T22:58:22Z,Brian Hirsh
13153924cc77b75f009279e26c6c2e92112282e7,"OpInfo porting for msort operator (#55488)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/55488

Reviewed By: ngimel

Differential Revision: D27708648

Pulled By: iramazanli

fbshipit-source-id: 62b6bc5bd6e54c593b9afac56cb2511411683416",2021-04-12T16:17:40Z,Ilqar Ramazanli
bc9dd969e15afe47b2e0100d5666a77001d71902,"Support inlining no_grad() decorator (#98121)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/98121
Approved by: https://github.com/anijain2305, https://github.com/voznesenskym",2023-04-02T20:36:38Z,Jason Ansel
c757647dd8c198fbcca4d4e6c7cba6f1d12b97e6,"[Better Transformer] make is_causal a hint and force attn_mask to be set on `is_causal=True` in F.MHA (#97214)

Summary:
This fixes an issue raised in [is_causal parameter in torch.nn.TransformerEncoderLayer.forward does not work #96941](https://github.com/pytorch/pytorch/issues/96941) where results computed with is_causal do not properly reflect causal masking.

In PyTorch 2.0, Accelerated PT Transformers added the is_causal parameter to legacy nn.Transformer* and nn.MHA APIs aligned with and intended to engage the is_causal parameter of the new scaled_dot_product_attention (SDPA) operator.

At present is_causal works differently for Transformer* modules, the nn.MHA and F.MHA:
* The nn.Transformer* modules treat is_causal as an optional indicator about the format of attn_mask. This is because some layers (such as the CLIP layer use the attention mask in the layer, and thus the attn_mask was a required feature.)
* Initially, nn.MHA and F.MHA were defined to align with F.SDPA in behavior: a user may specify either the attention mask, or is_causal, but not both.  It seemed to make sense at the time to align SDPA and MHA, esp since there was a larger overlap of parameters which have since changed, e.g., with the removal of need_weights from SDPA. (See below for why this makes sense.)

Unfortunately, this does not work because of how MHA was changed to handle the need_weights parameter.  When need_weights is present, we do not (any more) call SDPA because support for need_weights was removed from SDPA before the release.  The rationale is that need_weights defeats all optimization at the foundation of SDPA performance.  Having the flag might thus mislead users into thinking they get good performance and have them disappointed when they enable a legacy feature of MHA which massively degrades performance.  (They might not think anything of enabling that, because it is on by default in MHA today, which leads to more  issues.)

Since SDPA does not (no longer) support need_weights, we need to pick a separate path which implements attention using a set of discrete operations that allocates a tensor for weights.  Alas, this code path does not have support for is_causal, because attention is implemented as matmul and using the attention mask.  Thus, is_causal has no impact.  (A substantially similar situation arises with how kpm is implemented today because Nested Tensors are not supported by torch.compile() in 2.0)

This problem was masked because all uses of legacy nn.MHA (and F.MHA) come through nn.Transformer* which called self-attention (i.e., nn.MHA) only ever with the attention mask attn_mask, and never with is_causal, a missed optimization opportunit that would have been addressed in a future performance update.

Regrettably, always calling nn.MHA with attn_mask prevented diagnosing of the issue of not having a suitable attention mask when need_weights support was dropped from SDPA and a discrete implementation of attention was added for that scenario, and for the execution path with key_padding_mask.

We have two options to address this issue:

Solution 1: Whenever nn.MHA and F.MHA are executed with is_causal set, we internally create a causal mask at significant expense of allocating a tensor and filling it with a triangular causal matrix.  This increases memory usage, and runtime, for allocating a causal mask.  To add insult to injury, in all current (and likely future) execution scenarios, MHA is called by a model using the nn.Transformer API which already has that matrix and passes it from nn.module to nn.module.  Then the passing in of attn_mask has to be suppressed by nn.TransformerEncoderLayer, only for nn.MHA to immediately allocate the very same tensor again to satisfy the requirement to have an attention mask for the computation. (We expect new use cases to use SDPA directly.)

Solution 2: We align the behavior of nn.MHA and F.MHA with the rest of the existing nn.Transformer API, and require the attention mask to be passed into nn.MHA in addition to is_causal as an optional indicator about the nature of the attention mask rather than as an alternative to attn_mask.  Then, when we choose the code path for processing MHA with need_weights or a key_padding_mask, we have the attn_mask passed down through the nn.Transformer* hierarchy, without the added overhead of allocating an attention mask as in scenario 1.

This PR implements solution 2 which offers better performance and in retrospect aligns MHA better with the rest of the Transformer modules as the definition of SDPA evolved into a more streamlined high-performance operator.  It ostensibly changes how is_causal works, by requiring the attention mask to be specified.  However, as described here, and as shown in the submitted issue, is_causal is not working as intended today, so it requires a change regardless.

In that sense, a change in API does not occur per-se, as the current implementation is not working, and a change has to occur either way to resolve the submitted issue, breaking any use cases that depend on the current implementation.  Checks exist (and more can be added) that flag any scenarios where is_causal is passed as True, but no attention mask is provided, ensuring that there's not quiet change from even the faulty behavior present in 2.0.

As  an upside, the present implementation will improve performance by addressing the passing of the is_causal flag from Transformer modules to MHA, speeding up training for these examples, e.g., finetuning BERT, RoBERTa, XLM-R models.

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/97214
Approved by: https://github.com/albanD",2023-03-25T01:36:26Z,Michael Gschwind
7fa7333299b49a0336164da024b7a51ba9bb7957,"[Distributed][Test] Fix todo in distributed test files (#136836)

Refactor distributed test code:
- Fix TODO: (rohan-varma): remove model
- Fix TODO: add comments for TestTraverse
- Migrate deprecated method call `load_state_dict` and `save_state_dict`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136836
Approved by: https://github.com/kwen2501",2024-10-16T01:15:12Z,zeshengzong
cbf596bf8ee48f4ed895964fe4faf75a851b49c4,"Sparse CSR CPU: add `addmv_out` (#61536)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/61536

This PR adds CPU dispatch for `addmv_out` with Sparse CSR matrix.
The implementation uses MKL Sparse library. If it's not available then a
runtime error is thrown.
Since structured_delegate is used we only need to implement the out variant, the in-place and normal variants are autogenerated.

MKL descriptor of sparse matrices is implemented in `at::mkl::sparse::MklSparseCsrDescriptor`.
MKL Sparse doesn't allow switching indices type in runtime, it's
predetermined in build time. Only 32-bit version of MKL was tested
locally, but I expect 64-bit version to work correctly as well.

When indices type of PyTorch CSR tensor doesn't match with MKL's,
indices tensor is converted to MKL compatible type (`int` vs `int64_t`).

cc nikitaved pearu cpuhrsch IvanYashchuk

Test Plan: Imported from OSS

Reviewed By: ngimel

Differential Revision: *********

Pulled By: malfet

fbshipit-source-id: b818a0b186aa227982221c3862a594266a58a2a6",2021-11-09T20:30:24Z,Ivan Yashchuk
edfc787df494828bcbb2b05b34ad7a316f647b1e,"Migrate kernels with Tensor? to C10 full dispatcher (#54263)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54263

Codemod commands generated by https://github.com/pytorch/pytorch/pull/54223

Signatures of the following 8 methods in LegacyTHFunctionsCUDA.h are
manually changed.

```
_thnn_multi_margin_loss_forward
_thnn_multi_margin_loss_backward
_thnn_nll_loss_forward
_thnn_nll_loss_backward
_thnn_nll_loss2d_forward
_thnn_nll_loss2d_backward
_thnn_conv2d_forward
_thnn_conv_depthwise2d_forward
```

ghstack-source-id: 124539990

Test Plan: buck build //caffe2/aten/...

Reviewed By: smessmer

Differential Revision: D27164092

fbshipit-source-id: 59062179ffd958ca253cbf63fdd495799b9a9586",2021-03-22T23:03:47Z,Wenlei Xie
5c809de4b4663c688ebc2cd389c2c866aa22f6e5,Add missing derivatives.yaml input,2017-12-07T21:00:29Z,Zachary DeVito
6e1c18303bcb2a4798d18ea60ee54ab6998c9f80,"unify linear benchmark (#28897)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/28897

as title

Test Plan:
```
buck run mode/opt //caffe2/benchmarks/operator_benchmark/pt:linear_test
# ----------------------------------------
# PyTorch/Caffe2 Operator Micro-benchmarks
# ----------------------------------------
# Tag : short

# Benchmarking PyTorch: linear
# Mode: Eager
# Name: linear_N4_IN256_OUT128_cpu
# Input: N: 4, IN: 256, OUT: 128, device: cpu
Forward Execution Time (us) : 39.275

Reviewed By: hl475

Differential Revision: D18228070

fbshipit-source-id: 9c209eb74e574c6ef85ebcd78b824ef7d5e65dde",2019-10-30T23:23:21Z,Mingzhe Li
e3e6680b489c55e2acd3145503f734f1cd7b7381,"Add ElmanCell and ElmanRNN

Summary: Closes https://github.com/caffe2/caffe2/pull/1742

Reviewed By: dzhulgakov

Differential Revision: D6706809

Pulled By: anderspapitto

fbshipit-source-id: 15a05786a26aeb719ea4377f4dbbb62738d9e697",2018-01-18T19:55:30Z,Anders Papitto
34c7adf1d72177d9fd08d866a018df5c0d5aa2ec,"add Half support for sigmoid on CPU (#96077)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96077
Approved by: https://github.com/jgong5, https://github.com/ezyang",2023-04-02T09:39:37Z,mingfeima
63dac82444cc522f177b801d9f0cd2e22417c2f4,"Make grad mode error just a warning (#56401)

Summary:
Temporary fix to give people extra time to finish the deprecation.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/56401

Reviewed By: xw285cornell, drdarshan

Differential Revision: D27862196

Pulled By: albanD

fbshipit-source-id: ed460267f314a136941ba550b904dee0321eb0c6",2021-04-20T13:29:27Z,Alban Desmaison
2a1a51facbba6f9be2cc80aa6b91d795666eda46,"Fix typos. (#45195)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45195

Fix some typos in reducer class.
ghstack-source-id: 112673443

Test Plan: N/A

Reviewed By: rohan-varma

Differential Revision: D23862399

fbshipit-source-id: 0dc69e5ea1fa7d33c85d1909b2216bcd1f579f6a",2020-09-23T21:49:02Z,Yi Wang
4746b3d1fbd8c64da8fca26a156c8321f1f8dcd7,"Added missing VSX dispatch for cholesky_inverse (#51562)

Summary:
It was overlooked that vsx dispatch is also needed for cholesky_inverse cpu dispatch.
See https://github.com/pytorch/pytorch/pull/50269#issuecomment-771688180

Pull Request resolved: https://github.com/pytorch/pytorch/pull/51562

Reviewed By: H-Huang

Differential Revision: D26199581

Pulled By: anjali411

fbshipit-source-id: 5d02c6da52ce1d2e9e26001f5d4648a71dd0e829",2021-02-02T21:33:39Z,Ivan Yashchuk
a4e75ccf85bd580ae5cccd471cfe8aee60dc1aa2,"Registered _like metas (#85793)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/85793
Approved by: https://github.com/ezyang",2022-09-28T08:58:18Z,Horace He
03c660468eb57772e82c1034613f5ff8781c775a,"Removed q_num_blocks from constructor (#130819)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130819
Approved by: https://github.com/drisspg
ghstack dependencies: #130809, #130818",2024-07-16T20:50:44Z,chilli
978faf1fa29444f78a7ca805f8abc032cb29e0d8,"Use an op counter to decide when to realize a kernel (#117030)

Instead of checking the number of bytes in the string representation
of the kernel

Pull Request resolved: https://github.com/pytorch/pytorch/pull/117030
Approved by: https://github.com/lezcano, https://github.com/peterbell10",2024-01-27T02:28:36Z,Isuru Fernando
65ae897ae84373d1b34df1785880c62fd2b6c80d,"Pin nvidia-container-runtime version (#19195)

Summary:
This PR is to fix the CI error:
```
nvidia-docker2 : Depends: nvidia-container-runtime (= 2.0.0+docker18.09.4-1) but 2.0.0+docker18.09.5-1 is to be installed
E: Unable to correct problems, you have held broken packages.
Exited with code 100
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/19195

Differential Revision: D14913104

Pulled By: yf225

fbshipit-source-id: d151205f5ffe9cac7320ded3c25baa7e051c3623",2019-04-12T16:57:51Z,Will Feng
915cbf820863135283a3a62b6483bd7c3fa9943e,"[Inductor] Eliminate redundant to_dtype node (#96650)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96650
Approved by: https://github.com/jgong5, https://github.com/jansel",2023-03-17T06:51:30Z,"Wang, Eikan"
88429a8084d4dce8dc201c5c6ff7597568206261,"[inductor] Add split scan kernel (#117992)

This PR adds a new type of triton kernel in which data is persistent but the
reduction dimension is split over multiple blocks (up to the entire kernel).
though this is called a reduction dimension, in actuality we only support scans.
because of this limitation, i have to be able to block fusions of split scan
operations with reductions so chose to add a new `ir.SplitScan` node which
is identical but allows for differentiation in the scheduler.

The split scan kernel is also the first to require an additional workspace buffer
which is used to communicate between cuda blocks. this is slightly tricky as we
the exact scratch space requirement isn't known until the grid size is calculated.
here i workaround the issue by setting a minimum rblock size and always allocating
to the maximum possible grid size for a given input tensor.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/117992
Approved by: https://github.com/jansel
ghstack dependencies: #117991",2024-02-08T01:00:52Z,Peter Bell
c1aa05f80c46cf960ee0d4c553ea9883ade979fa,"[easy][dynamo] Use disable_dynamo for torch.manual_seed (#126192)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/126192
Approved by: https://github.com/yanboliang
ghstack dependencies: #126191",2024-05-14T18:11:39Z,Animesh Jain
ab18aaeba7c97d210a4c2bb615e962bb43228f17,Clarify output shapes of reduce=False losses (#5082),2018-02-13T18:11:14Z,Richard Zou
27e5299ee3dfe8a48f835e6a8ce11ae697d01937,"[DataPipe] Fix mishandling of exception message when error is not iterable (#84676)

We sometimes get an exception message like this:
```
This exception is thrown by __iter__ of TarArchiveLoaderIterDataPipe(datapipe=FileOpenerIterDataPipe, length=-1, mode='r:')    elif msg not in e.args[0] and single_iterator_msg not in e.args[0]:

TypeError: argument of type 'int' is not iterable
```

The `TypeError` raised by the mishandling of the error message obfuscates the true exception, which now will be show as:
```
FileNotFoundError: [Errno 2] No such file or directory:
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/84676
Approved by: https://github.com/ejguan",2022-09-08T23:51:21Z,Kevin Tse
d40a7bf9eb25aacf1d4568d8b076c5b6b4fab6d0,Fix Scatter.backward() (#232),2016-11-18T18:58:09Z,Sam Gross
def50d253401540cfdc6c0fffa444d0ee643cc11,"Create a new unstable workflow for periodic jobs (#98858)

And move ROCm distributed job there as it's very flaky in trunk at the moment.  Also move ROCm slow job to `slow` workflow as it should be.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/98858
Approved by: https://github.com/malfet, https://github.com/ZainRizvi",2023-04-11T20:12:23Z,Huy Do
4aeb98dee9756119f6a6414338e92f2b52c83346,"Move RefInfo classes into opinfo.refs (#83563)

Given that there is already a clear `op_db`, `python_ref_db` split I
think it makes sense to have the `RefInfo` classes be defined in a
different file.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/83563
Approved by: https://github.com/albanD",2022-08-19T02:32:17Z,Peter Bell
b652fbc57a331df5aa28b0bcd07f9e72db2fdbae,"Fix torch.nn.functional.gelu docstring formatting (#89061)

The docstring of `torch.nn.functional.gelu` is formatted incorrectly, so that part of the math isn't rendered and there are extra blocks when there shouldn't: https://pytorch.org/docs/stable/generated/torch.nn.functional.gelu.html

I didn't build the docs, so I am not 100% sure that I got the formatting right, but I am confident.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/89061
Approved by: https://github.com/bdhirsh, https://github.com/kit1980",2022-11-18T01:57:38Z,David Boetius
953f39578a7019c4c34bc1dbd6cb0facb554af79,"Mark IPU device as not supports_as_strided (#89130)

Currently causes issues in calls to `.to`.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/89130
Approved by: https://github.com/albanD",2022-11-23T19:51:50Z,Charlie West-Taylor
26f12af53774783337bdd3ac6abe890fc19e28d0,"Fix op benchmarks error in OSS environment (#19518)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/19518

Previous design needs to run the op benchmarks from PyTorch root directory which could lead to `module not found` error in OSS environment. This diff fixes that issue by making the benchmark to be launched in the `benchmarks` folder.

Reviewed By: ilia-cher

Differential Revision: D15020787

fbshipit-source-id: eb09814a33432a66cc857702bc86538cd17bea3b",2019-04-19T23:22:13Z,Mingzhe Li
bc1b4c89125a3ef6d46a60274fe44d755165cbd5,ByteTensor sum test (#6042),2018-03-30T14:58:38Z,cpuhrsch
89d5391bbf38fa0d514c9049a7b05422ce1345d7,"[inductor] Kill mark_node_as_mutating (#130834)

Resubmit of #129346

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130834
Approved by: https://github.com/lezcano
ghstack dependencies: #130832, #130833",2024-07-23T12:10:27Z,Peter Bell
3c2f6d2ecffaee848844a84907d3fe4ccba72629,"[caffe2] Extend dedup SparseAdagrad fusion with stochastic rounding FP16 (#43124)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/43124

Add the stochastic rounding FP16 support for dedup version of SparseAdagrad fusion.
ghstack-source-id: 111037723

Test Plan:
```
buck test mode/dev-nosan //caffe2/caffe2/fb/net_transforms/tests:fuse_sparse_ops_test -- 'test_fuse_sparse_adagrad_with_sparse_lengths_sum_gradient \(caffe2\.caffe2\.fb\.net_transforms\.tests\.fuse_sparse_ops_test\.TestFuseSparseOps\)' --print-passing-details
```

https://our.intern.facebook.com/intern/testinfra/testrun/5629499566042000

```
buck test mode/dev-nosan //caffe2/caffe2/fb/net_transforms/tests:fuse_sparse_ops_test -- 'test_fuse_sparse_adagrad_with_sparse_lengths_mean_gradient \(caffe2\.caffe2\.fb\.net_transforms\.tests\.fuse_sparse_ops_test\.TestFuseSparseOps\)' --print-passing-details
```

https://our.intern.facebook.com/intern/testinfra/testrun/1125900076333177

Reviewed By: xianjiec

Differential Revision: D22893851

fbshipit-source-id: 81c7a7fe4b0d2de0e6b4fc965c5d23210213c46c",2020-09-01T03:33:48Z,Jianyu Huang
e8836759d0898c29262b5370e16970d697cbaf3a,"[export] Add effect token to export (#121424)

Following the creation of effect tokens (https://github.com/pytorch/pytorch/pull/120296), we want to now add support for these tokens in export because the calling/returning convention has changed. The inputs are now `(tokens, params, buffers, constants, user_inputs)` and the outputs are `(tokens, buffer_mutations, user_mutations, user_outputs)`. The graph looks something like:
```
graph():
    %arg0_1 : [num_users=1] = placeholder[target=arg0_1]
    %attr : [num_users=2] = placeholder[target=attr]
    %arg1_1 : [num_users=2] = placeholder[target=arg1_1]
    %with_effects : [num_users=2] = call_function[target=torch._higher_order_ops.effects.with_effects](args = (%arg0_1, _TorchScriptTesting.takes_foo.default, %attr, %arg1_1), kwargs = {})
    %getitem : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects, 0), kwargs = {})
    %getitem_1 : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects, 1), kwargs = {})
    %with_effects_1 : [num_users=2] = call_function[target=torch._higher_order_ops.effects.with_effects](args = (%getitem, _TorchScriptTesting.takes_foo.default, %attr, %getitem_1), kwargs = {})
    %getitem_2 : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects_1, 0), kwargs = {})
    %getitem_3 : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects_1, 1), kwargs = {})
    %add : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%arg1_1, %getitem_3), kwargs = {})
    return (getitem_2, add)
```

During unlifting, we will first remove the tokens and with_effect calls using the `remove_effect_tokens` pass. (cc @SherlockNoMad on the pass to remove tokens). This is so that this won't change the calling conventions when retracing. The graph after unlifting looks something like:
```
graph():
    %attr_1 : [num_users=2] = get_attr[target=attr]
    %arg1_1 : [num_users=2] = placeholder[target=arg1_1]
    %takes_foo_default_1 : [num_users=1] = call_function[target=torch.ops._TorchScriptTesting.takes_foo.default](args = (%attr_1, %arg1_1), kwargs = {})
    %takes_foo_default : [num_users=1] = call_function[target=torch.ops._TorchScriptTesting.takes_foo.default](args = (%attr_1, %takes_foo_default_1), kwargs = {})
    %add : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%arg1_1, %takes_foo_default), kwargs = {})
    return (add,)
```

Serialization support will be added in a followup.
Note: tokens only affect custom ops that take in ScriptObjects, not ScriptObject methods yet.

Differential Revision: [D54639390](https://our.internmc.facebook.com/intern/diff/D54639390)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/121424
Approved by: https://github.com/tugsbayasgalan",2024-03-08T18:29:12Z,angelayi
ee73c752c67116d36cf268dde011b77df704d507,"Delete unnecessary empty file (#54796)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/54796

Reviewed By: albanD

Differential Revision: D27370733

Pulled By: iramazanli

fbshipit-source-id: 5f78e9250a545afb91b4bc7b14daa7135a2b6a1b",2021-03-26T20:43:24Z,Ilqar Ramazanli
63429bf4b3f491164f9896701134d81162b27469,"Removed JIT FC tweaks for interpolation options (#71937)

Summary:
Description:
- Removed JIT FC tweaks for interpolation options : nearest-exact and antialiasing

They were added in
- https://github.com/pytorch/pytorch/pull/64501 (Sept 04 2021)
- https://github.com/pytorch/pytorch/pull/65142 (Sept 16 2021)

cc jbschlosser

Pull Request resolved: https://github.com/pytorch/pytorch/pull/71937

Reviewed By: mrshenli

Differential Revision: D33845502

Pulled By: jbschlosser

fbshipit-source-id: 8a94454fd643cd2aef21b06689f72a0f16620d30
(cherry picked from commit b21173d64c27d3ee12b608f2805f209611077aa0)",2022-01-28T19:52:23Z,vfdev
de1b00abdaa0a9fa72c38bcf8b281e60c862bef4,"inductor: tigher upperbound for rblock scaling (#109839)

Previously when we deciding if dynamically scaling down rblock, we use the following formule to compute the upper bound of number of blocks per sm:
```
max_threads_per_multi_processo / (32 * num_warps)
```

This is correct but it's a bit loose and some times because of the loose upper bound, we skip some optimization opportunities.

The new upper bound is: 65536 / n_reg_used_by_each_block . This is a tighter upper bound and can be helpful if the kernel uses too many registers (i.e. much larger than 32).

For kernel https://gist.github.com/shunting314/59aeafd297ed8ff03aa12030a2dd41ae (this is a real kernel inductor generates for HF), the change improve its perf from:
0.485ms    0.332GB    684.29GB/s
to
0.240ms    0.332GB    1382.70GB/s

. The perf is bad previsouly because of register spills

Pull Request resolved: https://github.com/pytorch/pytorch/pull/109839
Approved by: https://github.com/jansel",2023-09-22T00:22:21Z,Shunting Zhang
2fd4d088ff20085cf8af3183d11c9bbdf66a526d,add Adaptive pooling methods to docs,2017-03-27T02:43:46Z,Soumith Chintala
4721553431e968ee5e90753346d92252cce98e84,"[vmap] Fix searchsorted batch rule for self_logical_rank == 0 (#99526)

Fixes #95888

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99526
Approved by: https://github.com/zou3519",2023-04-20T01:49:20Z,Li-Huai (Allan) Lin
fd68b0931f1b1f80a1f389f0722b76ed96f54543,"sym_numel (#82374)

### Description
This PR makes `numel` symint-aware similar to `sym_sizes()` and `sym_strides()`. Similar to https://github.com/pytorch/pytorch/pull/81300 . This PR is the part of a bigger project to support dynamic_shapes.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/82374
Approved by: https://github.com/ezyang",2022-08-03T06:33:45Z,Nikolay Korovaiko
0ca8f66e3aacf3e3ea0fbdd05dd59a59ddcbb8df,"[NestedTensor] Modify softmax on ragged dimension to allow for 2D nested tensors (#132812)

Summary:
Modify `softmax` on the ragged dimension, where `ragged_idx == 1`, to allow for 2D nested tensors. This diff now enables a `softmax` operation on tensors of shape `(B, *)`, where `*` is the ragged dimension.

Extend existing `softmax` unit tests to include 2D nested tensors using the `include_2d_tensor=True` keyword argument.

Test Plan:
Verify that existing and modified unit tests pass using the following commands:

```
buck2 run mode/{opt,inplace} //caffe2/test:nested -- --regex test_softmax
```

```
buck2 run mode/{opt,inplace} //caffe2/test:nested -- --regex test_jagged_op
```

Reviewed By: davidberard98

Differential Revision: D60780975

Pull Request resolved: https://github.com/pytorch/pytorch/pull/132812
Approved by: https://github.com/davidberard98",2024-08-08T15:41:28Z,Janani Sriram
0d03219a421310c2c8c0f287284e27e31ee74562,"Remove hack as integrated builds use FULL_CAFFE2 now (#10320)

Summary:
Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10320

Reviewed By: jerryzh168

Differential Revision: D9198902

Pulled By: ezyang

fbshipit-source-id: 8af28d607735e5f4450c40127c1f8c262ea602ce",2018-08-08T04:25:41Z,Edward Yang
8253cfaa72dd00ef74cfa78d7a3c2d1225ab0551,"Conv BN fusion for 3D conv (#10239)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10239

Make Conv + BN fusion also work for 3D convolutions

Reviewed By: duc0

Differential Revision: D9176314

fbshipit-source-id: 6604aa569c5c3afdb4480a5810890bc617e449c4",2018-08-25T04:23:08Z,Jongsoo Park
bd5ec6c8b7b8d33689109fe258b73ef7a61b9a3c,"[quant][core][gpu][improvement] Removed conv_output and set output tensors as virtual in quantized cudnn conv2d op

Summary:
With support for virtual tensors in cudnn, we no longer have to allocate
conv_output.

Test plan:
```
python test/test_quantization.py -k test_qconv2d_cudnn
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76787

Approved by: https://github.com/jerryzh168",2022-05-24T21:27:30Z,dzdang
6b6c63ce5e0b54eb92cdd88f3bb87efc7adeaf9c,"Upstream `argmax` shape function.

Keeping this first commit simple to test out the flow. Will bulk-add the
rest once this one goes through.

Shape function taken from:
https://github.com/llvm/torch-mlir/blob/5192a4e9f3e4cbc77838b70153cd4632aa43dd7f/python/torch_mlir/dialects/torch/importer/jit_ir/build_tools/shape_lib_gen.py#L488

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76592
Approved by: https://github.com/eellison",2022-05-03T16:52:09Z,Sean Silva
064b61009b66c714cb750c2660b7030113ebb131,"Correctly formatting the example in get_state_dict (#119532)

This PR corrects the example formatting provided in https://pytorch.org/docs/stable/distributed.checkpoint.html. In this issue, @wz337 is also commenting that the return type was not showing up correctly. I didn't see any formatting issue, but I could be wrong.

Fixes #118837

Pull Request resolved: https://github.com/pytorch/pytorch/pull/119532
Approved by: https://github.com/fegin",2024-02-12T21:28:22Z,jmarin
4ba3e6758d9d94be111cf3bc735421dfe4dc2b0a,"Canonicalize runtime asserts (#114509)

This allows us to remove quite a few redundant runtime asserts, and potentially a number of guards as well.

On
```
python test/dynamo/test_subclasses.py -k test_unbind
```
we go from
```
inserting runtime assert i0 <= s0
inserting runtime assert 0 <= -i0 + s0
inserting runtime assert i0 + i1 <= s0
inserting runtime assert i0 <= -i1 + s0
inserting runtime assert i0 + i1 + i2 <= s0
inserting runtime assert i0 + i1 <= -i2 + s0
inserting runtime assert Eq(i0 + i1 + i2 + i3, s0)
inserting runtime assert i0 + i1 + i2 + i3 <= s0
inserting runtime assert i0 + i1 + i2 <= -i3 + s0
```
to
```
inserting runtime assert i0 - s0 <= 0
inserting runtime assert i0 + i1 - s0 <= 0
inserting runtime assert i0 + i1 + i2 - s0 <= 0
inserting runtime assert Eq(i0 + i1 + i2 + i3, s0)
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/114509
Approved by: https://github.com/voznesenskym",2023-11-27T22:24:37Z,lezcano
d833f496021334d21994e600d105f5bf2e0b83d1,"[reland][Inductor] Rename `cpp_wrapper_cuda.py` as `cpp_wrapper_gpu.py` (#136046)

Summary: Reland https://github.com/pytorch/pytorch/pull/135313 after fixing internal build issues

Test Plan: CI

Differential Revision: D62658837

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136046
Approved by: https://github.com/chenyang78, https://github.com/etaf, https://github.com/jansel",2024-09-16T14:35:19Z,Bin Bao
5211fb97ac4c246151f1286c78d63e0e317a8a4a,"Remove device maps from TensorPipe for v1.7 release (#45353)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45353

Temporarily removing this feature, will add this back after branch cut.

Test Plan: Imported from OSS

Reviewed By: rohan-varma

Differential Revision: D23939865

Pulled By: mrshenli

fbshipit-source-id: 7dceaffea6b9a16512b5ba6036da73e7f8f83a8e",2020-09-25T23:49:32Z,Shen Li
12addc64a6a6787c224e4d96057a797ca7de1535,"Fixed MIOpen RNN Segfault issue and enabled RNN test (#14810)

Summary:
This pull request contains changes for:
1. Added MIOpen RNN API miopenGetRNNLayerBiasSize and miopenGetRNNLayerParamSize.
2. Fixed usage of API miopenGetRNNLayerParam.
3. Modifying the RNN test to run using MIOpen engine.

Differential Revision: D13355699

Pulled By: bddppq

fbshipit-source-id: 6f750657f8049c5446eca893880b397804120b69",2018-12-06T07:52:42Z,lcskrishna
ebb7f20afced4d831ad46e9a88cc63a166cb391a,"quant: make various configs printable (#91419)

Summary:

Makes various quantization configs print out human readable values instead
of just the class name. This is useful when printing these configs out when
debugging.

Test plan:

test script
```
conf_1 = torch.ao.quantization.backend_config.backend_config.DTypeConfig()
print(conf_1)

conf_2 = torch.ao.quantization.backend_config.backend_config.BackendConfig()
print(conf_2)

conf_3 = torch.ao.quantization.backend_config.backend_config.BackendPatternConfig()
print(conf_3)

conf_4 = torch.ao.quantization.fx.custom_config.PrepareCustomConfig()\
    .set_input_quantized_indexes([0])
print(conf_4)

conf_5 = torch.ao.quantization.fx.custom_config.ConvertCustomConfig()\
    .set_preserved_attributes(['foo'])
print(conf_5)

conf_6 = torch.ao.quantization.fx.custom_config.FuseCustomConfig()\
    .set_preserved_attributes(['foo'])
print(conf_6)
```

test script output
```
DTypeConfig(input_dtype_with_constraints=DTypeWithConstraints(dtype=None, quant_min_lower_bound=None, quant_max_
upper_bound=None, scale_min_lower_bound=None, scale_max_upper_bound=None, scale_exact_match=None, zero_point_exa
ct_match=None), output_dtype_with_constraints=DTypeWithConstraints(dtype=None, quant_min_lower_bound=None, quant
_max_upper_bound=None, scale_min_lower_bound=None, scale_max_upper_bound=None, scale_exact_match=None, zero_poin
t_exact_match=None), weight_dtype_with_constraints=DTypeWithConstraints(dtype=None, quant_min_lower_bound=None,
quant_max_upper_bound=None, scale_min_lower_bound=None, scale_max_upper_bound=None, scale_exact_match=None, zero
_point_exact_match=None), bias_dtype=None, is_dynamic=None)
BackendConfig({'name': '', '_pattern_complex_format_to_config': {}})
BackendPatternConfig({'observation_type': <ObservationType.OUTPUT_USE_DIFFERENT_OBSERVER_AS_INPUT: 0>})
PrepareCustomConfig({'input_quantized_indexes': [0]})
ConvertCustomConfig({'preserved_attributes': ['foo']})
FuseCustomConfig({'preserved_attributes': ['foo']})
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/91419
Approved by: https://github.com/andrewor14",2023-01-04T01:11:37Z,Vasiliy Kuznetsov
f326f7dda8051ec83c81ad725e48600a9d957bf3,"[package] use digraph to back dependency visualization (#57338)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/57338

Differential Revision: D28114190

Test Plan: Imported from OSS

Reviewed By: astaff

Pulled By: suo

fbshipit-source-id: 78b15edae3b991307fd3656ac7b374d4d218b460",2021-05-06T00:54:20Z,Michael Suo
34804e96009acc8956fefb19c52fcf322d5745e6,"Refactor file and tcp init methods
 * Add sanity checks
 * Refactor InitMethodFile and TCPInitMethod to more logical functions
 * Update few error messages
 * Add passing parameters by **kwargs, so now order of parameters is not relevant
 * Review comments",2017-05-31T09:52:22Z,Janusz Marcinkiewicz
eb6e70cf66129bc6347ff02c3a4116c1b784c628,"[C10D] Only open NCCL dump pipe file once per process (#115798)

The NCCL flight recorder is per-process (it is shared by all
processgroups), but individual process groups used to construct their
own pipe for being signaled to dump the flight recorder.

This ensures that only one pipe per process is created, by only creating
the pipe on the first ProcessGroup (uid_ == 0) which should be the world
group.

Filenames are still keyed off of rank, but this should now be global
rank instead of sub-pg rank, making the filenames unique across the
whole trainer process.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/115798
Approved by: https://github.com/zdevito
ghstack dependencies: #115771",2023-12-14T14:41:03Z,Will Constable
c3e2ee725f3ead3644c7030a051d51d2893b7989,"Automated submodule update: FBGEMM (#42496)

Summary:
This is an automated pull request to update the first-party submodule for [pytorch/FBGEMM](https://github.com/pytorch/FBGEMM).

New submodule commit: https://github.com/pytorch/FBGEMM/commit/87c378172a7fa757a8f0f015f31d4f6111d4744e

Pull Request resolved: https://github.com/pytorch/pytorch/pull/42496

Test Plan: Ensure that CI jobs succeed on GitHub before landing.

Reviewed By: dskhudia

Differential Revision: D22911638

fbshipit-source-id: f20c83908b51ff56d8bf1d8b46961f70d023c81a",2020-08-04T23:09:41Z,Facebook Community Bot
90105a4f3e729844989fec5419b58d916db4a02c,"[ts-migration] Support RaiseException, prim::Unitialized, prim::Enter, and prim::Exit (#129416)

- Support raise exception. It's behavior matches non-strict export now, thanks to @ydwu4's [PR](https://github.com/pytorch/pytorch/pull/128709).
- Support prim::Unitialized, prim::Enter, and prim::Exit
Pull Request resolved: https://github.com/pytorch/pytorch/pull/129416
Approved by: https://github.com/angelayi",2024-07-17T21:59:50Z,Boyuan Feng
a42d093db224528454c58a7cac1b4d9b7126440a,"FCTransposed to FbFCPacked (#29766)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/29766

Add FbgemmPackTranspose op to support the packing on FCTransposed weights

Add FCTransposed to FbFCPacked transformation to Dper fp16 exporter

Test Plan:
```
buck test mode/opt caffe2/caffe2/fb/fbgemm:fb_fc_packed_op_test
```

```
buck test mode/opt caffe2/caffe2/python:layers_test
```

Differential Revision: D18482306

fbshipit-source-id: e8f1947b3d0d04892293509ebf88742f5f0f5997",2019-12-10T18:13:20Z,Summer Deng
8d12ba9acfa20ed7df438a8892c9bf8e6bef5775,"add methods for open device in PackedSequence module. (#124923)

1) add is_{custom_device_name}() and {custom_device_name}() for open device register;
2) fix open device failed testcases.

@ezyang  @bdhirsh
Pull Request resolved: https://github.com/pytorch/pytorch/pull/124923
Approved by: https://github.com/ezyang",2024-04-26T15:26:20Z,Shan19900305
d4f831349756f3739ef69f82a15c86fc677f3eeb,"Add low level torch.profiler.kineto_profile base class (#63302)

Summary:
Refactor torch.profiler.profile by separate it into one low level class and one high level wrapper.

The PR include the following change:
1. separate class torch.profiler.profile into two separated class: kineto_profiler and torch.profiler.profile.
2. The former class has the low-level functionality exposed in C++ level like: prepare_profiler, start_profiler, stop_profiler.
3. The original logics in torch.profiler.profile including export_chrome_trace, export_stacks, key_averages, events, add_metadata are all moved into kineto_profiler since they are all exposed by the torch.autograd.profiler.
4. The new torch.profiler.profile is fully back-compatible with original class since it inherit from torch.profiler.kineto_profiler. Its only responsibility in new implementation is the maintenance of the finite state machine of ProfilerAction.

With the refactoring, the responsibility boundary is clear and the new logic is simple to understand.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/63302

Reviewed By: albanD

Differential Revision: D33006442

Pulled By: robieta

fbshipit-source-id: 30d7c9f5c101638703f1243fb2fcc6ced47fb690",2021-12-14T22:45:36Z,Mike Guo
123297a8c02e0ebbf8b0ae3d3cb16d0dc2e350b4,"[lint] use python to run flake8 and mypy in linter

Previously we were just using whatever version the shell picked up, but
malfet reported that this can (and is) overridden on some machines.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/75858

Approved by: https://github.com/malfet",2022-04-15T04:14:51Z,Michael Suo
de65f156ed6595f0748ff03d27928ddeee3695af,"Add distributed composable API contract (#87580)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/87580
Approved by: https://github.com/yhcharles",2022-10-25T22:30:54Z,Shen Li
54bdaf76d6bda1ca9c1428012deed73ced8dd755,"[PFC] Native UCC process group for Pytorch (#79918)

Summary:
This diff integrates UCC process group as a native component of Pytorch Distributed core. It is based on the existing torch-ucc (https://github.com/facebookresearch/torch_ucc) as the wrapper for UCC collective communication library.
The environment and cmake variables are named in mirroring to the existing process groups such as NCCL and Gloo. Specifically,
- USE_UCC: enables UCC PG. This defaults to OFF, so there is no breakage of existing builds that do not have UCX/UCC external libraries.
- USE_SYSTEM_UCC: uses external UCX and UCC shared libraries that are set accordingly with UCX_HOME and UCC_HOME.

Currently, this diff only supports USE_SYSTEM_UCC=ON, i.e., requiring users to specify external libraries for UCX and UCC. In subsequent diffs, we will add UCX and UCC repos as third-party dependencies in pytorch/third-party.

Test Plan:
Passed Torch-UCC tests that invoke UCC process group. For example:

$ sh test/start_test.sh test/torch_allreduce_test.py --backend gloo --use-cuda
...
Test allreduce: succeeded

Differential Revision: D36973688

Pull Request resolved: https://github.com/pytorch/pytorch/pull/79918
Approved by: https://github.com/kwen2501, https://github.com/kingchc",2022-07-12T14:45:44Z,Terry Lam
3e6164449fe285b7c9c9e4f0df63b5f3ed8a3dc8,"Add efficient zero tensors (#64837)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/64837

Test Plan: Imported from OSS

Reviewed By: gchanan

Differential Revision: D32834987

Pulled By: anjali411

fbshipit-source-id: 20ea08ade0db0044ca633d9c1a117a6a2e65d1fd",2021-12-08T18:34:08Z,anjali411
8f5fead86ea9a9eac85d20c6aee780e06ce04eb7,"Improves comparison of state dicts for Checkpoint E2E Tests (#113181)

Addresses the following comment - https://github.com/pytorch/pytorch/pull/112541#discussion_r1380197424

Changes the comparison of models in the checkpointing E2E test to compare a non-parallelized model against distribued model after training, saving, & loading.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/113181
Approved by: https://github.com/fegin",2023-11-14T14:54:40Z,Lucas Pasqualin
3897c479af80531a95120811f5ad48db259215ed,"Add API to construct the functional variant of an op (#102293)

`register_functional_op`:
- constructs the functional variant of an op
- registers a functionalization kernel to the op

To get this to work:
- `register_functional_op` makes assumptions that it checks about the
op's schema. In particular, the op is not allowed to return anything it
mutates. We can relax these constraints in the future.
- We add a ""boxed"" python functionalization kernel that handles this
case.

I'm not actually sure (or convinced) this should be public API or how
it should work. If we want this to be public, then it should probably be
a torch.library API, but does that also mean we should give the same
lifetime guarantees? If so, then it would be up to the user to construct
a Library object to actually register the functional variant onto.

Test Plan:
- new tests
Pull Request resolved: https://github.com/pytorch/pytorch/pull/102293
Approved by: https://github.com/bdhirsh",2023-06-01T18:44:57Z,Richard Zou
095886fa423acaa3781334fc1099ef0e22306ab9,"[caffe2] Fix the issues when using CUB RadixSort (#41299)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/41299

When using `cub::DeviceRadixSort::SortPairs` (https://nvlabs.github.io/cub/structcub_1_1_device_radix_sort.html), the `end_bit` argument, or the most-significant bit index (exclusive) needed for key comparison, should be passed with  `int(log2(float(num_rows)) + 1)` instead of `int(log2(float(num_indice)) + 1)`. This is because all the values in indices array are guaranteed to be less than num_rows (hash_size), not num_indices. Thanks ngimel for pointing this point and thanks malfet for quickly fixing the log2() compilation issues.

Note:
An optional bit subrange [begin_bit, end_bit) of differentiating key bits can be specified. This can reduce overall sorting overhead and yield a corresponding performance improvement.

Test Plan:
```
buck test mode/dev-nosan //caffe2/caffe2/fb/net_transforms/tests:fuse_sparse_ops_test -- 'test_fuse_sparse_adagrad_with_sparse_lengths_sum_gradient \(caffe2\.caffe2\.fb\.net_transforms\.tests\.fuse_sparse_ops_test\.TestFuseSparseOps\)' --print-passing-details
```

Reviewed By: malfet

Differential Revision: D22491662

fbshipit-source-id: 4fdabe86244c948af6244f9bd91712844bf1dec1",2020-07-11T05:36:38Z,Jianyu Huang
00459c2c8733d22ecea82047e46bd9087d093ebf,"[primTorch] Implement constant_pad_nd (#80182)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/80182
Approved by: https://github.com/mruberry, https://github.com/ngimel",2022-07-15T11:05:39Z,Peter Bell
772b3e92bfd4ae0ef1f42d11481aab8428de931a,"Parse symbolic shapes (#69775)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/69775

Adds parsing for Symbolic Shapes.

Test Plan: Imported from OSS

Reviewed By: jbschlosser

Differential Revision: D33515233

Pulled By: eellison

fbshipit-source-id: 7ebb22c0ab37d78e459ebcab67bb86f731d00376",2022-01-12T06:09:58Z,Elias Ellison
951582949bd45bbe892d936f58ff4cafa1fd6204,"[export] Enforce final classes in serialization. (#123861)

Summary: as title, these are private API and not meant to be used across repos.

Test Plan: CI

Differential Revision: D56027954

Pull Request resolved: https://github.com/pytorch/pytorch/pull/123861
Approved by: https://github.com/tugsbayasgalan",2024-04-12T15:44:56Z,Zhengxu Chen
345695e8f7a58240fb5ce647d86f44f2d70dea07,"Remove PY37 from binary build matrix (#92919)

Similar to https://github.com/pytorch/test-infra/pull/1416 but for binary build
Pull Request resolved: https://github.com/pytorch/pytorch/pull/92919
Approved by: https://github.com/atalman",2023-01-26T01:25:47Z,Wei Wang
f7dce8508c02f667b20c63fe0f31283f5b4ef279,"Revert *********: [pytorch][PR] Implement cusparse Descriptor class and clean up cusparse code

Test Plan: revert-hammer

Differential Revision:
*********

Original commit changeset: ecbb4063466c

fbshipit-source-id: 56ae47273691a12cc8d96635fb4ad9d09080ccc9",2020-04-29T19:54:42Z,Edward Yang
137f2a385af9a32a71296b8b6e00735c6b1017d7,"[ONNX] Handle sequence output for models (#50599)

Summary:
Duplicate of https://github.com/pytorch/pytorch/issues/46542

Pull Request resolved: https://github.com/pytorch/pytorch/pull/50599

Reviewed By: SplitInfinity

Differential Revision: *********

Pulled By: bzinodev

fbshipit-source-id: a898cef7b2d15a287aedd9798ce1423cebf378d4",2021-01-21T23:29:19Z,neginraoof
493a6ced74ea9271fc7ab16d6493f08750ecb5f4,"[fx] Throw error when symbolically tracing control flow ops (#92313)

Throws a better error when symbolically tracing control flow ops. Right now it throws an error when creating the function arguments.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/92313
Approved by: https://github.com/zhxchen17",2023-01-20T00:38:21Z,Angela Yi
4cc05c41fa26c3b8f76eb4005a5eae8329c1215b,"[MPS] Fix `torch.std` for negative dimentions (#107754)

By simply comparing output dimentions to a properly wrapped dim
Add regression test to opinfo

<!--
copilot:poem
-->
### <samp>🤖 Generated by Copilot at ca98536</samp>

> _`reduceTensor` bug_
> _negative dimensions wrapped_
> _autumn tests added_

Fixes https://github.com/pytorch/pytorch/issues/107116
Pull Request resolved: https://github.com/pytorch/pytorch/pull/107754
Approved by: https://github.com/kit1980",2023-08-23T03:49:59Z,Nikita Shulga
b6e330641aebf1b4acaa887de37ebf07fae0f8c7,"fix Android studio compilation error

Summary: Android studio auto -Werrors in debug mode and throws an error on non string literals in 3rd argument of android_log_print

Reviewed By: Yangqing

Differential Revision: D4465263

fbshipit-source-id: af6dc436b7c98a29aa89bb241c452e6da5c8ad1f",2017-01-26T04:20:32Z,Bram Wasti
eaab653376da76cd3038b7f2bed37b03e2048522,"Read via FileAdapter when loading files in torch if not flatbuffer - Part 2 (#84296)

Summary: D38998858 (https://github.com/pytorch/pytorch/commit/3fae89d4a468a02be501357eb123ce2bf7086d2f) used the wrong version of `_load_for_mobile` that kept the ""load everything in memory then parse"" technique.  This fixes it to call the `_load_for_mobile_impl` version which for non-flatbuffer models will stream parse.  See D38998858 (https://github.com/pytorch/pytorch/commit/3fae89d4a468a02be501357eb123ce2bf7086d2f) for the expected memory optimization gains.

Test Plan: CI Signals.

Reviewed By: qihqi

Differential Revision: D39138280

Pull Request resolved: https://github.com/pytorch/pytorch/pull/84296
Approved by: https://github.com/qihqi",2022-09-01T22:38:59Z,Ian Graves
068c80e6b600b99bb969a8a426eb568c66163fb6,"[BE][MPS] Fix deprecation warnings on MacOS 15.0 (#136292)

[reverseSquareRootWithTensor:](https://developer.apple.com/documentation/metalperformanceshadersgraph/mpsgraph/reversesquareroot(with:name:)?changes=__8&language=objc) were deprecated in favor of [reciprocalSquareRootWithTensor:](https://developer.apple.com/documentation/metalperformanceshadersgraph/mpsgraph/reciprocalsquareroot(_:name:)?changes=__8&language=objc)

Without it, following warnings are generated if compiled on recently released MacOS Sequoia:
```
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:720:35: warning: 'reverseSquareRootWithTensor:name:' is deprecated: first deprecated in macOS 15.0 [-Wdeprecated-declarations]
  720 |           rsqrtTensor = [mpsGraph reverseSquareRootWithTensor:varianceEpsTensor name:nil];
      |                                   ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                   reciprocalSquareRootWithTensor
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/invoke.h:341:10: note: in instantiation of function template specialization 'at::native::batch_norm_backward_mps(const Tensor &, const Tensor &, const std::optional<Tensor> &, const std::optional<Tensor> &, const std::optional<Tensor> &, const std::optional<Tensor> &, const std::optional<Tensor> &, bool, double, std::array<bool, 3>)::(anonymous class)::operator()<MPSGraph *, CachedGraph *>' requested here
  341 | decltype(std::declval<_Fp>()(std::declval<_Args>()...))
      |          ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/invoke.h:351:19: note: while substituting deduced template arguments into function template '__invoke' [with _Fp = (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &, _Args = <MPSGraph *, CachedGraph *>]
  351 |   static decltype(std::__invoke(std::declval<_XFp>(), std::declval<_XArgs>()...)) __try_call(int);
      |                   ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/invoke.h:357:28: note: while substituting deduced template arguments into function template '__try_call' [with _XFp = (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &, _XArgs = (no value)]
  357 |   using _Result = decltype(__try_call<_Fp, _Args...>(0));
      |                            ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/conjunction.h:27:32: note: in instantiation of template class 'std::__invokable_r<void, (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &, MPSGraph *, CachedGraph *>' requested here
   27 | __expand_to_true<__enable_if_t<_Pred::value>...> __and_helper(int);
      |                                ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/conjunction.h:38:39: note: while substituting explicitly-specified template arguments into function template '__and_helper'
   38 | using _And _LIBCPP_NODEBUG = decltype(std::__and_helper<_Pred...>(0));
      |                                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:828:20: note: (skipping 1 context in backtrace; use -ftemplate-backtrace-limit=0 to see all)
  828 |             bool = _And< _IsNotSame<__remove_cvref_t<_Fp>, function>, __invokable<_Fp, _ArgTypes...> >::value>
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:841:49: note: in instantiation of default argument for '__callable<(lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &>' required here
  841 |   using _EnableIfLValueCallable = __enable_if_t<__callable<_Fp&>::value>;
      |                                                 ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:851:32: note: in instantiation of template type alias '_EnableIfLValueCallable' requested here
  851 |   template <class _Fp, class = _EnableIfLValueCallable<_Fp>>
      |                                ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:852:25: note: in instantiation of default argument for 'function<(lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68)>' required here
  852 |   _LIBCPP_HIDE_FROM_ABI function(_Fp);
      |                         ^~~~~~~~~~~~~
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68: note: while substituting deduced template arguments into function template 'function' [with _Fp = (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68), $1 = (no value)]
  623 |     auto cachedGraph = LookUpOrCreateCachedGraph<CachedGraph>(key, [&](auto mpsGraph, auto newCachedGraph) {
      |                                                                    ^
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:24: note: while substituting deduced template arguments into function template 'LookUpOrCreateCachedGraph' [with T = CachedGraph]
  623 |     auto cachedGraph = LookUpOrCreateCachedGraph<CachedGraph>(key, [&](auto mpsGraph, auto newCachedGraph) {
      |                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Headers/MPSGraphArithmeticOps.h:123:1: note: 'reverseSquareRootWithTensor:name:' has been explicitly marked deprecated here
  123 | -(MPSGraphTensor *) reverseSquareRootWithTensor:(MPSGraphTensor *) tensor
      | ^
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:745:37: warning: 'reverseSquareRootWithTensor:name:' is deprecated: first deprecated in macOS 15.0 [-Wdeprecated-declarations]
  745 |             rsqrtTensor = [mpsGraph reverseSquareRootWithTensor:varianceEpsTensor name:nil];
      |                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                     reciprocalSquareRootWithTensor
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Headers/MPSGraphArithmeticOps.h:123:1: note: 'reverseSquareRootWithTensor:name:' has been explicitly marked deprecated here
  123 | -(MPSGraphTensor *) reverseSquareRootWithTensor:(MPSGraphTensor *) tensor
      | ^
2 warnings generated.
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/136292
Approved by: https://github.com/kit1980",2024-09-18T23:38:31Z,Nikita Shulga
d996acfbc2e7536b0438eb491628a328bdf59f05,"[XNNPACK] disable ARM_BF16 and ARM_FP16_VECTOR (#94020)

Summary: This is not used and will cause build failure

Test Plan: CI

Differential Revision: D42982023

Pull Request resolved: https://github.com/pytorch/pytorch/pull/94020
Approved by: https://github.com/Skylion007, https://github.com/tiandiao123, https://github.com/digantdesai",2023-02-03T05:00:58Z,Hansong Zhang
b34bb1f562421dbcc497bd625ffbd1814bbb9291,"Add support for parsing torch.Generator in JIT (#140489)

Fixes #140420

Pull Request resolved: https://github.com/pytorch/pytorch/pull/140489
Approved by: https://github.com/davidberard98",2024-11-13T23:06:54Z,Antonio Kim
ea50549ce62aeeccfe27035a0a975e83b9c2c987,"Suppress guards when creating fake tensors (#89349)

When we create fake tensors, we may call operators that introduce
guards, to accurately reconstruct views.  But these guards are spurious:
if a user is able to present a tensor that ""looks the same"", they have
implicitly fulfilled the contract that the view is creatable.

Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/89349
Approved by: https://github.com/voznesenskym",2022-11-21T23:12:21Z,Edward Z. Yang
d6a8d397dab2f8e31639c694fd7e9591c9a72fa7,"Fix formatting for merge failed message (#95234)

Fixes formatting so that the merge rule shows up on a different line than the ""Raised by"" text

Follow up to https://github.com/pytorch/pytorch/pull/94932

New version
<img width=""433"" alt=""image"" src=""https://user-images.githubusercontent.com/4468967/220441349-ac99096d-590a-42c1-b995-4a23b2d9b810.png"">
Pull Request resolved: https://github.com/pytorch/pytorch/pull/95234
Approved by: https://github.com/huydhn",2023-02-22T18:11:22Z,Zain Rizvi
2f6e8e84c51df80b4e783e5840c9443fbbce7a3c,"Fix `_chunk_cat.out` issue (#122076)

# PR
Vectors allocated inside `get_chunk_cat_metadata()` are out of local scope when used in `_chunk_cat_out_cuda_contiguous()`. This PR fixes the issue by returning vectors from `get_chunk_cat_metadata`.
This PR also added a few unit tests to cover more edge cases.

# Tests
This PR is tested with the following command and no error shows. So the flaky test error should be resolved.

- `PYTORCH_NO_CUDA_MEMORY_CACHING=1 compute-sanitizer python test/test_ops.py -v -k test_out__chunk_cat_cuda_float32`
- `PYTORCH_NO_CUDA_MEMORY_CACHING=1 python test/test_ops.py -v -k test_out__chunk_cat_cuda_float32 --repeat 1500`

Fixes #122026
Fixes #121950

Pull Request resolved: https://github.com/pytorch/pytorch/pull/122076
Approved by: https://github.com/yifuwang",2024-03-20T20:01:34Z,Boyuan Feng
94e197c262dad3fb3839e4de7ce2a4b2a9c30ecb,"Add utf-8 header to Python file with Unicode. (#8131)

Signed-off-by: Edward Z. Yang <<EMAIL>>",2018-06-04T21:49:32Z,Edward Z. Yang
a7933acd5a2a9ac0154a5e272a9c8a36dc4eb1d1,"Improve custom ops aliasing error message (#134688)

Fixes https://github.com/pytorch/pytorch/issues/134278

Test Plan:
- tested locally
Pull Request resolved: https://github.com/pytorch/pytorch/pull/134688
Approved by: https://github.com/yushangdi
ghstack dependencies: #134466, #134490, #134491, #134690, #134692",2024-08-28T17:17:01Z,rzou
d5b38984c844044bf28b0283f7be5186087cd097,"Let RPC return FutureIValue instead of FutureMessage (#37519)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37519

closes #37446

Currently FutureMessage is used in several places:

1. `rpc_async` returns a `FutureMessage` object and we expose it
   as `torch.distributed.rpc.Future`. From applications perspective,
   they are expecting a `py::object` instead of a `Message`, and we
   do the conversion in the `Future.wait()` pybind method.
2. RPC autograd profiler takes `FutureMessage` and installs
   callbacks to it. The profiler actually only need a `Future<T>`
   and does not care what `T` is.
3. `OwnerRRef` exposes a `getFuture()` API which returns a
   `FutureMessage`. This `FutureMessage` will be marked completed
   when the value referenced by the `OwnerRRef` is ready.
   `OwnerRRef` does not need it to be a Message type either, it
   actually creates an empty `Message` to mark the `Future`.

The above places are using `FutureMessage`, but they don't really
need a `Message`, and `Message` is a communication layer type that
applications or profiler or the RRef shouldn't be aware of.

Another motivation for making this change is that for async RPC
UDF #36071, we are going to allow application to call
`markCompleted` in Python. If we still use `FutureMessage`, then
in the `markCompleted` pybind function, it needs to convert the
provided `py::object` into a specific message type, which is
leaking communication layer code to pybind functions. Even if
this is doable, we will have two entities (RPC agent and pybind
Python frontend) accessing the same request callback logic. This is too messy.

This commit replaces all surface `FutureMessage` with `FutureIValue`,
so that `FutureMessage` is no longer visible from Python land. Note
that this does not cause BC issues, as the Python Future type name
and its API stay intact. Internally, we still have `FutureMessage`
in the communication layer.

Test Plan: Imported from OSS

Reviewed By: xush6528

Differential Revision: D21308887

Pulled By: mrshenli

fbshipit-source-id: 4f574f38e83125081f142813cfdde56119522089",2020-04-30T02:05:29Z,Shen Li
2ed3a73e404bed0795bc18c51af3d92de80dbd97,"[dynamo] treat `torch.device`, `torch.dtype` as constant literal; revise guards to have access to `torch` module (#112426)

Just like e.g. container - list/set of constant literals, these are constant literals.

We follow up to https://github.com/pytorch/pytorch/pull/112416, enforcing that we always use `ConstantVariable` to represent these.

Replace https://github.com/pytorch/pytorch/pull/112284, https://github.com/pytorch/pytorch/pull/112332 as incomplete, in case there is no movement there.

Ought to fix: https://github.com/pytorch/pytorch/issues/109910

We remove old guards special-casing, which fell back on str equality when not having access to `torch` module in `eval`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112426
Approved by: https://github.com/ezyang",2023-11-01T05:28:24Z,Jon Chuang
7362e22f8b780488490bb819d6f7d6e6f7469bcb,"Notify on outdated lintrunner (#96241)

Let users know if they have an outdated version of lintrunner installed on their box

Sets the minimum version to one which uses master as a default mergebase (see https://github.com/pytorch/pytorch/pull/95938)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/96241
Approved by: https://github.com/huydhn",2023-03-08T18:41:31Z,Zain Rizvi
9d37cefcb0c1e867efcfe15c6c0d734ab3b14c84,"Resubmit _int_mm (#96685)

Avoids any changes to gemm_and_bias

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96685
Approved by: https://github.com/drisspg, https://github.com/ngimel",2023-03-27T16:14:07Z,Christian Puhrsch
4883d39c6fd38431bdc60e1db6402e251429b1e1,"Avoid direct reference to at::native::tensor from TensorDataContainer (#47567)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47567

Test Plan: Imported from OSS

Reviewed By: ezyang

Differential Revision: D24822517

Pulled By: iseeyuan

fbshipit-source-id: f69bfc029aae5199dbc63193fc7a5e5e6feb5790",2020-11-18T01:30:37Z,Martin Yuan
a4f0f8b1e95636c302931cf8964aa80b495ecfa3,"[distributed] add base processgroup::options (#53662)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/53662

Add a base processgroup::options so that we can do inheritance and
provide
a universal option API in python

Test Plan: Imported from OSS

Reviewed By: rohan-varma

Differential Revision: D26968856

Pulled By: wanchaol

fbshipit-source-id: 858f4b61b27aecb1943959bba68f8c14114f67d8",2021-03-18T01:38:15Z,Wanchao Liang
f595467e5c6569d4a457033d7ee46c7b9b1d28b1,"Reenable slow gradcheck and make it pass (#80514)

Context: For a while slow gradcheck CI was skipping nearly all tests and this hid the fact that it should've been failing and timing out (10+h runtime for TestGradients). The CI configuration has since been fixed to correct this, revealing the test failures. This PR reenables slow gradcheck CI and makes it pass again.

This PR:
- makes slow and failing tests run in fast gradcheck mode only
- reduce the input size for slow gradcheck only for unary/binary ufuncs (alternatively, skip the test entirely)
- skip entire test files on slow gradcheck runner if they don't use gradcheck (test_ops, test_meta, test_decomp, test_ops_jit)
- reduces the input size for some ops

Follow ups:
1. Investigate slow mode failures https://github.com/pytorch/pytorch/issues/80411
2. See if we can re-enable slow gradcheck tests for some of the slow tests by reducing the sizes of their inputs

The following are failing in slow mode, they are now running in fast mode only.
```
test_fn_fwgrad_bwgrad___rmod___cuda_float64
test_fn_fwgrad_bwgrad_linalg_householder_product_cuda_complex128
test_fn_fwgrad_bwgrad__masked_prod_cuda_complex128
test_fn_fwgrad_bwgrad__masked_prod_cuda_float64
test_fn_fwgrad_bwgrad_linalg_matrix_power_cuda_complex128
test_fn_fwgrad_bwgrad_cat_cuda_complex128
test_fn_fwgrad_bwgrad_linalg_lu_factor_ex_cuda_float64
test_fn_fwgrad_bwgrad_copysign_cuda_float64
test_fn_fwgrad_bwgrad_cholesky_inverse_cuda_complex128
test_fn_fwgrad_bwgrad_float_power_cuda_complex128
test_fn_fwgrad_bwgrad_fmod_cuda_float64
test_fn_fwgrad_bwgrad_float_power_cuda_float64
test_fn_fwgrad_bwgrad_linalg_lu_cuda_float64
test_fn_fwgrad_bwgrad_remainder_cuda_float64
test_fn_fwgrad_bwgrad_repeat_cuda_complex128
test_fn_fwgrad_bwgrad_prod_cuda_complex128
test_fn_fwgrad_bwgrad_slice_scatter_cuda_float64
test_fn_fwgrad_bwgrad_tile_cuda_complex128
test_fn_fwgrad_bwgrad_pow_cuda_float64
test_fn_fwgrad_bwgrad_pow_cuda_complex128
test_fn_fwgrad_bwgrad_fft_*
test_fn_fwgrad_bwgrad_zero__cuda_complex128
test_fn_gradgrad_linalg_lu_factor_cuda_float64
test_fn_grad_div_trunc_rounding_cuda_float64
test_fn_grad_div_floor_rounding_cuda_float64
```

Marks the OpInfos for the following ops that run slowly in slow gradcheck as `fast_gradcheck` only (the left column represents runtime in seconds):
```
0  918.722  test_fn_fwgrad_bwgrad_nn_functional_conv_transpose3d_cuda_float64
1  795.042  test_fn_fwgrad_bwgrad_nn_functional_unfold_cuda_complex128
2  583.63  test_fn_fwgrad_bwgrad_nn_functional_max_pool3d_cuda_float64
3  516.946  test_fn_fwgrad_bwgrad_svd_cuda_complex128
4  503.179  test_fn_fwgrad_bwgrad_linalg_svd_cuda_complex128
5  460.985  test_fn_fwgrad_bwgrad_linalg_lu_cuda_complex128
6  401.04  test_fn_fwgrad_bwgrad_linalg_lstsq_grad_oriented_cuda_complex128
7  353.671  test_fn_fwgrad_bwgrad_nn_functional_max_pool2d_cuda_float64
8  321.903  test_fn_fwgrad_bwgrad_nn_functional_gaussian_nll_loss_cuda_float64
9  307.951  test_fn_fwgrad_bwgrad_stft_cuda_complex128
10  266.104  test_fn_fwgrad_bwgrad_svd_lowrank_cuda_float64
11  221.032  test_fn_fwgrad_bwgrad_istft_cuda_complex128
12  183.741  test_fn_fwgrad_bwgrad_lu_unpack_cuda_complex128
13  132.019  test_fn_fwgrad_bwgrad_nn_functional_unfold_cuda_float64
14  125.343  test_fn_fwgrad_bwgrad_nn_functional_pad_constant_cuda_complex128
15  124.2  test_fn_fwgrad_bwgrad_kron_cuda_complex128
16  123.721  test_fn_fwgrad_bwgrad_pca_lowrank_cuda_float64
17  121.074  test_fn_fwgrad_bwgrad_nn_functional_max_unpool3d_cuda_float64
18  119.387  test_fn_fwgrad_bwgrad_rot90_cuda_complex128
19  112.889  test_fn_fwgrad_bwgrad__masked_normalize_cuda_complex128
20  107.541  test_fn_fwgrad_bwgrad_dist_cuda_complex128
21  106.727  test_fn_fwgrad_bwgrad_diff_cuda_complex128
22  104.588  test_fn_fwgrad_bwgrad__masked_cumprod_cuda_complex128
23  100.135  test_fn_fwgrad_bwgrad_nn_functional_feature_alpha_dropout_with_train_cuda_float64
24  88.359  test_fn_fwgrad_bwgrad_mH_cuda_complex128
25  86.214  test_fn_fwgrad_bwgrad_nn_functional_max_unpool2d_cuda_float64
26  83.037  test_fn_fwgrad_bwgrad_nn_functional_bilinear_cuda_float64
27  79.987  test_fn_fwgrad_bwgrad__masked_cumsum_cuda_complex128
28  77.822  test_fn_fwgrad_bwgrad_diag_embed_cuda_complex128
29  76.256  test_fn_fwgrad_bwgrad_mT_cuda_complex128
30  74.039  test_fn_fwgrad_bwgrad_linalg_lu_solve_cuda_complex128
```
```
0  334.142  test_fn_fwgrad_bwgrad_unfold_cuda_complex128
1  312.791  test_fn_fwgrad_bwgrad_linalg_lu_factor_cuda_complex128
2  121.963  test_fn_fwgrad_bwgrad_nn_functional_max_unpool3d_cuda_float64
3  108.085  test_fn_fwgrad_bwgrad_diff_cuda_complex128
4  89.418  test_fn_fwgrad_bwgrad_nn_functional_max_unpool2d_cuda_float64
5  72.231  test_fn_fwgrad_bwgrad___rdiv___cuda_complex128
6  69.433  test_fn_fwgrad_bwgrad___getitem___cuda_complex128
7  68.582  test_fn_fwgrad_bwgrad_ldexp_cuda_complex128
8  68.572  test_fn_fwgrad_bwgrad_linalg_pinv_cuda_complex128
9  67.585  test_fn_fwgrad_bwgrad_nn_functional_glu_cuda_float64
10  66.567  test_fn_fwgrad_bwgrad_lu_cuda_float64
```
```
0  630.13  test_fn_gradgrad_nn_functional_conv2d_cuda_complex128
1  81.086  test_fn_gradgrad_linalg_solve_triangular_cuda_complex128
2  71.332  test_fn_gradgrad_norm_cuda_complex128
3  64.308  test_fn_gradgrad__masked_std_cuda_complex128
4  59.519  test_fn_gradgrad_div_no_rounding_mode_cuda_complex128
5  58.836  test_fn_gradgrad_nn_functional_adaptive_avg_pool3
```

Reduces the sizes of the inputs for:
- diff
- diag_embed

Pull Request resolved: https://github.com/pytorch/pytorch/pull/80514
Approved by: https://github.com/albanD",2022-07-21T20:42:35Z,soulitzer
40ada91161ca73952f4fda4cfec7c4710070b061,"[PyTorch][easy] Fix borrowing from optional in binary_cross_entry_with_logits

Pull Request resolved: https://github.com/pytorch/pytorch/pull/78266

Saves a refcount bump.

Differential Revision: [D36650626](https://our.internmc.facebook.com/intern/diff/D36650626/)

Approved by: https://github.com/jbschlosser",2022-05-31T16:47:06Z,Scott Wolchok
2268cae9c7952b71a6b42feae327dcebd0fba6f1,atan2 implementation,2014-11-07T19:19:58Z,soumith
e2dc1fc715d3d6add9eba3fe0a23cf277c7df66d,"Add a bitwise NOT operator for integer and Boolean types (CPU).

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/22283

Test Plan: Imported from OSS

Differential Revision: D16183576

Pulled By: colesbury

fbshipit-source-id: 2e539fab8ff885dddb9bff334d1d784b28d65b8f",2019-07-10T19:03:07Z,Hong Xu
dbb31a2984fa616b4bb6fac7abb2a06ec0533eb1,"[Inductor] Add triton.autotune support for user defined triton kernels with constant/simple grids (#112228)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112228
Approved by: https://github.com/jansel",2023-10-27T07:48:33Z,Oguz Ulgen
14b409952135256de2d37f2fa2cf7ce8a3f6d84a,"[FSDP2] support torch._foreach_copy_(float8) for fully_shard(Float8Linear) (#135955)

this PR unblocks unit test with single Float8Linear module. It fixes following error
```
torch._foreach_copy_(foreach_copy_dsts, all_gather_inputs)
[rank0]:E0913 13:44:29.829000 2179476 torch/testing/_internal/common_distributed.py:671] RuntimeError: ""foreach_tensor_copy"" not implemented for 'Float8_e4m3fn'
```

Differential Revision: [D63961071](https://our.internmc.facebook.com/intern/diff/D63961071)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/135955
Approved by: https://github.com/vkuzo, https://github.com/eqy",2024-10-07T03:03:23Z,Wei Feng
070169e4d0cb6ddab61b83cffe4cf93807a39087,"[ATen] tensor.contiguous() -> tensor.expect_contiguous (#55022)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/55022

Replace tensor.contiguous() with tensor.expect_contiguous in aten::narrow_copy

Test Plan: CI

Reviewed By: edvgha

Differential Revision: D27453866

fbshipit-source-id: c5a6e64ccca4cf52cb879dfb02fd4c451fb397cb",2021-04-01T18:20:42Z,Hao Lu
54056c1705bdfd6e8b077b1cd501c6f36d0e192a,"Update cudnn_frontend to 0.7.3 (#93272)

Updating cudnn_frontend to 0.7.3 To enable CUDNN 8.7 integration

Pull Request resolved: https://github.com/pytorch/pytorch/pull/93272
Approved by: https://github.com/malfet, https://github.com/Skylion007",2023-01-30T20:45:00Z,atalman
6e1e27fc4e36edc7d8dad602de7e8250ad16073b,"[inductor] Refactor pre-grad passes into inductor.fx_passes (#99130)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99130
Approved by: https://github.com/ngimel",2023-04-15T03:16:34Z,Jason Ansel
31808dcdd8ec2c10699e6df7fd305f724c9ece8b,"[RELAND] [CUDA graphs] Make CUDAGeneratorImpl capturable (ci-all edition) (#48694)

Summary:
Resubmission of https://github.com/pytorch/pytorch/pull/47989 with attempted fix for the unexpected context creation that caused revert (https://github.com/pytorch/pytorch/pull/47989#issuecomment-736689145).

Submitting from a ci-all branch because the failing test isn't public.

Diffs relative to master should be the same as https://github.com/pytorch/pytorch/pull/47989 's approved diffs, aside from the fix itself https://github.com/pytorch/pytorch/pull/48688/commits/a5c80f63d3aae66d691bbafc726615e9be8e68be.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/48694

Reviewed By: mruberry

Differential Revision: D25291431

Pulled By: ngimel

fbshipit-source-id: 8c27f85c64eecaf1f5cb925020fa6d38a07ff095",2020-12-04T20:33:13Z,Michael Carilli
7557a993ab9b38583d5fdbebd25d927f31ffb7a9,"Allow dataloader to accept a custom memory pinning function (#14171)

Summary:
Currently, the `pin_memory_batch` function in the dataloader will return a batch comprised of any unrecognized type without pinning the data, because it doesn't know how.

This behavior was preventing us from overlapping data prefetching in Mask-RCNN, whose custom `collate_fn` returns a custom batch type.

The present PR adds the ability for the user to pass a `pin_fn` alongside any custom `collate_fn` to handle such custom types.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/14171

Differential Revision: *********

Pulled By: soumith

fbshipit-source-id: ca965f9841d4a259b3ca4413c8bd0d8743d433ab",2018-11-23T16:08:35Z,Michael Carilli
fa799132d82c3c48253aaf7d3ee3a8c5e007350d,"[MPS] Better error message for `slow_conv2d_forward` (#86303)

Error `Could not run 'aten::_slow_conv2d_forward' with arguments from the 'MPS' backend.` is very misleading as usually this method is only invoked if input is on CPU but weights are on MPS device.
Raise a more user friendly error in this case

Add test to `test_invalid_conv2d` to check for those conditions.

Fixes https://github.com/pytorch/pytorch/issues/77931

Pull Request resolved: https://github.com/pytorch/pytorch/pull/86303
Approved by: https://github.com/kulinseth",2022-10-06T15:38:57Z,Nikita Shulga
1dbf44c00d4f77e72fcdf48ecf7628c3bcac4f96,Add SmoothL1Loss to functional,2017-01-15T19:11:32Z,Adam Paszke
1ece1ab6c2c5488b8475c70681aebddbdb9579ba,"[ci] print rerun stacktraces for pytest (#86831)

example: https://github.com/pytorch/pytorch/actions/runs/3238428826/jobs/5306808276

Pull Request resolved: https://github.com/pytorch/pytorch/pull/86831
Approved by: https://github.com/huydhn",2022-10-14T17:31:31Z,Catherine Lee
1d53d0756668ce641e4f109200d9c65b003d05fa,"Add docs to CI (#24435)

Summary:
Stacked PRs
 * #24445 - [jit] Misc doc updates #2
 * **#24435 - [jit] Add docs to CI**

This integrates the [doctest](http://www.sphinx-doc.org/en/master/usage/extensions/doctest.html) module into `jit.rst` so that we can run our code examples as unit tests. They're added to `test_jit.py` under the `TestDocs` class (which takes about 30s to run). This should help prevent things like #24429 from happening in the future. They can be run manually by doing `cd docs && make doctest`.

* The test setup requires a hack since `doctest` defines everything in the `builtins` module which upsets `inspect`
* There are several places where the code wasn't testable (i.e. it threw an exception on purpose). This may be resolvable, but I'd prefer to leave that for a follow up. For now there are `TODO` comments littered around.
](https://our.intern.facebook.com/intern/diff/16840882/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/24435

Pulled By: driazati

Differential Revision: D16840882

fbshipit-source-id: c4b26e7c374cd224a5a4a2d523163d7b997280ed",2019-08-21T04:39:09Z,davidriazati
9d80969fa47a4f921f43adc60762cbec895bdaf8,"Retry brew and gem installation in trunk ios workflow (#96970)

Per title, I don't want to see network flakiness like this https://github.com/pytorch/pytorch/actions/runs/4439991996/jobs/7793213476 ever again :P
Pull Request resolved: https://github.com/pytorch/pytorch/pull/96970
Approved by: https://github.com/clee2000",2023-03-16T21:30:57Z,Huy Do
65e87052e7f8db05fa7c692cb9ea46a8424f4b54,TH: fix speed issue due to calls to THTensor_nElement(),2014-03-06T18:02:51Z,Ronan Collobert
b0833533a779d656cd6e9f6d103956ff105e7ef5,"Update internal code for torch.linalg.solve (#56613)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/56613

Replace linalg_solve_helper with `lu_stub` + `lu_solve_stub`.
Once `lu_stub` and `lu_solve_stub` have cuSOLVER-based codepath,
`torch.linalg.solve` will have it as well.

Test Plan: Imported from OSS

Reviewed By: agolynski

Differential Revision: D28379394

Pulled By: mruberry

fbshipit-source-id: b47f66bc1ee12715da11dcffc92e31e67fa8c8f6",2021-05-13T23:55:56Z,Ivan Yashchuk
dee43798d762d5282f373598a8f97aaf0f19ae07,"Revert ""Create a new Ubunutu 22.04 (jammy) build for platform010 (#77591)""

This reverts commit 71d82917f45920d096da421f33ce0057cace611f.

Reverted https://github.com/pytorch/pytorch/pull/77591 on behalf of https://github.com/zengk95 due to this is breaking linux slow test on trunk",2022-06-18T00:10:06Z,PyTorch MergeBot
3a4c0900c737fe73f900f0d21fc21d972f9bbd2e,"Reland 3 of Merge more symbolic meta kernels and symint changes from branch (#86795)

Take 3
Contains:
- symintification of split*
- floor support on SymFloat
- pad_backward, gather, scatter meta
Pull Request resolved: https://github.com/pytorch/pytorch/pull/86795
Approved by: https://github.com/z-a-f",2022-10-17T02:09:40Z,albanD
8f627fc658542db4a9442a3e57791371463f03d2,"CharTensor should be signed (#5512)

CharTensor is actually int8_t which is signed",2018-03-02T16:34:10Z,Sam Gross
bb157dd4eb21019ff396e5ce123d115914dd04a5,"Make methods of internal file_obj visible from StreamWrapper (#71653)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/71653

Test Plan: Imported from OSS

Reviewed By: NivekT

Differential Revision: D33718749

Pulled By: ejguan

fbshipit-source-id: f3a8244f22ca37049b8678afa0e329b23c957a9d
(cherry picked from commit a4d12ca48ec153ad5f058152e7df4a9a1421b184)",2022-01-25T14:57:41Z,Erjia Guan
6894bb0a853982e2ef39d0d0bf8160bfc17dd3c2,"Remove on_green and mandatory_only (#96400)

Our default behavior is on green, and currently the two main modes are on green and force.
Surprisingly, both these flags are pretty much not used anywhere.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/96400
Approved by: https://github.com/huydhn",2023-03-09T17:38:49Z,Catherine Lee
****************************************,"[ROCm] Update triton pin to fix libtanh issue (#125396)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/125396
Approved by: https://github.com/pruthvistony, https://github.com/nmacchioni",2024-05-30T19:26:58Z,Prachi Gupta
e392d428b11128bcf203a4fe5b5f24b04bf545c9,"Allowing TaskGroups to carry remote nets (#14342)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/14342

Sometimes, when we are creating a TaskGroup, we are in fact creating a TaskGroup for a distributed job. In some cases, we may want to register a few nets as ""remote"" to a TaskGroup. The remote net should have sufficient attributes on where they should be executed later on.

This diff adds the remote net attribute to the TaskGroup class. It exposes two minimal functionalities: adding a remote net, and getting all remote nets added to a TaskGroup.

Reviewed By: d4l3k

Differential Revision: D13188320

fbshipit-source-id: efe947aec30817e9512a5e18be985713b9356bdc",2018-11-27T21:31:59Z,Hassan Eslami
8269f7b6527fa16b9fc914e80a0c3a8f0964b58b,"Delete redundant THC_API on THCStorage_new (#30312)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/30312

It's not necessary because it's already defined in the header.

Signed-off-by: Edward Z. Yang <<EMAIL>>

Test Plan: Imported from OSS

Differential Revision: D18762363

Pulled By: ezyang

fbshipit-source-id: 418bf355d460dd171ac449559f20bf55415e54ae",2019-12-03T18:41:25Z,Edward Yang
0bd2955f158d8041657c447d82944d159e07b801,"Memory leak from bsr_scatter_mm_indices_data argument cache (#112301)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112301
Approved by: https://github.com/cpuhrsch, https://github.com/pearu",2023-11-01T21:04:32Z,Andrew M. James
d68df54269552dc8312b7c2eba23be20c255c59f,"OpInfo: fill_ (#59138)

Summary:
Reference: https://github.com/pytorch/pytorch/issues/54261

Pull Request resolved: https://github.com/pytorch/pytorch/pull/59138

Reviewed By: ngimel

Differential Revision: D28776451

Pulled By: mruberry

fbshipit-source-id: 2e8e9f1805ec7d900223ea749a4a0b86a1bedb54",2021-05-29T07:32:55Z,kshitij12345
5ecb966e0ff383d65531c8f6de23e704b9cafc54,"Add ciflow-tracking issue to pytorch-probot (#64125)

Summary:
Doesn't do anything yet...

Pull Request resolved: https://github.com/pytorch/pytorch/pull/64125

Reviewed By: zhouzhuojie

Differential Revision: D30620283

Pulled By: malfet

fbshipit-source-id: 91869d35c1b70a55e32261d2c32fb0136ec33960",2021-09-01T00:33:11Z,Nikita Shulga
b45b9673a16ba02fdeaee4ada9ed30c9b757a44a,"Fixes clang format (#36787)

Summary:
Fixes clang format.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/36787

Differential Revision: D21084603

Pulled By: mruberry

fbshipit-source-id: 7e29da135f9a2aa126cb68640e33c1914fd570e3",2020-04-17T07:40:23Z,Mike Ruberry
c7db642a72d80facf9c8a17f42ac0bb04d04723f,"Adding collective quantization API (#62142)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/62142

Created wrapper that takes the collective op and a quantization type as an arguments. It quantize the input, performs the collective op, and and perform dequantization

Test Plan:
Tested through distributed_gloo_fork.
e.g., buck test mode/dev-nosan caffe2/test/distributed:distributed_nccl_fork -- test_all_to_all_quantized

Reviewed By: wanchaol

Differential Revision: D29682812

fbshipit-source-id: 79c39105ff11270008caa9f566361452fe82a92e",2021-08-09T15:09:49Z,Marjan Fariborz
13538c88b38b8fe038e344ab0a588bb2462193c9,"[1/n] Consolidate `replicate` and `DDP`: setup ufmt for `distributed.py` (#96597)

As we already enabled ufmt for composable APIs in https://github.com/pytorch/pytorch/pull/90873, it seems a good idea to enable ufmt for other distributed APIs as well. This change setup ufmt for DDP.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96597
Approved by: https://github.com/rohan-varma",2023-03-17T03:12:23Z,Charlie Yan
a2cb9b7331524eb0d9e62b38c57d38d8725cbc1b,"Flip triton kernel default layout constraint to ""needs_fixed_stride_order"" (#135581)

This is to match the default layout constraint for custom operators. By
default, Inductor should match the stride order of inputs to a triton
kernel.

Test Plan:
- existing tests

Pull Request resolved: https://github.com/pytorch/pytorch/pull/135581
Approved by: https://github.com/eellison
ghstack dependencies: #135530",2024-09-10T15:00:03Z,rzou
2ac34b98ea0e5c2b975b9d144e8589a9c9e829d3,"[auto] Update onnx to 490c4c6 - fix build dependency between onnx-operators.proto and (onnx/onnx#934)
https://github.com/onnx/onnx/commit/490c4c6ca99bceed0499bab3535b43917dea0537",2018-05-13T03:14:44Z,onnxbot
f1f3bd8c36d20dde9ef6f69f294cc365a2c5eb2c,"Back out ""Revert D31005792: [NCCL] Init dummy NCCL comms in constructor"" (#65883)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/65883

Original commit changeset: d8e962b8aab6
ghstack-source-id: 139836954

Test Plan: ci

Reviewed By: zhaojuanmao

Differential Revision: D31299350

fbshipit-source-id: 9ad5c8fa17f7038ba579cb1eda6d9271ac07a130",2021-10-08T22:58:27Z,Rohan Varma
a732bbea232fa32191f259d7cb15e9fabb6c2926,"[meta] Add meta support for fft ops (#79311)

As per title
Pull Request resolved: https://github.com/pytorch/pytorch/pull/79311
Approved by: https://github.com/ezyang",2022-06-13T01:56:42Z,kshitij12345
cbb76eae0479faa629c5eb1164916f183209fe07,add int and long tests for abs and fix recursive bug in abs,2016-09-10T20:40:27Z,soumith
2dd23ebfdb4a4581ebeebe9b51002652ead11369,"Add support for multi output nodes in partial eval graph stitching (#66097)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/66097

Adding logic to generate runtime shapes for nodes with multi-outputs. It is generalizing existing flow of looking at a node, getting its shape graph, inlining it, and adding a mapping from the output to the new value in the stitched shape compute graph to loop over multiple outputs.

Test Plan: Imported from OSS

Reviewed By: navahgar

Differential Revision: D31797468

Pulled By: eellison

fbshipit-source-id: 2c182b71a46b36d33f23ad35b89790a4a5d4471c",2021-10-20T23:09:33Z,Elias Ellison
8b4c4875818ab2193e27fb004aa052f755c4ca0c,"Fix AOTInductor complication on ROCM (#134522)

Summary:
Original PR (https://github.com/pytorch/pytorch/pull/124123) is broken by cpp_builder refactoring

So resubmit it to fix

Test Plan: Test with command here: https://www.internalfb.com/phabricator/paste/view/P1549765548

Differential Revision: D61827208

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134522
Approved by: https://github.com/frank-wei",2024-08-29T21:59:04Z,Zhuoran Zhao
d3f98b5ffc991d22df9b2783ec43a5148b824e6d,"Add matrix power (#11421)

Summary:
vishwakftw Your patch needed some updates because the default native function dispatches changed from `[function, method]` to `[function]`. The CI was run before that change happened so it still shows green, but the internal test caught it.

I did some changes when rebasing and updating so I didn't just force push to your branch. Let's see if this passes CI and internal test. If it does, let me know if you want me to force push to your branch or use this PR instead.

Note to reviewers: patch was already approved at #10068 .

cc yf225
Pull Request resolved: https://github.com/pytorch/pytorch/pull/11421

Differential Revision: D9733407

Pulled By: SsnL

fbshipit-source-id: cf2ed293bb9942dcc5158934ff4def2f63252599",2018-09-08T22:21:14Z,Tongzhou Wang
3e1859959a3f720bb5f5e47c3ca15fb3cbfae4da,"Updating submodules

Summary:
GitHub commits:

https://github.com/facebook/fbthrift/commit/2b59db7359f555b87f1565d2024057369ed8bdc2
https://github.com/facebook/folly/commit/242186f5ff988bd9a243401833614acdace99f25
https://github.com/facebook/mcrouter/commit/4bf6682fe6e1a8497489333b83c69b0fbab78aac
https://github.com/facebook/rocksdb/commit/fe238e54389934ed3d1b96e226d5bed62eb29188
https://github.com/facebook/watchman/commit/e8cf50093e18b2bc40a2c4e5fd6f32f181e05e0a
https://github.com/pytorch/fbgemm/commit/17d9a609f24c2829de7be6ad10977dee35915347

Test Plan: n/a

Reviewed By: yns88

fbshipit-source-id: 3c1030ebc3768a827583b50c2d47fba494816943",2020-04-30T18:51:17Z,svcscm
ee777a7c3c3c263bb6dca15a61e8755bb0dab231,"docs: Add docstring for torch.masked._ops.logaddexp (#113206)

logaddexp is not a reduction and normalization, so
_apply_docstring_templates cannot be used to add a docstring.

Fixes https://github.com/pytorch/pytorch/issues/113082

Also fix another misspelling.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/113206
Approved by: https://github.com/cpuhrsch",2023-11-08T22:45:32Z,BJ Hargrave
cc49f5abd33857d3e143c03303150c0c14e09142,"[Re-land 90265] [inductor] add conv_transpose2d unary fusion for cpu in inference mode (#91954)

Re-land https://github.com/pytorch/pytorch/pull/90265.
Depend on internal ideep upgrade.
[Update]: internal ideep upgrade issue is resolved in https://github.com/pytorch/pytorch/pull/92239.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/91954
Approved by: https://github.com/jgong5, https://github.com/desertfire",2023-01-31T06:18:34Z,chunyuan
291cbf09263c786b0e6277ebef8927d00bc130b5,"[functorch] Introduce ForceLocalDispatchKeySet

IncludeDispatchKeyGuard and ExcludeDispatchKeyGuard were getting too
confusing",2021-12-13T14:58:09Z,Richard Zou
a6fa6a6cda06038b9b5a800c4c4eae70974d0684,"[fx minimizer] Add an option to minimizer to allow return all intermediate results (#57279)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/57279

Added an option ""return_intermediate"". If true, when building the submodule we want to run , we will replace the output with all the nodes, so that intermediate results of all the nodes will be returned as output.

This is recommended to use with `run_node()` function.

Test Plan: `buck test glow/fb/nnpi/lowering:net_min_tests`

Reviewed By: khabinov

Differential Revision: D27913887

fbshipit-source-id: 5a3eab02da05214fb9adeb25656c267b58075b1d",2021-04-29T20:45:10Z,Shiyan Deng
874f9bd5090c1f57afdfbd454952ed4f71d210cc,"[FX] Gate FXGraphDrawer on whether pydot is installed (#65088)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/65088

Test Plan: Imported from OSS

Reviewed By: khabinov

Differential Revision: D30967951

Pulled By: jamesr66a

fbshipit-source-id: dba2f13a47889b3d4187de925b4fe74ee90b7f79",2021-09-16T17:00:59Z,James Reed
130881f0e37cdedc0e90f6c9ed84957aee6c80ef,"Delete build_caffe2.sh, replace with build_libtorch.py (#10508)

Summary:
delete build_caffe2.sh, replace with build_libtorch.py as suggested by peter (and copy-pasted from his draft PR).  This ensures that all consumers of the torch CMake file go through as unified a path as possible.

In order to change the surrounding infrastructure as little as possible, I made some tweaks to enable build_pytorch_libs.sh to generate the test binaries relative to the current directory, rather than hardcoding to pytorch/build.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10508

Differential Revision: D9354398

Pulled By: anderspapitto

fbshipit-source-id: 05b03df087935f88fca7ccefc676af477ad2d1e9",2018-08-16T14:57:00Z,Anders Papitto
e85f3fccb33d041621e81616f7404a9d991e8681,"Fix relying on UB in test_data_parallel_nested_output (#11092)

Summary:
We shouldn't reply on plain `dict` ordering. Example failure: https://ci.pytorch.org/jenkins/job/pytorch-builds/job/pytorch-linux-xenial-cuda8-cudnn6-py3-test1/8417/console
Pull Request resolved: https://github.com/pytorch/pytorch/pull/11092

Reviewed By: ezyang

Differential Revision: D9583274

Pulled By: SsnL

fbshipit-source-id: ba80b96648c98c24c2ec5fa6fd9aa566c095cce7",2018-08-30T19:56:16Z,Tongzhou Wang
adb2b380baf1d78a5e4a48d8a6999b94aaeff403,"[quant][graphmode][fx] qconfig_dict support more types of configurations (#44856)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/44856

Support following format of qconfig_dict
```python
qconfig_dict = {
    # optional, global config
    """": qconfig?,

    # optional, used for module and function types
    # could also be split into module_types and function_types if we prefer
    ""object_type"": [
      (nn.Conv2d, qconfig?),
      (F.add, qconfig?),
      ...,
    ],

    # optional, used for module names
    ""module_name"": [
      (""foo.bar"", qconfig?)
      ...,
    ],

    # optional, matched in order, first match takes precedence
    ""module_name_regex"": [
      (""foo.*bar.*conv[0-9]+"", qconfig?)
      ...,
    ]
    # priority (in increasing order): global, object_type, module_name_regex, module_name
    # qconfig == None means fusion and quantization should be skipped for anything
    # matching the rule
}
```

Test Plan: Imported from OSS

Reviewed By: vkuzo

Differential Revision: D23751304

fbshipit-source-id: 5b98f4f823502b12ae2150c93019c7b229c49c50",2020-09-23T20:53:52Z,Jerry Zhang
8e78a1b084d3856da6406ab8627c1343cc48e16f,"[Resubmit] Fix for incorrect usage of logging in torch/distributed/distributed_c10d.py (#52757)

Summary:
Resubmit of https://github.com/pytorch/pytorch/pull/51739
Fixes https://github.com/pytorch/pytorch/issues/51428

Pull Request resolved: https://github.com/pytorch/pytorch/pull/52757

Reviewed By: cbalioglu

Differential Revision: D26646843

fbshipit-source-id: df4962ef86ea465307e39878860b9fbbcc958d52",2021-04-06T18:31:17Z,Szymon Migacz
eea680a3549c3de1cad73d9eb75ddd417fb1e711,"[auto] Update onnx to 31ca96c - Microbenchmark for encoding+decoding ModelProto and GraphProto with a single operator (#609)
https://github.com/onnx/onnx/commit/31ca96ca3331d05884a71c38975d34870eb9c81d",2018-03-15T03:21:09Z,onnxbot
b62827b81a1a1ba618b57e924c09947f482c6990,"[Quant][devs] Separated implementations for quantized & non-quantized tensors in fill_ (#71939)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/71939

This PR is part of a series of PRs addressing https://github.com/pytorch/pytorch/issues/54150,
related to using dispatcher for calls to quantized backends as opposed to if/else conditionals.
This particular PR separates the calls to quantized & non-quantized backends for fill_
using a dispatcher.

Differential Revision:
D33827371
D33827371

Test Plan: Imported from OSS

Reviewed By: jerryzh168

Pulled By: dzdang

fbshipit-source-id: d034f83de844ef777a2d71e5464f582cba634550
(cherry picked from commit 9f38385051e41a32ccc631dc3354caa03188649b)",2022-02-03T15:38:39Z,dzdang
ce409d8f500b2f916af3976f8e0030ae5f257363,"docs: clarify smooth l1 == l1 when beta == 0 (#70673)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/68558.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/70673

Reviewed By: albanD

Differential Revision: D33430267

Pulled By: jbschlosser

fbshipit-source-id: db92187ff4f2799b19a6c4a5a6b653e9211c3aca",2022-01-05T22:34:18Z,Jake Tae
6d222116a13d55c2aa2211938f9df686535fbd51,"[Documentation] Minor  rendering issue (#84856)

There is a Rendering issue with the docstring of nn.GELU.

Hope this fixes the [issue.](https://pytorch.org/docs/stable/generated/torch.nn.GELU.html)

cc: @malfet
Pull Request resolved: https://github.com/pytorch/pytorch/pull/84856
Approved by: https://github.com/kit1980",2022-09-13T00:29:50Z,Abhijit Deo
008a8c9720183d7bf8b00bf64d8d21c62270089f,Implement lgamma function.,2017-04-20T23:24:14Z,ethanluoyc
dfd822d756067f114b73d4d455c1d8c467e108ad,"Fix deserialization for UpsamplingBilinear2d (#101248)

Fixes #100935 , adding handling for the recompute_scale_factor field. I would be happy to write a test for this, but might need some advice on where it should go/how to reliably reproduce the given issue. I'd also be happy to iterate on the proposed changes.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/101248
Approved by: https://github.com/albanD",2023-05-12T15:40:13Z,ts
aa7e27fa7049c9e6621c093ead7f13258ca53e14,"Emit Loop Condition as Separate Block (#21611)

Summary:
Emit loop condition as a separate block in loops, then inline them before conversion to SSA. This is needed for breaks & continues where we will inline the condition block after the continue pass and before the break pass.

I also considered emitting a prim::For and a prim::While, but i think it's easier to just have one pathway.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/21611

Differential Revision: *********

Pulled By: eellison

fbshipit-source-id: de17c5e65f6e4a0256a660948b1eb630e41b04fb",2019-06-12T05:00:02Z,Elias Ellison
314a502eb04c6382e2cc9af0573533efba54109d,"Revert ""Reland ""[C10] PG observability hooks. (#108815)"" (#110907)""

This reverts commit 7678cd22af46c9df4fb47a409d3e8ad71a6127ea.

Reverted https://github.com/pytorch/pytorch/pull/110907 on behalf of https://github.com/huydhn due to Sorry for reverting this, but macos job in trunk starts failing after this https://hud.pytorch.org/pytorch/pytorch/commit/7678cd22af46c9df4fb47a409d3e8ad71a6127ea ([comment](https://github.com/pytorch/pytorch/pull/110907#issuecomment-1756497387))",2023-10-11T00:23:42Z,PyTorch MergeBot
18cf30fb2ab4c80491a56127789bf513885dc9e5,"[Inductor] preserve AliasedLayout on View (#96948)

Fix https://github.com/pytorch/pytorch/issues/96728

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96948
Approved by: https://github.com/Chillee",2023-03-17T01:16:19Z,Jiong Gong
cba79f48726a5e74e8a0e96b3a70cc8ccff60880,"Revert *********: [wip] Replace Type dispatch with ATenDispatch

Differential Revision:
*********

Original commit changeset: fcfaea0b5480

fbshipit-source-id: 9bca7ebb91d7a3609b86663089140d7c5a33f58d",2019-06-20T00:28:17Z,Ailing Zhang
f54eac7eba9ce60fe03032fd8cd96cfcf2ef6e6a,Add flag and warning for Python 2.7 users on Windows (#6499),2018-04-12T03:06:51Z,peterjc123
22a77d7b92b62a114830986299e4f593ca868c5f,"[warning] Disable broken assert (#71778)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/71778

This assert was broken (never triggers).  Fixing the assert leads to test failures.  We need to fix those test failures, so a FIXME has been filed.  The urgency is avoiding the compile time failure that will come with enabling `-Wstring-conversion` as an error.

Test Plan: CI Pass

Reviewed By: r-barnes

Differential Revision: D33754171

fbshipit-source-id: 834b070b94007af583d0fc6c022f23b6703f3fbc
(cherry picked from commit ac8f905fb11c75b470b964f5ff5157e79d4c4b60)",2022-01-25T19:28:24Z,Nolan O'Brien
768b7c0dee34b614ab1cd8f89c69ec7d86c19c88,Static linking against libstdc++ in Binary Build mode,2017-07-14T20:27:20Z,Soumith Chintala
23fad9111e816c5ee0e6c327ff9f067399b0b3a6,"[quant][graphmode][fx] Add additional_qat_module_mapping (#46344)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/46344

Test Plan: Imported from OSS

Reviewed By: vkuzo

Differential Revision: D24317438

fbshipit-source-id: f9e73aeb4c7a107c8df0bae8319464e7d5d7275b",2020-10-22T20:02:55Z,Jerry Zhang
1bf3dc51ae68bccb672145daeb4715bf08b78556,"[JIT] Add `__prepare_scriptable__` duck typing to allow replacing nn.modules with scriptable preparations (#45645)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/45072

As discussed with zdevito gchanan cpuhrsch and suo, this change allows developers to create custom preparations for their modules before scripting. This is done by adding a `__prepare_scriptable__` method to a module which returns the prepared scriptable module out-of-place. It does not expand the API surface for end users.

Prior art by jamesr66a: https://github.com/pytorch/pytorch/pull/42244

cc: zhangguanheng66

Pull Request resolved: https://github.com/pytorch/pytorch/pull/45645

Reviewed By: dongreenberg, ngimel

Differential Revision: D24039990

Pulled By: zhangguanheng66

fbshipit-source-id: 4ddff2d353124af9c2ef22db037df7e3d26efe65",2020-11-10T16:58:02Z,Donny Greenberg
018c3420b80c7bbdb87caa91f0430a90988872a4,"Make dim, numel, element_size into prim ops (#36551)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/36551

Before, those ops were special cased in the jit codegen but that blocks our unboxing refactoring.
Instead, make those regular prim ops.
ghstack-source-id: 102081858

Test Plan: waitforsandcastle

Differential Revision: D21009196

fbshipit-source-id: b90320fce589fc0553f17582b66a5a05d0fd32d1",2020-04-14T09:15:49Z,Sebastian Messmer
977a66fe8807f4b64cba021825723a8366355ee2,"switch DimensionNode's base from TsNode to Node

Switching the base of DimensionNode to TsNode so it can be reused by XLA and other backends.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/75916
Approved by: https://github.com/alanwaketan",2022-04-16T02:57:29Z,Nikolay Korovaiko
081b56fd41de591669bd1f61d4aadd7eb65ec335,"Improve readability of cuda_lazy_init (#80788)

This PR cleans up the implementation of `cuda_lazy_init.cpp` and improves its readability. No behavioral changes are introduced.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/80788
Approved by: https://github.com/ezyang",2022-07-04T16:47:09Z,Can Balioglu
02dd1f38f289ec1cadef086f7bc0fcc15666a928,"[pytorch] CUDA kernel for torch.cat on contiguous tensors with wide loads (#102815)

This PR creates a CUDA kernel for `CatArrayBatchedCopy` that makes use of vectorized memory loads to maximize HBM bandwidth. It also simplifies the kernel code by removing the path handling not-contiguous inputs.  It gets called when the following conditions are met:

- tensors are contiguous
- input data types are of 32bit and 64 bit
- all the input are aligned to 16 bytes boundary

We tested on a larger set of problem sizes and there is net gain for 32 bit types and marginal gain for 64 bit types. Based on our analysis the 32 bit cats are by far the dominant kernel being called.

Results:

<img width=""1320"" alt=""Screenshot 2023-06-02 at 8 10 21 AM"" src=""https://github.com/pytorch/pytorch/assets/23515689/6f083f7c-2e1a-4513-a994-e0cb072d9b5d"">

The SASS Code confirms using the wide loads for input tensors and the stores to global memory are unrolled to maximize oversubscription:

<img width=""1648"" alt=""Screenshot 2023-06-02 at 8 16 29 AM"" src=""https://github.com/pytorch/pytorch/assets/23515689/10325ee6-d3a0-402a-af0d-29cd1a32813b"">

Test Code:

```python
import sys

import torch

l_inputs = [
    ((1024,), 0, 2, 100),
    ((4096,), 0, 2, 100),
    ((16384,), 0, 4, 100),
    ((32000,), 0, 8, 100),
    ((128 * 1024,), 0, 2, 100),
    ((256 * 1024,), 0, 3, 100),
    ((1 * 1024 * 1024,), 0, 2, 100),
    ((4 * 1024 * 1024,), 0, 2, 100),
    ((16 * 1024 * 1024,), 0, 2, 100),
    ((32 * 1024 * 1024,), 0, 2, 100),
    ((128 * 1024 * 1024,), 0, 2, 50),
    ((64, 256), 0, 4, 100),
    ((400, 400), 0, 2, 100),
    ((640, 1080), 0, 2, 100),
    ((128, 4096), 1, 2, 100),
    ((512, 512), 1, 2, 100),
    ((699, 713), 1, 2, 100),
    ((1024, 1024), 1, 2, 100),
    ((2000, 1000), 1, 2, 100),
    ((4096, 4096), 1, 2, 100),
    ((16384, 16384), 1, 2, 50),
    ((384, 256, 16), 1, 2, 100),
    ((400, 200, 13), 1, 2, 100),
    ((128, 64, 256), 0, 2, 100),
    ((512, 256, 256), 1, 2, 100),
    ((512, 1024, 1024), 2, 2, 10),
    ((1024, 512, 1024), 2, 2, 10),
    ((1024, 1024, 512), 2, 2, 10),
    ((128, 64, 64, 32), 0, 2, 50),
    ((128, 64, 128, 16), 1, 2, 50),
    ((100, 45, 45, 32), 3, 2, 50),
    ((128, 32, 256, 32), 3, 2, 50),
]

prof_inputs = [
    ((1234567,), 0, 2, 5),
    ((16 * 1024 * 1024,), 0, 3, 5),
    ((1013, 1013), 0, 2, 5),
    ((1024, 1024), 1, 2, 5),
    ((69, 74, 128), 0, 2, 5),
    ((128, 128, 128), 2, 2, 5),
]

def generate_tensors(dim_tuple, cat_type, num_tensors):
    if cat_type in [torch.int8, torch.int32, torch.int64]:
        l_tensors = [
            torch.randint(
                high=torch.iinfo(cat_type).max,
                size=dim_tuple,
                dtype=cat_type,
                device=""cuda"",
            )
        ] * num_tensors
        return l_tensors
    else:
        l_tensors = [
            torch.randn(dim_tuple, dtype=cat_type, device=""cuda"")
        ] * num_tensors
        return l_tensors

def test_simple_cat(
    dim_tuple, cat_dim: int, num_tensors: int, iterations: int, cat_type
):
    torch.cuda.synchronize()

    # Allocate a tensor equal to L2 cache size on A100 GPUs
    l2_cache_flusher = torch.empty(
        int(80 * (1024**2)), dtype=torch.float, device=""cuda""
    )

    # All the tensors in the list get read and written once
    total_MB = 2 * num_tensors
    for dim in dim_tuple:
        total_MB *= dim
    total_MB /= 1024 * 1024

    # Get the number of bits per element
    if cat_type in [torch.int8, torch.int32, torch.int64]:
        total_MB *= torch.iinfo(cat_type).bits / 8
    else:
        total_MB *= torch.finfo(cat_type).bits / 8

    l_tensors = generate_tensors(dim_tuple, cat_type, num_tensors)
    c = torch.cat(l_tensors, dim=cat_dim)
    torch.cuda.synchronize()

    # Measure correctness
    l_tensors_cpu = []
    for t in l_tensors:
        l_tensors_cpu.append(t.detach().to(""cpu""))
    c_cpu = torch.cat(l_tensors_cpu, dim=cat_dim)
    c_cpu_dev = c.detach().to(""cpu"")

    if not torch.equal(c_cpu, c_cpu_dev):
        missmatches = torch.count_nonzero(torch.abs(c_cpu - c_cpu_dev))
        print(""Error; num missmatches for {0} = {1}"".format(dim_tuple, missmatches))
        return

    # Measure a few iterations
    l_ev_start = [torch.cuda.Event(enable_timing=True)] * iterations
    l_ev_stop = [torch.cuda.Event(enable_timing=True)] * iterations

    l_cat_times = []
    torch.cuda.synchronize()
    for i in range(iterations):
        l2_cache_flusher.zero_()
        torch.cuda._sleep(1_000_000)

        l_ev_start[i].record()
        c = torch.cat(l_tensors, dim=cat_dim)
        l_ev_stop[i].record()
    torch.cuda.synchronize()

    for i in range(iterations):
        t_cat = l_ev_start[i].elapsed_time(l_ev_stop[i]) / 1000
        l_cat_times.append(t_cat)

    min_cat_time = min(l_cat_times)

    # return bandwidth in GB/s
    estimated_bw_GBps = total_MB / min_cat_time / 1024
    return estimated_bw_GBps

def main(argv):
    if len(argv) > 0:
        if ""profile"" in str(argv[0]):
            for l_input in prof_inputs:
                gbps = test_simple_cat(
                    l_input[0], l_input[1], l_input[2], l_input[3], torch.float
                )
                print(
                    ""Bandwidth (GB/s) for {0} fp32 | {1:.2f}"".format(
                        (l_input[0], l_input[1]), gbps
                    )
                )
            return

    for l_input in l_inputs:
        gbps_int8 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.int8
        )
        gbps_fp16 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.float16
        )
        gbps_fp32 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.float32
        )
        gbps_int32 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.int32
        )
        gbps_fp64 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.float64
        )
        gbps_long = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.long
        )

        print(
            ""Bandwidth (GB/s) for {0} int8;fp16;fp32;int32;fp64;long|{1:.2f}|{2:.2f}|{3:.2f}|{4:.2f}|{5:.2f}|{6:.2f}"".format(
                (l_input[0], l_input[1]),
                gbps_int8,
                gbps_fp16,
                gbps_fp32,
                gbps_int32,
                gbps_fp64,
                gbps_long,
            )
        )

if __name__ == ""__main__"":
    main(sys.argv[1:])
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/102815
Approved by: https://github.com/ngimel, https://github.com/malfet",2023-06-02T22:33:24Z,Valentin Andrei
ae6dd20ba7725ea7c10d759f82891d86eb724c11,"[cuDNN V8 API] (reopen 2) Allow the number of kernels profiled under torch.backends.cudnn.benchmark = True to be limitedCudnnv8 benchmark limit (#78299)

Reopen of #77002 to address comments by @malfet

CC @ngimel @ptrblck
Pull Request resolved: https://github.com/pytorch/pytorch/pull/78299
Approved by: https://github.com/ngimel",2022-07-07T23:25:23Z,Eddie Yan
76abbbe3179a83b05629ac3010817c39ad102242,"Adding output_size to to_padded_tensor (#76640)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/76640

- Adding output_size argument to to_padded_tensor
- Modified add_padding_kernelLauncher and kernels to iterate over padded tensor batch size instead of nested tensor batch size
- No fast path for CPU version

Test Plan:
buck test mode/dev-nosan  //caffe2/test:nested

Performance test using N1763981:

{F728168808}

Reviewed By: cpuhrsch

Differential Revision: D36056902

fbshipit-source-id: d6df2939d6649128a7f43a2ef32d227870a8e583
(cherry picked from commit 09465f36f09d4d74c9b3303981d8cce0c7c1092a)",2022-05-03T18:17:59Z,Michael Anderson
d985cf46f18d202756dc9ce2ddbebd9507b29d43,Add workaround to fix include warnings in Python 2 builds. (#6716),2018-04-24T19:30:19Z,Zachary DeVito
6db3853eebb3b2d2e4c68adcb0d04d3259da5910,"Add doc for torch.cond (#108691)

We add a doc for torch.cond. This PR is a replacement of https://github.com/pytorch/pytorch/pull/107977.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/108691
Approved by: https://github.com/zou3519",2023-10-03T16:31:26Z,ydwu4
1d34f33d009d9d1e90ba913a6debbc0c518ab9ff,"Scale XBLOCK in triton reduction configs to avoid hitting max grid (#128826)

Scale XBLOCK size in triton_config_reduction to avoid hitting maxGridSize limits.

This issue was observed in gpt-fast examples with large sequence length:
Reproducer: https://gist.github.com/jataylo/8a0ba922fbf68e345d360a418b48b9f1

`RuntimeError: Triton Error [HIP]:  Code: 9, Messsage: invalid configuration argument`

Co-authored-by: Jason Ansel <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/128826
Approved by: https://github.com/jansel, https://github.com/nmacchioni",2024-08-05T19:34:36Z,Jack Taylor
1dbbef6b4823b3eee441102fb250849e8efaec9d,"Fix crash in blob deallocation

Summary: We have to use copy constructor in Concat when copying non-primitive types

Reviewed By: Yangqing

Differential Revision: D6002883

fbshipit-source-id: 0aebc955079975bb6423291589ed09ce0660acf3",2017-10-11T01:53:13Z,Ilia Cherniavskii
d84173c025313143c20b8c7c5ffe8b2a24c74216,"[export] fix unlifting of custom class constants (#117979)

we didn't have a test covering this case, add one.

Aside: we should invest in actually unit testing the lifting/unlifting passes, both separately and also against each other. I have a diff cooking for that.

Differential Revision: [D52962180](https://our.internmc.facebook.com/intern/diff/D52962180/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/117979
Approved by: https://github.com/avikchaudhuri
ghstack dependencies: #115222, #117978",2024-01-22T19:14:47Z,suo
c51827b8ce956b166490c8e1540e538ddfb8a4d7,"[ez] Hash update to reuse issues again (#113961)

The bot that creates the issue got changed, but the search did not, so it wasn't finding old PRs and was just making new ones.

This PR makes it reuse PRs again instead of making a new one everytime.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/113961
Approved by: https://github.com/huydhn",2023-11-17T19:06:38Z,Catherine Lee
f684e44fd6ba99fb7497b9fc5e29b53504a02f11,"Revert ""Reduce pytest prints (#117069)""

This reverts commit 40dbd567e04483c671f9c897171bf9d1e7162b68.

Reverted https://github.com/pytorch/pytorch/pull/117069 on behalf of https://github.com/clee2000 due to need to handle timeout expired better ([comment](https://github.com/pytorch/pytorch/pull/117069#issuecomment-1901270953))",2024-01-19T23:07:51Z,PyTorch MergeBot
b56939dae117afd61937c78b92b0ad247dd27176,"Annotate more InstructionTranslator (#131680)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/131680
Approved by: https://github.com/zou3519
ghstack dependencies: #131676",2024-07-24T18:25:03Z,Oguz Ulgen
a7fba7de22688f56f03a048676ff1404d417fb00,"Convert StoreTestUtils to Gtest (#43382)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/43382

StoreTestCommon defines standard helper functions that are used by all of our Store tests. These helpers currently throw exceptions upon failure, this PR changes them to use gtest assertions instead.
ghstack-source-id: 111690833

Test Plan: Tested the 2 PR's above this on devvm

Reviewed By: jiayisuse

Differential Revision: *********

fbshipit-source-id: 9e116cf2904e05ac0342a441e483501e00aad3dd",2020-09-10T00:13:36Z,Omkar Salpekar
209c6f9ab57f7c6b3d79ac69a23b2b38bfcfcf63,"Move device type init from BackendSelect to backend kernels (#37402)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37402

Previously, BackendSelect kernels did just-in-time device type
initialization by calling `LegacyTypeDispatch.initForDispatchKey()`
with a computed dispatch key. Here we move the initialization to
the backend kernels themselves, where we can call the device-
specific initializer directly.

Putting this up to run tests on it, but a couple questions remain:
* why were only BackendSelect kernels doing this initialization?
  Not all factory ops appear there, nor are all the ops that do
  appear there factory ops. Currently we generate init code for
  exactly the BackendSelect ops, but the choice should be better
  motivated.
* the previous scheme maps HIP to its own legacy type dispatch
  entry, but the logic assumes it's exclusive with CUDA, and no
  ops appear to mention HIP explicitly, so the new logic doesn't
  expose a static entry point for it. Needs to be verified.

Test Plan: Imported from OSS

Differential Revision: D21282974

Pulled By: bhosmer

fbshipit-source-id: cd46eb788596948e0572a15fac0f8b43feca5d75",2020-05-05T01:42:28Z,Basil Hosmer
c4bf196334bea0579b4ce6083b292be8712c7561,"Strided masked reduction: mean (2nd try) (#67088)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/67088

Stack from [ghstack](https://github.com/ezyang/ghstack):
* __->__ #67088

Test Plan: Imported from OSS

Reviewed By: anjali411

Differential Revision: D32070264

Pulled By: cpuhrsch

fbshipit-source-id: 08a91550dd24fb0f51abf06591a0e26186c4f9f9",2021-11-01T23:10:03Z,Pearu Peterson
e05ee4c421a86eb9976346bb0e962a42ee7b9c52,"Remove BUILD_NAMEDTENSOR macros (#30894)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/30894

This PR begins the process of removing BUILD_NAMEDTENSOR macros. There
will be followups.

Reasons for removing the macros:
- BUILD_NAMEDTENSOR is always on and has been on since pytorch 1.3.0.
- Since we don't test building without it, it is useless to keep around.
- Code becomes nicer to read without the macros

Reasons for not removing the macros:
- potential for feature flagging

Now, I argue against needing to feature flag. The main reason why we
might want to feature flag is if we need to disable the feature.
We'd need a fast switch to disable the feature if someone discovers
in the future that named tensors caused some regression in some existing workflows.

In https://github.com/pytorch/pytorch/pull/25798, I did a variety of
macro- and micro- benchmarks to determine the performance impact of named
tensors on regular tensors.

[The
microbenchmarks](https://github.com/pytorch/pytorch/pull/25798#issuecomment-529014810)
were not very stable, and running the
microbenchmarks for more iterations doesn't actually help because the
noise is not distributed in a nice way. Instead of microbenchmarks I ran
a [profiler
(perf)](https://github.com/pytorch/pytorch/pull/25798#issuecomment-555707645)
to estimate how much overhead named tensors add to unnamed code. I
estimated the overhead to be less than 100ns for `add` and even smaller
for `mm`; there are ways to optimize even futher if we find this to be a
problem.

[Initial
macrobenchmarks](https://github.com/pytorch/pytorch/pull/25798#issuecomment-530539104)
were also not very stable. I ran imagenet for some number of epochs. To
make them more stable, I got rid of the data loading (which seemed to
vary between runs). [In some benchmarkers without data
loading](https://github.com/pytorch/pytorch/pull/25798#issuecomment-562214053),
we can see that the results are less noisy now. These results support
no noticeable regressions in speed.

Test Plan: - wait for CI

Differential Revision: D18858543

Pulled By: zou3519

fbshipit-source-id: 08bf3853a9f506c6b084808dc9ddd1e835f48c13",2019-12-10T15:49:14Z,Richard Zou
b3bce01e264f0022a106b16d7e5038a38a72bb4d,"Have add_video use NamedTemporaryFile directly (#20223)

Summary:
address comment in #16196
https://github.com/pytorch/pytorch/pull/16196/files#r278676986

cc orionr
Pull Request resolved: https://github.com/pytorch/pytorch/pull/20223

Reviewed By: natalialunova

Differential Revision: D15261528

Pulled By: orionr

fbshipit-source-id: 1aebcc6cb1c9313d890c5b506973855ebc63fb3b",2019-05-08T21:57:40Z,Tzu-Wei Huang
8cc57593b90f0196a54e5c08c7344d725ed5cd4e,"remove redundant trailing semicolons in StorageImpl.h (#97658)

remove redundant trailing semicolons in StorageImpl.h

Pull Request resolved: https://github.com/pytorch/pytorch/pull/97658
Approved by: https://github.com/kit1980, https://github.com/malfet",2023-04-25T21:04:22Z,mikey dagitses
ef156f913655c44056202ab43322f398bd7387aa,"Enable retry support for MPS tests (#94070)

Here is an example https://hud.pytorch.org/pytorch/pytorch/commit/d7c71a95b68dfd3b126acd021e05b18b5fa38f03 where the MPS test was flaky but not retried.  Thus it failed.  We probably would want to support retry on MPS tests like the rest of the CI
Pull Request resolved: https://github.com/pytorch/pytorch/pull/94070
Approved by: https://github.com/clee2000",2023-02-03T22:21:31Z,Huy Do
5ed5dfd915aa8988b6d30c3c424a31935f3a8002,"Don't run ios jobs on forks (#91112)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/91112
Approved by: https://github.com/huydhn",2022-12-20T19:13:11Z,clee2000
7bbd9befed0d64bb1c241cf4cdbfdd25486f12ce,"Improve example for ``torch.mode()`` (#115308)

Fixes #89820 and improves the documentation.

Co-authored-by: Sam Gross <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/115308
Approved by: https://github.com/colesbury",2024-02-03T00:13:20Z,Linus
90040afc4494ca716ef2fa5e11552b973b05d8c0,Fix cwrap option filtering,2017-01-15T16:25:32Z,Adam Paszke
a40e0a7f2d235586237122e92561eef13169bb8f,"Add torch.version.git_version (#18299)

Summary:
Fixes: https://github.com/pytorch/pytorch/issues/18293
cc: colesbury
Pull Request resolved: https://github.com/pytorch/pytorch/pull/18299

Differential Revision: *********

Pulled By: soumith

fbshipit-source-id: cdb48ef37c8869713a9a43ea0da08e1bed9279a2",2019-03-26T02:54:27Z,"Gao, Xiang"
95d0b3199b2e0eb0516e439c8aa1a94b62113e1e,"Back out ""[ONNX] Fix an issue that optimizations might adjust graph inputs unexpectedly. (#61280)"" (#64004)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/64004

Pull Request resolved: https://github.com/pytorch/pytorch/pull/63904

Fixes *********

Test Plan: *********

Reviewed By: msaroufim

Differential Revision: *********

fbshipit-source-id: 6262901a78ca929cecda1cf740893139aa26f1b4",2021-08-26T19:48:01Z,Meghan Lele
ea4fbb2e5e646fd28c1bbfabe128ab2404a969c5,"[StaticRuntime] Replace hashtable based workspace with vector<IValue> (#45892)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45892

Previously we were using hashtable (`std::unordered_map` in OSS, `folly::F14FastMap` in fb) for workspace, a container for all the IValues in the graph. Hashtable based lookups can be expensive. This diff replaces the hashtable with `std::vector` and extra bookkeepings are introduced to keep track of the indices of graph inputs/outputs in `StaticRuntime` and op inputs/outputs in `ProcessedNode`.

Reviewed By: dzhulgakov

Differential Revision: D24098763

fbshipit-source-id: 337f835ee144985029b5fa2ab98f9bcc5e3606b6",2020-10-08T16:48:37Z,Hao Lu
d6ff78fd00eab35435f2e0531e8a74f62d8071e9,"fix an over-indented return in trace_module

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23358

Differential Revision: D16519010

Pulled By: Krovatkin

fbshipit-source-id: a7e4225b70e915d91c74874e3eca9bcb87baf84c",2019-07-29T18:11:44Z,Nikolay Korovaiko
9afdf017dc99a9d948b0daa754f77159ba6dcc4a,"Add force_on_cpu test to win cuda10.2 on GHA (#65094)

Summary:
Part of migrating from Circle.

Once we get a successful force_on_cpu test, we can move it to trunk only.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/65094

Reviewed By: seemethere

Differential Revision: D31086289

Pulled By: janeyx99

fbshipit-source-id: e1d135cc844d51f0b243b40efb49edca277d9de8",2021-09-21T18:08:44Z,Jane Xu
3fccc0446cc4f78f97e2b227a06e196ffee488d4,"Add dtensor and fsdp/2d tests to inductor_distributed CI (#114642)

Smuggle important and not too slow tests to run on this trunk job,
instead of just on the periodic job where they currently reside.
 - test_dtensor_compile took 70sec, test_fsdp_2d_parallel took 198sec
   locally

As a follow up, organize the distributed-mgpu tests better and maybe
rename this job to reflect its more 'general dist mgpu'

Pull Request resolved: https://github.com/pytorch/pytorch/pull/114642
Approved by: https://github.com/wanchaol, https://github.com/malfet",2023-11-27T23:30:03Z,Will Constable
438cc79f5a384a9508de5c462705f3cbb42c645b,"Improve more the error message with explicit recommendation

Following feedback.
The new message looks like:
```
# torch.nn.intrinsic.modules._FusedModule:
  - Is public: it is inside the module's (`torch.nn.intrinsic.modules`) `__all__`
  - Does NOT look public: because it starts with `_` (`_FusedModule`)
  - You can do either of these two things to fix this problem:
    - To make it NOT public: remove it from the modules's (`torch.nn.intrinsic.modules`) `__all__`
    - To make it look public: remove the `_` at the beginning of the name
# torch.ao.nn.sparse.quantized.dynamic.linear.LinearBlockSparsePattern:
  - Is public: it is an attribute that does not start with `_` on a module that does not have `__all__` defined
  - Does NOT look public: because its `__module__` attribute (`torch.ao.nn.sparse.quantized.utils`) is not within the torch library or does not start with the submodule where it is defined (`torch.ao.nn.sparse.quantized.dynamic.linear`)
  - You can do either of these two things to fix this problem:
    - To make it NOT public: either define a `__all__` for `torch.ao.nn.sparse.quantized.dynamic.linear` or add a `_` at the beginning of the name
    - To make it look public: make sure the `__module__` is properly set and points to a submodule of `torch.ao.nn.sparse.quantized.dynamic.linear`

```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/76261
Approved by: https://github.com/NivekT",2022-04-25T13:59:55Z,Alban Desmaison
a5a1f0a6b14c1d5ae1088028065ab33b1f2ff5c7,"[executorch hash update] update the pinned executorch hash (#114996)

This PR is auto-generated nightly by [this action](https://github.com/pytorch/pytorch/blob/main/.github/workflows/_update-commit-hash.yml).
Update the pinned executorch hash.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/114996
Approved by: https://github.com/pytorchbot",2023-12-02T03:57:43Z,PyTorch UpdateBot
67416a2996349a2339328cac0f7e54c7d3b3c1d9,"[c10d] Introduce a util for detecting DMA connectivity among devices (#129510)

This PR introduces `_detect_dma_connectivity` - a utility for detecting DMA connectivity among devices.

The ""DMA connectivity"" in this context is more stringent than the ability to perform memory copy without CPU involvement. We define it as the ability for a device to issue load/store instructions and perform atomic operations on memory that resides on connected devices. The ability translates to the ability to run most aten GPU operations with operands backed by remote memory. `_detect_dma_connectivity` can help PyTorch and its users to determine whether certain DMA-based optimizations are possible.

`_detect_dma_connectivity` takes a `(device_type, connection_type)` pair and returns a matrix describing the connectivity. Connectivity detectors are statically registered on a `(device_type, connection_type)` basis. This PR implements the detector for `(CUDA, ""nvlink"")`. Later, detectors for pairs such as `(ROCM, ""infinity_fabric"")` can be introduced.

Example:

```python3
>>> from torch._C._autograd import DeviceType
>>> from torch._C._distributed_c10d import _detect_dma_connectivity
>>> connectivity = _detect_dma_connectivity(DeviceType.CUDA, ""nvlink"")
>>> for row in connectivity.matrix:
...     print(row)
...
[0, 18, 18, 18, 18, 18, 18, 18]
[18, 0, 18, 18, 18, 18, 18, 18]
[18, 18, 0, 18, 18, 18, 18, 18]
[18, 18, 18, 0, 18, 18, 18, 18]
[18, 18, 18, 18, 0, 18, 18, 18]
[18, 18, 18, 18, 18, 0, 18, 18]
[18, 18, 18, 18, 18, 18, 0, 18]
[18, 18, 18, 18, 18, 18, 18, 0]
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/129510
Approved by: https://github.com/weifengpy",2024-06-26T23:43:42Z,Yifu Wang
40d826074546558f6665a4c118335a7725503cac,"[ROCm] remove caffe2 from hipify (#137157)

- Remove all ""MasqueradingAsCUDA"" files and classes.
- Do not rename ""CUDA"" classes to ""HIP"".

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137157
Approved by: https://github.com/eqy",2024-10-05T12:48:54Z,Jeff Daily
e6b361bd47ea719d6b3830a6898679d3d281dd38,"Refactor dynamo benchmark test script to reduce duplication (#96096)

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96096
Approved by: https://github.com/desertfire",2023-03-06T20:56:24Z,Edward Z. Yang
b0e2ed4d67f698ccb63d4e99ad1e82a6c8a4b68a,"removing some macros (#120314)

Summary: Will be making some changes in the surrounding code, they are going to be easier without macros

Differential Revision: D54001770

Pull Request resolved: https://github.com/pytorch/pytorch/pull/120314
Approved by: https://github.com/zhxchen17",2024-03-06T22:06:05Z,Denis Yaroshevskiy
ec48280afa495bba540d161b186bbd8b0e6c5cf4,"Improve error message when input is not in the right format (#25928)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/25928

Improved error message
ghstack-source-id: 89854172

Test Plan:
if given the input of wrong dimension, the message earlier
```
[QConv2D] each dimension of output tensor should be greater than 0
```
message now
```
Given groups=1, weight of size 20, 5, 5, 1, expected input (NHWC) 10, 1, 32, 32 to have 1 channels, but got 32 channels instead
```

Reviewed By: jianyuh

Differential Revision: D17287290

fbshipit-source-id: d91573d6d69f2a5e0e615ffbd47a0bd233636a0b",2019-09-11T20:31:59Z,Daya Khudia
b69c685c4a9f4d44427ebd1b4b45bbd7859e1430,"try to find cudnn header in /usr/include/cuda (#31755)

Summary:
With fedora negativo17 repo, the cudnn headers are installed in /usr/include/cuda directory, along side with other cuda libraries.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/31755

Differential Revision: D19697262

Pulled By: ezyang

fbshipit-source-id: be80d3467ffb90fd677d551f4403aea65a2ef5b3",2020-02-04T22:05:48Z,nihui
f5b68e74d75c38d5e2044fc6b62112181080bb3f,"Revert D25574962: [pytorch][PR] Updated derivative rules for complex svd and pinverse

Test Plan: revert-hammer

Differential Revision:
D25574962 (https://github.com/pytorch/pytorch/commit/9955355853a1c189a4a79209f82d39393b4be010)

Original commit changeset: 832b61303e88

fbshipit-source-id: d73f77f3e51b0f535dad6d21c5bebf8d41a6bfbd",2020-12-17T08:58:25Z,Mike Ruberry
cf811d2fb365fa7f8543dcb5f71863c1a812f180,"retain undefined tensors in backward pass (#41490)

Summary:
Leave undefined tensors / None returned from custom backward functions as undefined/None instead of creating a tensor full of zeros. This change improves performance in some cases.

**This is BC-Breaking:** Custom backward functions that return None will now see it potentially being propagated all the way up to AccumulateGrad nodes. Potential impact is that .grad field of leaf tensors as well as the result of autograd.grad may be undefined/None where it used to be a tensor full of zeros. Also, autograd.grad may raise an error, if so, consider using allow_unused=True ([see doc](https://pytorch.org/docs/stable/autograd.html?highlight=autograd%20grad#torch.autograd.grad)) if it applies to your case.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/41490

Reviewed By: albanD

Differential Revision: D22578241

Pulled By: heitorschueroff

fbshipit-source-id: f4966f4cb520069294f8c5c1691eeea799cc0abe",2020-07-17T19:41:06Z,Heitor Schueroff de Souza
6c56e1ce2b8d850eb8f51731ecc8be415160e02b,"Use fmt::format in NCCLUtils and ProcessGroupNCCL instead of c10::str (#107268)

Fixes #64604

Pull Request resolved: https://github.com/pytorch/pytorch/pull/107268
Approved by: https://github.com/fduwjj",2023-10-20T05:26:47Z,Andrei Gheorghe
4cb73f5a4c841ffa0f20a27d920173e16549c4f7,"Allow for string literal return during symbolic tracing (#47618)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47618

Test Plan: Imported from OSS

Reviewed By: jamesr66a

Differential Revision: D24870422

Pulled By: ansley

fbshipit-source-id: 41c56c2f4f1f7bb360cea0fb346f6e4d495f5c2b",2020-11-11T16:52:06Z,Ansley Ussery
0e2330d84ce13ac4b92ce75c5dc9141ac7d25b92,"fix lint (#119395)

Summary: as title

Test Plan: lint

Differential Revision: D53532399

Pull Request resolved: https://github.com/pytorch/pytorch/pull/119395
Approved by: https://github.com/tugsbayasgalan, https://github.com/malfet",2024-02-07T19:06:41Z,Michael Suo
855b7e28ee24c2af98557f4dc1952b912bfc32df,"START_IND & END_IND macros, removed unnecessary computation in updateGradInput",2017-09-20T17:19:19Z,SsnL
f3bf46e801dec2637751224fd6e27fbf97453bc6,"enable bf16 emb (#94163)

Merge https://github.com/pytorch/pytorch/pull/89199 and https://github.com/pytorch/pytorch/pull/91949 into one PR.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/94163
Approved by: https://github.com/jianyuh, https://github.com/malfet, https://github.com/jgong5",2023-02-06T07:11:37Z,haozhe.zhu
7ab6f56ca72a5f1b8c7b0c73e3947c0af3f998c8,"[quant][core] Add quantize/dequantize ops for decomposed quantized Tensor representation (#87093)

Summary:
Added q/dq implementation for out of core (decomposed) quantized Tensor representation, meaning that
instead of storing quantization parameters (e.g. scale/zero_point) in a separate quantized Tensor object, we will store
quantization parameters in the argument of operators.
```
quantize(float32_tensor, scale, zero_point, dtype) -> int8_tensor
dequantize(int8_tensor, scale, zero_point, dtype) -> float32_tensor
```

Test Plan:
python test/test_quantization.py TestQuantizedTensor.test_decomposed_quantize
python test/test_quantization.py TestQuantizedTensor.test_decomposed_dequantize

Reviewers:

Subscribers:

Tasks:

Tags:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/87093
Approved by: https://github.com/dzdang, https://github.com/z-a-f",2022-10-25T17:39:24Z,Jerry Zhang
bf1b8adee661ae72bd294bc3ab98fb8e1de8eff0,"Turn static inline into static function (#139843)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/139843
Approved by: https://github.com/ezyang",2024-11-07T23:58:18Z,cyy
43ea782af37ffbcd3c583b0f63cc2817c19429f7,"Multiprocessing support for NT (#110292)

Fixes #110161

Allows NTs to be used in DataLoaders with `num_workers > 1`.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/110292
Approved by: https://github.com/cpuhrsch, https://github.com/albanD",2023-10-10T18:22:52Z,Joel Schlosser
4307ccde99fbec3afcd7cbee1a0c2db0070187cc,"Move ONNX's TorchModelType to pytorch_test_common to fix circ. dep. (#115353)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/115353
Approved by: https://github.com/BowenBao",2023-12-11T19:14:53Z,Thiago Crepaldi
97509c8eb2aef89c8bf8429018aa6ce4a8269fde,"Revert ""[Inductor][Quant] Fix PT2E Dynamic Quant regression (#125207)""

This reverts commit 3da949b0fbe91e802d30e00165141d1390621d71.

Reverted https://github.com/pytorch/pytorch/pull/125207 on behalf of https://github.com/huydhn due to Sorry for reverting your change but I think there is a land race with the change https://hud.pytorch.org/pytorch/pytorch/commit/33e6791645b5950b0f39301f55b8a4a79c0ca847 ([comment](https://github.com/pytorch/pytorch/pull/124041#issuecomment-2101766558))",2024-05-09T01:34:19Z,PyTorch MergeBot
711be82951739e8c3cf7e5d2f81d22e93451fa11,"Make optimize a thread_local flag

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23170

Test Plan: Imported from OSS

Differential Revision: D16441912

Pulled By: suo

fbshipit-source-id: a33485178a329d54e41e364c4f14950f88481c55",2019-07-25T06:05:48Z,Michael Suo
f43351718165579d908150e785815d1e65d439ab,"[dynamo][decorator] Support disable on nn modules (#124185)

Fixes https://github.com/pytorch/pytorch/issues/123979

Pull Request resolved: https://github.com/pytorch/pytorch/pull/124185
Approved by: https://github.com/weifengpy, https://github.com/yoyoyocmu",2024-04-16T16:24:14Z,Animesh Jain
bdd9ef19815520f0680b6218a21045fdb5894da0,"Support RowWiseSparseAdam on GPU (#35404)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/35404

Implement RowWiseSparseAdam on CUDA

Reviewed By: xw285cornell

Differential Revision: D20650225

fbshipit-source-id: 5f871e2f259e362b713c9281b4d94534453995cf",2020-07-31T17:41:57Z,Yan Xie
827a00cf63df1ddcf1c776713925c0c1ee0d7c2b,"Support interface python assignment as an attribute (#26734)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/26734

This PR added the python assignment for interface as an attribute in the
module, it enables any object that implicitly inheriting the specific
interface to be able to be assigned to the interface type in python.

Serialization support for interface/class assignment will be done in the
follow up PR

Test Plan: Imported from OSS

Differential Revision: D17742708

Pulled By: wanchaol

fbshipit-source-id: a0a2d8c74b60ed3fa6c05e1b0d49b7ad1abc670b",2019-10-04T00:13:02Z,Wanchao Liang
6cc15c1a22fb98709d930585aa5781799f9638db,"Simplify typeid SFINAE (#12706)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/12706

If both branches are valid C++ code independent from the type passed in, then we can just use if/else inside of a constexpr function
to decide between the cases. Only if one branch would be invalid code (say because type T doesn't have a default constructor), we'd
need ""constexpr if"" or SFINAE.

Reviewed By: ezyang

Differential Revision: D10400927

fbshipit-source-id: 16d9855913af960b68ee406388d6b9021bfeb34a",2018-10-22T18:22:56Z,Sebastian Messmer
c0c43aee9c967921f4686dbefea53a6143a3ddcc,"Ignore cudaErrorPeerAccessAlreadyEnabled.

If we call cudaDeviceEnablePeerAccess for the same devices twice, it
will return cudaErrorPeerAccessAlreadyEnabled. THCudaCheck treats that
as an error and fails, whereas it really only indicates that peer access
has already been enabled. In which case we shouldn't fail.",2015-02-05T12:10:19Z,Dominik Grewe
c06dfd7c26102ac2436ca25609c92fa794e972ca,"[fx2trt] Check input device in TRTModule (#63893)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/63893

Add a check to ensure all the inputs are on cuda device.

Test Plan: CI

Reviewed By: kflu, houseroad

Differential Revision: D30525265

fbshipit-source-id: 6e50b70fd535defc1f802d51e8bb991b2dd73741",2021-08-25T17:22:17Z,Shiyan Deng
75ce0406207d0a6db82fed93b3a9893948e7796c,"[TensorExpr] Allow for 'keepdim' argument in aten::mean in NNC's external call. (#68756)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/68756

That fixes some warnings in our tests.

Test Plan: Imported from OSS

Reviewed By: navahgar

Differential Revision: D32600952

Pulled By: ZolotukhinM

fbshipit-source-id: 548eaf3659e20795cce44d8f57e77f4a47d44d98",2021-11-30T08:03:21Z,Mikhail Zolotukhin
5c79046d39a0d585744b8c195bc3500a657707f7,Use persistent tensor to store exp_inf (part of optimizer's state) (#1152),2017-03-31T14:30:31Z,Tudor Berariu
76b290344f917ee0b9e1c69863ae04354a298dd2,"Defer setting capturable in optimizer variable (#123497)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/123497
Approved by: https://github.com/anijain2305
ghstack dependencies: #123496",2024-04-06T02:52:51Z,Michael Lazos
975ff6624b098171c6ce8222fbb0cd407b5b5f26,"DOC: backport doc build fix from 1.7, tweak link (#47349)

Summary:
xref gh-46927 to the 1.7 release branch

This backports a fix to the script to push docs to pytorch/pytorch.github.io. Specifically, it pushes to the correct directory when a tag is created here. This issue became apparent in the 1.7 release cycle and should be backported to here.

Along the way, fix the canonical link to the pytorch/audio documentation now that they use subdirectories for the versions, xref pytorch/audio#992. This saves a redirect.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47349

Reviewed By: zhangguanheng66

Differential Revision: *********

Pulled By: seemethere

fbshipit-source-id: c778c94a05f1c3e916217bb184f69107e7d2c098",2020-11-19T17:48:49Z,mattip
d8732b3b43983dd16d8f3c4bfd3970adc4c03b89,"Gradle build with offline dependencies (#29262)

Summary:
https://github.com/pytorch/pytorch/issues/29159

Introducing GRADLE_OFFLINE environment variable to use '--offline' gradle argument which will only use local gradle cache without network.

As it is cache and has some expiration logic - before every start of gradle 'touch' files to update last access time.

Deploying new docker images that includes prefetching to gradle cache all android dependencies, commit with update of docker images: https://github.com/pytorch/pytorch-ci-dockerfiles/commit/df07dd56812a42105e04b0ba4267912e1bf0834e

Reenable android gradle jobs on CI (revert of https://github.com/pytorch/pytorch/pull/29606/commits/54e6a7eede660b953c27e566eb4a9cdcab86b25d)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/29262

Differential Revision: *********

Pulled By: IvanKobzarev

fbshipit-source-id: 8fb0b54fd94e13b3144af2e345c6b00b258dcc0f",2019-11-13T06:46:38Z,Ivan Kobzarev
136dadd689981a334985f2029f6d3e747c36da5c,"fix norrow_copy correctness issue for non-contiguous input for cpu path (#91789)

Fix https://github.com/pytorch/pytorch/issues/91690.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/91789
Approved by: https://github.com/jgong5, https://github.com/lezcano",2023-01-06T03:48:38Z,XiaobingSuper
c5bee1ec4f261f3e250ea3ee974a0e13fb79de3b,"[PyTorch] Parallelize gelu via tensoriterator (#58950)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/58950

Use tensor iterator's API to set grain size in order to parallelize gelu op.
ghstack-source-id: 130947174

Test Plan: test_gelu

Reviewed By: ezyang

Differential Revision: D28689819

fbshipit-source-id: 0a02066d47a4d9648323c5ec27d7e0e91f4c303a",2021-06-09T23:07:12Z,Kimish Patel
6e86a40694f03c66680201c01c13347eff38a951,"Revert ""[Dynamo] Check for __bool__ attribute before accessing it (#120943)""

This reverts commit dd7aeedb72f8a96d0f168308292e0d41c095f01b.

Reverted https://github.com/pytorch/pytorch/pull/120943 on behalf of https://github.com/DanilBaibak due to Broken trunk ([comment](https://github.com/pytorch/pytorch/pull/120943#issuecomment-2063098295))",2024-04-18T06:34:32Z,PyTorch MergeBot
c6e9e9359f5538dfde3a6c4b0ed3c0cb2045b59b,"[Codemod][GleanFbcode] Remove dead includes in caffe2/test (#39023)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/39023

Reviewed By: orionr

Differential Revision: D21702529

fbshipit-source-id: 6945bba95609102409850b105a8a091e33b8acc9",2020-05-27T21:00:34Z,Nikita Shulga
7c2290e7cee76591c1990fa60e4c4c223ab71012,"Better error when module attr is used (#18164)

Summary:
Adds a suggestion to add to __constants__ when a torch.nn.Module attr is accessed
Pull Request resolved: https://github.com/pytorch/pytorch/pull/18164

Differential Revision: D14580060

Pulled By: eellison

fbshipit-source-id: 0c5adc21d7341a5691d4b45930947cb1ba84c8e8",2019-03-23T03:13:02Z,Elias Ellison
183c2071f9b814cad2766ad43089ad8cd0e7259e,"Generate wrap_dim code on derived type rather than base type.
Either should work, but code feels more natural this way.",2017-09-06T19:05:34Z,Gregory Chanan
3d34afa9ae035b4263ee0381fbdce2e3858993ae,"Revert ""Removed python dispatch keys from dispatch key extraction""

This reverts commit 3c1dd4e752162db1d7cc993187da623cec3f0d2b.

Reverted https://github.com/pytorch/pytorch/pull/74971 on behalf of https://github.com/atalman",2022-03-31T21:44:56Z,PyTorch MergeBot
0ae0fac1bb9eb82671d0a70fb76602db3670b3a0,"Clarify, make consistent, and test the behavior of logspace when dtype is integral (#47647)

Summary:
torch.logspace doesn't seem to have explained how integers are handled.
Add some clarification and some test when dtype is integral.

The CUDA implementation is also updated to be consistent with CPU implementation.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47647

Reviewed By: gchanan

Differential Revision: *********

Pulled By: walterddr

fbshipit-source-id: 45237574d04c56992c18766667ff1ed71be77ac3",2021-01-15T20:27:03Z,Hong Xu
92d0700520b80d768bab199fa60c7883f0ef9c3d,"ci: Switch MPS tests to self hosted runners on AWS (#81772)

Signed-off-by: Eli Uriegas <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/81772
Approved by: https://github.com/janeyx99",2022-07-20T16:47:48Z,Eli Uriegas
53b4f6c0f61141ec85a74d9dbf584a22af826e48,"Revert ""[jit] Add c++ stacktraces for jit::ErrorReport (#94842)"" (#95886)

This reverts commit 70029214f300f611e7dd816b5f64426224f6ab96.

It broke some internal tests.

Differential Revision: [*********](https://our.internmc.facebook.com/intern/diff/*********)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/95886
Approved by: https://github.com/malfet, https://github.com/qihqi",2023-03-02T15:38:51Z,David Berard
61db8b64ec45d43144deefd9525d1c2cc716cc83,"Build option USE_NUMA should only show up on Linux. (#23673)

Summary:
(intentionally left blank)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/23673

Differential Revision: D16627453

Pulled By: vincentqb

fbshipit-source-id: df62f1b26901bec6369b5589b98124165f40e6f1",2019-08-09T15:10:22Z,Hong Xu
74c3dcd1d2ba6de0e7fdd1969e1a09333e375c84,"Revert D23725053: [pytorch][PR] change self.generator to generator

Test Plan: revert-hammer

Differential Revision:
D23725053 (https://github.com/pytorch/pytorch/commit/a011b86115541365ebd55598f85ff9a42a6875d8)

Original commit changeset: 89706313013d

fbshipit-source-id: 035214f0d4298d29a52f8032d364b52dfd956fe8",2020-09-17T16:38:32Z,Natalia Gimelshein
eaf9b28c55322336754e824de61490af5ce6166b,"[quantization] Use torchbind for Linear PackedParams (#34140)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/34140

Test Plan: Imported from OSS

Reviewed By: ZolotukhinM

Differential Revision: D20229168

Pulled By: jamesr66a

fbshipit-source-id: 3607cac9aa5b4b044572329742baed03350491c6",2020-05-08T02:01:19Z,James Reed
17567e5b29a77dee137dfc365e9ed3f1cf358950,"[pytorch@arvr/windows] Fix pytorch build/import on Windows @ ovrsource (#97193)

Summary:

- Importing torch on Windows can cause a crash within python.
- The problem was introduced by the change in `Module.cpp` from https://github.com/pytorch/pytorch/pull/94927
- The cause is that a call to `PyObject* initModule(void)` declared with a `__declspec(dllimport)` specifier can lead to a crash if the definition doesn't include the `__declspec(dllexport)` counterpart.
- To mitigate the problem without introducing  customized macros and changing the build system (note, `#include <c10/macros/Export.h>` doesn't work in `stub.c`) is to simply remove the `__declspec(dllimport)` specifier.
- According to https://web.archive.org/web/20140808231508/http://blogs.msdn.com/b/russellk/archive/2005/03/20/399465.aspx and other sources, `__declspec(dllimport)` only leads to some code optimizations, and since `initModule()` is only called once at startup, this is marginal.
- Note: the `stub_with_flatbuffer.c` file counterpart wasn't affected, therefore, not touched.

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/97193
Approved by: https://github.com/ezyang",2023-03-24T18:32:40Z,Eric Sauser
4ff8cd8f3a84358cb614bc870c8c47f2fa779893,"[pytorch][codegen] gen_python_functions.py loading native_functions.yaml / deprecated.yaml directly (#47746)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/47746

- Removed the integration hack in gen_python_functions.py. It now directly
  loads native_functions.yaml. All dependencies on Declarations.yaml
  have been removed / moved to elsewhere.
- Rewrote the deprecated.yaml parsing logic to work with new data model directly.

Confirmed byte-for-byte compatible with the old codegen:
```
Run it before and after this PR:
  .jenkins/pytorch/codegen-test.sh <baseline_output_dir>
  .jenkins/pytorch/codegen-test.sh <test_output_dir>

Then run diff to compare the generated files:
  diff -Naur <baseline_output_dir> <test_output_dir>
```

Differential Revision: D24885067

Test Plan: Imported from OSS

Reviewed By: bhosmer

Pulled By: ljk53

fbshipit-source-id: 8e906b7dd36a64395087bd290f6f54596485ceb4",2020-11-14T10:22:37Z,Jiakai Liu
4b26cafb8fa7eef7cbfdc0327f85f30e0a38e8ec,"make validate debug-only in Device copy ctr (#47854)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47854

Test Plan: Imported from OSS

Reviewed By: ezyang

Differential Revision: D25003113

Pulled By: bdhirsh

fbshipit-source-id: e17e6495db65c48c7daf3429acbd86742286a1f3",2020-12-09T16:09:08Z,Brian Hirsh
3f4b46ac9e1a3d88964be357675aa1225aa82aad,Add potrs with MAGMA,2016-03-13T01:54:23Z,Brandon Amos
85121a7a0f24faccdb36093e92262c186fc65ee0,"Added CUDA support for complex input for torch.cholesky_solve (#47047)

Summary:
`torch.cholesky_solve` now works for complex inputs on GPU.
I moved the existing tests to `test_linalg.py` and modified them to test complex and float32 dtypes.
Differentiation also works correctly with complex inputs now.

Ref. https://github.com/pytorch/pytorch/issues/33152

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47047

Reviewed By: ngimel

Differential Revision: D24730020

Pulled By: mruberry

fbshipit-source-id: 95402da5789c56e5a682019790985207fa28fa1f",2020-12-06T04:16:50Z,Ivan Yashchuk
74a5d62d7ca9204b3b24137065c73fc7c66cc02d,"NCCL process group: avoid workEnqueue when capturing cuda graph (#102542)

Summary:
In torch.distributed, we make ProcessGroupNCCL not call workEnqueue when the cuda stream is capturing. I.e., when capturing a CUDA graph, we do not enqueue anything for the watchdog thread to consider. This allows capturing NCCL operations in a CUDA Graph.

This is followup to an internal discussion [1] where the watchdog thread was observed to crash when using cuda graphs containing an all_reduce. The watchdog thread wants to query events pertaining to enqueued work items, but this can't be done for ""events"" created during cuda graph capture.

[1] https://fb.workplace.com/groups/1405155842844877/posts/6975201909173548/

Test Plan: Test added. Also, the repro mentioned in https://fb.workplace.com/groups/1405155842844877/posts/7003002339726838/ runs successfully after this change.

Differential Revision: D46274814

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102542
Approved by: https://github.com/kwen2501",2023-06-09T18:15:58Z,Jeremy Reizenstein
e7293ee5ae7195cff24d7166e2bf1378e0e02693,[functorch] skip flaky(?) test,2022-04-23T02:15:47Z,Horace He
237c27c35f368e419c805a445e36aaf50f8c74d2,Fix reduction functions not respecting the strides of output when output is correct size (#4995),2018-02-06T15:50:28Z,Richard Zou
7f66fa62ca31cc1b5178c532cb152288e55a7c3c,"Fix typing errors in torch.distributed.nn.* directory. (#47533)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47533

Test Plan: Imported from OSS

Reviewed By: walterddr

Differential Revision: D24952500

Pulled By: xuzhao9

fbshipit-source-id: 8e66784fd8f9f111b6329e0bb48d6cd61c690a4a",2020-11-17T07:16:02Z,Xu Zhao
8bcfb30d9734ef218135e716e4ae2c81fd2de19f,make android,2015-12-19T06:42:29Z,Yangqing Jia
89a6680036ee4371caad48e753d3d5e2ec4604ea,"Update the pull request template (#81991)

### Description
Makes the pull request template more useful by adding a couple concrete sections which should usually be filled out

Pull Request resolved: https://github.com/pytorch/pytorch/pull/81991
Approved by: https://github.com/huydhn",2022-07-22T19:29:18Z,Zain Rizvi
3187a71bbe0689c1f81d3ac1f85c5ddcdaaeeb72,"[test] vc toolchain modification (#54589)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/54502
Needs to be merged after https://github.com/pytorch/builder/pull/684

Pull Request resolved: https://github.com/pytorch/pytorch/pull/54589

Reviewed By: walterddr

Differential Revision: D27402066

Pulled By: seemethere

fbshipit-source-id: 68f92485d89edf2c3315de8c57447f180679c77d",2021-03-29T18:17:52Z,peter
2e2a74670dc231078666f8fbc16b63766ebe480b,"torch.sparse.softmax: allow negative dim (#102172)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102172
Approved by: https://github.com/cpuhrsch",2023-05-24T16:17:24Z,Nikita Vedeneev
d1f9c03cef90f1bac64e66dfeb2a185d85f24739,"Use `const auto` with irange (#62990)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/62990

Test Plan: Sandcastle

Reviewed By: zhouzhuojie

Differential Revision: D30199748

fbshipit-source-id: 284b208ffa3c6c4749e5ac9b1fccb28914590f2c",2021-08-11T00:57:22Z,Richard Barnes
dc43ad428603539a2051940c09b191825f66203d,"add is_grad_enabled check in runtime_wrapper before running with torch.no_grad (#117089)

We observed that `with torch.no_grad()` in runtime_wrapper introduced ~10% (0.06ms->0.066ms) inference performance regression on lennard_jones on cpu.
For inference tasks in benchmark, grad has been disabled, but in the current runtime_wrapper, no_grad is set again and its time is counted into the running time.
Therefore, we add `is_grad_enabled` check in runtime_wrapper before running with torch.no_grad. If grad has been disabled, there is no need to set no_grad.

Before this pr:
1.043x
dev,name,batch_size,speedup,abs_latency,compilation_latency,compression_ratio,eager_peak_mem,dynamo_peak_mem,calls_captured,unique_graphs,graph_breaks,unique_graph_breaks
cpu,lennard_jones,1,**1.043427**,**0.068366**,4.756151,0.941846,45.056819,47.838822,9,1,0,0

After this pr:
1.146x
dev,name,batch_size,speedup,abs_latency,compilation_latency,compression_ratio,eager_peak_mem,dynamo_peak_mem,calls_captured,unique_graphs,graph_breaks,unique_graph_breaks
cpu,lennard_jones,1,**1.146190**,**0.061844**,4.468380,0.936456,44.427264,47.441920,9,1,0,0

Pull Request resolved: https://github.com/pytorch/pytorch/pull/117089
Approved by: https://github.com/jgong5, https://github.com/bdhirsh",2024-01-10T03:37:09Z,blzheng
3339da30df298c54030bb7fdb4364db6746f9159,[functorch] Fix CI,2021-12-20T04:05:14Z,Richard Zou
564296f05166812f501ee19dcf24af50e7bd7e08,"[2/3] [JIT] Make sure fusion occurs in test_tensorexpr (#45789)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45789

Making sure that more tests invoke a run with a Fusion Group.

Test Plan: Imported from OSS

Reviewed By: Krovatkin

Differential Revision: D24169535

Pulled By: eellison

fbshipit-source-id: 54d7af434772ba52144b12d15d32ae30460c0c3c",2020-10-08T18:59:57Z,Elias Ellison
a1e63251497feaddea3d26cbc9ec2093993d2b24,"Implement linear module for APoT quantization (#82105)

### Summary
Implement linear module to support APoT quantization. Use bitshifting method discussed in APoT paper https://arxiv.org/pdf/1909.13144.pdf to multiply PoT terms in APoT quantized weight tensor with uniformly quantized activation tensor to demonstrate alternative to matrix multiplication.

Multiplication using bitshifting for PoT:

<img width=""340"" alt=""Screen Shot 2022-07-25 at 12 44 26 PM"" src=""https://user-images.githubusercontent.com/68875504/180831050-ff849bca-8eb0-4b69-9b7f-c6c94a4cdfb5.png"">

### Test Plan
Run unit tests with: `python /pytorch/test/quantization/core/experimental/test_linear.py`
Pull Request resolved: https://github.com/pytorch/pytorch/pull/82105
Approved by: https://github.com/HDCharles",2022-07-28T03:57:30Z,asl3
49b69b2adeb230a15ed27ee2d25bb44835769681,"[JIT] fix broadcasting lists of ints (#39481)

Summary:
Previously, on conversion from python -> c++ it was casted to double list through bad copy pasta. It's pretty unusual for someone to script a broadcasting list function directly since it's an internal api, so it was unlikely to affect anyone.

Fix for https://github.com/pytorch/pytorch/issues/39450
Pull Request resolved: https://github.com/pytorch/pytorch/pull/39481

Reviewed By: jamesr66a

Differential Revision: D21870557

Pulled By: eellison

fbshipit-source-id: e704e5e87d2702a270b7d65c4df444246a134480",2020-06-04T19:13:33Z,Elias Ellison
57e52393213b6b4fba3b334654b96396a2904087,"Introduce Tensor overload to linspace and logspace (#104889)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/104889
Approved by: https://github.com/zou3519
ghstack dependencies: #107958",2023-09-07T13:53:39Z,Li-Huai (Allan) Lin
294f9d12826d63df1a736ffd4ef9dab2af2d10d0,"[Profiler][Minor] Organize collection.h/.cpp (#82992)

Collection of Torch ops is quite complex compared to backend events / allocations / ooms. Python is also complex, however it is already factored into a standalone unit. This PR just shuffles the contents of collection.cpp to group the Torch op specific parts together, and does various cleanups to the code.

Differential Revision: [D38426344](https://our.internmc.facebook.com/intern/diff/D38426344/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/82992
Approved by: https://github.com/chaekit",2022-08-20T01:42:34Z,Taylor Robie
b35f70da0555650bb3b358753ea615ba5ecba6df,"[ez] fixup the export of D62879819 (#136900)

a line from D62879819 (#136190) went missing somehow
Pull Request resolved: https://github.com/pytorch/pytorch/pull/136900
Approved by: https://github.com/atalman",2024-09-28T13:46:17Z,Ivan Zaitsev
72a7351993c953500bd8cdb1fb7a9e33aaa7ef9d,"Pin linux ninja dep to 1.10.2 (#88548)

The latest version 1.11.1 breaks PyTorch CI.  A bunch of tests are failing now in master https://hud.pytorch.org/pytorch/pytorch/commit/d1ee0730410ac910760c0a21156e574093a0d15a.  Curiously, the latest commit https://hud.pytorch.org/pytorch/pytorch/commit/81042d3a53335259c60e5aa8c9b9614c3d87b05f looks green, but it's good to pin this dependency anyway

https://github.com/pytorch/pytorch/blob/master/.circleci/docker/requirements-ci.txt#L95-L97 has a curious note about ninja and why it's not part of the docker container (need to revisit this later on):

```
#ninja
#Description: build system.  Note that it install from
#here breaks things so it is commented out
```

This is one more reason to justify the effort to consolidating all pip and conda dependencies to get rid of this family of issue.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/88548
Approved by: https://github.com/clee2000",2022-11-07T23:53:17Z,Huy Do
1114b051222c3478aa3f0140802e4f4f3147f994,"Updating submodules

Summary:
GitHub commits:

https://github.com/facebook/rocksdb/commit/97631357aa274d06a7ab09b3cde7b909262cc4dd
https://github.com/pytorch/fbgemm/commit/2f1477dfee9465c1e2dbdf21722970b3fa1baf86

Test Plan: n/a

Reviewed By: 2d2d2d2d2d

fbshipit-source-id: 33029d2e8c6a3664a35823829670f6ed9dfc3b44",2019-09-13T22:07:43Z,svcscm
a036f9a65f713c144b97c902cf9773cf8f21142e,"Create README.md of caffe2/quantization/server

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/14217

Reviewed By: csummersea

Differential Revision: *********

Pulled By: jspark1105

fbshipit-source-id: bddf4f1c2dc5ec8ea6ebe9e265956f367e082d52",2018-11-20T05:44:29Z,Jongsoo Park
9f519d2d2d5dd4490de43ec2bcea59efab13e225,"Simplify benchmark patterns in mypy-strict.ini (#55700)

Summary:
These two lines were added in https://github.com/pytorch/pytorch/issues/53296, but they are needlessly complicated; this PR consolidates them.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/55700

Test Plan:
Run this command, and verify that the same number of files is given both before and after this PR:
```
mypy --config=mypy-strict.ini
```

Reviewed By: robieta

Differential Revision: *********

Pulled By: samestep

fbshipit-source-id: a34968cdff29cb8ad83813b277114224b5e37569",2021-04-09T21:47:24Z,Sam Estep
f9d32c4fa8f5990ac09ba0f9a1298d0348433179,"[JIT] Add selective backend lowering API (#43613)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/43613

**Summary**
This commit adds a helper/utility to faciliate the selective lowering of
specific submodules within a module hierarchy to a JIT backend. The reason
that this is needed is that lowering a submodule of a scripted
module to a backend after the module has been scripted requires
adjusting its JIT type.

**Test Plan**
This commit refactors `NestedModuleTest` in `jit/test_backends.py` to
use this new selective lowering API.

**Fixes**
This commit fixes ##41432.

Test Plan: Imported from OSS

Reviewed By: mortzur

Differential Revision: D23339855

Pulled By: SplitInfinity

fbshipit-source-id: d9e69aa502febbe04fd41558c70d219729252be9",2020-10-30T07:36:13Z,Meghan Lele
c8d1ec02be475764e6a62a4e0d0c7d09a9d79fcf,"[jit] Have ScriptModule inherit from Module (#5769)

* Have ScriptModule inherit from Module
  This is accomplished by created replacement _parameters, _buffers,
  and _modules which implement the OrderedDict APIs but which
  actually get/set their members inside script::Module
* Merge TracedModule with ScriptModule
* Move logic of attribute handling into Python bindings rather than
  make script::Module handle it. This was redundant with nn.Module,
  which already handles attribute.
* Make TracedModule a subclass of ScriptModule
* Move handling of attribute kind logic into bindings.
* Allow ScriptModule to contain non-script module submodules.",2018-03-22T04:17:49Z,Zachary DeVito
38c97fb6f0725fa0c02687023b101f0f8339cca7,"[shape inference] add shape inference support

Summary:
* To make pruning op compatible with shape inference, we introduced a new quantile argument (as in *********) to differentiate dynamic/fixed pruning.

* The fixed pruning op has defined output shapes. However, the input shapes are not determined therefore we want to bypass the input shapes checking for two pruning ops, as implemented in this diff.

Test Plan:
buck test caffe2/caffe2/opt:bound_shape_inference_test

```
Started reporting to test run: https://our.intern.facebook.com/intern/testinfra/testrun/844425102187909
    ✓ ListingSuccess: caffe2/caffe2/opt:bound_shape_inference_test - main (1.973)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.FC3D (2.604)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSumFused4BitRowwise (2.635)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.FC (2.690)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Int8QuantizeInferInputBackwards (2.705)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSum (2.729)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Reshape (2.754)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ConcatMissingInput (2.770)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ElementwiseOp (2.770)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Tile (2.785)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Bucketize (2.789)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSumFused8BitRowwise (2.807)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSum8BitRowwiseSparse (2.841)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Split (2.863)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ConcatInferInputBackwards (2.894)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ElementwiseInferInputBackwards (2.898)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Combo0 (2.902)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.LengthsRangeFill (2.964)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Quantization (2.964)
Summary
  Pass: 18
  ListingSuccess: 1
Finished test run: https://our.intern.facebook.com/intern/testinfra/testrun/844425102187909
```

buck test caffe2/caffe2/fb/opt:bound_shape_inference_net_test

```
 Started reporting to test run: https://our.intern.facebook.com/intern/testinfra/testrun/3096224780078093
    ✓ ListingSuccess: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - main (14.092)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipLengths (15.508)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdListFeaturePreProcessing (15.521)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipRanges (16.198)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.RowwisePrune (16.302)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.GatherRanges1 (16.585)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.Combo3 (16.865)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdListFeaturePreProcessingWithCast (16.907)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.GatherRanges2 (16.921)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.LengthsRangeFill (17.157)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipRangesAndGatherRanges (17.277)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdScoreListFeaturePreProcessing (17.274)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipRangesGatherSigridHash (17.554)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.Combo1 (17.645)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdScoreListFeaturePreProcessingDEFAULT (17.887)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdListFeaturePreProcessingDEFAULT (17.929)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.f97293388_0 (19.343)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.GatherRangesToDense1 (19.489)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdScoreListFeaturePreProcessingWithCast (19.887)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.xray_v11 (19.905)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.SigridTransforms (20.080)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.Combo2 (20.086)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.vanillaSparseNN (59.847)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.gather (97.822)
Summary
  Pass: 23
  ListingSuccess: 1
```

## Workflow testing

===
* non-DI/fixed quantile/user side/non-self-binning
f224250571

*  non-DI/fixed quantile/user+ad side/non-self-binning
f224250610

* DI/fixed quantile/user side/self-binning
f224250637

* DI/fixed quantile/user+ad side/self-binning
f224250662

*  non-DI/dynamic quantile/user+ad side/non-self-binning
f224250705

* DI/dynamic quantile/user+ad side/self-binning
f224250760

Reviewed By: ChunliF

Differential Revision: D23647390

fbshipit-source-id: 3ec1c0eaea53bd4d5eda4a0436577216f7fa8ead",2020-10-15T07:43:53Z,Zeliang Chen
7af6f9515f50a73126bf1fd59c1561600879dacb,"Move TensorAccessor to ATen/core

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/11014

Reviewed By: cpuhrsch

Differential Revision: D9561802

fbshipit-source-id: d3dbe6d7e76e2419ead81fb448711f101daee19f",2018-09-02T04:38:49Z,Edward Yang
3c977fb7cea9aeb2895166a13ed8ac13fd85fb1e,"Error out on in-place (unary) ops on tensors that have internal overlap (#17927)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/17927
ghimport-source-id: 626d321e430b6b5c0ea3aa1eb9df8c1e2d058bf8

Stack:
* #17926 Implement at::has_internal_overlap helper function
* **#17927 Error out on in-place (unary) ops on tensors that have internal overlap**

On the way to #17935.

Works for CPU and CUDA on the following ops:
- abs_, acos_, asin_, atan_, ceil_, cos_, erf_, erfc_, exp_, expm1_
- floor_, log_, log10_, log1p_, log2_, round_, rsqrt_,
- sin_, sqrt_, tan_, tanh_, trunc_

This PR adds a check to see if the out/result tensor has internal
overlap. If it does, then we error out because the result **may** be
incorrect.

This is overly conservative; there are some cases where if the result is
the same as the input, the inplace operation is OK (such as floor_,
round_, and trunc_). However, the current code isn't organized in such a
way that this is easy to check, so enabling those will come in the future.

Reviewed By: ezyang

Differential Revision: *********

fbshipit-source-id: 15e12bf1fdb2ab7f74bb806e22bc74840bd6abd1",2019-03-15T14:41:08Z,Richard Zou
50e73a8313fbdb85d1be296b4890d12709816818,"Support synchronous mode in ibverbs transport

Summary:
Synchronous mode means using the calling thread instead of the device
thread for completion handling. Since this saves a context switch in
the critical path, this is very beneficial for low latency algorithms.

For example: the p99 of a 4-way barrier drops from 17us to 4us.

Reviewed By: andrewwdye

Differential Revision: ********

fbshipit-source-id: 013b1680497589fe5ad0bca38600bce6a410200b",2017-02-28T20:34:49Z,Pieter Noordhuis
9d4731f952e1c414bf5fe6d14190b7338a883dc3,"[AOTI] Disable stack allocation for OSS (#125732)

Summary: Stack allocation is for certain small CPU models, but its coverage still needs improvement, so default to OFF for OSS.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/125732
Approved by: https://github.com/chenyang78
ghstack dependencies: #126720, #126801",2024-05-24T14:21:27Z,Bin Bao
d478605dec65a746d41506b23693d6013bfa11b2,"Fix classmethod override argument passing. (#47114)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/47069.
Fixes https://github.com/pytorch/pytorch/issues/46824.
Fixes https://github.com/pytorch/pytorch/issues/47186

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47114

Reviewed By: ngimel

Differential Revision: D24649598

Pulled By: ezyang

fbshipit-source-id: af077affece7eceb1e4faf9c94d15484796b0f0e",2020-11-11T17:23:02Z,Hameer Abbasi
421f40e051431c377a3a541fb5a8dbaba61c0dd6,"Use binary units for CUDA memory summary (#91854)

To reduce confusion, use for example `KiB` instead of `KB` since we're talking powers of 2 and not 10.

https://en.wikipedia.org/wiki/Byte#Multiple-byte_units

```
import torch
x = torch.zeros(1024 * 1024, dtype=torch.uint8, device='cuda')
print(torch.cuda.memory_summary())
```

```
|===========================================================================|
|                  PyTorch CUDA memory summary, device ID 0                 |
|---------------------------------------------------------------------------|
|            CUDA OOMs: 0            |        cudaMalloc retries: 0         |
|===========================================================================|
|        Metric         | Cur Usage  | Peak Usage | Tot Alloc  | Tot Freed  |
|---------------------------------------------------------------------------|
| Allocated memory      |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|       from large pool |      0 KiB |      0 KiB |      0 KiB |      0 B   |
|       from small pool |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|---------------------------------------------------------------------------|
| Active memory         |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|       from large pool |      0 KiB |      0 KiB |      0 KiB |      0 B   |
|       from small pool |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|---------------------------------------------------------------------------|
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/91854
Approved by: https://github.com/ngimel",2023-01-14T05:10:47Z,milesial
9f17037e8ba7d98d8fbbcf40625fcad48c4c0b16,"[dtensor] move tensor constructors to the api module (#133129)

This is to ensure __init__.py only contain public APIs

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133129
Approved by: https://github.com/awgu, https://github.com/tianyu-l",2024-08-13T02:05:08Z,Wanchao Liang
d4a94ad0413db201519eb55bd8f3d80e9956bea6,"[ONNX] Fix upsample_bilinear2d decomp skip with output shape (#118823)

The previous output size missed the first two dimensions.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/118823
Approved by: https://github.com/titaiwangms",2024-02-01T22:04:35Z,BowenBao
002d4f9f7d61fa8ef2f8a63f0e76903a11df7517,"Erase shape information from class types (#23362)

Summary:
](https://our.intern.facebook.com/intern/diff/16681944/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/23362

Pulled By: driazati

Differential Revision: D16681944

fbshipit-source-id: dba46b6fc3223a2f94dc502531df438f3212d8fb",2019-08-07T05:21:30Z,davidriazati
bda40639c53a96cecc9eb1c21d47257d927ebe74,"[nnc] Move operator implementations into a subdirectory (#59988)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/59988

As we broaden operator support, putting all the implementations into
kernel.cpp is getting unwieldy.  Let's factor them out into the ""operators""
subdirectory.

This diff is big but it's entirely code movement; I didn't change anything,
other than to expose a few utilities in kernel.h.
ghstack-source-id: 131405139

Test Plan: CI

Reviewed By: ZolotukhinM

Differential Revision: D29115916

fbshipit-source-id: ba0df1d8dd4a108b584da3baf168407e966b2c78",2021-06-16T12:07:43Z,Bert Maher
d0ad696f9d5319730d91a450011cd9ee1fde3c2f,"Warn about THPObjectPtr needing GIL. (#9265)

Summary:
Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/9265

Differential Revision: D8767687

Pulled By: ezyang

fbshipit-source-id: 900b37f2749112cafc5b48e7b444a256df18186a",2018-07-09T20:44:51Z,Edward Z. Yang
96651458eb97a1898a307a0f7c03fa963b4cc7d3,"Automated submodule update: tensorpipe (#59374)

Summary:
This is an automated pull request to update the first-party submodule for [pytorch/tensorpipe](https://github.com/pytorch/tensorpipe).

New submodule commit: https://github.com/pytorch/tensorpipe/commit/e942ea15138d1ca5eac08b194858b54fa1e4ab2f

Pull Request resolved: https://github.com/pytorch/pytorch/pull/59374

Test Plan: Ensure that CI jobs succeed on GitHub before landing.

Reviewed By: lw

Differential Revision: D28867855

fbshipit-source-id: e1325046003f5c546f02024ff4c427c91721cd7e",2021-06-10T11:39:49Z,Facebook Community Bot
8c927b208ca9ca5b8bac481f9b54d104038def3c,"improve test_docs_coverage error messages (#21029)

Summary:
Most important fix: Correct ""tensor.rst"" to ""tensors.rst""

Secondary fix: some minor English spelling/grammar fixes.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/21029

Differential Revision: D15523230

Pulled By: umanwizard

fbshipit-source-id: 6052d8609c86efa41a4289cd3a099b2f1037c810",2019-05-31T18:09:37Z,Brennan Vincent
d96aac8d2ae231070c6d0613f39f02c342dfbcd5,"[MPS] Add logit op (#95162)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/95162
Approved by: https://github.com/kulinseth",2023-02-21T07:02:45Z,Li-Huai (Allan) Lin
8dcd256201a16827bae3610fe05f6566cce787d0,"Memory layout for pooling ops

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/25374

Test Plan: Imported from OSS

Differential Revision: D17107577

Pulled By: jamesr66a

fbshipit-source-id: e40dacaddf5ee17e6483be9e9302d3afc1a708c7",2019-08-29T18:41:33Z,James Reed
8da18af6ce66a3cec925065a52674b0b679e1a8e,update to new storage mmap api,2014-04-07T12:54:09Z,Ronan Collobert
232530cc28bce864e04ab7af2a43873c37226a3a,Move scalar tests from common_nn to legacy_nn. (#5223),2018-02-13T21:44:21Z,gchanan
d38a71d579dc2f03d7a35132ccb168e697c1838e,"`torch.nn.modules.LazyModuleMixin` and `torch.nn.LazyLinear` (Shape Inference II) (#44538)

Summary:
Retake on https://github.com/pytorch/pytorch/issues/40493 after all the feedback from albanD

This PR implements the generic Lazy mechanism and a sample `LazyLinear` layer with the `UninitializedParameter`.

The main differences with the previous PR are two;
Now `torch.nn.Module` remains untouched.
We don't require an explicit initialization or a dummy forward pass before starting the training or inference of the actual module. Making this much simpler to use from the user side.

As we discussed offline, there was the suggestion of not using a mixin, but changing the `__class__` attribute of `LazyLinear` to become `Linear` once it's completely initialized. While this can be useful, by the time being we need `LazyLinear` to be a `torch.nn.Module` subclass since there are many checks that rely on the modules being instances of `torch.nn.Module`.
This can cause problems when we create complex modules such as
```
class MyNetwork(torch.nn.Module):
    def __init__(self):
        super(MyNetwork, self).__init__()
        self.conv = torch.nn.Conv2d(20, 4, 2)
        self.linear = torch.nn.LazyLinear(10)
    def forward(self, x):
        y = self.conv(x).clamp(min=0)
        return self.linear(y)
```
Here, when the __setattr__ function is called at the time LazyLinear is registered, it won't be added to the child modules of `MyNetwork`, so we have to manually do it later, but currently there is no way to do such thing as we can't access the parent module from LazyLinear once it becomes the Linear module. (We can add a workaround to this if needed).

TODO:

Add convolutions once the design is OK
Fix docstrings

Pull Request resolved: https://github.com/pytorch/pytorch/pull/44538

Reviewed By: ngimel

Differential Revision: D24162854

Pulled By: albanD

fbshipit-source-id: 6d58dfe5d43bfb05b6ee506e266db3cf4b885f0c",2020-10-19T20:09:16Z,Emilio Castillo
99608ceed660fb308f3ac1226c99be349c3f0b9c,"Scoped extension building for C++ backed custom ops tests (#136695)

FIXES #125579 #131103 #133197 #133283 #134738 #135369 #135685

Tests that create C++ extensions can cause flakiness in CI due to library namespace conflict and test ordering. We can build them in temp dirs to ensure isolation.

An alternative is to build these as part of the build process and have build time errors.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136695
Approved by: https://github.com/zou3519",2024-10-25T20:19:47Z,Simon Fan
e568b3fa2d8efcc9a6c43f15520a90aa98c134e6,"test nan and inf in TestTorchMathOps (#41225)

Summary:
Per title. `lgamma` produces a different result for `-inf` compared to scipy, so there comparison is skipped.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/41225

Differential Revision: D22473346

Pulled By: ngimel

fbshipit-source-id: e4ebda1b10e2a061bd4cef38d1d7b5bf0f581790",2020-07-10T16:44:58Z,Natalia Gimelshein
cb9ef4668ed37460d99cc8ee3d9960fef2075902,"Updated library level maintainers for torchtext (#84950)

- Updated library level maintainers for torchtext to reflect internal changes to the team

Pull Request resolved: https://github.com/pytorch/pytorch/pull/84950
Approved by: https://github.com/mthrok",2022-09-14T00:35:36Z,Nayef Ahmed
e4c41b6936ed433aff8e60735eba938ba66334e8,"Remove codegen logic to support non-c10-full ops (#49164)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/49164

This PR removes the logic paths in codegen that were responsible for handling non-c10-full ops.
This only goes through our basic codegen. It does not simplify C++ code yet and it does not remove the codegen for generated unboxing wrappers yet.
ghstack-source-id: 119450487

Test Plan: waitforsandcastle

Reviewed By: ezyang

Differential Revision: *********

fbshipit-source-id: 7e70d14bea96948f5056d98125f3e6ba6bd78285",2021-01-06T22:14:24Z,Sebastian Messmer
5d45140d6874be04c22c8abba55e4438c25d2fdb,"[numpy] torch.{all/any} : output dtype is always bool (#47878)

Summary:
BC-breaking note:

This PR changes the behavior of the any and all functions to always return a bool tensor. Previously these functions were only defined on bool and uint8 tensors, and when called on uint8 tensors they would also return a uint8 tensor. (When called on a bool tensor they would return a bool tensor.)

PR summary:

https://github.com/pytorch/pytorch/pull/44790#issuecomment-725596687

Fixes 2 and 3

Also Fixes https://github.com/pytorch/pytorch/issues/48352

Changes
* Output dtype is always `bool` (consistent with numpy) **BC Breaking (Previously used to match the input dtype**)
* Uses vectorized version for all dtypes on CPU
* Enables test for complex
* Update doc for `torch.all` and `torch.any`

TODO
* [x] Update docs
* [x] Benchmark
* [x] Raise issue on XLA

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47878

Reviewed By: albanD

Differential Revision: D25714324

Pulled By: mruberry

fbshipit-source-id: a87345f725297524242d69402dfe53060521ea5d",2021-01-08T19:01:52Z,kshitij12345
6ae99aa5bc616f63649be3831cb10bf0d09c477e,"onnx/caffe2 tests: Do not execute models with CPU-only operators on GPU.

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/20720

Reviewed By: bddppq

Differential Revision: D15422322

Pulled By: houseroad

fbshipit-source-id: c79795434157ff5f0a7b2774fd40edc71cf35ba7",2019-05-20T22:58:27Z,Matthew Brandyberry
6a951a6f4c06dff162e3b81e99a964c8b6ad84f0,"Fix a KaTeX crash and many docstring issues (#49684)

Summary:
The first commit fixes the `MultiheadAttention` docstrings, which are causing a cryptic KaTeX crash.

The second commit fixes many documentation issues in `torch/_torch_docs.py`, and closes gh-43667 (missing ""Keyword arguments"" headers). It also fixes a weird duplicate docstring for `torch.argmin`; there's more of these, it looks like they were written based on whether the C++ implementation has an overload. That makes little sense to a Python user though, and the content is simply duplicate.

The `Shape:` heading for https://pytorch.org/docs/master/generated/torch.nn.MultiheadAttention.html looked bad, here's what it looks like with this PR:

<img width=""475"" alt=""image"" src=""https://user-images.githubusercontent.com/98330/102797488-09a44e00-43b0-11eb-8788-acdf4e936f2f.png"">

Pull Request resolved: https://github.com/pytorch/pytorch/pull/49684

Reviewed By: ngimel

Differential Revision: D25730909

Pulled By: mruberry

fbshipit-source-id: d25bcf8caf928e7e8e918017d119de12e10a46e9",2020-12-30T22:15:49Z,Ralf Gommers
81c2412721446a38434c715ff544f9915949b490,"[caffe2] Switch to using `public_include_directories

Summary:
caffe2 uses `-I` all over the place, but really we should use the Buck built-in version of this

Alternatively, the `exported_header` clean up means we need to standardize to a single path

Test Plan:
```
buck build caffe2:torch-cpp-cpu
buck build caffe2/...
```

Reviewed By: malfet

Differential Revision: D19150098

fbshipit-source-id: e99aaf69d6c474afaedbd5f693a7736d3d67aafc",2020-03-31T15:12:30Z,Michael Lee (Engineering)
68cfc52452802f918003fcc17d72c42a8d869c11,"MomemtumSGDUpdate -- version of MomentumSGD with update.

Summary:
It gives a significant perf boost to do the parameter update inside MomentumSGD, instead of with a separate WeightedSum op.
To ensure backwards compatibility, I made it a separate op.

Also added an unit test.

Reviewed By: prigoyal

Differential Revision: D4262446

fbshipit-source-id: 38e7ee6d7677b398658ac7fe9b7a59b569e033f4",2016-12-07T19:49:24Z,Aapo Kyrola
cd2929c70798dbd810adef353e045960858ba060,"ConvTransposeMobileOp respects the `shared_buffer` arg.

Summary:
This makes ConvTransposeMobileOp inline with other implementations,
allows us to account for these buffers in the workspace, and is generally a good
thing to do.

Differential Revision: D4767431

fbshipit-source-id: b14a96a089136e305ab42680772272f4e5f16f53",2017-03-31T17:24:38Z,Andrew Tulloch
64dd1419c58997bf8c9dd4c8a22ae8cc4a241d0b,Fix Variable indexing bugs (#96),2016-10-03T18:49:21Z,Adam Paszke
40869884cd33e7172552a583402508d02ff0d509,"Add outer export to onnx (#53603) (#54869)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54869

Add symbolic fuction to support torch.outer export to onnx.
Support for transfo-xl-wt103 model.

Test Plan: Imported from OSS

Reviewed By: nikithamalgifb

Differential Revision: D27408978

Pulled By: SplitInfinity

fbshipit-source-id: 70c89a9fc1a5e4a4ddcf674afb1e82e492a7d3b9",2021-04-01T04:11:25Z,DeyuHuang
b685864f504155f8926498b82307c9afba7b0cc5,"[quant][graphmode][fx] Add reference option support for linear_static_fp16 (#52650)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/52650

linear_dynamic_fp16 has following dtypes for activation, weight, bias, output:
(fp32, fp16, fp32, fp32)

linear_static_fp16 has following dtypes:
(fp16, fp16, fp16, fp16)

Test Plan: Imported from OSS

Reviewed By: vkuzo

Differential Revision: D26599803

fbshipit-source-id: b4a8345d355125070be718a227288cc848cc8bbc",2021-02-27T16:23:45Z,Jerry Zhang
85bd6bc0105162293fa0bbfb7b661f85ec67f85a,"Cache pretrained mobilenet_v2 and mobilenet_v3_large models in Docker (#100302)

Follow the example I did for ONNX in https://github.com/pytorch/pytorch/pull/96793, this caches the pretrained `mobilenet_v2 model` and `mobilenet_v3_large` used by CI jobs.  I think there might be an issue either with AWS or with the domain download.pytorch.org as the connection to the latter has been failing a lots in the past few days.

Related flaky jobs:
* https://github.com/pytorch/pytorch/actions/runs/4835873487/jobs/8618836446
* https://github.com/pytorch/pytorch/actions/runs/4835783539/jobs/8618404639
* https://github.com/pytorch/pytorch/actions/runs/4835783539/jobs/8618404639

```
Downloading: ""https://download.pytorch.org/models/mobilenet_v2-b0353104.pth"" to /var/lib/jenkins/.cache/torch/hub/checkpoints/mobilenet_v2-b0353104.pth
Traceback (most recent call last):
  File ""/opt/conda/envs/py_3.8/lib/python3.8/urllib/request.py"", line 1354, in do_open
    h.request(req.get_method(), req.selector, req.data, headers,
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1256, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1302, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1251, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1011, in _send_output
    self.send(msg)
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 951, in send
    self.connect()
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 1418, in connect
    super().connect()
  File ""/opt/conda/envs/py_3.8/lib/python3.8/http/client.py"", line 922, in connect
    self.sock = self._create_connection(
  File ""/opt/conda/envs/py_3.8/lib/python3.8/socket.py"", line 808, in create_connection
    raise err
  File ""/opt/conda/envs/py_3.8/lib/python3.8/socket.py"", line 796, in create_connection
    sock.connect(sa)
OSError: [Errno 99] Cannot assign requested address
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/100302
Approved by: https://github.com/ZainRizvi",2023-05-01T19:31:37Z,Huy Do
d2a9b256f00742b3fd1271ad087fc4e02144aed8,"[DCP][Test]Remove broken 2d checkpoint test (#106367)

Removing this broken test as we are not going to land the fix for 2D regression. Instead, we are going to migrate to use device_mesh and dtensor state_dict for 2D.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/106367
Approved by: https://github.com/fduwjj",2023-08-01T19:54:37Z,wz337
cf3638a9ccb39d29b4fc01d19bf3f847655c1e03,"[dynamo] Clear cache on dynamo dashboard accuracy tests (#95726)

Might fix some flaky accuracy tests?

Pull Request resolved: https://github.com/pytorch/pytorch/pull/95726
Approved by: https://github.com/ngimel, https://github.com/anijain2305, https://github.com/desertfire",2023-03-01T00:50:15Z,William Wen
9d209e78348ee5c3e1ead700d240fb476b3bc4de,"Revert ""[ao] making _is_activation_post_process private (#87520)""

This reverts commit 45c62a337756ff9db97cd64d2d42d9e65dda0a85.

Reverted https://github.com/pytorch/pytorch/pull/87520 on behalf of https://github.com/bigfootjon due to Diff reverted internally",2022-11-21T16:48:26Z,PyTorch MergeBot
fd3ed2e4f7661b586f525c05f6252903bf9d59ac,"[Caffe2] Use more irange()s in loops. (#72262)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/72262

Using an explicitly typed loop variable risks type mismatches with the loop
bound; using an const auto variable and c10::irange eliminates this
possibility. This change modifies loops in files under
fbsource/fbcode/caffe2/aten from the format
`for(TYPE var=x0;var<x_max;x++)` to `for(const auto var: irange(xmax))`

This was achieved by running r-barnes's loop upgrader script (D28874212)
modified for the appropriate directory.

Test Plan: arc sanity

Reviewed By: r-barnes

Differential Revision: D33952435

fbshipit-source-id: ad72a9e2448d3c03f16bcccda904ffe5003fd557
(cherry picked from commit 8140be4ba9c37f934d013c2cd0c25bdd8d2e7c19)",2022-02-08T19:00:36Z,Sweet Tea Dorminy
a09c4d3997f2e57eedc22cb38c896f9e8f9a5607,"[pt][quant] Vectorized qmul and more methods on qint data types (#34376)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/34376

Vectorized implementation of qmul. qmul is now ~16x faster on my development machine. This implementation works for qint8, quint8 and qint32. Also added some commonly used operations, such as multiply operator, requantize operation etc., to qint vector classes for future use.

```
#!/usr/bin/env python

import time
import torch
import torch.nn as nn
torch.set_num_threads(1)
# print(torch.__config__.parallel_info())

A = torch.rand(1, 54, 54, 256)
B = torch.rand(1, 54, 54, 256)

scale = .05
zero_point = 50

for dtype in [torch.quint8, torch.qint8]:

    qA = torch.quantize_per_tensor(A, scale=scale, zero_point=zero_point,
            dtype=dtype)
    qB = torch.quantize_per_tensor(B, scale=scale, zero_point=zero_point,
            dtype=dtype)

    NITER = 1000
    s = time.time()
    for i in range(NITER):
        out = torch.ops.quantized.mul(qA, qB, scale=scale, zero_point=zero_point)
    time_per_iter = (time.time() - s) / NITER

    print('dtype: {} time per iter ms: {:.3f}'.format(dtype, time_per_iter * 1000))
```
### Before
dtype: torch.quint8 time per iter ms: 6.714
dtype: torch.qint8 time per iter ms: 6.780

### After
dtype: torch.quint8 time per iter ms: 0.431
dtype: torch.qint8 time per iter ms: 0.417

### Test
Modified qmul tests to include qint8 and qint32 data types.

python test/test_quantized.py TestQuantizedOps.test_qmul_relu_same_qparams
python test/test_quantized.py TestQuantizedOps.test_qmul_relu_different_qparams
python test/test_quantized.py TestQuantizedOps.test_qmul_broadcast
ghstack-source-id: 99862681

Differential Revision: D20308515

fbshipit-source-id: 4fa65b2ba433cfd59260fc183a70f53a6fcc36b4",2020-03-10T23:48:48Z,Daya Khudia
fa597ee17fbf8364e7ccae21bdc8a367efe48975,"Fix torch.randperm for CUDA (#59352)

Summary:
Context https://github.com/pytorch/pytorch/issues/58545

The logic is that we are going to keep it consistent for both
torch.randperm and torch.randint

1. Generators can have either a fully-specified or non-fully specified device
2. As long as the device type match with the result, we don't error out

Pull Request resolved: https://github.com/pytorch/pytorch/pull/59352

Test Plan:
```
python test/test_tensor_creation_ops.py -k TestRandomTensorCreation
```

Reviewed By: ngimel

Differential Revision: D28855920

Pulled By: zhouzhuojie

fbshipit-source-id: f8141a2c4b2f177e1aa7baec6999b65916cba02c",2021-06-04T15:55:14Z,Zhuojie Zhou
6a8c2758d558baabf3e012ba45ef6349c957ba4e,"Add better performing versions for groupwise and depthwise convolutions (#22869)

Summary:
Groupwise and depthwise convolutions become faster with this diff
Pull Request resolved: https://github.com/pytorch/pytorch/pull/22869

Test Plan:
buck test mode/dev caffe2/test:quantized -- 'test_qconv'  --print-passing-details

```
Running 2 tests
Started new test run: https://our.intern.facebook.com/intern/testinfra/testrun/562950091484224
      ✓ caffe2/test:quantized - test_qconv (test_quantized.TestQuantizedConv) 2.731 1/2 (passed)
Test output:
> test_qconv (test_quantized.TestQuantizedConv) ... ok
>
> ----------------------------------------------------------------------
> Ran 1 test in 2.732s
>
> OK
      ✓ caffe2/test:quantized - test_qconv_unpack (test_quantized.TestQuantizedConv) 5.187 2/2 (passed)
Test output:
> test_qconv_unpack (test_quantized.TestQuantizedConv) ... ok
>
> ----------------------------------------------------------------------
> Ran 1 test in 5.188s
>
> OK
Finished test run: https://our.intern.facebook.com/intern/testinfra/testrun/562950091484224
Summary (total time 15.66s):
  PASS: 2
  FAIL: 0
  SKIP: 0
  FATAL: 0
  TIMEOUT: 0
  OMIT: 0

```

buck test mode/dev caffe2/test:quantized -- 'test_conv_api'
```
Running 2 tests
Started new test run: https://our.intern.facebook.com/intern/testinfra/testrun/3940649676010406
      ✓ caffe2/test:quantized - test_conv_api (test_nn_quantized.ModuleAPITest) 0.040 1/2 (passed)
      ✓ caffe2/test:quantized - test_conv_api (test_quantized_conv.FunctionalAPITest) 5.402 2/2 (passed)
Finished test run: https://our.intern.facebook.com/intern/testinfra/testrun/3940649676010406
Summary (total time 11.83s):
  PASS: 2
  FAIL: 0
  SKIP: 0
  FATAL: 0
  TIMEOUT: 0
  OMIT: 0
```

Differential Revision: D16264144

Pulled By: dskhudia

fbshipit-source-id: 32fa43e5c3d97c8aaa6e0858327a2ac0aef8df5c",2019-07-26T00:51:39Z,Daya Khudia
908ba05a06c4c40bf103b3272d05acd2d554130f,"[Pytorch] Add python binding to use mobile cpu allocator. (#52376)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/52376

Using default cpu allocator for ops executed on qnnpack backend will result in
asan failures with heap overflow since qnnpack (and xnnpack) can access input
beyond their and/beginning.

Here we are enabling this feature specifically to enable dynamic sparse linear op test
using qnnpack engine. In dynamic linear op, the fp32 bias is not packed and
hence can result in out-of-bound access.

Test Plan: CI

Reviewed By: z-a-f

Differential Revision: D26491943

fbshipit-source-id: bcc2485e957c7abdef0853c36f6e0f876c20cee3",2021-02-18T02:20:18Z,Kimish Patel
1e8a16224f2a0fff7d6fef8809f9756f690ec4ab,"PackSegments: return value presence.

Summary:
Optionally return a blob of shape [batch size, max length] that is
false only in locations where the output tensor was padded.
One can separately convert lengths to segment ids and cast, but
this is more convenient, and possibly more efficient.

Differential Revision: D6006073

fbshipit-source-id: af6c4ea31972566e7d059dcd3fdd8afba97a88e9",2017-10-12T18:04:35Z,Tilak Sharma
348e0af6e191f9ad94d873ab6c484aaa34174dfc,"Remove unused binary fb_run_plan_mpi

Summary:
TSIA

This caused a compilation problem on gcc-6, see
https://github.com/caffe2/caffe2/issues/456.

Differential Revision: D5002823

fbshipit-source-id: 764aae1eaf78ee9918455b95a12e982597b85fdc",2017-05-04T22:09:56Z,Pieter Noordhuis
14d4bdb4067cee72290d22cd4bf3a623f49fcab2,"Reformat output data format to make it more general for other binaries (#9555)

Summary:
This is to simplify the data format during benchmarking. After this change, we can use the same benchmarking harness data conversion method to parse data from multiple binaries.

This change should be coordinated with the PR: https://github.com/facebook/FAI-PEP/pull/63
Pull Request resolved: https://github.com/pytorch/pytorch/pull/9555

Reviewed By: pjh5

Differential Revision: D8903024

Pulled By: sf-wind

fbshipit-source-id: 61cabcff99f0873729142ec6cb6dc230c685d13a",2018-07-23T17:54:25Z,Fei Sun
cd7408e9505e3a7ae00e72a69ab17389ce086475,"Add aten _assert_tensor_metadata op (#84617)

Example:
```
graph():
    %arg0 : [#users=3] = placeholder[target=arg0]
    %arg_guard_equality_check : [#users=1] = call_function[target=torch._tensor_equal](args = (%arg0, (1, 1, 2), (2, 2, 1), torch.float32), kwargs = {})
    %_assert_true : [#users=0] = call_function[target=torch._assert_true](args = (%arg_guard_equality_check, Guard evaluation failed equality check for arg0), kwargs = {})
    %add : [#users=1] = call_function[target=operator.add](args = (%arg0, 1), kwargs = {})
    return ([arg0, arg0], (add, add))
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/84617
Approved by: https://github.com/jansel",2022-09-19T20:48:09Z,Michael Voznesensky
61a0df5af060bc44ca5cde4303e815ee3b7761ce,"Canonicalize THC/THCTensorMasked.cuh include

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/13977

Reviewed By: jerryzh168

Differential Revision: D13062564

fbshipit-source-id: 77d42585198cd75bc8a2625787604552e5369787",2018-11-14T22:30:44Z,Edward Yang
7705175f8378a769232aa6b1dde38e70ab749be9,"CPUBlas: Use opmath_type for alpha/beta scalars (#65839)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/65839
Approved by: https://github.com/ngimel",2022-07-04T18:04:53Z,Peter Bell
5651e1e3ad435ec45b9435b9d78ff2fbc715fa0b,"Add auto_linear formulas and some others (#69727)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/69727

Still need to test the backward ones. We would need to update gradgradcheck to check forward over backward.

Test Plan: Imported from OSS

Reviewed By: albanD

Differential Revision: D33031728

Pulled By: soulitzer

fbshipit-source-id: 86c59df5d2196b5c8dbbb1efed9321e02ab46d30",2021-12-20T20:13:11Z,soulitzer
fc4209bd4fe9b06e6bfc6ef6db38b08f4b2616e1,"Fix the bucketization wrong doc for right argument (#45684)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/45684

Test Plan: Imported from OSS

Reviewed By: zou3519

Differential Revision: D24057996

Pulled By: glaringlee

fbshipit-source-id: 3db1c24f3cae9747effa4b1f3c5c3baf6888c9a1",2020-10-02T01:15:09Z,lixinyu
c6bc766184cf149da21f959c19997bc93f3c1f4f,"Remove unnecessary copy constructor (#83030)

Re-land of https://github.com/pytorch/pytorch/pull/82626 which somehow broke deploy build
Fixes annoying warnings when building with clang:
```
../c10/util/variant.h:2256:9: warning: definition of implicit copy constructor for 'impl<c10::Scalar, c10::basic_string_view<char>>' is deprecated because it has a user-declared copy assignment operator [-Wdeprecated-copy]
  impl& operator=(const impl& other) = default;
        ^
```

Preliminary step for switching MacOS builds to ones with `-Werror`
Pull Request resolved: https://github.com/pytorch/pytorch/pull/83030
Approved by: https://github.com/kit1980, https://github.com/seemethere",2022-08-09T01:07:13Z,Nikita Shulga
a3588b6ed9fe4021ee4c218f9f9b4215ddd227b2,"Updating submodules

Summary:
GitHub commits:

https://github.com/pytorch/fbgemm/commit/62c3b48cf4beb1c56922c4afebcfe9c35f3513d8

Test Plan: n/a

Reviewed By: 2d2d2d2d2d

fbshipit-source-id: 41d1346f2405bce84984b02e3a951bb0e30868b7",2019-11-18T08:33:43Z,svcscm
8ed906030c58ebb3b07438ecb41bbdb4644cc4d5,"add fp16 support for mkldnn conv and deconv on CPU (#99496)

The PR is part of https://github.com/pytorch/pytorch/issues/97068, which is to add fp16 support for mkldnn conv and mkldnn deconv to leverage  avx_ne_convert, avx512-fp16, and amx-fp16 via the oneDNN library.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99496
Approved by: https://github.com/jgong5, https://github.com/cpuhrsch",2023-09-19T03:39:45Z,CaoE
3837a962d32565264d86cda6033043cba9300f7d,"Fix typo in Concat and Softmax

Reviewed By: Maratyszcza

Differential Revision: D6629260

fbshipit-source-id: 06fff59a770312b6948b3b5e1c04db6f539ea268",2017-12-23T01:27:59Z,Hao Lu
e33b4b6761d2cf9745e94af1308d98ebd168a3ab,"Use c10::variant-based enums for Reduction

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/27942

Test Plan: Imported from OSS

Differential Revision: D18202857

Pulled By: yf225

fbshipit-source-id: 0303ce2508e3b7665c6a91ae270a7d0ef0e45900",2019-10-29T21:13:37Z,Will Feng
7cb72704ccaffc47ad0fb37698b364e3e0d0f11b,"Constrain sdpa to fx strides (#111721)

Fix for https://github.com/pytorch/pytorch/issues/109607. sdpa requires last dimension strides to be 1. Add constraint so that we run the op with the strides we observed in tracing.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/111721
Approved by: https://github.com/drisspg, https://github.com/Chillee, https://github.com/jansel
ghstack dependencies: #111976",2023-10-26T23:59:56Z,Elias Ellison
506d41d65981983f0ea9661bdeca20f4f04462a5,"Improve disable name match (#71499)

Summary:
Allows disabling issues to disable all parametrized tests with dtypes.

Tested locally with:
1. .pytorch-disabled-tests.json as
```
{""test_bitwise_ops (__main__.TestBinaryUfuncs)"": [""https://github.com/pytorch/pytorch/issues/99999"", [""mac""]]}
```
and running `python test_binary_ufuncs.py --import-disabled-tests -k test_bitwise_ops` yields all tests skipped.

2. .pytorch-disabled-tests.json as
```
{""test_bitwise_ops_cpu_int16 (__main__.TestBinaryUfuncsCPU)"": [""https://github.com/pytorch/pytorch/issues/99999"", [""mac""]]}
```
and running `python test_binary_ufuncs.py --import-disabled-tests -k test_bitwise_ops` yields only `test_bitwise_ops_cpu_int16` skipped.

NOTE: this only works with dtype parametrization, not all prefixes, e.g., disabling `test_async_script` would NOT disable `test_async_script_capture`. This is the most intuitive behavior, I believe, but I can be convinced otherwise.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/71499

Reviewed By: mruberry

Differential Revision: *********

Pulled By: janeyx99

fbshipit-source-id: 98a84f9e80402978fa8d22e0f018e6c6c4339a72
(cherry picked from commit 3f778919caebd3f5cae13963b4824088543e2311)",2022-01-25T00:44:47Z,Jane Xu
0c55f1bdecf2df6d0fb3ebff8efde70d4da6aafc,"[torchelastic] Improve process termination logic (#61602)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/61602

The diff introduces signal handlers and SignalException that is raised when the agent process receives SIGTERM or SIGINT.

When any of these signals received, the termination handler will raise the `SignalException`. The exception will then be processed by the main agent loop. The `shutdown(signum)` will be invoked, that would propagate the received signal to the child processes. The default 30 seconds timeout introduced: if child processes will not be able gracefully terminate during this timeout, the agent process would kill the processes via SIGKILL.

Test Plan: unittests, sandcastle

Reviewed By: cbalioglu

Differential Revision: *********

fbshipit-source-id: 3dbca2125676dc18d417cc3e3bb0301fdd42737a",2021-07-23T17:58:39Z,Aliaksandr Ivanou
49611a33297140a9c736b8630142af8438c526ac,"[PyTorch] MHA: simplify gemm_nt (#72460)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/72460

Just call existing matmul (which, IIUC, handles batching itself) rather than doing a few view ops. (Please let me know if this is actually a bad idea and why!)
ghstack-source-id: 149067333

Test Plan: CI

Reviewed By: ngimel

Differential Revision: *********

fbshipit-source-id: ace37ad3110e1134db6c8b638ae302f0d556e00a
(cherry picked from commit 258231c0f951bd701da179eaedc1ef795416c53f)",2022-02-15T02:12:50Z,Scott Wolchok
b19cf868e8ba5c068738b0ce940701027b67f84f,"Back out ""Support fp8 in AOTInductor + support optional<> in C ABI (#112527)"" (#113747)

Test Plan: sandcastle

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/113747
Approved by: https://github.com/chenyang78, https://github.com/khabinov",2023-11-15T22:42:22Z,Wei Wei
1a2edf6dca0111dc32a041284a0f3d9688fecef2,"[AOTI] Fix _mm_plus_mm codegen (#131689)

Summary: Fixes https://github.com/pytorch/pytorch/issues/128474

Pull Request resolved: https://github.com/pytorch/pytorch/pull/131689
Approved by: https://github.com/chenyang78",2024-07-25T13:43:18Z,Bin Bao
0c6a18de8de2a791a0d8b3526eab6401df49197f,"Add torch.promote_types function

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/26655

Test Plan: Imported from OSS

Differential Revision: D17556196

Pulled By: nairbv

fbshipit-source-id: eeebce8968bfb2ffd25c066595bc19e5dee6ea6f",2019-09-27T23:46:43Z,Brian Vaughan
0048243f70f37a3ae74725fb21c88704d3ab62bb,"Check compiler -v to determine compiler (fix #33701) (#37293)

Summary:
As described in the issue (https://github.com/pytorch/pytorch/issues/33701) the compiler check
	for building cpp extensions does not work with ccache.
	In this case we check compiler -v to determine which
	compiler is actually used and check it.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37293

Differential Revision: D21256913

Pulled By: ezyang

fbshipit-source-id: 5483a10cc2dbcff98a7f069ea9dbc0c12b6502dc",2020-04-27T17:46:07Z,Lukas Koestler
66d27504e3feddfc731457fc0cea7b1c1dff7dbe,"allow building docker without torchvision (#26168)

Summary:
There is an issue with the torchvision version not matching the pytorch version if one builds the docker from a tag, see issue https://github.com/pytorch/pytorch/issues/25917.  The current solution requires one to re-init the submodules or manually change the version of torchvision.  This PR allows one to build the docker image without torchvision, which not only fixes the above mentioned bug but also frees non-image pytorch users from the tyranny of torchvision :laughing:.

In all seriousness, for NLP researchers especially torchvision isn't a necessity for pytorch and all non-essential items shouldn't be in the docker.  This option removes one extra thing that can go wrong.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/26168

Differential Revision: D17550001

Pulled By: soumith

fbshipit-source-id: 48b8b9e22b75eef3afb392c618742215d3920e9d",2019-09-24T16:09:58Z,David Pollack
6988e40b484205c4354da8a7691dacfb23a90bbe,"[quant][fx] Lower operator.matmul in convert_fx (#113954)

Summary: We support lowering `torch.matmul` but not
`operator.matmul`. This commit adds support for the latter,
which enables lowering the shorthand `@`. This address
https://github.com/pytorch/pytorch/issues/111450.

Test Plan:
python test/test_quantization.py TestQuantizeFx

Reviewers: jerryzh168

Subscribers: jerryzh168, supriyar
Pull Request resolved: https://github.com/pytorch/pytorch/pull/113954
Approved by: https://github.com/jerryzh168",2023-12-08T21:53:57Z,andrewor14
97da60d5116fa2e81a8177ea95716832023599ed,"Updating submodules

Summary:
GitHub commits:

https://github.com/facebook/fbthrift/commit/ea8bae1f0f2a57618e8316bcdb2ecc4d34d9f538
https://github.com/facebook/folly/commit/134472ee45780ca2afa6f64cb7baac318c60a7c3
https://github.com/facebook/proxygen/commit/37e6cf9d62637be4936bfcebab188ccdf374e0fc
https://github.com/facebook/rocksdb/commit/eb367d45c0d96969f66aff0a16bee201f52beb1a
https://github.com/facebookincubator/mvfst/commit/76de6e15c0b6c0bcad664d59e40d113f86ccc0ea
https://github.com/pytorch/fbgemm/commit/e1b1a55309701f0f6d70afd7ad659d6f22c3e1ab

Test Plan: n/a

Reviewed By: wittgenst

fbshipit-source-id: 9d0d688d81be822900475223a787c5649e143e85",2020-02-25T01:31:33Z,svcscm
826550a32ed447abb7cc32de21bf7c192c6c2dd3,"Update the onnx Gemm op to FC/FCTransposed logic in caffe2 onnx backend (#10108)

Summary:
The broadcast is used by default when the opset version is greater then 6.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10108

Reviewed By: bddppq

Differential Revision: D9176934

Pulled By: houseroad

fbshipit-source-id: b737bd87b0ddc241c657d35856d1273c9950eeba",2018-08-20T23:02:06Z,JerryShih
f0ea6862ba9fab34878fb2fa881f817b71156283,"Support for pruning delays in Adagrad Optimizer (#34527)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/34527

Adding support for prune_delays and prune ratios in Adagrad optimizer.

Test Plan:
Tested via unit tests in masked_adagrad_optimizer_test. Added unit test  for prune_delay versions of MaskedAdagrad

buck build caffe2/caffe2/fb/optimizers:masked_adagrad_optimizer_test; buck-out/gen/caffe2/caffe2/fb/optimizers/masked_adagrad_optimizer_test#binary.par

buck test caffe2/caffe2/fb/dper/layer_models/tests/split_1:sparse_nn_test -- 'test_pruning'

All Dper tests passed https://our.intern.facebook.com/intern/testinfra/testrun/7599824380741217

Reviewed By: chocjy

Differential Revision: D20313419

fbshipit-source-id: 5c2c8d4e0fc2ec538bcd6f145c6b87a2381f90f3",2020-04-09T19:46:58Z,Dhruv Choudhary
1280363badddd622481695ac677dc8b75cad6a48,"Port `mean` kernel to structured kernels. (#61643)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/61643

Tracking issue: #55070

Test Plan: Imported from OSS

Reviewed By: ejguan

Differential Revision: *********

Pulled By: ezyang

fbshipit-source-id: dc95baf593096c03fb5f292ee6c36de3cc7f2b35",2021-08-13T15:20:19Z,Yukio Siraichi
532670cce0f1acf3d3fcc98a9a1c26f5428e184e,[cuda] add all gencode archs,2015-07-24T01:59:32Z,Yangqing Jia
e867831b8426a7fd9e4dcb86a2660e98c0962da1,"extend replaceConvolutionWithAtenConv to handle conv_transpose3d (#76888)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76888
Approved by: https://github.com/eellison",2022-05-13T16:40:12Z,Jiayi Sun
d3b6c5e556a75871eb5d62b35ba5703c6953f75d,Support output_padding in ConvTranspose while doing ONNX exporting (#4583),2018-01-11T17:31:06Z,Lu Fang
c5333cdfba36932dd1aaa14d6961e7cfc31c0f00,"[nnc] tensorexpr for quantized::add (#70188)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/70188

Test Plan: Imported from OSS

Reviewed By: ZolotukhinM

Differential Revision: *********

Pulled By: IvanKobzarev

fbshipit-source-id: bd4e451bfd7531f31f216def2c3c1ba2f2e566e7",2021-12-21T20:28:24Z,Ivan Kobzarev
79534867ac549c29b7895c43740d411ff63af867,"Migrate about 100 kernel to C10 full dispatcher (#54109)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54109

Codemod command generated by https://github.com/pytorch/pytorch/pull/54098

ghstack-source-id: 124114894

Test Plan: CI

Reviewed By: smessmer

Differential Revision: D27100359

fbshipit-source-id: 8338405274a2a020856af6e4a35a2fb21438f2a8",2021-03-17T20:33:39Z,Wenlei Xie
3a77f9aaaf60b348ac4fc3bdcedc3e3238fb272f,"[quant][api] Move torch.ao.quantization.pt2e.quantizer to torch.ao.quantization.quantizer (#105885)

Summary: moving quantizer to torch.ao.quantization to make it a public api, since pt2e is a folder for implementations

Test Plan:
CIs

sanity check: ""buck test //executorch/backends/xnnpack/test:test_xnnpack_quantized_models -- test_resnet18""

Differential Revision: D47727838

Pull Request resolved: https://github.com/pytorch/pytorch/pull/105885
Approved by: https://github.com/andrewor14",2023-07-26T18:20:09Z,Jerry Zhang
a919742149601888c793447c1a6ab262979f1dde,"c10::optional -> std::optional in PyTorch (#137333)

Test Plan: Sandcastle

Differential Revision: D63876535

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137333
Approved by: https://github.com/Skylion007, https://github.com/albanD",2024-10-11T00:16:10Z,Richard Barnes
362525724bdba375defd6405cfe1b46a6ea222d3,"type promote clamp (#77035)

Fixes #76630
When clamp(Tensor, Tensor) is structured, big parts of this PR won't be needed, but for now let's fix type promotion to make behavior more regular.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/77035
Approved by: https://github.com/mruberry",2022-05-09T05:54:17Z,Natalia Gimelshein
3611d26a25bd889627403a808ea667ac99c09904,"[JIT] Optimize FunctionSchema::checkArg for the Tensor case. (#48034)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/48034

The Tensor case is one of the most common and the existing check can be
made faster. This results in a ~21% improvement on DeepAndWide model and
would improve other models as well.

Before the change:
```
505[ms]
491[ms]
514[ms]
538[ms]
514[ms]
554[ms]
556[ms]
512[ms]
516[ms]
527[ms]
```

After the change:
```
406[ms]
394[ms]
414[ms]
423[ms]
449[ms]
397[ms]
410[ms]
389[ms]
395[ms]
414[ms]
```

Differential Revision: D24999486

Test Plan: Imported from OSS

Reviewed By: zdevito

Pulled By: ZolotukhinM

fbshipit-source-id: 7139a3a38f9c44e8ea793afe2fc662ff51cc0460",2020-11-17T03:00:49Z,Mikhail Zolotukhin
e913f77c60b8c86434da3b8d88e6e6b6b2319e0b,"Revert ""Made FlexAttention rewrite getitem calls to use aten.index in score_mod (#124799)""

This reverts commit 9bccafc31c9d489b727155e95633efd19adbceaa.

Reverted https://github.com/pytorch/pytorch/pull/124799 on behalf of https://github.com/clee2000 due to broke tests but only on crossref https://github.com/pytorch/pytorch/actions/runs/8841521519/job/24279075171, added no td label so itll actually run this time ([comment](https://github.com/pytorch/pytorch/pull/124799#issuecomment-2078530797))",2024-04-26T02:35:14Z,PyTorch MergeBot
33a950924ad8a370d9945515c51feb0eb75fbf3b,"Skip Slice if it's no op (#19155)

Summary:
If it's identity op, just skip the slice and return the input.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/19155

Reviewed By: zrphercule

Differential Revision: D14890238

Pulled By: houseroad

fbshipit-source-id: f87b93df2cca0cb0e8ae2a1d95ba148044eafd4a",2019-04-11T19:23:30Z,Lu Fang
534db77e738ce53625a4b1a870f6fda332e2e8a2,"Autotune pointwise/reduction in max_autotune mode (#94556)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/94556
Approved by: https://github.com/ngimel",2023-02-10T04:29:07Z,Jason Ansel
3182642b2c7880618998d90e08edbd2a19619df7,"[functorch] Beef up transform limitations doc (pytorch/functorch#879)

I want to be able to point someone at this page whenever we get asked
about the limitations of vmap. Please let me know if there are things
we're still missing from here",2022-06-17T17:22:07Z,Richard Zou
ae1c365dbdbf667ae24c57eec9f2e6b9debf16bd,Add TH_INDEX_BASE to nDimension and stride functions,2017-03-26T13:25:53Z,albanD
3107f1dcd5f5aa6e2053a7f610d1478277f2a415,"fix align_corners doc

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23707

Differential Revision: D16617565

Pulled By: ezyang

fbshipit-source-id: 9ae581e9233d8c2b92f35b9486af1dab30ce8e3a",2019-08-02T19:29:29Z,Tongzhou Wang
5dd07324578f5110a2ec5c213fb559bc49004c7a,"[ZeRO] Add ctor support for multiple param groups (#72578)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/72578

**Overview**
This adds `ZeroRedundancyOptimizer` constructor support for multiple parameter groups (i.e. passing an `iterable` of `dict`s instead of an `iterable` of `torch.Tensor` as the `parameters` argument) to mirror the API for non-sharded optimizers.

Fixes https://github.com/pytorch/pytorch/issues/71347 and https://github.com/pytorch/pytorch/issues/59973.

This modifies `test_collect_shards()` to skip if ROCm.

**Test Plan**
I adjusted the existing constructor test, and I added a test for parity between constructing with two parameter groups up front versus constructor with one parameter group and adding the second parameter group after (via `add_param_group()`) versus a non-sharded optimizer.

Test Plan: Imported from OSS

Reviewed By: rohan-varma

Differential Revision: D34106940

Pulled By: awgu

fbshipit-source-id: 7e70fc0b3cec891646e0698eaedf02ff4354c128
(cherry picked from commit 40f2d45172ba3286b64000a466e42c055cca8ddc)",2022-02-15T16:47:06Z,Andrew Gu
2bf68e72d5d443e1aae7533894595a008299347f,Add hook system to autograd and nn,2016-08-23T18:50:12Z,Adam Paszke
dcc1e1cd87d1b341a853ad700520919a7a39821e,"[BE] Use `!{{ common.checkout_pytorch(""recursive"") }}` in binary builds workflows

Pull Request resolved: https://github.com/pytorch/pytorch/pull/71663",2022-01-21T23:43:29Z,Nikita Shulga
87c5f02f3dccd67b61bdb5098c6048f5981dc1e2,"jit: Conv3d + BatchNorm3d fusion (#40082)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/40082

Differential Revision: D22120340

Pulled By: jerryzh168

fbshipit-source-id: fce6c5f03fe7ab6c60620cbdf547d5a466a470e3",2020-06-22T18:14:18Z,"Zhang, Xiaobing"
84b7daadb22ff913f3aaa5fa59754f55228845a3,"Relax verify of VariableFlags (#4191)

* Fix another leak in pybind11 code.

This time caused by an upstream pybind11 bug:

https://github.com/pybind/pybind11/pull/1216

This changes causes the code to go down a non-buggy pathway.

* Relax verify of VariableFlags

If we trace with a defined tensor, but see a run with a undefined
tensors we now allow that run to happen, replacing the tensor with
zeros.

This also fixes a bug where stage 0 tensors were not
checked against their verify flags.

This change does _not_ handle all bad situations that can happen.
For instance if the first thing traced has a undefined tensor but
a later tensor is defined, then it will fail because the graph itself
does not contain the trace for the derivative of the tensor.
However it is possible to work around this later case by
dry-running the function:

   z = Variable(...,requires_grad=True)
   x,y = f(z)
   (x.sum() + y.sum()).backward()",2017-12-15T17:57:31Z,Zachary DeVito
f71c3d265ab52589f983dd252d61461db4e7dbbd,"[ROCm] remove triton-rocm commit pin and merge pins with triton.txt (#133438)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133438
Approved by: https://github.com/jithunnair-amd, https://github.com/malfet",2024-08-24T18:26:49Z,Jack Taylor
8423ab4f99fb499d540316e047f0d4a0c9ad630c,"Fix `CosineAnnealingWarmRestart` annotation (#61106)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/44770.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/61106

Reviewed By: 1ntEgr8

Differential Revision: D29635764

Pulled By: walterddr

fbshipit-source-id: ddc45a7f04532a76d033ae7774706da1fa8608f7",2021-07-09T14:52:22Z,Philip Meier
d957c2d5de004083b404031bbcbee21dac52c22f,"[Doc] update default magma cuda version in readme (#122125)

Since we use cuda 12.1 by default now, it would be better to update the doc.

Many people (including me), want to directly copy-paste commands in readme 😉  Let's make our life easier.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/122125
Approved by: https://github.com/malfet",2024-05-28T03:37:23Z,youkaichao
8520ce5f78227a81fa1a0b37dd73f9a43bf31fc2,"Fix incorrect trace of post-accumulate grad hook on tensor with zero dims (#135226)

Fix incorrect trace of post-accumulate grad hook on tensor with zero dimensions

Fixes #135207

Pull Request resolved: https://github.com/pytorch/pytorch/pull/135226
Approved by: https://github.com/xmfan",2024-09-06T18:19:52Z,wdziurdz
cfbd06d7a1d0dedfc770938755eba5d754d800df,"add all pools, Batchnorm and Tanh (i.e. all ideeped MKLDNN ops) to MKLDNNFuser (#56541)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/56541

Reviewed By: pbelevich

Differential Revision: D27930353

Pulled By: Krovatkin

fbshipit-source-id: 4d5b932bad4154e8bdd6e35498354e13b39c87a1",2021-04-27T15:57:50Z,Nikolay Korovaiko
f11120967e7c28f5ad9bae261e6a65fb2183927e,"Support EnumerableShardingSpec in ShardedTensor. (#59061)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/59061

Overall Design: https://github.com/pytorch/pytorch/issues/55207

This PR builds upon https://github.com/pytorch/pytorch/pull/58517 and
https://github.com/pytorch/pytorch/pull/57409 to support creating a
ShardedTensor using EnumerableShardingSpec.
ghstack-source-id: 130780376

Test Plan:
1) unit tests
2) waitforbuildbot

Reviewed By: SciPioneer

Differential Revision: D28734551

fbshipit-source-id: 656f5f2b22041dae071bc475f19fe94c969716e8",2021-06-10T06:19:55Z,Pritam Damania
8d7607e3461870b884cf56f43efea4c9e9f6c0f8,"Add attribute exhaustive_search in _blacklist_caffe2_args (#12805)

Summary:
- exhaustive_search attribute will be blacklisted so it
     will be discarded from the coverted onnx model. At present
     it throws error while verifying the onnx model

Signed-off-by: Parth Raichura <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/12805

Differential Revision: D10502374

Pulled By: ezyang

fbshipit-source-id: 0926dfa3237a8a431184e7f7250146e5b0cbfb85",2018-10-23T05:45:50Z,Parth Raichura
e89685b0b541386825479bf120f6a1aa6d000238,"Revert ""[inductor] Use decomposition for _to_copy (#90314)""

This reverts commit 3fdb5f2dda7164f6282e80c39799843527d135e7.

Reverted https://github.com/pytorch/pytorch/pull/90314 on behalf of https://github.com/desertfire due to regresses performance on hf_Bert",2022-12-08T18:29:06Z,PyTorch MergeBot
a721d27c5124e9a601ae8bb1c1129f636a461633,"Make TORCH_SHOW_DISPATCH_TRACE actually work (#82277)

It looks like DEBUG macro is never actually set anywhere, see
https://github.com/pytorch/pytorch/issues/82276

Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/82277
Approved by: https://github.com/malfet",2022-07-27T00:06:17Z,Edward Z. Yang
bd0e9a73c79128153be56a4de1a3086ae1f4e960,"Fix some simple build error on MacOS (#949)

Issue #948

Signed-off-by: Zhou Chang <<EMAIL>>",2017-03-07T14:47:49Z,Zhou Chang
326a4cc8156a4d9eb11bb4ef2a275f81bfe96742,"Support map autograd and pytree in/out. (#101633)

Rebased https://github.com/pytorch/pytorch/pull/100494 and added dummy AOTConfig.

This PR adds autograd and pytree support for map operator.

Implementation-wise:

1. We temporarily make two HigherOrderOperators, ""map"" and ""map_impl"":
- ""map"" is user-facing. Currently, it unwraps the pytrees in inputs and create a flat_fn for it. Dynamo currently cannot deal with pytree.tree_flatten and pytree.tree_unflatten, we therefore make it a HigherOrderOperator to trigger dynamo logic of handling HigherOrderOperators.
- ""map_impl"" is the actual operator that works with the rest of torch subsystems such as functionalization, make_fx. It accepts flattend arguments, and a num_mapped_args integer denoting how many of the flattend arguments need to mapped i.e. their first dimension will be unstacked.

2. We create the forward and backward graph in autograd key and call torch.autograd.Function. Currently, the backward graph is recomputation-based and we need to partition the joint graph in the future to be more efficient.

Example traced graphs for map operators:
### Case 1: simple f and autograd
```python
def f(x, y):
    return x + y

def g(xs, y):
    out = control_flow.map(f, xs, y)
    return torch.autograd.grad(out, (xs, y), torch.ones_like(out))

gm = make_fx(g, tracing_mode=""symbolic"")(torch.ones(3, 4, 5, requires_grad=True), torch.ones(5, requires_grad=True))
# gm.print_readable() produces following:
class g(torch.nn.Module):
    def forward(self, xs_1: f32[3, s1, s2], y_1: f32[s2]):
        # No stacktrace found for following nodes
        body_graph_0 = self.body_graph_0
        map_impl = torch.ops.map_impl(body_graph_0, 1, xs_1, y_1);  body_graph_0 = None
        getitem: f32[3, s1, s2] = map_impl[0];  map_impl = None
        ones_like: f32[3, s1, s2] = torch.ops.aten.ones_like.default(getitem, pin_memory = False)
        is_same_size = torch.ops.aten.is_same_size.default(getitem, ones_like);  getitem = None
        body_graph_1 = self.body_graph_1
        map_impl_1 = torch.ops.map_impl(body_graph_1, 2, xs_1, ones_like, y_1);  body_graph_1 = xs_1 = ones_like = None
        getitem_1 = map_impl_1[0]
        getitem_2: f32[3, s1, s2] = map_impl_1[1]
        getitem_3: f32[3, s2] = map_impl_1[2];  map_impl_1 = None
        sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(getitem_3, [0], True);  getitem_3 = None
        sym_size: Sym(s2) = torch.ops.aten.sym_size(y_1, 0);  y_1 = None
        view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = sym_size = None
        return (getitem_2, view)

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s2]):
            # No stacktrace found for following nodes
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(arg1_1, arg2_1);  arg1_1 = arg2_1 = None
            return [add]

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s1, s2], arg3_1: f32[s2]):
            # No stacktrace found for following nodes
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(arg1_1, arg3_1);  arg1_1 = None
            is_same_size = torch.ops.aten.is_same_size.default(add, arg2_1);  add = None
            sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(arg2_1, [0], True)
            sym_size: Sym(s2) = torch.ops.aten.sym_size(arg3_1, 0);  arg3_1 = None
            view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = sym_size = None
            return [None, arg2_1, view]
```
### Case 2: list input/output f and autograd
```python
def f(x, y):
    return [x[0].cos() + y.sin(), x[1].sin() * y.cos()]

def g(xs, y):
    out = control_flow.map(f, xs, y)
    flat_out, _ = pytree.tree_flatten(out)
    flat_inp, _ = pytree.tree_flatten((xs, y))
    requires_grad_inp = [inp for inp in flat_inp if inp.requires_grad]
    return torch.autograd.grad(flat_out, requires_grad_inp, [torch.ones_like(out) for out in flat_out])

gm = make_fx(g, tracing_mode=""symbolic"")(
    [torch.ones(3, 4, 5), torch.ones(3, 4, 5, requires_grad=True)],
    torch.ones(5, requires_grad=True))

# gm.print_readable() produces following:
class g(torch.nn.Module):
    def forward(self, xs, y):
        xs_1: f32[3, s1, s2], xs_2: f32[3, s1, s2], y_1: f32[s2], = fx_pytree.tree_flatten_spec([xs, y], self._in_spec)
        # No stacktrace found for following nodes
        body_graph_0 = self.body_graph_0
        map_impl = torch.ops.map_impl(body_graph_0, 2, xs_1, xs_2, y_1);  body_graph_0 = None
        getitem: f32[3, s1, s2] = map_impl[0]
        getitem_1: f32[3, s1, s2] = map_impl[1];  map_impl = None
        ones_like: f32[3, s1, s2] = torch.ops.aten.ones_like.default(getitem, pin_memory = False)
        ones_like_1: f32[3, s1, s2] = torch.ops.aten.ones_like.default(getitem_1, pin_memory = False)
        is_same_size = torch.ops.aten.is_same_size.default(getitem, ones_like);  getitem = None
        is_same_size_1 = torch.ops.aten.is_same_size.default(getitem_1, ones_like_1);  getitem_1 = None
        body_graph_1 = self.body_graph_1
        map_impl_1 = torch.ops.map_impl(body_graph_1, 4, xs_1, xs_2, ones_like, ones_like_1, y_1);  body_graph_1 = xs_1 = xs_2 = ones_like = ones_like_1 = None
        getitem_2 = map_impl_1[0]
        getitem_3 = map_impl_1[1]
        getitem_4: f32[3, s1, s2] = map_impl_1[2]
        getitem_5: f32[3, s2] = map_impl_1[3];  map_impl_1 = None
        sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(getitem_5, [0], True);  getitem_5 = None
        sym_size: Sym(s2) = torch.ops.aten.sym_size(y_1, 0);  y_1 = None
        view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = sym_size = None
        return pytree.tree_unflatten([getitem_4, view], self._out_spec)

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s1, s2], arg3_1: f32[s2]):
            # No stacktrace found for following nodes
            cos: f32[s1, s2] = torch.ops.aten.cos.default(arg1_1);  arg1_1 = None
            sin: f32[s2] = torch.ops.aten.sin.default(arg3_1)
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(cos, sin);  cos = sin = None
            sin_1: f32[s1, s2] = torch.ops.aten.sin.default(arg2_1);  arg2_1 = None
            cos_1: f32[s2] = torch.ops.aten.cos.default(arg3_1);  arg3_1 = None
            mul: f32[s1, s2] = torch.ops.aten.mul.Tensor(sin_1, cos_1);  sin_1 = cos_1 = None
            return [add, mul]

    class <lambda>(torch.nn.Module):
        def forward(self, arg0_1, arg1_1: f32[s1, s2], arg2_1: f32[s1, s2], arg3_1: f32[s1, s2], arg4_1: f32[s1, s2], arg5_1: f32[s2]):
            # No stacktrace found for following nodes
            cos: f32[s1, s2] = torch.ops.aten.cos.default(arg1_1);  arg1_1 = None
            sin: f32[s2] = torch.ops.aten.sin.default(arg5_1)
            add: f32[s1, s2] = torch.ops.aten.add.Tensor(cos, sin);  cos = sin = None
            sin_1: f32[s1, s2] = torch.ops.aten.sin.default(arg2_1)
            cos_1: f32[s2] = torch.ops.aten.cos.default(arg5_1)
            mul: f32[s1, s2] = torch.ops.aten.mul.Tensor(sin_1, cos_1)
            is_same_size = torch.ops.aten.is_same_size.default(add, arg3_1);  add = None
            is_same_size_1 = torch.ops.aten.is_same_size.default(mul, arg4_1);  mul = None
            mul_1: f32[s1, s2] = torch.ops.aten.mul.Tensor(arg4_1, sin_1);  sin_1 = None
            mul_2: f32[s1, s2] = torch.ops.aten.mul.Tensor(arg4_1, cos_1);  arg4_1 = cos_1 = None
            sum_1: f32[1, s2] = torch.ops.aten.sum.dim_IntList(mul_1, [0], True);  mul_1 = None
            sym_size: Sym(s2) = torch.ops.aten.sym_size(arg5_1, 0)
            view: f32[s2] = torch.ops.aten.view.default(sum_1, [sym_size]);  sum_1 = None

            #
            sin_2: f32[s2] = torch.ops.aten.sin.default(arg5_1)
            neg: f32[s2] = torch.ops.aten.neg.default(sin_2);  sin_2 = None
            mul_3: f32[s2] = torch.ops.aten.mul.Tensor(view, neg);  view = neg = None
            cos_2: f32[s1, s2] = torch.ops.aten.cos.default(arg2_1);  arg2_1 = None
            mul_4: f32[s1, s2] = torch.ops.aten.mul.Tensor(mul_2, cos_2);  mul_2 = cos_2 = None
            sum_2: f32[1, s2] = torch.ops.aten.sum.dim_IntList(arg3_1, [0], True);  arg3_1 = None
            view_1: f32[s2] = torch.ops.aten.view.default(sum_2, [sym_size]);  sum_2 = sym_size = None
            cos_3: f32[s2] = torch.ops.aten.cos.default(arg5_1);  arg5_1 = None
            mul_5: f32[s2] = torch.ops.aten.mul.Tensor(view_1, cos_3);  view_1 = cos_3 = None
            add_1: f32[s2] = torch.ops.aten.add.Tensor(mul_3, mul_5);  mul_3 = mul_5 = None
            return [None, None, mul_4, add_1]
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/101633
Approved by: https://github.com/zou3519",2023-05-17T16:52:26Z,ydwu4
ad8386c1320c3abddaf6cbdd580866de1720a735,"Lint fix

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76876

Approved by: https://github.com/ngimel",2022-05-05T04:50:17Z,anjali411
bccb727b65b3f670c57aaac9be4f0afbbb495956,"Remove wrong ""input"" arg from scatter_() docstring (#7550)",2018-05-14T19:33:47Z,Martin Drawitsch
6ceec53579dc88e9a3823dc602013fbd405c2a14,"[dynamo][cpp-guards] Fix test for CPP guard manager (#123515)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/123515
Approved by: https://github.com/guilhermeleobas, https://github.com/jansel",2024-04-07T17:53:51Z,Animesh Jain
730965c246192c94c804e5ac4a95f175dca2fb18,"Improve `torch.flatten` docs and add tests to test_view_ops (#49501)

Summary:
Addresses https://github.com/pytorch/pytorch/issues/39474

Pull Request resolved: https://github.com/pytorch/pytorch/pull/49501

Reviewed By: mruberry

Differential Revision: D25734450

Pulled By: soulitzer

fbshipit-source-id: 993667dd07acd81a4616465e0a3b94bde449193e",2020-12-31T04:32:51Z,Jeffrey Wan
4d53c632e0409e66f40f9c74486a58ce41ab70a2,"Remove unnecessary cuda flags.

-Xcompiler -std=c++11 is not needed, otherwise gcc produces warnings.",2017-01-04T08:08:41Z,Yangqing Jia
9dea86f86bddbe9ff767e2346f8b3aba7cd5b41c,"Make ProfiledTensorType hashable

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23116

Differential Revision: D16519748

Pulled By: Krovatkin

fbshipit-source-id: 25090678d82d5dc9ca0a48aef45eeb62b8ac8d45",2019-07-30T20:02:26Z,Nikolay Korovaiko
8011405a7b8e33eefdeb2b6ccc7535c4403088e3,"Adding docs on how to run backwards compatability test (#81431)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/81431
Approved by: https://github.com/salilsdesai",2022-07-13T22:03:40Z,John Clow
5ce88e7e71771d8ac730b32d5c4155b9cbe3c8ea,"remove unnecessary import introduced in PR 106535 (#107440)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/107440
Approved by: https://github.com/fduwjj
ghstack dependencies: #106535",2023-08-18T04:34:35Z,Xilun Wu
3a26772c53f856cc6bf24adf3256abb16843983c,"[functorch] Actually made some modifications to fix OpOverload changes (pytorch/functorch#579)

* actually fixed recent issues

* fixed new issues

* fixed overload issue

* Fix epsilons",2022-03-11T00:20:10Z,Horace He
e645771e954d342c232e33ea61e38710cc1fdb85,"Revert ""as_strided: Fix default storage_offset for reference implementation (#89513)""

This reverts commit ba70a8be03f2fca222deee030bf7d9d15260b549.

Reverted https://github.com/pytorch/pytorch/pull/89513 on behalf of https://github.com/kit1980 due to Broke multiple workflows, 2 unexpected successes for autograd tests",2022-12-06T07:14:16Z,PyTorch MergeBot
20a2e526efb353e302f5b8778b63e45a5eade240,"build a generic future<T> (#29579)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/29579

Per #28923, this diff is to move Future<Message> to torch::utils and extend it to be Future<T>, most of implementations are copied from FutureMessage and ivalue::Future. merge ivalue::Future with Future<T> will be done separately.

The main difference between Future<T>  and FutureMessage is the error handling, instead of checking message type inside Future to handle error, this future<T> owns has_error_ and error_ states.

also this future passes value_, has_error_ and error_ states to callbacks for easily read future states.

In next diff, a torch script rpc async API will be created, before the API returns, it will create an ivalue::Future and passes it to Future<T>'s call back where state of ivalue::Future will be set.  In this way, the torch script rpc async API  can still return a ivalue::Future and call wait() to get its state appropriately afterwards.
ghstack-source-id: 95479525

Test Plan: unit tests

Differential Revision: *********

fbshipit-source-id: 48a65712656a72c2feb0bb3ec8b308c0528986a6",2019-12-13T00:54:23Z,Yanli Zhao
fc3103b1162a3312e4ad74005d0b698a333c0750,"fixing a naming issue in creating a residual loop node in a bailout graph (#31400)

Summary:
This addresses the issue of differentiating between `%4` in
`%12 : int, %y.1 : Tensor = prim::Loop(%9, %6, %4, %3)` and `%y.5 : Double(3) = aten::cat(%22, %4) # test_jit.py:3772:24` in `%4` loop's body in a residual continuation loop, because these should be different values.

```
[DUMP profiling_graph_executor_impl.cpp:124] with prim::BailoutTemplate_0 = graph(%z.1 : int,
[DUMP profiling_graph_executor_impl.cpp:124]       %size.1 : int):
[DUMP profiling_graph_executor_impl.cpp:124]   %2 : Tensor = prim::Constant[value= 1  1 [ CPUDoubleType{2} ]]()
[DUMP profiling_graph_executor_impl.cpp:124]   %3 : Double(2) = prim::BailOut[index=0](%2, %z.1, %size.1)
[DUMP profiling_graph_executor_impl.cpp:124]   %4 : int = prim::Constant[value=0]() # test_jit.py:3772:54
[DUMP profiling_graph_executor_impl.cpp:124]   %5 : None = prim::Constant()
[DUMP profiling_graph_executor_impl.cpp:124]   %6 : bool = prim::Constant[value=1]() # test_jit.py:3770:16
[DUMP profiling_graph_executor_impl.cpp:124]   %counters.1 : int[] = prim::ListConstruct()
[DUMP profiling_graph_executor_impl.cpp:124]   %8 : int = prim::Constant[value=8]()
[DUMP profiling_graph_executor_impl.cpp:124]   %9 : int = aten::__round_to_zero_floordiv(%size.1, %8)
[DUMP profiling_graph_executor_impl.cpp:124]   %10 : int = aten::mul(%9, %8)
[DUMP profiling_graph_executor_impl.cpp:124]   %11 : int = aten::sub(%size.1, %10)
[DUMP profiling_graph_executor_impl.cpp:124]   %12 : int, %y.1 : Tensor = prim::Loop(%9, %6, %4, %3) # test_jit.py:3770:16
[DUMP profiling_graph_executor_impl.cpp:124]     block0(%i.2 : int, %15 : int, %y.7 : Tensor):
[DUMP profiling_graph_executor_impl.cpp:124]       %17 : Double(2) = prim::BailOut[index=1](%y.7, %z.1, %counters.1, %9, %11, %i.2, %15)
[DUMP profiling_graph_executor_impl.cpp:124]       %18 : int[] = aten::append(%counters.1, %15) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %19 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %20 : Tensor = aten::ones(%19, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %21 : Double(1) = prim::BailOut[index=2](%20, %z.1, %counters.1, %9, %11, %i.2, %15, %17)
[DUMP profiling_graph_executor_impl.cpp:124]       %22 : Tensor[] = prim::ListConstruct(%17, %21)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.5 : Double(3) = aten::cat(%22, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %24 : int = prim::Constant[value=1]()
[DUMP profiling_graph_executor_impl.cpp:124]       %25 : int = aten::add(%15, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %26 : int[] = aten::append(%counters.1, %25) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %27 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %28 : Tensor = aten::ones(%27, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %29 : Double(1) = prim::BailOut[index=3](%28, %z.1, %counters.1, %9, %11, %i.2, %y.5, %25)
[DUMP profiling_graph_executor_impl.cpp:124]       %30 : Tensor[] = prim::ListConstruct(%y.5, %29)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.9 : Double(4) = aten::cat(%30, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %32 : int = aten::add(%25, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %33 : int[] = aten::append(%counters.1, %32) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %34 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %35 : Tensor = aten::ones(%34, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %36 : Double(1) = prim::BailOut[index=4](%35, %z.1, %counters.1, %9, %11, %i.2, %y.9, %32)
[DUMP profiling_graph_executor_impl.cpp:124]       %37 : Tensor[] = prim::ListConstruct(%y.9, %36)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.10 : Double(5) = aten::cat(%37, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %39 : int = aten::add(%32, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %40 : int[] = aten::append(%counters.1, %39) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %41 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %42 : Tensor = aten::ones(%41, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %43 : Double(1) = prim::BailOut[index=5](%42, %z.1, %counters.1, %9, %11, %i.2, %y.10, %39)
[DUMP profiling_graph_executor_impl.cpp:124]       %44 : Tensor[] = prim::ListConstruct(%y.10, %43)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.11 : Double(6) = aten::cat(%44, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %46 : int = aten::add(%39, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %47 : int[] = aten::append(%counters.1, %46) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %48 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %49 : Tensor = aten::ones(%48, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %50 : Double(1) = prim::BailOut[index=6](%49, %z.1, %counters.1, %9, %11, %i.2, %y.11, %46)
[DUMP profiling_graph_executor_impl.cpp:124]       %51 : Tensor[] = prim::ListConstruct(%y.11, %50)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.12 : Double(7) = aten::cat(%51, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %53 : int = aten::add(%46, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %54 : int[] = aten::append(%counters.1, %53) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %55 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %56 : Tensor = aten::ones(%55, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %57 : Double(1) = prim::BailOut[index=7](%56, %z.1, %counters.1, %9, %11, %i.2, %y.12, %53)
[DUMP profiling_graph_executor_impl.cpp:124]       %58 : Tensor[] = prim::ListConstruct(%y.12, %57)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.13 : Double(8) = aten::cat(%58, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %60 : int = aten::add(%53, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %61 : int[] = aten::append(%counters.1, %60) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %62 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %63 : Tensor = aten::ones(%62, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %64 : Double(1) = prim::BailOut[index=8](%63, %z.1, %counters.1, %9, %11, %i.2, %y.13, %60)
[DUMP profiling_graph_executor_impl.cpp:124]       %65 : Tensor[] = prim::ListConstruct(%y.13, %64)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.14 : Double(9) = aten::cat(%65, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %67 : int = aten::add(%60, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       %68 : int[] = aten::append(%counters.1, %67) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %69 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %70 : Tensor = aten::ones(%69, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %71 : Double(1) = prim::BailOut[index=9](%70, %z.1, %counters.1, %9, %11, %i.2, %y.14, %67)
[DUMP profiling_graph_executor_impl.cpp:124]       %72 : Tensor[] = prim::ListConstruct(%y.14, %71)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.15 : Tensor = aten::cat(%72, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %74 : int = aten::add(%67, %24)
[DUMP profiling_graph_executor_impl.cpp:124]       -> (%6, %74, %y.15)
[DUMP profiling_graph_executor_impl.cpp:124]   %75 : Double(10) = prim::BailOut[index=10](%y.1, %z.1, %counters.1, %11, %12)
[DUMP profiling_graph_executor_impl.cpp:124]   %76 : int, %y : Tensor = prim::Loop(%11, %6, %12, %75) # test_jit.py:3770:16
[DUMP profiling_graph_executor_impl.cpp:124]     block0(%i.1 : int, %79 : int, %y.6 : Tensor):
[DUMP profiling_graph_executor_impl.cpp:124]       %81 : Double(*) = prim::BailOut[index=11](%y.6, %z.1, %counters.1, %11, %i.1, %79)
[DUMP profiling_graph_executor_impl.cpp:124]       %82 : int[] = aten::append(%counters.1, %79) # test_jit.py:3771:20
[DUMP profiling_graph_executor_impl.cpp:124]       %83 : int[] = prim::ListConstruct(%z.1)
[DUMP profiling_graph_executor_impl.cpp:124]       %84 : Tensor = aten::ones(%83, %5, %5, %5, %5) # test_jit.py:3772:38
[DUMP profiling_graph_executor_impl.cpp:124]       %85 : Double(1) = prim::BailOut[index=12](%84, %counters.1, %11, %i.1, %79, %81)
[DUMP profiling_graph_executor_impl.cpp:124]       %86 : Tensor[] = prim::ListConstruct(%81, %85)
[DUMP profiling_graph_executor_impl.cpp:124]       %y.4 : Tensor = aten::cat(%86, %4) # test_jit.py:3772:24
[DUMP profiling_graph_executor_impl.cpp:124]       %88 : int = prim::Constant[value=1]()
[DUMP profiling_graph_executor_impl.cpp:124]       %89 : int = aten::add(%79, %88)
[DUMP profiling_graph_executor_impl.cpp:124]       -> (%6, %89, %y.4)
[DUMP profiling_graph_executor_impl.cpp:124]   %90 : Double(12) = prim::BailOut[index=13](%y, %counters.1)
[DUMP profiling_graph_executor_impl.cpp:124]   %91 : (Tensor, int[]) = prim::TupleConstruct(%90, %counters.1)
[DUMP profiling_graph_executor_impl.cpp:124]   return (%91)
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/31400

Differential Revision: D19172750

Pulled By: Krovatkin

fbshipit-source-id: 85d3aac4e80b65b83b6be3c0bca8075a731a2b7e",2019-12-19T08:32:43Z,Nikolay Korovaiko
aeed8a6ea4650d1092289a60e71d8d83875a0ba6,Remove duplicate entries and add optional marks in THCUNN.h,2016-11-15T20:22:14Z,Adam Paszke
8fe91d16b022954206c378f637cb3de81f8f219a,"Remove CUDA 11.6 note from complex docs (#100118)

Removes note in the complex docs pointing to the CUDA 11.6 wheels introduced in https://github.com/pytorch/pytorch/pull/80363.
Background: this warning was added via https://github.com/pytorch/pytorch/issues/79876 which pointed out a slow compilation time in 11.3. The 11.6 pip wheels were thus recommended but are not build anymore as our current support is 11.7, 11.8 (and 12.1 experimental in nightlies).

The note is confusing users as it doesn't explain why 11.6 is needed.
Reference: https://discuss.pytorch.org/t/complex-numbers-cuda-11-6-documentation-warning/178588/1

Pull Request resolved: https://github.com/pytorch/pytorch/pull/100118
Approved by: https://github.com/msaroufim",2023-04-27T16:26:24Z,pbialecki
6efcb6c718c6954df2157d93989ec7ed1821aafe,"Fix wrong ufmt exclusions in `.lintrunner.toml` (#124135)

Part of: #123062

In this pull request(#123809), there were some exclusions that should have been removed, but weren't.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/124135
Approved by: https://github.com/ezyang",2024-04-17T12:22:50Z,Yuanhao Ji
a4355d6b9a6adb06043ac75f99c0b4af35d026c5,"Revert ""Add --filter-rank to torchrun: allow logs filtering by rank (#118562)""

This reverts commit 73229b4f931f8cd1799b0905d61e3d8e85157bcd.

Reverted https://github.com/pytorch/pytorch/pull/118562 on behalf of https://github.com/xmfan due to breaks MAST precheck, flag naming conflict ([comment](https://github.com/pytorch/pytorch/pull/118562#issuecomment-1924916601))",2024-02-02T23:56:20Z,PyTorch MergeBot
444b52ff40cf4afce7bc3fdcf021a88eab3b954c,"[Dynamo] Simplify torch function mode stack guard (#135444)

The semantics of ignored modes previously had edge cases, this eliminates these by in essence filtering any ignored modes out of both the ref stack and the current torch function mode stack. This is purely to fix complexity in #135422.  The ignored modes handling will be removed in a future PR after https://github.com/pytorch/pytorch/pull/135422 lands, since we will then trace through DeviceContexts vs inserting them into the graph which needed these extra workarounds for correctness.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/135444
Approved by: https://github.com/anijain2305, https://github.com/williamwen42
ghstack dependencies: #134732, #133137, #135443",2024-09-09T23:02:11Z,Michael Lazos
8e4161517e112478a1c1f0290fedb91965f95aff,"div_kernel: throw when dividing by integer zero (#32629)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/327
Pull Request resolved: https://github.com/pytorch/pytorch/pull/32629

Differential Revision: *********

Pulled By: ezyang

fbshipit-source-id: f5bbb298f150efe63a698e8a0b53a84871d16560",2020-01-28T05:37:49Z,Wojciech Baranowski
631f0351313da2edeeb72cf8bb963035b11425d9,"Update forward AD not supported error message

Pull Request resolved: https://github.com/pytorch/pytorch/pull/75105

Approved by: https://github.com/albanD",2022-04-02T13:51:18Z,soulitzer
dcaa111dc8359a293e9efb3cd968073bb2038ac2,"support intersection by polyfill (#130672)

Fixes https://github.com/pytorch/pytorch/issues/130557

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130672
Approved by: https://github.com/anijain2305",2024-07-14T10:44:26Z,awayzjj
b3e24c53eb349f9d2614906599a3f62fd197c035,"use performance-unnecessary-value-param in clang-tidy (#102615)

performance-unnecessary-value-param has been disabled in clang-tidy for a long time. However, this check is actually useful and able to some interesting performance problems.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102615
Approved by: https://github.com/malfet, https://github.com/Skylion007",2023-07-28T17:36:59Z,cyy
42bd1abc62c17c349e1d53d512f3e0964ad1d77a,"[Inductor Cutlass backend] Tolerate dynamic shapes (#121497)

Previously, when the Cutlass backend was enabled, using dynamic shapes could lead to exceptions during JIT.

With this change, there are guards in place to just disable the Cutlass backend if dynamic dimensions are involved.

In addition, if no choices for a GEMM are available using the selected backends, then an ATen Kernel is used as fallback, even if the ATen backend is not enabled.

Test:
CI
Additional unit test in test_cutlass_backend.py

Pull Request resolved: https://github.com/pytorch/pytorch/pull/121497
Approved by: https://github.com/jansel",2024-04-21T22:48:20Z,Kai Londenberg
92a17f454ae3b4956a62d3cf6cb19988c7015766,"[1/N][dtensor] introduce StridedShard placement type and _split_tensor() logic (#126697)

**Summary**
This PR adds a new private placement type `_StridedShard` for FSDP2 + TP style tensor sharding. The previously used `Shard` placement type cannot produce correct `full_tensor()` result because it assumes the tensor to be first sharded over `dp` mesh dimension then `tp` mesh dimension which does not hold true in FSDP2 + TP case.

**Test**
`pytest test/distributed/_tensor/test_utils.py -s -k strided_sharding`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/126697
Approved by: https://github.com/wanchaol",2024-08-06T21:34:41Z,Xilun Wu
22d258427baf226fe67f888de044a62941c66dd7,"[BE][Easy] enable UFMT for `torch/distributed/_shard/` (#128867)

Part of #123062

- #123062

Pull Request resolved: https://github.com/pytorch/pytorch/pull/128867
Approved by: https://github.com/fegin
ghstack dependencies: #128866",2024-06-18T06:31:39Z,Xuehai Pan
f1f99ab310840b2476075b4a97f610b0c4e05140,"Fix conv1d with explicit precision (#75824)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/75824

X-link: https://github.com/pytorch/fx2trt/pull/57

Reviewed By: jerryzh168

Differential Revision: D35621145

fbshipit-source-id: 639c373f5e3e187eb6971d7f2d4b5e92f11dfc62
(cherry picked from commit 69602c0089941096e242fbb065685267e50bf26e)",2022-04-19T23:45:37Z,Shirong Wu
0a38aed02537de2fc2cd6d85fdef4de6dd1f8065,"Auto set libuv_ROOT env var for Gloo submodule on Windows platform (#45484)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/45484

Reviewed By: lw

Differential Revision: D23990724

Pulled By: mrshenli

fbshipit-source-id: 1987ce7eb7d3f9d3120c07e954cd6581cd3caf59",2020-09-29T15:56:46Z,gunandrose4u
eef72f3f8ac752350f3fc73b5aa67424ffdc799f,"[NNC] Update Buf on mutation instead of creating new ones (#57513)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/57513

Test Plan: Imported from OSS

Reviewed By: VitalyFedyunin

Differential Revision: D28226917

Pulled By: navahgar

fbshipit-source-id: 4e74c56a85b7aadc285b872b8ef8f8e26f31c8ce",2021-05-06T08:05:49Z,Raghavan Raman
4f2d869095034301b903cd2ef807b416547c0d9c,"Fix distributed issue by including distributed files (#87615)

This fixes regression in distributed headers installation.
Caused by following PR: https://github.com/pytorch/pytorch/pull/85953
which removed the inclusions

Fixes #87173

Test plan from wheel build by this CI: https://github.com/pytorch/pytorch/actions/runs/3314742519

```
[ec2-user@ip-10-0-9-132 c10d]$ pwd
/home/<USER>/actions-runner/_work/_temp/artifacts/torch/include/torch/csrc/distributed/c10d
[ec2-user@ip-10-0-9-132 c10d]$ ls -las
total 300
 4 drwxr-xr-x 2 <USER> <GROUP>  4096 Oct 24 19:12 .
 0 drwxr-xr-x 4 <USER> <GROUP>    29 Oct 24 19:12 ..
12 -rw-r--r-- 1 <USER> <GROUP>  9051 Oct 24 17:28 Backend.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   216 Oct 24 17:28 c10d.h
 4 -rw-r--r-- 1 <USER> <GROUP>  3880 Oct 24 17:28 comm.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   604 Oct 24 17:28 debug.h
 4 -rw-r--r-- 1 <USER> <GROUP>  1717 Oct 24 17:28 default_comm_hooks.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1316 Oct 24 17:28 error.h
 4 -rw-r--r-- 1 <USER> <GROUP>   962 Oct 24 17:28 exception.h
 4 -rw-r--r-- 1 <USER> <GROUP>  1461 Oct 24 17:28 FileStore.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   771 Oct 24 17:28 GlooDeviceFactory.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1154 Oct 24 17:28 HashStore.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  4058 Oct 24 17:28 logger.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2059 Oct 24 17:28 logging.h
 8 -rw-r--r-- 1 <USER> <GROUP>  7979 Oct 24 17:28 NCCLUtils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2756 Oct 24 17:28 Ops.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1814 Oct 24 17:28 ParamCommsUtils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1478 Oct 24 17:28 PrefixStore.hpp
16 -rw-r--r-- 1 <USER> <GROUP> 13235 Oct 24 17:28 ProcessGroupGloo.hpp
12 -rw-r--r-- 1 <USER> <GROUP> 11298 Oct 24 17:28 ProcessGroup.hpp
12 -rw-r--r-- 1 <USER> <GROUP>  8645 Oct 24 17:28 ProcessGroupMPI.hpp
28 -rw-r--r-- 1 <USER> <GROUP> 26526 Oct 24 17:28 ProcessGroupNCCL.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  3805 Oct 24 17:28 ProcessGroupRoundRobin.hpp
12 -rw-r--r-- 1 <USER> <GROUP> 10361 Oct 24 17:28 ProcessGroupUCC.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  5062 Oct 24 17:28 ProcessGroupWrapper.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  4201 Oct 24 17:28 PyProcessGroup.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1072 Oct 24 17:28 python_comm_hook.h
24 -rw-r--r-- 1 <USER> <GROUP> 23859 Oct 24 17:28 reducer.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2330 Oct 24 17:28 reducer_timer.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  1683 Oct 24 17:28 sequence_num.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2108 Oct 24 17:28 socket.h
 4 -rw-r--r-- 1 <USER> <GROUP>  2589 Oct 24 17:28 Store.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  3264 Oct 24 17:28 TCPStore.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  6944 Oct 24 17:28 TraceUtils.h
 8 -rw-r--r-- 1 <USER> <GROUP>  4539 Oct 24 17:28 Types.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   580 Oct 24 17:28 UCCForNCCL.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>  2301 Oct 24 17:28 UCCTracing.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  4933 Oct 24 17:28 UCCUtils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   584 Oct 24 17:28 UnixSockUtils.hpp
24 -rw-r--r-- 1 <USER> <GROUP> 20796 Oct 24 17:28 Utils.hpp
 4 -rw-r--r-- 1 <USER> <GROUP>   575 Oct 24 17:28 WinSockUtils.hpp
 8 -rw-r--r-- 1 <USER> <GROUP>  4259 Oct 24 17:28 Work.hpp
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/87615
Approved by: https://github.com/malfet",2022-10-24T19:38:07Z,atalman
0ca1ff3dce71b3fa4a905512ec7be381be753240,"Revert ""Add support for capturing tensors with score_mod (#124444)""

This reverts commit 7c253a777641791247f7fcc19fe5c60f24be32b9.

Reverted https://github.com/pytorch/pytorch/pull/124444 on behalf of https://github.com/jeanschmidt due to Breaking internal tests, check D56522566 ([comment](https://github.com/pytorch/pytorch/pull/124444#issuecomment-2076908582))",2024-04-25T10:56:38Z,PyTorch MergeBot
ae71c5c7e6922c54189399dc1e5554cef7e0011d,"Optimized bincount for the CPU by removing extra size() calls (#35822)

Summary:
By removing the calls of `size` that were effectively nops, I've managed to make `bincount_cpu` run around 6 times faster on my machine. EDIT: (Running Windows 10, I'm suspecting this may be a Windows-specific bug)

For histogramming 1e7 samples with 1e5 bins, best of 20 with 10 runs each
Before: 3.201189
After: 0.466188
Pull Request resolved: https://github.com/pytorch/pytorch/pull/35822

Differential Revision: D20919885

Pulled By: ezyang

fbshipit-source-id: 1657056d69a02f1e61434f4cc8fa800f8d4e1fe8",2020-04-08T18:07:05Z,Marian Ivanov
4543cf4eb16611ab8847064e3b826f756e810c51,"[JIT] add support for torch.lu to torchscript (#33724)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/33724

Fix for https://github.com/pytorch/pytorch/issues/33381, partial fix of https://github.com/pytorch/pytorch/issues/30786

Test Plan: Imported from OSS

Differential Revision: D20077321

Pulled By: eellison

fbshipit-source-id: a1e6a0370712b36c9f66979098ac2f9d500ca5f6",2020-02-27T02:28:47Z,Elias Ellison
00996006d10395cb564ff8245622846aa355cf57,Remove type inference from value,2017-10-12T05:51:50Z,Lu Fang
1a4ee2a6bbe2c987c38f3c246f0b5192d7f79e3f,"Add XPU support for storage resize_ (#105262)

We'd like to add XPU device support for storage resize_

Pull Request resolved: https://github.com/pytorch/pytorch/pull/105262
Approved by: https://github.com/mikaylagawarecki",2023-07-18T12:46:00Z,zhuhong61
9cda7b9e4725f37093ed383691d5713ddd168af4,"[hotfix] Do not import torch.ao.quantization._pt2e from dynamo (#100194)

Summary: Importing torch.ao.quantization._pt2e from dynamo led to
internal test failures related to memory profiling. For now,
let's express the path using a simple string instead.

Reviewers: jerryzh168, kimishpatel

Pull Request resolved: https://github.com/pytorch/pytorch/pull/100194
Approved by: https://github.com/jerryzh168",2023-04-27T19:17:28Z,andrewor14
9e5045e978d8800a6dbeb919745169e4de18927c,"[pytorch] clean up normalized_dynamic_type() hack (#44889)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/44889

This HACK doesn't seem to be necessary any more - there is no 'real'
type in generated Declarations.yaml file.
Verified by comparing generated code before/after.

Test Plan: Imported from OSS

Reviewed By: ezyang

Differential Revision: D23761624

Pulled By: ljk53

fbshipit-source-id: de996f04d77eebea3fb9297dd90a8ebeb07647bb",2020-09-19T06:47:48Z,Jiakai Liu
e4d7676c1b756a0b332dffa71d6c66b8ee2af418,"[CPU] Expand `torch.special.i1` to Half and BF16 (#137899)

To match behavior of `torch.special.i0`

Noticed while looking at the failures in https://github.com/pytorch/pytorch/pull/137849

Also, add explicit high-precision template specialization for  `calc_i0` and `calc_i1` for `BFloat16` and `Half`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137899
Approved by: https://github.com/Skylion007",2024-10-15T17:00:58Z,Nikita Shulga
9d4cb0d3ebb41fd8a2c83af0612a569d3fedf586,"Fix param and buffer mapping for state_dict when there are state_dict hooks (#137609)

Resolve #137540

Summary:

We might get different state_dict and named_parameters result when the module has registered custom state_dict_hooks.
For exported_program's state_dict, we want the state_dict to reflect the actual module hierarchy at runtime, and it might be different from the model's state_dict() output if the model has state_dict hooks.
To do weight swapping, one needs to either re-export or turn-off the hooks when saving model's state_dict().
Previously, ExportedProgram uses nn.Module's state_dict() method to populate its own state_dict, but it doesn't work for some models (e.g. llama3_3_vision) because ExportedProgram's state_dict and an nn.Module's state_dict have some subtle differences semantically.

nn.Module's state_dict is about how the state should be serialized, and it reflects the structure of the original user model code. In contrast, export specializes on a “run” of a model, and its state_dict needs to reflect the runtime module hierarchy.

One example where these two are different is TorchTune's Llama3_2_vision text decoder. Here, a FusionLayer is added as a local optimization and it is not part of the ""static model definition"".  In runtime, we have mod.layers[3].layer.sa_norm.scale.

But in nn.Module's state_dict, the authors of the model added a state_dict hook to remove the ""layer"" in mod.state_dict() to reflect the static model definition, so we have mod.state_dict()[""layers.3.sa_norm.scale""].
In this Diff, we change ExportedProgram to populate its state_dict using named_parameters() and named_buffers() instead. So in ExportedProgram's state_dict, we have ""layers.3.layer.sa_norm.scale"", which reflects the runtime module hierarchy.

Now one problem this presents is weight swapping. Since ExportedProgram's state and the model's state is not the same anymore, weight swapping procedure also needs to change slightly.

In internal Ads and RecSys models deployment, weight swapping is where they have one model that is currently being being deployed and serving traffic, and they want to swap out the weights with newly trained model weights without having to redo the whole exporting/lowering process and create a new artifact. So they would move the deployed model’s pointer to the state dict over to the new state dict. Because of this, it’s previously a requirement that the FQNs are matching between the exported and the eager model’s state dict.

The new ExportedProgram's state dict still supports weight swapping, but the state_dict to be swapped needs to be obtained from torch.export.exported_program instead of model.state_dict() if the model has state_dict hooks.
The new requirement is that the FQNs are matching between the exported’s state dict and the state_dict obtained from `_disabled_load_state_dict_hooks(M)` context manager. One benefit of having this new API is that we are now in full control within export of gathering and updating the model state.
If a model doesn't have any state_dict hooks, one can still use model.state_dict() for weight swapping, so it's BC.

Test Plan:
```
buck2 run 'fbcode//mode/dev-nosan' fbcode//caffe2/test:test_export  -- -r  test_export_for_training_with_state_dict_hooks
```

Differential Revision: D64080561

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137609
Approved by: https://github.com/angelayi, https://github.com/pianpwk",2024-10-11T01:33:50Z,Shangdi Yu
3072c97017a2ad2a22b11edf08d0842c22688f44,"Gelu Backward, Contribution from Kevin Stephano (#58249)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/58249

Test Plan: Imported from OSS

Reviewed By: ejguan

Differential Revision: D28425629

Pulled By: Krovatkin

fbshipit-source-id: 494ab165d548aa76f036344ab1c19c5fd64bae82",2021-05-14T02:36:38Z,Nikolay Korovaiko
3cf267bfa6d0a8d8f3d6e355ee4da84a444fdd5e,"Embedding: Remove dispatch in parallel region (#60597)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/60597

Ref #56794

Test Plan: Imported from OSS

Reviewed By: jbschlosser

Differential Revision: D29446191

Pulled By: ngimel

fbshipit-source-id: d6ff010104ae621d5e3d9c269ed2b48407e71d67",2021-06-30T19:26:19Z,Peter Bell
be3b16daad2dcd861ca9931025194041689478c6,"[decomp] Fix baddbmm decomposition (#109714)

The decomposition is currently registered without the pw_cast_for_opmath
decorator, due to the ordering of decorators being meaningful.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/109714
Approved by: https://github.com/lezcano",2023-09-28T17:12:41Z,Peter Bell
414ec6ce97a360a60b46ccee9cf84d764b978016,"Turn off automatic_dynamic_shapes in prep for dynamic-by-default (#103320)

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/103320
Approved by: https://github.com/Skylion007",2023-06-09T17:56:05Z,Edward Z. Yang
98d6a6eb7da57e2edb56102c7a4a274761ef26f0,"[inductor] clean up TODO comments. (#133718)

clean up TODO comments.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133718
Approved by: https://github.com/henrylhtsang",2024-08-16T22:11:59Z,Xu Han
d027aef8f86bd70c4ae525fc1bef92f1301e71c2,"Revert ""Removed q_num_blocks from constructor (#130819)""

This reverts commit 03c660468eb57772e82c1034613f5ff8781c775a.

Reverted https://github.com/pytorch/pytorch/pull/130819 on behalf of https://github.com/atalman due to Internal problem with previous PR in stack https://github.com/pytorch/pytorch/pull/130818 ([comment](https://github.com/pytorch/pytorch/pull/130819#issuecomment-2233359569))",2024-07-17T13:43:35Z,PyTorch MergeBot
82eb09aafd7e4ee6e4fb0580f2221ea6253d218b,"[Environment Variable][4/N] Use thread-safe getenv functions (#137843)

Follows #137328

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137843
Approved by: https://github.com/ezyang",2024-10-21T02:58:59Z,cyy
269e92669a8f25003e639230e18eeabb863533a7,"[c2] Remove unused private fields (#69709)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/69709

Fix logical bug in `caffe2/ideep/operators/conv_op.cc`, which
contained an always false statement (fusion_type_ == X && fusion_type_ == Y ) statement

Test Plan: Imported from OSS

Reviewed By: r-barnes

Differential Revision: D32997006

Pulled By: malfet

fbshipit-source-id: 23e4db1b17cf8a77eae6a8691847ffa484d4736c",2021-12-14T19:25:24Z,Nikita Shulga
68238606f3e9f70ac000187ed17ea347fdc0c549,"Revert ""Reland: Add PyObject preservation for UntypedStorage (#103907)""

This reverts commit 56b848157c259b4e53225e2516d603e9c8cfab79.

Reverted https://github.com/pytorch/pytorch/pull/103907 on behalf of https://github.com/huydhn due to Sorry for reverting your change, but it is failing torchdistx build which uses check_pyobj here https://github.com/pytorch/torchdistx/blob/9c1b9f5cb2fa36bfb8b70ec07c40ed42a33cc87a/src/python/torchdistx/_C/deferred_init.cc#L87 ([comment](https://github.com/pytorch/pytorch/pull/103907#issuecomment-1712121158))",2023-09-08T19:27:07Z,PyTorch MergeBot
640b4863394d0a0e4d6e6f89c2b52ba35f501fd9,"add clang-tidy to github actions (#27755)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/27755

This gives us nice annotations. See
https://github.com/suo/pytorch/pull/22/files for an approximation of
what it will look like (ignore the warnings on the lint.yml file).

I deleted the old azure pipelines one since making the code work for
both was annoying, and unlike flake8 this one does not affect master

Test Plan: Imported from OSS

Differential Revision: D17888974

Pulled By: suo

fbshipit-source-id: d8928a1451b6ef500dc1889284cab2845ecdeeea",2019-10-12T00:00:14Z,Michael Suo
d37c2d7c8d63563bffdbebe8224ef28795b952e4,"Revert D17495965: TensorRT 6.0 support and PyTorch->ONNX->TRT6 unit test

Test Plan: revert-hammer

Differential Revision:
D17495965

Original commit changeset: 3e8dbe8943f5

fbshipit-source-id: d47fcbec22b0d61df41d7dbf15cfdde196ac818f",2019-10-25T20:56:34Z,Junjie Bai
9c144bc4fe993fcc767c8755cbb7b06b592af3cb,"Dont increment generation if forward of backward exists, and warning on deallocation of live tensors (#97168)

Refining the logic for when it is okay to ignore previously live outputs from cudagraphs. If there is a forward that has been invoked without invocation of the corresponding backwards, dont allow overwriting outputs.

Differential Revision: [D44228369](https://our.internmc.facebook.com/intern/diff/D44228369)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/97168
Approved by: https://github.com/ezyang, https://github.com/jansel",2023-03-21T23:26:58Z,Elias Ellison
c9511e8ac9cfe633a0a58005757ece5801f1bd7b,"[foreach][BE] cleaning up MultiTensorApply.cuh (#110228)

Followup edits to #109402 as suggested by @r-barnes

Pull Request resolved: https://github.com/pytorch/pytorch/pull/110228
Approved by: https://github.com/drisspg",2023-09-28T16:29:12Z,Jane Xu
85851b1e8fc12fc824edd24aa1f1e0f9075b0cd7,"remove useless clang-tidy suppression  (#92287)

remove NOLINTNEXTLINE(cppcoreguidelines-pro-type-member-init)
remove NOLINTNEXTLINE(performance-move-const-arg)
remove NOLINTNEXTLINE(performance-no-automatic-move)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/92287
Approved by: https://github.com/albanD",2023-01-21T02:33:24Z,cyy
f98edfcc48c903d0d22a0105b0fafe4ca58121e6,"Make TorchElastic timer importable on Windows (#88522)

Also, add `torch.distributed` to test imports, so that we would not
regress in the future

Fixes https://github.com/pytorch/pytorch/issues/85427
Pull Request resolved: https://github.com/pytorch/pytorch/pull/88522
Approved by: https://github.com/d4l3k",2022-11-10T17:42:20Z,Nikita Shulga
b347b8c19155a78f18ca98e9e3d02fd4c89fb1f6,"[quant][fx] Support some default ops in the native backend config (#74600)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/74600

Following https://github.com/pytorch/pytorch/pull/74210, this PR adds the support for some ops
using the DefaultNodeQuantizeHandler in the backend_config_dict defintion for pytorch native backend

TODO: There is still a few ops we didn't handle with backend_config_dict path: gelu and softmax, need to discuss if we still need them, if so we can change the test
to use backend_config_dict and remove the DefaultNodeQuantizeHandler after that

Test Plan:
python test/test_quantization.py TestQuantizeFxOps

Imported from OSS

Reviewed By: andrewor14

Differential Revision: *********

fbshipit-source-id: 70351d2810ca1ac7dc09d4a9c239f6757ccb51ca
(cherry picked from commit 5e68f755a32ba7d90d6c73db9c2017f9c58d7fa5)",2022-03-25T02:54:07Z,Jerry Zhang
77d29bcee200f04bece4a86283acfb8e1ec830ad,"[primTorch] special: ndtr, ndtri, log_ndtr, erfcx (#86077)

- Adds prims and _refs for `erfcx` and `ndtri`.
- Adds _refs for `ndtr`, and `log_ndtr`.

cc @kshitij12345 @lezcano @mruberry
Pull Request resolved: https://github.com/pytorch/pytorch/pull/86077
Approved by: https://github.com/mruberry",2022-10-13T01:18:30Z,Khushi Agrawal
ce9614662392574af4163a3c8456a731056c5c38,"[PT2] Fix node metadata setting in group_batch_fusion_aten (#134543)

Summary: Current impl results in `meta` missing fields like`val`, use `FakeTensorProp` to update the information

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134543
Approved by: https://github.com/frank-wei",2024-08-29T18:32:04Z,Xintong Hu
be5191a00b9c9683e1e3b0f85c2f0ec6e0b4139a,Add documentation for keepdim.,2017-05-04T18:19:46Z,Gregory Chanan
524adfbffd82666cc9281325a9f0ae4250c7fad1,"Use new FFT operators in stft (#47601)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/47601

Fixes https://github.com/pytorch/pytorch/issues/42175#issuecomment-719933913

Test Plan: Imported from OSS

Reviewed By: ngimel

Differential Revision: D25457217

Pulled By: mruberry

fbshipit-source-id: 455d216edd0b962eb7967ecb47cccc8d6865975b",2020-12-10T18:27:21Z,Peter Bell
43416e3059a787629d28799f832c854d554a3ca2,"Correctly read the cache key for remote cache (#121151)

Summary: While investigating why we were calling put each time, I noticed that memcache backend returns a list instead of direct result, which means that we were correctly fetching the cached result but not using it.

Test Plan: The test should now work as expected

Differential Revision: D54500851

Pull Request resolved: https://github.com/pytorch/pytorch/pull/121151
Approved by: https://github.com/aakhundov",2024-03-05T07:33:20Z,Oguz Ulgen
bac0878780e3c2ea2933e07ef64cbbcb53eb0996,"Error if compiled nondeterministic backward called in deterministic mode (#114780)

Part of #113707

Pull Request resolved: https://github.com/pytorch/pytorch/pull/114780
Approved by: https://github.com/ezyang, https://github.com/albanD",2024-01-15T22:45:40Z,Kurt Mohler
bb99008c9e7c357b88047bcd6971dc2078341484,"Only thunkify proxies in some situations (#132421)

The goal of this PR is to avoid stack overflow when we create extremely long chains of thunks, and then evaluate them (e.g., as occurs if you sum(long list of symint)). The basic idea behind this PR is to only thunkify proxies if they're being created in places where they may or may not be used--crucially, symint operations that occur in user code we are tracing are eagerly placed into the graph, even if they may eventually be dead.

I annotated the PR with explanation of changes.

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/132421
Approved by: https://github.com/Skylion007, https://github.com/zou3519
ghstack dependencies: #132674, #132675",2024-08-07T01:25:28Z,Edward Z. Yang
84a9694ed0c85c0bc844915adf9f2c8fd53bacb1,"Fix windows msbuild bug (#18748)

Summary:
Fix the bug introduced by #18681 where an undefined variable was being used to limit max cpu count when building for Windows without Ninja.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/18748

Differential Revision: D14733209

Pulled By: soumith

fbshipit-source-id: 52fc0dd4dde99da75a6956b63f02da2e647eed4f",2019-04-02T21:25:28Z,vaeksare
1fa0bb6d9da4d375d8bc29d319b5dcc934e8b602,"Use workspace to persist and restore images for Windows CI build and … (#38971)

Summary:
Inspired by malfet

> By the way, once we have build_artifacts property, can someone try if its faster to use it as mean of transferring images between build and test instead of using AWS (i.e. use artifacts instead of jenkins/pytorch/win-test-helpers/upload_image.py /download_image.py pair)

Use CircleCI to store intermediate binaries and make them available to be downloaded as artifacts instead of uploading to S3.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38971

Differential Revision: D21717080

Pulled By: seemethere

fbshipit-source-id: e3498b058778d02ae2f38daefbc7118a1a2cbe76",2020-05-25T23:52:10Z,Yang Gu
153e2e96d4aaf0c3d97dea7ee9375b2ad26d679f,"Make Sequential ref-counted (#9151)

Summary:
In the C++ API, `Sequential` currently was not refcounted itself, but stored `shared_ptr<AnyModule>` to get the reference semantics. This is unfortunate because most modules in the API are accessed via `->`, e.g. `Linear l(1, 2); l->forward(...);`. `Sequential` was different in that it had value semantics itself, thus was accessed via `.`.

This PR makes `Sequential` store `AnyModule` (without extra indirection), and uses the same pImpl mechanism we use for all other modules to make `Sequential` have reference semantics itself. This makes it consistent with the rest of the library. It also removes one level of indirection inside of `Sequential`, which is cool.

One thing I had to change was that the `ModuleHolder` with which the whole pImpl thing is implemented previously did some tricks to make `Linear(3, 4)` actually construct `Linear(LinearOptions(3, 4))`. This doesn't work well with `Sequential` since it takes a variadic parameter pack. Instead, I made `ModuleHolder` forward all arguments to the underlying module, and then further pushed the trick to forward parameters to modules' options types into the actual Modules. This adds one constructor per Module in the library. This is not something user modules have to do (unless they want this nice forwarding themselves). It makes the code simpler overall.

ezyang ebetica apaszke
Pull Request resolved: https://github.com/pytorch/pytorch/pull/9151

Reviewed By: ezyang

Differential Revision: D8809298

Pulled By: goldsborough

fbshipit-source-id: da68452c3de912fbc67af330ba93b5220de6909f",2018-07-12T00:15:08Z,Peter Goldsborough
64efd88845467f5255be50ddbd784ce8db3b688f,"Add directly referenced header files  for ""ceil_div.h"" (#99607)

std::enable_if_t is defined in <type_traits>. Directly referencing header files is good programming style

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99607
Approved by: https://github.com/albanD, https://github.com/kit1980",2023-04-28T01:05:01Z,zhi.cai
fa7ae6cdbca4b16f4c2cfce09d6e05574b89a518,"can't infer device on benchmarked function with no args or kwargs (#133290)

when we call benchmarker.benchmark(fn, (), {}) it attempts to infer the device from the args and kwargs, which are both empty. in this case the default behavior is to assume CPU, since `is_cpu_device` is implemented as `all([x.device == ""cpu"" for x in ... if x is Tensor])`, and `all([]) == True`. I've added a PR that makes this raise an error, but we should just fix this one callsite first

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133290
Approved by: https://github.com/eellison",2024-08-13T20:13:44Z,Nicolas Macchioni
32ecaa0870dcd291f3336450c612d581ff8512cb,regenerate docs w/ recent changes (#126),2017-10-24T20:11:32Z,Trevor Killeen
441d75ce569f89bad3e2f1f2a2075e68ae3bc76b,Adapts basic operations to new THXVector interface,2017-04-07T14:51:12Z,Pedro Porto Buarque de Gusmao
c458bb985e620376e60493ab83e426f098926634,"make it easier to grep for unary/binary op kernels (#60128)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/60128

Test Plan: Imported from OSS

Reviewed By: wenleix

Differential Revision: D29175499

Pulled By: bdhirsh

fbshipit-source-id: 1838900276e0b956edf25cdddcff438ff685a50e",2021-06-17T00:47:55Z,Brian Hirsh
c6aa03bd4e9fe5b223eef2e97fda0bc61d593b1f,"Add allow_xpu to enable XPU UTs (#130312)

# Motivation
enable UTs under folder test/xpu/

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130312
Approved by: https://github.com/EikanWang, https://github.com/gujinghui, https://github.com/albanD",2024-07-12T15:49:38Z,"Yu, Guangye"
eb15b1a016c6facaf8605dde2c20b5de1586542d,"[dtensor][MTPG] make sharding prop lru cache not shared among threads (#134294)

**Summary**
Before this PR, `sharding propagator` is shared among threads. The result is the cache result of rank 0 would be accessible by other ranks e.g. rank 1 and this could lead to wrong DTensor resharding. This PR fixes it by making the cache a local variable at thread level, and it fixes `dstack` test (#126493), `inner` (https://github.com/pytorch/pytorch/issues/126852), and `vstack` (https://github.com/pytorch/pytorch/issues/126868). It also fixes `poisson_nll` (https://github.com/pytorch/pytorch/issues/131446) as a bi-product.

**Test**
`pytest test/distributed/_tensor/test_dtensor_ops.py`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134294
Approved by: https://github.com/wz337, https://github.com/awgu",2024-08-23T18:59:19Z,Xilun Wu
ee143d31ef071b277e21ac5eb3d7b725f60e7756,"Fix ImageInput op in resnet50_trainer.py

Summary:
Fix #1269 (from fa0fcd4053dd42a4ec3a2a12085662179f0e11df).
Closes https://github.com/caffe2/caffe2/pull/1314

Reviewed By: bwasti

Differential Revision: ********

Pulled By: bddppq

fbshipit-source-id: 7d7c45f8b997c25f34530f826729d700a9c522d4",2017-10-10T18:15:07Z,Luke Yeager
38a9984451ac6f6d453fb06ec13583df09f31227,"[TensorExpr] Properly handle all dtypes in evaluation of CompareSelect exprs. (#42493)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/42493

Test Plan: Imported from OSS

Reviewed By: nickgg

Differential Revision: *********

Pulled By: ZolotukhinM

fbshipit-source-id: cf7073d6ea792998a9fa3989c7ec486419476de0",2020-08-04T19:17:09Z,Mikhail Zolotukhin
a4e7b8001c41b5e8a088281a09992233b3ac38db,"refuse to generate a symbolic variable if a float input is inf (#139846)

Fixes `PYTORCH_TEST_WITH_INDUCTOR=1 tlp python test/test_torch.py TestTorchDeviceTypeCPU.test_cauchy_cpu_float64` when `specialize_float=False`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/139846
Approved by: https://github.com/ruidazeng, https://github.com/ezyang
ghstack dependencies: #139569, #139457, #139568, #139572",2024-11-07T05:52:33Z,Bob Ren
1cefc589054dcb698686f118bd04875c592bd2a7,"init tls grad_mode/local_dispatch_key set while fork new thread in (#113246)

TorchDynamo will guard grad_mode and the local dispatch key set.
https://github.com/pytorch/pytorch/blob/3a429423fcf72430e7a36c79e263c877d7a4ef72/torch/csrc/dynamo/guards.cpp#L13-L16

While using ThroughputBenchmark, those tls state will not be init as same as the main thread status.
https://github.com/pytorch/pytorch/blob/3a429423fcf72430e7a36c79e263c877d7a4ef72/torch/csrc/utils/throughput_benchmark-inl.h#L64-L94

Run following scripts
```
import torch
linear = torch.nn.Linear(128, 128)
compiled = torch.compile(linear)
x = torch.rand(10, 128)
with torch.no_grad(), torch.cpu.amp.autocast(enabled=True, dtype=torch.bfloat16):
    compiled(x)
    compiled(x)

from torch._dynamo import config
config.error_on_recompile = True
from torch.utils import ThroughputBenchmark
with torch.no_grad(), torch.cpu.amp.autocast(enabled=True, dtype=torch.bfloat16):
    bench = ThroughputBenchmark(compiled)
    bench.add_input(x)
    stats = bench.benchmark(
        num_calling_threads=10,
        num_warmup_iters=100,
        num_iters=100,
    )
    print(stats)
```
will lead to 2 re-compile reasons:
```
triggered by the following guard failure(s): ___check_global_state()
triggered by the following guard failure(s): tensor 'x' dispatch key set mismatch.
```

This will trigger a re-compile in torchdynamo. But since `ThroughputBenchmark` is used for sharing weight within threads, the model should not be changed anymore while running the benchmark. So this PR is to init the tls state as same as main thread. Then we can use ` ThroughputBenchmark` to run torchdynamo optimized models.

throughputbenchmark
Pull Request resolved: https://github.com/pytorch/pytorch/pull/113246
Approved by: https://github.com/jgong5, https://github.com/desertfire",2024-01-03T01:20:33Z,haozhe.zhu
d52404779fad7cd82b9075adec5ce7c127e9c224,"Revert D5803245: [caffe2][MPSCNN][segmentation] Make android segmentation net run with MPSCNN

Summary:
This reverts commit 6808e9c3504389c113c7a16504d6554e83bdcc3e

bypass-lint

Differential Revision: D5803245

fbshipit-source-id: e6e2e90dd196ae958d729af2e19942e922207a2a",2017-09-12T01:31:59Z,Hao Lu
d59ecc02df70bad2273858c2fad2b4993133a3d3,"[DDP] Fix when buffers are reassigned in module (#64472)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/64472

Sometimes, user module can reassign tensor buffer, as in:

```
self.buffer = torch.randn(1, 2) # in init
self.buffer += 1 # in forward
```

in this case, `self.modules_buffers` will become outdated and we should
repopulate self.modules_buffers if we need to sync module buffers.

See https://github.com/pytorch/pytorch/issues/63916 for full description of the
issue.
ghstack-source-id: 137526309

Test Plan: CI

Reviewed By: zhaojuanmao

Differential Revision: D30745921

fbshipit-source-id: 25eb1edbf445703a481802e07f3058d38ea6fc64",2021-09-09T02:13:33Z,Rohan Varma
e5f5bcf6d4ec022558caf4d0611d928497394a88,"[inductor] include global cache dir in inductor resources (#102130)

Summary: adding global cache dir glob to inductor resources

Test Plan: sandcastle + CI + tested locally

Differential Revision: D46131451

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102130
Approved by: https://github.com/jansel",2023-07-17T15:44:16Z,Nicolas Macchioni
1b66915f3916273edcf9dc5c3853bb31f21ed8f4,"Have type_parser return const reference (#70477)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/70477

Test Plan: Sandcastle

Reviewed By: cccclai

Differential Revision: D33340030

fbshipit-source-id: b2a295b7c1c01e86971f6b9bbdd7d3718a2d3f0c",2022-01-04T00:17:18Z,Richard Barnes
c5abe8844a87f4ed47ac5126d65f6021208417b7,"Add IDEEP fallbacks for Resnet50 training ops (#8541)

Summary:
1. Add fallback gradient ops
2. In fallback ops, set the output Tensor as CPUTensor instead of IDEEPTensor if ndim = 0. Because IDEEPTensor doesn't support 0 dim.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/8541

Reviewed By: yinghai

Differential Revision: D9115233

Pulled By: wesolwsk

fbshipit-source-id: 163e6a76f02bd781c95d1060ccbacf2cab90055e",2018-08-03T22:48:13Z,wuhuikx
22964d1007d1d87964df1db609da53b35b00316f,"[DSD] Deprecate submodules feature for DSD (#127793)

Summary:
Getting a partial of the state_dict and set the state_dict with the type of Dict[nn.Module, Dict[str, Any]] is too complicated and can confuse users. The features can be achieved by simple pre-processing and post-processing by users. So this PR adds the deprecation warning to the feature.

The previous PR, https://github.com/pytorch/pytorch/pull/127070, assumes
no one is using the feature and remove it without the grace period. This
seems to be too aggresive and causes some concerns. This PR adds the
deprecation warning and tests.

We will remove the support in 2.5.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/127793
Approved by: https://github.com/LucasLLC",2024-06-03T19:31:48Z,Chien-Chin Huang
b592e675166f84645ce62098f7ddd3d4482942ed,"Use C++17 [[fallthrough]]; (#102849)

Test Plan: Sandcastle

Reviewed By: meyering

Differential Revision: D46385240

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102849
Approved by: https://github.com/Skylion007",2023-06-06T07:06:26Z,Richard Barnes
1932bc69e9fcf176726fddb4a75c5e3a0a797c91,"Move GHA to ONNX (#65975)

Summary:
- Delete CircleCI ONNX config
- Add sharded ONNX job to the list of generated workflows
- Move ONNX runtime installation from `pytorch-job-specs.yml` to `.jenkins/caffe2/test.sh`
- Limit MKLDNN to AVX2 ISA while running  Caffe2 tests

Pull Request resolved: https://github.com/pytorch/pytorch/pull/65975

Reviewed By: seemethere

Differential Revision: D31327206

Pulled By: malfet

fbshipit-source-id: 15aa53e4481e846c62b4ee2db5c03047d68679a4",2021-10-05T16:29:27Z,Nikita Shulga
d9aeb7e71ba3a17c14d437cdd2b8e1e3ca2f09a1,"clamp now has subgradient 1 at min and max (#7049)

* subgradient 1 at min and max for clamp

* clamp max and clamp min too

* add comment",2018-04-30T13:21:56Z,Tongzhou Wang
04ad0134ae51a50a1f657c1e4b86c3c16f0e9158,"[FSDP] Use `reduce_scatter_tensor()` (#87240)

Let us silence some more warnings 👍🏼
Pull Request resolved: https://github.com/pytorch/pytorch/pull/87240
Approved by: https://github.com/rohan-varma",2022-10-24T03:39:38Z,Andrew Gu
a777dea3b338efb1110f37ff6ff844d0ad54c5ef,"Remove dtype check on meta device (#136774)

Summary:
# Latest Update

This diff is no longer needed because we did need the check to exist, to make meta behave the same as other devices, see D54526190.

---------------------------------

# Background

T176105639

| case | embedding bag weight | per_sample_weight | fbgemm lookup | forward in meta |
| A | fp32 | fp32 | good | good |
| B | fp16 | fp32 | good| failed [check](https://fburl.com/code/k3n3h031) that forces weight dtype ==  per_sample_weights dtype |
| C | fp16 | fp16 | P1046999270, RuntimeError: ""expected scalar type Float but found Half from fbgemm call"" | good |
| D | fp32 | fp16 | N/A | N/A |

Currently we are in case A. Users need to add `use_fp32_embedding` in training to force embedding bag dtype to be fp32. However, users actually hope for case B to use fp16 as the embedding bag weight. When deleting `use_fp32_embedding`, they would fail the [check](https://fburl.com/code/k3n3h031) that forces `weight dtype ==  per_sample_weights dtype ` in meta_registration.

The check is actually not necessary. Is it because the backend fbgemm does support case B. Additionally, later on in the `meta_embedding_bag`, `weight` and `per_sample_weights` don't need to be in the same dtype (https://fburl.com/code/q0tho05h, weight is src, per_sample_weights is scale) for `is_fast_path_index_select`.

# This diff
Therefore, this diff remove the unnecessary [check](https://fburl.com/code/k3n3h031) to support case B in meta forward. With such, users are able to use fp16 to be the emb bag dtype without the need to force per_sample_weights the same dtype in meta forward (see Test Plan).

# Reference diffs to resolve this issue
Diff 1: D52591217
This passes embedding bag dtype to feature_processor to make per_sample_weights same dtype as emb bag weight. However, `is_meta` also needs to be passed because of case C. fbgemm still does not support per_sample_weights = fp16 (see the above table). Therefore users are forced to only make per_sample_weights fp16 when it is on meta. The solution requires too many hacks.

Diff 2: D53232739
Basically doing the same thing in diff 1 D52591217, except that the hack is added in TorchRec library. This adds an if in EBC and PEA for: when emb bag weight is fp16, it forces per_sample_weight fp16 too. However, it would then result in fbgemm issue too and has broken a bunch of prod models.

Test Plan:
# APS
The following command will run icvr_launcher which triggers ads_launcher and run forward in meta device:
```
buck2 run mode/opt -c python.package_style=inplace //aps_models/ads/icvr:icvr_launcher_publish -- mode=mast_ig_fm_when_combo0_uhm_publish launcher.fbl_entitlement=ads_global_tc_ads_score launcher.data_project=oncall_ads_model_platform launcher.tags=[ads_ranking_taxonomy_exlarge_fm_prod] stages.train=false
```

Result:
 {F1461463993}

Reviewed By: ezyang

Differential Revision: D54175438

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136774
Approved by: https://github.com/ezyang",2024-10-12T05:45:21Z,Angel Yang
c0bfe2a6ed06a4e66f681d736453415643a60ff7,"Clean up conversion registration

Summary:
[x] get registry working
[x] move all current ops to registry

Reviewed By: yinghai

Differential Revision: D8706115

fbshipit-source-id: 8dfce79039b57dea1c15e8e291cdd74f39766ade",2018-07-06T20:35:44Z,Bram Wasti
45010833069c02162fee40291ef7d444dfe7f41b,"dedupe test skipping in common_distributed and test_distributed (#38078)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38078

`common_distributed` and `test_distributed` have some error codes that overlap but are for different reasons, for example, code 75 in `test_distributed` is ""no cuda available"" but in common_distributed it is ""need at least 2 CUDA devices"".

This is an issue because the tests in `test_distributed` now use the utils in `common_distributed`, so we could get the wrong reason for skipping tests.

It is also the source of test failures in https://github.com/pytorch/pytorch/pull/37990.

This diff makes it so that the test skipping logic is deduped and put into `common_distributed.py`, where it can be reused and then imported into `test_distributed`
ghstack-source-id: 103782583

Test Plan: CI

Differential Revision: D21466768

fbshipit-source-id: 53b5af36672ebd8b51ba8b42709d87e96cadef20",2020-05-09T06:17:28Z,Rohan Varma
45a3231bb8a0c8b7498917a2eb43a8e9ccf04f96,"[codemod] Enforce proper use of emplacy functions

Summary: The goal of this diff is enforce proper use of ""emplacy"" functions. In each case, this saves at worst a move constructor call, and at best a full copy of the object (in the case of a constructor call where the object does not have a move constructor).

Test Plan: CI.

Reviewed By: marksantaniello

Differential Revision: D27888714

fbshipit-source-id: 235d0b31066463588c7e4ab86e132c430a352500",2021-05-05T03:56:28Z,Nicolas Jean van Kempen
252e68a83b56343f723a35c3cd16b05f66b8e726,"Revert ""Add support for `torch.Generator` type in TorchScript (#110413)""

This reverts commit 54493fe8c4b1cca4c5ff993b99eb3e3dbc984226.

Reverted https://github.com/pytorch/pytorch/pull/110413 on behalf of https://github.com/huydhn due to Sorry for reverting your change but it is, unfortunately, still breaking internal builds ([comment](https://github.com/pytorch/pytorch/pull/110413#issuecomment-1811625557))",2023-11-15T00:51:23Z,PyTorch MergeBot
0951f4424aba2be8bc94fc0cb2712bd6cef1791b,CUDA 9.2 adds support to GCC 7.3.1. (#7880),2018-05-29T20:53:06Z,xkszltl
af8b04d5f622ea6e2b07594b73ac741ef7dddefa,"Add create_graph_input debug log (#108836)

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/108836
Approved by: https://github.com/mlazos, https://github.com/voznesenskym",2023-09-08T14:10:22Z,Edward Z. Yang
def76eee1cf90f136c9ccdf82afa03ee1e54c365,"[auto] Update onnx to e2e8003 - add output shape as input for reshape (#608)
https://github.com/onnx/onnx/commit/e2e8003ec36800038959569cc6f3057ffee69fc9",2018-03-16T23:32:44Z,onnxbot
041bff77b620ecf3a9280b012746bf10a49d2972,"Make tools/actions_local_runner.py PY-3.X compatible (#58787)

Summary:
Do not use `shlex.join`, which is a simple join over quoted args, i.e.
https://github.com/python/cpython/blob/a9e43615c2e1fc5dd60063c1509e8b1c5daad095/Lib/shlex.py#L318-L320

Pull Request resolved: https://github.com/pytorch/pytorch/pull/58787

Reviewed By: driazati

Differential Revision: D28619996

Pulled By: malfet

fbshipit-source-id: dd4e939a88e2923b41084da2b5fbdbee859c0104",2021-05-22T00:39:04Z,Nikita Shulga
5817695bfa577f0ea08bef715bcae48ef9d34a02,"[pt2] Fix arange to match ATen behavior (#93353)

Fixes #92676

`arange` infers the output dtype from the argument types, but in order to reduce
falling back to ATen, inductor preferred to cast whole number float arguments to
int which gave the wrong output dtype. Instead, this decomposes floating point
arange into the prim equivalent for integers.

This also changes the signature of `prims.arange` to

```python
prims.iota(length, *, start, step, **factory_kwargs)
```

which only supports integers arguments. This is done because calculating the
output size from `start, end, step` is surprisingly complex and liable to off by
one errors so should not be duplicated in each backend.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/93353
Approved by: https://github.com/ngimel, https://github.com/lezcano",2023-02-02T17:46:48Z,Peter Bell
ca7ce2fca1769b49d0ef6a39d6df76c34e1431d0,"[ts-migration][1/N]: Add prim::Loop for constant number of iterations and condition (#131418)

#### Description
This PR adds prim::Loop support for the simplest case where the number of iteration is constant and the loop termination condition is also a constant.

[PR by stages](https://docs.google.com/document/d/1q6OprW3HBHbYPwEyE_DikBn-uzmhnN284Cmen_CnlhI/edit?usp=sharing)

#### Test Plan
Add reprod example.
* `pytest test/export/test_converter.py -s -k test_ts2ep_with_loop`
Pull Request resolved: https://github.com/pytorch/pytorch/pull/131418
Approved by: https://github.com/angelayi",2024-08-06T16:51:08Z,Jiashen Cao
0f419abf40d337a063184ccf24c21acfa789e5ba,"Roll nomnigraph build into caffe2 (#11303)

Summary:
We need to remove nomnigraph from the list of public libraries in order to support libtorch extensions. Easiest way to do this is to include it into the Caffe2 source like all other caffe2/core/ code.

However, because the headers are in a different place, we need to include them for linked libraries (pybind, tests, etc).

On an upside, this means that nomnigraph is now default hidden visibility too.

FYI peterjc123 xkszltl goldsborough bwasti Yangqing
Pull Request resolved: https://github.com/pytorch/pytorch/pull/11303

Reviewed By: pjh5

Differential Revision: ********

Pulled By: orionr

fbshipit-source-id: 5db3eb20bc5ddc873ce9151236b74663fbb33ed8",2018-09-07T02:35:36Z,Orion Reblitz-Richardson
de400fa5acb4ba2fd045d504391a4aaecfb8c65e,"[JIT] handle specially mapped ops (#41503)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/41503

Fix for https://github.com/pytorch/pytorch/issues/41192

We can map fill_ and zero_ to their functional equivalents full_like and zeros_like

Test Plan: Imported from OSS

Reviewed By: jamesr66a

Differential Revision: *********

Pulled By: eellison

fbshipit-source-id: f1c62684dc55682c0b3845022e0461ec77d07179",2020-07-20T18:59:08Z,Elias Ellison
57133e6ae6601f968a82b19b7eeec692d5b9aec6,"Add FP16 support (CudaHalfStorage, CudaHalfTensor)",2016-03-07T10:21:14Z,Adam Paszke
34ef473d92afd1ff7e89bd9749e2c3cd6361ffc6,"[Tensorpipe Agent] Timeouts for RPC requests (#38448)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38448

This PR implements timeout support for RPCs, and respects the new per-RPC timeout functionality.

A map containing RPC futures, keyed by an expiration time, is populated by the send function for each RPC.

A separate watchdog thread polls this map and sets all incomplete futures with errors.
Note: we cannot set errors to a future with the lock held (this will trigger callbacks immediately and, if one of the callback functions tries to acquire the lock that we held when setting the error, we have a lock order cycle). Thus we add all incomplete futures to a list, and then iterate through the list outside the lock to set errors on those futures if necessary.
ghstack-source-id: 104227075

Test Plan: Will patch the testing diff on top of this to run tests.

Differential Revision: D21468526

fbshipit-source-id: 4514484ece6fb6be673427d44c7f3164ab3d9d7c",2020-05-18T18:50:28Z,Omkar Salpekar
143d2881a844934c95c4ada63b38179d97e65af3,"[Profiler] Memory profiler part 10: Mark optimizer state (#88925)

This is also a fairly simple pass, since we're simply collecting values from the python tracer.

Differential Revision: [D40868664](https://our.internmc.facebook.com/intern/diff/D40868664/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/88925
Approved by: https://github.com/chaekit",2022-11-26T18:33:19Z,Taylor Robie
1905bbb01dc1e92f6248a11543e6d1bee2ff161c,"Include ATen/core/functional.h directly instead of torch/csrc/utils/functional.h. (#16377)

Summary:
One more shim removed.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/16377

Differential Revision: D13821816

Pulled By: ZolotukhinM

fbshipit-source-id: 007f014d404de51841437db7eef28367a2f6e46b",2019-01-30T21:30:30Z,Mikhail Zolotukhin
0696db820266c1402315f78458d57ea1cd48a2a7,"Revert ""Teach dynamo about torch.func.jvp (#119926)""

This reverts commit 17489784b635187316c6c856c5fe6b6a28d8a15a.

Reverted https://github.com/pytorch/pytorch/pull/119926 on behalf of https://github.com/peterbell10 due to broken mac jobs on main ([comment](https://github.com/pytorch/pytorch/pull/119926#issuecomment-2010327997))",2024-03-20T18:34:41Z,PyTorch MergeBot
b7be4b1e4803351d05d5ed5b219532f7dc5daaa7,"[AMD] Turn on fast path for index_put (#136136)

Summary:
This slow path is bad because it has a sync point which makes CPU really slow. I'm not very sure if AMD actually needs this with the newer rocm versino

{F1870213925}

Test Plan: CI

Differential Revision: D62731130

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136136
Approved by: https://github.com/danzimm, https://github.com/jeffdaily, https://github.com/eqy",2024-10-15T08:39:17Z,Xiaodong Wang
33e4b3e52d9dbb9b5e38913c372a05b69a0aed03,[functorch] Rename pointwise operator CompileCache to PointwiseOperatorCompileCache (pytorch/functorch#243),2021-11-04T23:19:02Z,Animesh Jain
cfc71f56e4f0e981f3ab6e7f49e537be48ac3384,"[quant][fx][graphmode] Support standalone module in _convert_do_not_use (#70151)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/70151

this supports converting an observed standalone module to quantized standalone module
in the new convert flow (convert observers to quant-dequant operators)

Test Plan:
```
python test/test_quant_trt.py TestConvertFxDoNotUse
```

Imported from OSS

Reviewed By: supriyar

Differential Revision: D33205163

fbshipit-source-id: 01ea44fb2a8ffe30bec1dd5678e7a72797bafafc",2021-12-30T20:29:32Z,Jerry Zhang
099a545376f805ce4da10bcb5cfc7bc71bbcba7c,"Hipify Caffe2 binaries (#10468)

Summary:
petrex
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10468

Reviewed By: yinghai

Differential Revision: ********

Pulled By: bddppq

fbshipit-source-id: 5da88aa4d79a5142f8e744cdcd8ae85951bc387c",2018-08-14T03:51:28Z,Junjie Bai
cf11fc0dcbb9c907cf6e851109b92f4157e445c9,"dynamo: Only log if we've disabled eval_frame once. (#134529)

This spams logs pretty badly otherwise

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134529
Approved by: https://github.com/chuanhaozhuge, https://github.com/oulgen",2024-08-30T00:35:23Z,Colin L. Rice
8028162103bf4393a4acc6e07105160c58895a04,Update the script to avoid the protobuf lib issue and add ZFNet (#6966),2018-04-25T23:38:43Z,Lu Fang
a11a49af585c5d0bc408e1a610438b770b813b8d,"Add NCCL work sequence number to work info (#120596)

Summary: Expose sequence number to work info. The number can help applications identify a NCCL work more precisely.

Test Plan:
1. pytest test/distributed/test_c10d_nccl.py::WorkHookTest::test_on_completion_hook_seq
2. pytest test/distributed/test_c10d_nccl.py::WorkHookTest

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/120596
Approved by: https://github.com/kwen2501",2024-02-28T07:54:37Z,Chao Zhou
44e2b8da280f5f095c1157741fd6b7c8bb1f896c,"Automated submodule update: FBGEMM (#72068)

Summary:
This is an automated pull request to update the first-party submodule for [pytorch/FBGEMM](https://github.com/pytorch/FBGEMM).

New submodule commit: https://github.com/pytorch/FBGEMM/commit/35d4dd4eb39f6fceb1bc3a3dafce1ed1f6449bd1

Pull Request resolved: https://github.com/pytorch/pytorch/pull/72068

Test Plan: Ensure that CI jobs succeed on GitHub before landing.

Reviewed By: malfet

Differential Revision: D33892960

fbshipit-source-id: 462b24ab3a81862bbfdc8e80fe07ea262e11829f
(cherry picked from commit c5d2b40fa61e185fab1237c07a0ddc875bcb9203)",2022-02-01T16:19:35Z,Facebook Community Bot
b96f49885f3c51d4ad0a2f02478aae77cf274f1c,"caffe2 python ideep conv_op test_int8_convolution skip for python 3

Summary: This test was failing in 3.7,  turns out it was ommitted by test director in 3.6 so I added a skip for both versions

Test Plan: unittests is skipped in 3.7 and 3.6 all other tests pass.

Reviewed By: tomdz

Differential Revision: D17820967

fbshipit-source-id: 571f0ec7fe1b0cb50ead4e0d18c00151a701f36a",2019-10-09T04:29:32Z,Jason Fried
34f1f2208be5cd31f515d795142f2caef6860c34,"Build c10 HIP test

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/15233

Reviewed By: ezyang

Differential Revision: D13471002

Pulled By: bddppq

fbshipit-source-id: b42c3bc2b9db672ce50a52eb700cc6ed13d3535f",2018-12-14T23:34:38Z,bddppq
9907a3eb652682d0c60cd9b5e0edfb3940a004d9,"Update Argmin/Argmax ONNX Export (#38329)

Summary:
Update Argmin/Argmax ONNX export in opset 12 to export with ""select_last_index"", and export correctly cases where the same value appears multiple time in the input tensor.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/38329

Reviewed By: hl475

Differential Revision: D21613799

Pulled By: houseroad

fbshipit-source-id: 4597e23561f444c4e56d30c735dae7e9a8a41c5e",2020-05-19T23:49:01Z,Lara Haidar
6294a9a87722797c7bbed31bef485d6359869411,"C++ API parity: RReLU

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/27437

Test Plan: Imported from OSS

Differential Revision: D17835413

Pulled By: pbelevich

fbshipit-source-id: 5d943fdac4fd2633e7f7ca13db1a7fed5636ca50",2019-10-11T02:12:51Z,Pavel Belevich
df136df8d5f646d49bcd68cf6e0b9ce3c553a700,"Remove upload_test_stat_aggregates script (#139915)

Instead of moving these queries to ClickHouse, we're just going to remove it since it's not really used.  We do want something for test aggregates, but we can make a new script instead
Pull Request resolved: https://github.com/pytorch/pytorch/pull/139915
Approved by: https://github.com/huydhn",2024-11-07T20:14:12Z,Catherine Lee
8d8a99c2442f7485124bea7134624b70df787c33,Add ONNX Pad reflect and edge mode support (#3048),2017-10-10T21:02:08Z,Lu Fang
c8974d649d684a33a5c02a0b112a6e0743201d97,"[test] AOTAutograd: support mutations on buffers that happen during th bw (#112906)

I can hold off on reviews / landing until I talk to Driss and we confirm that we need this for FP8. This PR also needs testing and probably shouldn't land until Tugsuu's input mutation handling [PR](https://github.com/pytorch/pytorch/pull/111046) goes through.

What this PR tries to solve is when you have a model that tries to mutate some nn module state (a buffer), but during the **backward**. It appears that this might be necessary for FP8's delayed scaling.

Today, AOTAutograd will just not realize if you happened to mutate any graph inputs when running the backward pass, and functionalize them away but not realize that they were input mutations. This PR tries to:

(a) detect this situation (input mutations during the backward)

(b) put `copy_()`'s in the graph to properly handle the input mutation when we can. In cases where we can't keep the copy_() in the graph, we just error loudly (I imagine that these cases will be extremely rare, but we can fix them if they ever come up).

This is mostly a prototype for now, not ready for review.

I made this example locally to test out:
```
import torch

class MutatingAutogradFn(torch.autograd.Function):

    @staticmethod
    def forward(ctx, x, buf):
        ctx.save_for_backward(buf)
        return x

    @staticmethod
    def backward(ctx, x_grad):
        buf = ctx.saved_tensors[0]
        buf.add_(x_grad)
        return x_grad * 3, None

class Mod(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.buf = torch.ones(2)

    @torch._dynamo.allow_in_graph
    def backward_mutating_fn(self, x, buf):
        return MutatingAutogradFn.apply(x, buf)

    def forward(self, x):
        tmp = self.backward_mutating_fn(x, self.buf)
        return tmp + self.buf

m = Mod()

x = torch.ones(2, requires_grad=True)
out = m(x)
# After the fw, buf should not have been mutated
print(m.buf)
out.sum().backward()
# bw has run, so buf should now be mutated
print(m.buf)
print(x.grad)
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112906
Approved by: https://github.com/ezyang",2023-11-28T19:32:51Z,Brian Hirsh
0fa9f7af838ee53c4e25e8fb0619807657f0160c,[functorch] Fix CI,2021-11-29T21:56:34Z,Richard Zou
493ae7820100ab6afdfc19883dd9ad8c5198f1e2,"[inductor] nan-checker (#112091)

This PR is spilt out of https://github.com/pytorch/pytorch/pull/108193 . It adds the ability to add assertion after each triton kernel calls to make sure all tensor arguments are not nan/inf. It helps me find a few bugs when working on benchmark fusion (due to messing up some kernel/graph level states when generating kernel code).

Right now we have to disable cudagraphs to enable the nan/inf checks. Otherwise we will see errors like: https://gist.github.com/shunting314/053db66c4f121e5f4c5de159bf0032ed . My best guess is it's due to GPU->CPU copy during capturing for cudagraphs. cc @voznesenskym @penguinwu @EikanWang @jgong5 @Guobing-Chen @XiaobingSuper @zhuhaozhe @blzheng @wenzhe-nrv @jiayisunx @peterbell10 @ipiszy @yf225 @chenyang78 @kadeng @muchulee8 @aakhundov @ColinPeppler @eellison  if there is easy way to make it work with cudagraphs.  But even if the nan-checker is not compatible with cudagraphs, it's probably still fine since it's just for debugging purpose.

Test command:
```
TORCHINDUCTOR_BENCHMARK_KERNEL=1 TORCHINDUCTOR_NAN_ASSERTS=1 python benchmarks/dynamo/huggingface.py --backend inductor --amp --performance --only BertForMaskedLM --training --disable-cudagraphs
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112091
Approved by: https://github.com/eellison, https://github.com/jansel",2023-11-01T22:48:10Z,Shunting Zhang
1acced4ebaa8d394d2c0bae9a7c698c6dee017a0,"Implemented getCodeText(string attr) in llvm/cuda codegen and added python bindings for it - #52974 (#53664)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/53664

Test Plan: Imported from OSS

Reviewed By: bertmaher

Differential Revision: *********

Pulled By: huiguoo

fbshipit-source-id: 281fe6c25f4664636b29d51dba396056a222a9e7",2021-03-11T19:55:41Z,Hui Guo
e8ec84864fc1ca75fae226f5d3fa816557de7ffb,"[StaticRuntime] Add aten::narrow (#48991)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/48991

Add native impl of `aten::narrow` to skip dispatcher, because `aten::narrow` calls `aten::slice` in its implementation, here we reduce the dispatcher overhead by two-fold by calling the native impl of `aten::slice`.

Reviewed By: bwasti

Differential Revision: D25387119

fbshipit-source-id: c020da2556a35bc57a8a2e21fa45dd491ea516a0",2020-12-08T21:47:01Z,Hao Lu
a6e94d274fa9d4a262fb047a10b7dce4a20e4e06,"[Pytorch] Add python binding to use mobile cpu allocator. (#52323)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/52323

Using default cpu allocator for ops executed on qnnpack backend will result in
asan failures with heap overflow since qnnpack (and xnnpack) can access input
beyond their and/beginning.

Here we are enabling this feature specifically to enable dynamic sparse linear op test
using qnnpack engine. In dynamic linear op, the fp32 bias is not packed and
hence can result in out-of-bound access.

Test Plan: test_set_default_mobile_cpu_allocator.py

Reviewed By: z-a-f

Differential Revision: D26263481

fbshipit-source-id: a49227cac7e6781b0db4a156ca734d7671972d9f",2021-02-17T16:40:04Z,Kimish Patel
e886122e9811df44af40251a94b27e7509b20911,"[dtensor][debug] add module level tracing and readable display (#128369)

**Summary**
Currently, CommDebugMode only allows displaying collective tracing at a model level whereas a user may require a more detailed breakdown. In order to make this possible, I have changed the ModuleParamaterShardingTracker by adding a string variable to track the current sub-module as well as a dictionary keeping track of the depths of the submodules in the model tree. CommModeDebug class was changed by adding a new dictionary keeping track of the module collective counts as well as a function that displays the counts in a way that is easy for the user to read. Two examples using MLPModule and Transformer have been added to showcase the new changes. The expected output of the simpler MLPModule example is:

<img width=""255"" alt=""Screenshot 2024-06-10 at 4 58 50 PM"" src=""https://github.com/pytorch/pytorch/assets/50644008/cf2161ef-2663-49c1-a8d5-9f97e96a1791"">

**Test Plan**
torchrun --standalone --nnodes=1 --nproc-per-node=4 torch/distributed/_tensor/examples/display_sharding_example.py

Pull Request resolved: https://github.com/pytorch/pytorch/pull/128369
Approved by: https://github.com/XilunWu",2024-06-13T20:19:55Z,Anshul Sinha
08820cb030927200c92733436f454f5fcf19c6bd,"[xla hash update] update the pinned xla hash (#80113)

This PR is auto-generated nightly by [this action](https://github.com/pytorch/pytorch/blob/master/.github/workflows/_update-commit-hash.yml).
Update the pinned xla hash.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/80113
Approved by: https://github.com/pytorchbot",2022-06-23T06:35:55Z,PyTorch MergeBot
b994ce359ecd9eb47b876732a0fe2bb0a19fde32,"Revert ""[cuDNN V8 API] (reopen) Allow the number of kernels profiled under torch.backends.cudnn.benchmark = True to be limitedCudnnv8 benchmark limit (#77002)""

This reverts commit c274f2ad52504e0d20724b05171da33c340e60f8.

Reverted https://github.com/pytorch/pytorch/pull/77002 on behalf of https://github.com/malfet due to please, as it breaks internal CI, but also no CUDA heads should be included from `torch/csrc/Module.cpp`, but rather should be implemented/registered in `torch/csrc/cuda/Module.cpp`",2022-05-24T21:52:35Z,PyTorch MergeBot
