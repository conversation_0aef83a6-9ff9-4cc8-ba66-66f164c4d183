#!/usr/bin/env python3
"""
测试配置系统
"""

import yaml
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_loading():
    """测试配置加载"""
    logger.info("测试配置加载...")
    
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        logger.info("✅ 配置文件加载成功")
        
        # 检查主要配置项
        assert 'model' in config, "缺少model配置"
        assert 'output' in config, "缺少output配置"
        assert 'tasks' in config, "缺少tasks配置"
        assert 'system' in config, "缺少system配置"
        
        logger.info("✅ 配置结构验证通过")
        
        # 检查任务配置
        tasks = config['tasks']
        assert 'sentiment_analysis' in tasks, "缺少sentiment_analysis任务"
        assert 'explorative_coding' in tasks, "缺少explorative_coding任务"
        
        # 检查sentiment_analysis任务配置
        sentiment_task = tasks['sentiment_analysis']
        required_fields = ['enabled', 'name', 'task_category', 'dataset_source', 
                          'data_file', 'prompt_dir', 'num_prompts', 'sample_prompts', 
                          'attempts_per_prompt']
        for field in required_fields:
            assert field in sentiment_task, f"sentiment_analysis任务缺少{field}字段"
        
        # 检查explorative_coding任务配置
        coding_task = tasks['explorative_coding']
        for field in required_fields:
            assert field in coding_task, f"explorative_coding任务缺少{field}字段"
        
        logger.info("✅ 任务配置验证通过")
        
        # 检查模型配置
        model_config = config['model']
        logger.info(f"模型配置内容: {model_config}")
        logger.info(f"模型配置键: {list(model_config.keys())}")

        model_fields = ['name', 'base_url', 'api_key_env', 'temperature', 'top_p',
                       'enable_thinking', 'enable_logprobs', 'top_logprobs']
        for field in model_fields:
            if field not in model_config:
                logger.error(f"模型配置缺少{field}字段，当前有: {list(model_config.keys())}")
            assert field in model_config, f"模型配置缺少{field}字段"
        
        logger.info("✅ 模型配置验证通过")
        
        # 检查输出配置
        output_config = config['output']
        assert 'format' in output_config, "输出配置缺少format字段"
        assert 'mongo' in output_config, "输出配置缺少mongo字段"
        
        logger.info("✅ 输出配置验证通过")
        
        return config
        
    except Exception as e:
        logger.error(f"❌ 配置测试失败: {e}")
        return None

def test_llm_generator_init():
    """测试LLM生成器初始化"""
    logger.info("测试LLM生成器初始化...")

    try:
        # 这里只测试导入和基本初始化，不测试实际的API调用
        from llm_response_generator import load_config, CONFIG

        # 测试配置加载函数
        config = load_config()
        assert isinstance(config, dict), "配置应该是字典类型"

        logger.info("✅ 配置加载函数测试通过")

        # 测试全局配置
        assert isinstance(CONFIG, dict), "全局配置应该是字典类型"

        logger.info("✅ 全局配置测试通过")

        return True

    except ImportError as e:
        if "pymongo" in str(e):
            logger.warning("⚠️ 跳过LLM生成器测试（pymongo未安装）")
            return True
        else:
            logger.error(f"❌ LLM生成器初始化测试失败: {e}")
            return False
    except Exception as e:
        logger.error(f"❌ LLM生成器初始化测试失败: {e}")
        return False

def test_prompt_loading():
    """测试prompt加载"""
    logger.info("测试prompt加载...")
    
    try:
        from llm_response_generator import LLMResponseGenerator
        
        # 创建生成器实例（不连接MongoDB）
        generator = LLMResponseGenerator()
        
        # 检查prompt模板是否加载
        assert hasattr(generator, 'prompt_templates'), "生成器应该有prompt_templates属性"
        
        templates = generator.prompt_templates
        assert isinstance(templates, dict), "prompt_templates应该是字典类型"
        
        # 检查是否加载了任务的prompt
        config = generator.config
        tasks = config.get('tasks', {})
        
        for task_name, task_config in tasks.items():
            if task_config.get('enabled', True):
                assert task_name in templates, f"应该加载{task_name}的prompt模板"
                task_prompts = templates[task_name]
                assert isinstance(task_prompts, list), f"{task_name}的prompt应该是列表类型"
                
                expected_count = task_config.get('num_prompts', 10)
                logger.info(f"{task_name}: 期望{expected_count}个prompt，实际加载{len(task_prompts)}个")
        
        logger.info("✅ Prompt加载测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ Prompt加载测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始配置系统测试...")
    
    # 测试配置加载
    config = test_config_loading()
    if not config:
        return False
    
    # 测试LLM生成器初始化
    if not test_llm_generator_init():
        return False
    
    # 测试prompt加载
    if not test_prompt_loading():
        return False
    
    logger.info("🎉 所有测试通过！配置系统工作正常")
    
    # 显示配置摘要
    logger.info("\n=== 配置摘要 ===")
    logger.info(f"模型: {config['model']['name']}")
    logger.info(f"输出格式: {config['output']['format']}")
    logger.info(f"启用thinking: {config['model']['enable_thinking']}")
    logger.info(f"启用logprobs: {config['model']['enable_logprobs']}")
    
    logger.info("\n=== 任务配置 ===")
    for task_name, task_config in config['tasks'].items():
        if task_config.get('enabled', True):
            logger.info(f"{task_name}:")
            logger.info(f"  数据文件: {task_config['data_file']}")
            logger.info(f"  Prompt目录: {task_config['prompt_dir']}")
            logger.info(f"  Prompt数量: {task_config['num_prompts']}")
            logger.info(f"  采样数量: {task_config['sample_prompts']}")
            logger.info(f"  每个prompt尝试次数: {task_config['attempts_per_prompt']}")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
