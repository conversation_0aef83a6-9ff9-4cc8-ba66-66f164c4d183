#!/bin/bash

# Counterfactual QA响应生成脚本
# 使用tmux运行，支持多线程并发处理

SESSION_NAME="counterfactual_gen"
LOG_FILE="counterfactual_generation.log"

echo "🚀 启动Counterfactual QA响应生成"
echo "会话名称: $SESSION_NAME"
echo "日志文件: $LOG_FILE"

# 检查tmux会话是否已存在
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "⚠️  tmux会话 '$SESSION_NAME' 已存在"
    echo "选择操作:"
    echo "1. 附加到现有会话 (tmux attach -t $SESSION_NAME)"
    echo "2. 杀死现有会话并创建新的"
    echo "3. 退出"
    read -p "请选择 (1/2/3): " choice
    
    case $choice in
        1)
            echo "附加到现有会话..."
            tmux attach -t $SESSION_NAME
            exit 0
            ;;
        2)
            echo "杀死现有会话..."
            tmux kill-session -t $SESSION_NAME
            ;;
        3)
            echo "退出"
            exit 0
            ;;
        *)
            echo "无效选择，退出"
            exit 1
            ;;
    esac
fi

# 检查环境变量
if [ -z "$DASHSCOPE_API_KEY" ] || [ "$DASHSCOPE_API_KEY" = "null" ]; then
    echo "❌ 错误: DASHSCOPE_API_KEY 环境变量未设置"
    echo "请设置API密钥: export DASHSCOPE_API_KEY='your-api-key'"
    exit 1
fi

# 检查必要文件
required_files=(
    "config.yaml"
    "llm_response_generator.py"
    "data/conterfactual/counterfactual_prompts.csv"
    "prompts/prompts_config.json"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 缺少必要文件 $file"
        exit 1
    fi
done

echo "✅ 环境检查通过"

# 创建新的tmux会话
echo "创建tmux会话: $SESSION_NAME"
tmux new-session -d -s $SESSION_NAME

# 设置会话环境
tmux send-keys -t $SESSION_NAME "cd $(pwd)" Enter
tmux send-keys -t $SESSION_NAME "export DASHSCOPE_API_KEY='$DASHSCOPE_API_KEY'" Enter

# 显示启动信息
tmux send-keys -t $SESSION_NAME "echo '🧪 Counterfactual QA响应生成开始'" Enter
tmux send-keys -t $SESSION_NAME "echo '数据: 前10个counterfactual问题'" Enter
tmux send-keys -t $SESSION_NAME "echo '配置: 5个prompts × 6次重复 = 30次/问题'" Enter
tmux send-keys -t $SESSION_NAME "echo '总计: 10 × 30 = 300次LLM调用'" Enter
tmux send-keys -t $SESSION_NAME "echo '多线程: 启用并发处理'" Enter
tmux send-keys -t $SESSION_NAME "echo '日志: 完整prompt和response输出'" Enter
tmux send-keys -t $SESSION_NAME "echo ''" Enter

# 启动生成任务
echo "启动Counterfactual QA生成任务..."
tmux send-keys -t $SESSION_NAME "python run_llm_generation.py --generate 2>&1 | tee $LOG_FILE" Enter

echo ""
echo "🎯 任务已启动！"
echo ""
echo "监控选项:"
echo "1. 附加到tmux会话: tmux attach -t $SESSION_NAME"
echo "2. 查看日志: tail -f $LOG_FILE"
echo "3. 检查进度: python -c \"from pymongo import MongoClient; c=MongoClient(); print(f'已生成: {c[\"LLM-UQ\"][\"response_collections\"].count_documents({\"task_name\": \"counterfactual_qa\"})} 条响应')\""
echo ""
echo "管理命令:"
echo "- 分离会话: Ctrl+B, 然后按 D"
echo "- 杀死会话: tmux kill-session -t $SESSION_NAME"
echo "- 列出会话: tmux list-sessions"
echo ""
echo "预计完成时间: 约30-60分钟（取决于网络和API响应速度）"
