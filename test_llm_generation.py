#!/usr/bin/env python3
"""
LLM响应生成测试脚本
从采样数据中各取一个条目进行测试
"""

import os
import csv
import sys
import logging
from llm_response_generator import LLMResponseGenerator

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_llm_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_test_data():
    """加载测试数据 - 每个数据集各取一个条目"""
    test_data = {
        'semeval': None,
        'commits': None
    }
    
    # 加载SemEval测试数据
    try:
        with open('sampled_semeval.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i == 0:  # 只取第一个条目
                    test_data['semeval'] = {
                        'id': row['id'],
                        'text': row['text'],
                        'label': row['label']
                    }
                    break
        print(f"✓ 加载SemEval测试数据: {test_data['semeval']['id']}")
    except FileNotFoundError:
        print("✗ 未找到sampled_semeval.csv文件")
        return None
    
    # 加载Commits测试数据
    try:
        with open('sampled_commits.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i == 0:  # 只取第一个条目
                    test_data['commits'] = {
                        'sha': row['sha'],
                        'message': row['message'],
                        'date': row['date'],
                        'author': row['author']
                    }
                    break
        print(f"✓ 加载Commits测试数据: {test_data['commits']['sha'][:8]}")
    except FileNotFoundError:
        print("✗ 未找到sampled_commits.csv文件")
        return None
    
    return test_data

def debug_llm_response(llm_response):
    """详细调试LLM响应"""
    print("\n" + "-"*40)
    print("LLM响应调试信息")
    print("-"*40)
    
    print(f"响应类型: {type(llm_response)}")
    print(f"响应键: {list(llm_response.keys())}")
    
    for key, value in llm_response.items():
        print(f"\n{key}:")
        print(f"  类型: {type(value)}")
        print(f"  值: {value}")
        
        if key == 'logprobs' and value is not None:
            print(f"  Logprobs详细信息:")
            if isinstance(value, list):
                print(f"    列表长度: {len(value)}")
                if len(value) > 0:
                    print(f"    第一个元素: {value[0]}")
            elif isinstance(value, dict):
                print(f"    字典键: {list(value.keys())}")
                for k, v in value.items():
                    print(f"    {k}: {type(v)} - {v}")
            else:
                print(f"    其他类型: {value}")
    
    # 额外调试信息
    print(f"\n额外调试信息:")
    print(f"  Content长度: {len(llm_response.get('content', ''))}")
    print(f"  Logprobs是否为None: {llm_response.get('logprobs') is None}")
    print(f"  Model: {llm_response.get('model')}")
    print(f"  Finish reason: {llm_response.get('finish_reason')}")

def test_semeval_single_item(generator, test_item):
    """测试单个SemEval条目"""
    print("\n" + "="*60)
    print("测试SemEval数据")
    print("="*60)
    
    print(f"测试条目ID: {test_item['id']}")
    print(f"文本: {test_item['text'][:100]}...")
    print(f"标签: {test_item['label']}")
    
    # 测试所有prompt变体
    prompt_variants = ["only_answer", "answer_then_reason", "reasoning_then_answer"]
    dataset_source = "twitter_sentiment"
    
    for prompt_variant in prompt_variants:
        print(f"\n--- 测试 {prompt_variant} ---")
        
        if prompt_variant not in generator.prompt_templates[dataset_source]:
            print(f"✗ Prompt模板 {prompt_variant} 未找到")
            continue
        
        # 填充prompt模板
        prompt_template = generator.prompt_templates[dataset_source][prompt_variant]
        final_prompt = prompt_template.replace("{tweet}", test_item['text'])
        
        print(f"Prompt: {final_prompt[:200]}...")
        
        # 调用LLM
        print("调用LLM...")
        llm_response = generator.call_llm(final_prompt)
        
        # 详细调试响应
        debug_llm_response(llm_response)
        
        # 解析响应
        parsed_response = generator.parse_response(llm_response['content'], prompt_variant, dataset_source)
        
        print(f"原始响应: {llm_response['content']}")
        print(f"解析答案: {parsed_response['parsed_answer']}")
        if parsed_response['parsed_reason']:
            print(f"解析推理: {parsed_response['parsed_reason']}")
        
        # 保存到MongoDB
        document = {
            "run_id": "test_run_semeval",
            "task_id": f"test_task_semeval_{test_item['id']}_{prompt_variant}",
            "dataset_source": dataset_source,
            "task_category": "sentiment_analysis",
            "input_text": test_item['text'],
            "reference_answer": test_item['label'],
            "model_identifier": llm_response['model'],
            "prompt_variant": prompt_variant,
            "prompt_template": prompt_template,
            "final_prompt": final_prompt,
            "generation_config": {
                "temperature": 0.7,
                "top_p": 0.95,
                "enable_thinking": False
            },
            "task_attempt_prompt": 1,
            "task_attempt_total": 1,
            "raw_response": llm_response['content'],
            "response_logprobs": llm_response['logprobs'],
            "finish_reason": llm_response.get('finish_reason'),
            "raw_answer": parsed_response['raw_answer'],
            "parsed_answer": parsed_response['parsed_answer'],
            "parsed_reason": parsed_response['parsed_reason'],
            "execution_timestamp": "2024-01-01T00:00:00Z"  # 测试时间戳
        }
        
        inserted_id = generator.save_to_mongodb(document)
        if inserted_id:
            print(f"✓ 保存到MongoDB: {inserted_id}")
        else:
            print("✗ 保存到MongoDB失败")

def test_commits_single_item(generator, test_item):
    """测试单个Commits条目"""
    print("\n" + "="*60)
    print("测试Commits数据")
    print("="*60)
    
    print(f"测试条目SHA: {test_item['sha']}")
    print(f"消息: {test_item['message'][:100]}...")
    print(f"作者: {test_item['author']}")
    print(f"日期: {test_item['date']}")
    
    # 测试所有prompt变体
    prompt_variants = ["only_answer", "answer_then_reason", "reasoning_then_answer"]
    dataset_source = "pytorch_commits"
    
    for prompt_variant in prompt_variants:
        print(f"\n--- 测试 {prompt_variant} ---")
        
        if prompt_variant not in generator.prompt_templates[dataset_source]:
            print(f"✗ Prompt模板 {prompt_variant} 未找到")
            continue
        
        # 准备commits数据
        commit_data = {
            'repo_name': 'pytorch/pytorch',
            'sha': test_item['sha'],
            'author': test_item['author'],
            'date': test_item['date'],
            'message': test_item['message']
        }
        
        # 填充prompt模板
        prompt_template = generator.prompt_templates[dataset_source][prompt_variant]
        final_prompt = prompt_template.format(**commit_data)
        
        print(f"Prompt: {final_prompt[:200]}...")
        
        # 调用LLM
        print("调用LLM...")
        llm_response = generator.call_llm(final_prompt)
        
        # 详细调试响应
        debug_llm_response(llm_response)
        
        # 解析响应
        parsed_response = generator.parse_response(llm_response['content'], prompt_variant, dataset_source)
        
        print(f"原始响应: {llm_response['content']}")
        print(f"解析答案: {parsed_response['parsed_answer']}")
        if parsed_response['parsed_reason']:
            print(f"解析推理: {parsed_response['parsed_reason']}")
        
        # 保存到MongoDB
        document = {
            "run_id": "test_run_commits",
            "task_id": f"test_task_commits_{test_item['sha'][:8]}_{prompt_variant}",
            "dataset_source": dataset_source,
            "task_category": "open_explorative_coding",
            "input_text": test_item['message'],
            "reference_answer": None,
            "model_identifier": llm_response['model'],
            "prompt_variant": prompt_variant,
            "prompt_template": prompt_template,
            "final_prompt": final_prompt,
            "generation_config": {
                "temperature": 0.7,
                "top_p": 0.95,
                "enable_thinking": False
            },
            "task_attempt_prompt": 1,
            "task_attempt_total": 1,
            "raw_response": llm_response['content'],
            "response_logprobs": llm_response['logprobs'],
            "finish_reason": llm_response.get('finish_reason'),
            "raw_answer": parsed_response['raw_answer'],
            "parsed_answer": parsed_response['parsed_answer'],
            "parsed_reason": parsed_response['parsed_reason'],
            "execution_timestamp": "2024-01-01T00:00:00Z"  # 测试时间戳
        }
        
        inserted_id = generator.save_to_mongodb(document)
        if inserted_id:
            print(f"✓ 保存到MongoDB: {inserted_id}")
        else:
            print("✗ 保存到MongoDB失败")

def main():
    """主函数"""
    print("="*60)
    print("LLM响应生成测试")
    print("="*60)
    
    # 检查环境
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key or api_key == 'null':
        print("✗ 请设置DASHSCOPE_API_KEY环境变量")
        sys.exit(1)
    
    # 检查CSV文件
    if not os.path.exists('sampled_semeval.csv') or not os.path.exists('sampled_commits.csv'):
        print("✗ 请先运行数据采样生成CSV文件")
        print("运行: python data_sampler.py")
        sys.exit(1)
    
    try:
        # 加载测试数据
        test_data = load_test_data()
        if not test_data:
            sys.exit(1)
        
        # 创建生成器
        print("\n初始化LLM响应生成器...")
        generator = LLMResponseGenerator()
        
        # 测试SemEval数据
        if test_data['semeval']:
            test_semeval_single_item(generator, test_data['semeval'])
        
        # 测试Commits数据
        if test_data['commits']:
            test_commits_single_item(generator, test_data['commits'])
        
        print("\n" + "="*60)
        print("测试完成！")
        print("请检查MongoDB中的以下文档:")
        print("- run_id: 'test_run_semeval' (SemEval测试)")
        print("- run_id: 'test_run_commits' (Commits测试)")
        print("详细日志请查看: test_llm_generation.log")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
