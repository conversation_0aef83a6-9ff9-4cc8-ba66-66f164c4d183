import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_results(csv_file='data/uq_methods_optimized_results.csv'):
    """加载和分析UQ结果"""
    df = pd.read_csv(csv_file)
    
    print("=== UQ Methods Results Analysis ===")
    print(f"Total records: {len(df)}")
    print(f"Data types: {df['data_type'].unique()}")
    print(f"UQ methods: {df['uq_method'].unique()}")
    print(f"Prompt types by data type:")
    for data_type in df['data_type'].unique():
        prompt_types = df[df['data_type'] == data_type]['prompt_type'].unique()
        print(f"  {data_type}: {prompt_types}")
    print(f"Sample sizes by data type:")
    for data_type in df['data_type'].unique():
        sample_sizes = sorted(df[df['data_type'] == data_type]['sample_size'].unique())
        print(f"  {data_type}: {sample_sizes}")
    
    return df

def create_method_comparison_plots(df):
    """创建UQ方法比较图表"""
    
    # 创建图表目录
    Path('plots').mkdir(exist_ok=True)
    
    # 1. 不确定性分数比较 - 按数据类型分组
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    for i, data_type in enumerate(['twitter', 'commit']):
        data = df[df['data_type'] == data_type]
        
        # 创建pivot表便于可视化
        pivot_data = data.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='uncertainty_score',
            aggfunc='mean'
        )
        
        # 绘制热力图
        sns.heatmap(pivot_data, annot=True, fmt='.3f', cmap='viridis', 
                   ax=axes[i], cbar_kws={'label': 'Uncertainty Score'})
        axes[i].set_title(f'{data_type.upper()} - Uncertainty Scores by UQ Method')
        axes[i].set_xlabel('UQ Method')
        axes[i].set_ylabel('Prompt Type & Sample Size')
        
        # 旋转x轴标签
        axes[i].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('plots/uncertainty_scores_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 平均相似度比较
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    for i, data_type in enumerate(['twitter', 'commit']):
        data = df[df['data_type'] == data_type]
        
        # 过滤掉num_sets方法（它的mean_similarity总是0）
        data_filtered = data[data['uq_method'] != 'num_sets']
        
        pivot_data = data_filtered.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='mean_similarity',
            aggfunc='mean'
        )
        
        sns.heatmap(pivot_data, annot=True, fmt='.3f', cmap='coolwarm', 
                   ax=axes[i], cbar_kws={'label': 'Mean Similarity'})
        axes[i].set_title(f'{data_type.upper()} - Mean Similarity by UQ Method (excl. NumSets)')
        axes[i].set_xlabel('UQ Method')
        axes[i].set_ylabel('Prompt Type & Sample Size')
        axes[i].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('plots/mean_similarity_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_sample_size_analysis(df):
    """分析样本大小对不确定性的影响"""
    
    fig, axes = plt.subplots(2, 3, figsize=(24, 16))
    
    # 为每个数据类型和prompt类型创建图表
    plot_idx = 0
    for data_type in ['twitter', 'commit']:
        data = df[df['data_type'] == data_type]
        prompt_types = data['prompt_type'].unique()
        
        for prompt_type in prompt_types:
            if plot_idx >= 6:  # 最多6个子图
                break
                
            prompt_data = data[data['prompt_type'] == prompt_type]
            
            row = plot_idx // 3
            col = plot_idx % 3
            
            # 按UQ方法分组绘制线图
            for method in prompt_data['uq_method'].unique():
                method_data = prompt_data[prompt_data['uq_method'] == method]
                method_data = method_data.sort_values('sample_size')
                
                axes[row, col].plot(method_data['sample_size'], 
                                  method_data['uncertainty_score'], 
                                  marker='o', label=method, linewidth=2)
            
            axes[row, col].set_title(f'{data_type.upper()} - {prompt_type}')
            axes[row, col].set_xlabel('Sample Size')
            axes[row, col].set_ylabel('Uncertainty Score')
            axes[row, col].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            axes[row, col].grid(True, alpha=0.3)
            
            plot_idx += 1
    
    # 隐藏多余的子图
    for i in range(plot_idx, 6):
        row = i // 3
        col = i % 3
        axes[row, col].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('plots/sample_size_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_method_type_comparison(df):
    """比较NLI vs Jaccard vs NumSets方法"""
    
    # 添加方法类型列
    df['method_type'] = df['uq_method'].apply(
        lambda x: 'NLI' if 'nli' in x else ('Jaccard' if 'jaccard' in x else 'NumSets')
    )
    df['method_base'] = df['uq_method'].apply(
        lambda x: x.replace('_nli', '').replace('_jaccard', '') if x != 'num_sets' else 'num_sets'
    )
    
    # 1. 方法类型比较
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    for i, data_type in enumerate(['twitter', 'commit']):
        data = df[df['data_type'] == data_type]
        
        # 按方法类型分组的平均不确定性
        method_comparison = data.groupby(['method_type', 'prompt_type'])['uncertainty_score'].mean().reset_index()
        
        # 创建分组条形图
        pivot_method = method_comparison.pivot(index='prompt_type', columns='method_type', values='uncertainty_score')
        pivot_method.plot(kind='bar', ax=axes[i], width=0.8)
        
        axes[i].set_title(f'{data_type.upper()} - Uncertainty by Method Type')
        axes[i].set_xlabel('Prompt Type')
        axes[i].set_ylabel('Average Uncertainty Score')
        axes[i].legend(title='Method Type')
        axes[i].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('plots/method_type_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 基础方法比较 (deg_mat vs eccentricity vs eig_val)
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    
    for i, data_type in enumerate(['twitter', 'commit']):
        # NLI方法comparison
        nli_data = df[(df['data_type'] == data_type) & (df['method_type'] == 'NLI')]
        base_comparison = nli_data.groupby(['method_base', 'prompt_type'])['uncertainty_score'].mean().reset_index()
        pivot_base = base_comparison.pivot(index='prompt_type', columns='method_base', values='uncertainty_score')
        pivot_base.plot(kind='bar', ax=axes[i, 0], width=0.8)
        axes[i, 0].set_title(f'{data_type.upper()} - NLI Methods Comparison')
        axes[i, 0].set_xlabel('Prompt Type')
        axes[i, 0].set_ylabel('Average Uncertainty Score')
        axes[i, 0].legend(title='Base Method')
        axes[i, 0].tick_params(axis='x', rotation=45)
        
        # Jaccard方法comparison
        jaccard_data = df[(df['data_type'] == data_type) & (df['method_type'] == 'Jaccard')]
        base_comparison = jaccard_data.groupby(['method_base', 'prompt_type'])['uncertainty_score'].mean().reset_index()
        pivot_base = base_comparison.pivot(index='prompt_type', columns='method_base', values='uncertainty_score')
        pivot_base.plot(kind='bar', ax=axes[i, 1], width=0.8)
        axes[i, 1].set_title(f'{data_type.upper()} - Jaccard Methods Comparison')
        axes[i, 1].set_xlabel('Prompt Type')
        axes[i, 1].set_ylabel('Average Uncertainty Score')
        axes[i, 1].legend(title='Base Method')
        axes[i, 1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('plots/base_method_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_detailed_analysis_tables(df):
    """创建详细的分析表格"""
    
    print("\n=== Detailed Analysis Tables ===")
    
    # 1. 总体统计
    print("\n1. Overall Statistics by Method:")
    overall_stats = df.groupby('uq_method')['uncertainty_score'].agg(['mean', 'std', 'min', 'max']).round(4)
    print(overall_stats)
    
    # 2. 按数据类型的统计
    print("\n2. Statistics by Data Type and Method:")
    for data_type in df['data_type'].unique():
        print(f"\n{data_type.upper()}:")
        type_stats = df[df['data_type'] == data_type].groupby('uq_method')['uncertainty_score'].agg(['mean', 'std']).round(4)
        print(type_stats)
    
    # 3. 样本大小影响分析
    print("\n3. Sample Size Impact Analysis:")
    for data_type in df['data_type'].unique():
        print(f"\n{data_type.upper()}:")
        data = df[df['data_type'] == data_type]
        sample_impact = data.groupby(['uq_method', 'sample_size'])['uncertainty_score'].mean().unstack('sample_size').round(4)
        print(sample_impact)
    
    # 4. NLI vs Jaccard 比较
    print("\n4. NLI vs Jaccard Comparison:")
    df['method_type'] = df['uq_method'].apply(
        lambda x: 'NLI' if 'nli' in x else ('Jaccard' if 'jaccard' in x else 'NumSets')
    )
    
    nli_jaccard_data = df[df['method_type'].isin(['NLI', 'Jaccard'])]
    comparison = nli_jaccard_data.groupby(['data_type', 'method_type'])['uncertainty_score'].agg(['mean', 'std']).round(4)
    print(comparison)
    
    # 5. Prompt类型影响
    print("\n5. Prompt Type Impact:")
    for data_type in df['data_type'].unique():
        print(f"\n{data_type.upper()}:")
        data = df[df['data_type'] == data_type]
        prompt_impact = data.groupby(['prompt_type', 'uq_method'])['uncertainty_score'].mean().unstack('uq_method').round(4)
        print(prompt_impact)

def main():
    # 加载数据
    df = load_and_analyze_results()
    
    # 创建各种分析图表
    print("\nGenerating analysis plots...")
    
    create_method_comparison_plots(df)
    create_sample_size_analysis(df)
    create_method_type_comparison(df)
    
    # 生成详细分析表格
    create_detailed_analysis_tables(df)
    
    print(f"\nAnalysis complete! Plots saved to 'plots/' directory")
    print("Generated plots:")
    print("- uncertainty_scores_heatmap.png: Overall uncertainty scores comparison")
    print("- mean_similarity_heatmap.png: Similarity scores comparison")
    print("- sample_size_analysis.png: Sample size impact analysis")
    print("- method_type_comparison.png: NLI vs Jaccard vs NumSets comparison")
    print("- base_method_comparison.png: deg_mat vs eccentricity vs eig_val comparison")

if __name__ == "__main__":
    main()