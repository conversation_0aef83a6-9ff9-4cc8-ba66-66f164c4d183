# Development dependencies for LLM Uncertainty Quantification Framework
# Install with: pip install -r requirements-dev.txt

# Include base requirements
-r requirements.txt

# Jupyter and notebook support
jupyter>=1.0.0
jupyterlab>=4.0.0
ipywidgets>=8.0.0

# Data visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0

# Code quality
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# Profiling and debugging
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Additional utilities
requests>=2.28.0
beautifulsoup4>=4.11.0
