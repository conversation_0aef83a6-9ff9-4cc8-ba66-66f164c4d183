#!/usr/bin/env python3
"""
测试Ollama NLI的两种方法：JSON解析 vs 采样估计
对比不同方法的准确性和一致性
"""

import sys
import os
import logging
import time
from typing import List, Dict, Any, Tuple

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.nli_calculator import OllamaNLICalculator, get_available_ollama_models
from core.nli_shared import get_nli_calculator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def get_test_cases() -> List[Tuple[str, str, str]]:
    """获取测试用例，包含预期的NLI关系"""
    return [
        (
            "The cat is sleeping on the mat",
            "A feline is resting on a rug",
            "ENTAILMENT"  # 预期关系
        ),
        (
            "It is raining heavily outside",
            "The weather is sunny and clear",
            "CONTRADICTION"
        ),
        (
            "The student passed the exam",
            "The student is happy",
            "NEUTRAL"
        ),
        (
            "All birds can fly",
            "Penguins are birds but cannot fly",
            "CONTRADICTION"
        ),
        (
            "The book is on the table",
            "There is a novel somewhere in the room",
            "NEUTRAL"
        )
    ]


def test_nli_methods(model_name: str, test_cases: List[Tuple[str, str, str]]):
    """测试不同的NLI方法"""
    print(f"\n🧪 测试模型: {model_name}")
    print("="*60)
    
    # 初始化两种方法的计算器
    json_calc = OllamaNLICalculator(
        model_name=model_name, 
        verbose=False, 
        use_sampling=False
    )
    
    sampling_calc = OllamaNLICalculator(
        model_name=model_name, 
        verbose=False, 
        use_sampling=True,
        num_samples=5  # 使用5次采样
    )
    
    # 初始化DeBERTa基线
    try:
        deberta_calc = get_nli_calculator("microsoft/deberta-large-mnli")
        use_deberta = True
    except Exception as e:
        print(f"⚠️ 无法加载DeBERTa模型: {e}")
        use_deberta = False
    
    results = []
    
    for i, (text1, text2, expected) in enumerate(test_cases, 1):
        print(f"\n📝 测试用例 {i}/{len(test_cases)}")
        print(f"   句子1: {text1}")
        print(f"   句子2: {text2}")
        print(f"   预期: {expected}")
        
        # JSON方法
        print(f"\n🔍 JSON解析方法:")
        start_time = time.time()
        try:
            json_result = json_calc.compute_nli_scores(text1, text2)
            json_time = time.time() - start_time
            json_pred = get_prediction(json_result)
            print(f"   结果: E={json_result.entailment:.3f}, N={json_result.neutral:.3f}, C={json_result.contradiction:.3f}")
            print(f"   预测: {json_pred} ({json_time:.2f}s)")
            json_success = True
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            json_result = None
            json_pred = "ERROR"
            json_time = 0
            json_success = False
        
        # 采样方法
        print(f"\n🎲 采样估计方法:")
        start_time = time.time()
        try:
            sampling_result = sampling_calc.compute_nli_scores(text1, text2)
            sampling_time = time.time() - start_time
            sampling_pred = get_prediction(sampling_result)
            print(f"   结果: E={sampling_result.entailment:.3f}, N={sampling_result.neutral:.3f}, C={sampling_result.contradiction:.3f}")
            print(f"   预测: {sampling_pred} ({sampling_time:.2f}s)")
            sampling_success = True
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            sampling_result = None
            sampling_pred = "ERROR"
            sampling_time = 0
            sampling_success = False
        
        # DeBERTa基线
        deberta_result = None
        deberta_pred = "N/A"
        deberta_time = 0
        if use_deberta:
            print(f"\n🎯 DeBERTa基线:")
            start_time = time.time()
            try:
                deberta_result = deberta_calc.compute_nli_scores_cached(text1, text2)
                deberta_time = time.time() - start_time
                deberta_pred = get_prediction(deberta_result)
                print(f"   结果: E={deberta_result.entailment:.3f}, N={deberta_result.neutral:.3f}, C={deberta_result.contradiction:.3f}")
                print(f"   预测: {deberta_pred} ({deberta_time:.2f}s)")
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        # 保存结果
        result = {
            'case_id': i,
            'text1': text1,
            'text2': text2,
            'expected': expected,
            'json_pred': json_pred,
            'json_success': json_success,
            'json_time': json_time,
            'sampling_pred': sampling_pred,
            'sampling_success': sampling_success,
            'sampling_time': sampling_time,
            'deberta_pred': deberta_pred,
            'deberta_time': deberta_time
        }
        
        if json_result:
            result.update({
                'json_entailment': json_result.entailment,
                'json_neutral': json_result.neutral,
                'json_contradiction': json_result.contradiction
            })
        
        if sampling_result:
            result.update({
                'sampling_entailment': sampling_result.entailment,
                'sampling_neutral': sampling_result.neutral,
                'sampling_contradiction': sampling_result.contradiction
            })
        
        if deberta_result:
            result.update({
                'deberta_entailment': deberta_result.entailment,
                'deberta_neutral': deberta_result.neutral,
                'deberta_contradiction': deberta_result.contradiction
            })
        
        results.append(result)
    
    return results


def get_prediction(nli_result) -> str:
    """从NLI结果获取预测标签"""
    if nli_result is None:
        return "ERROR"
    
    scores = [nli_result.entailment, nli_result.neutral, nli_result.contradiction]
    labels = ['ENTAILMENT', 'NEUTRAL', 'CONTRADICTION']
    return labels[scores.index(max(scores))]


def analyze_results(results: List[Dict[str, Any]], model_name: str):
    """分析测试结果"""
    print(f"\n📊 {model_name} 结果分析")
    print("="*60)
    
    # 成功率统计
    json_success_rate = sum(1 for r in results if r['json_success']) / len(results)
    sampling_success_rate = sum(1 for r in results if r['sampling_success']) / len(results)
    
    print(f"📈 成功率:")
    print(f"   JSON解析方法:  {json_success_rate:.1%}")
    print(f"   采样估计方法:  {sampling_success_rate:.1%}")
    
    # 准确性统计（与预期对比）
    successful_results = [r for r in results if r['json_success'] and r['sampling_success']]
    
    if successful_results:
        json_accuracy = sum(1 for r in successful_results if r['json_pred'] == r['expected']) / len(successful_results)
        sampling_accuracy = sum(1 for r in successful_results if r['sampling_pred'] == r['expected']) / len(successful_results)
        
        print(f"\n🎯 准确性（与预期对比）:")
        print(f"   JSON解析方法:  {json_accuracy:.1%}")
        print(f"   采样估计方法:  {sampling_accuracy:.1%}")
        
        # 方法间一致性
        method_consistency = sum(1 for r in successful_results if r['json_pred'] == r['sampling_pred']) / len(successful_results)
        print(f"   方法间一致性:  {method_consistency:.1%}")
        
        # 时间对比
        avg_json_time = sum(r['json_time'] for r in successful_results) / len(successful_results)
        avg_sampling_time = sum(r['sampling_time'] for r in successful_results) / len(successful_results)
        
        print(f"\n⏱️ 平均计算时间:")
        print(f"   JSON解析方法:  {avg_json_time:.2f}s")
        print(f"   采样估计方法:  {avg_sampling_time:.2f}s")
        print(f"   速度比:        {avg_sampling_time/avg_json_time:.1f}x")
        
        # 与DeBERTa对比
        deberta_results = [r for r in successful_results if r['deberta_pred'] != 'N/A']
        if deberta_results:
            json_vs_deberta = sum(1 for r in deberta_results if r['json_pred'] == r['deberta_pred']) / len(deberta_results)
            sampling_vs_deberta = sum(1 for r in deberta_results if r['sampling_pred'] == r['deberta_pred']) / len(deberta_results)
            
            print(f"\n🤝 与DeBERTa一致性:")
            print(f"   JSON解析方法:  {json_vs_deberta:.1%}")
            print(f"   采样估计方法:  {sampling_vs_deberta:.1%}")
    
    # 详细结果表格
    print(f"\n📋 详细结果:")
    print(f"{'用例':<4} {'预期':<12} {'JSON':<12} {'采样':<12} {'DeBERTa':<12}")
    print("-" * 60)
    for r in results:
        print(f"{r['case_id']:<4} {r['expected']:<12} {r['json_pred']:<12} {r['sampling_pred']:<12} {r['deberta_pred']:<12}")


def main():
    """主函数"""
    print("🚀 Ollama NLI方法对比测试")
    print("🔍 对比JSON解析 vs 采样估计两种方法")
    print("="*60)
    
    # 检查可用模型
    available_models = get_available_ollama_models()
    target_models = ["gpt-oss:20b", "llama3.3:latest"]
    available_targets = [model for model in target_models if model in available_models]
    
    if not available_targets:
        print(f"❌ 目标模型都不可用: {target_models}")
        print(f"📋 可用模型: {available_models}")
        return
    
    print(f"✅ 可用目标模型: {available_targets}")
    
    # 获取测试用例
    test_cases = get_test_cases()
    print(f"📝 准备了 {len(test_cases)} 个测试用例")
    
    # 测试每个可用模型
    for model in available_targets:
        try:
            results = test_nli_methods(model, test_cases)
            analyze_results(results, model)
        except Exception as e:
            log.error(f"测试模型 {model} 失败: {e}")
    
    print(f"\n✅ 测试完成！")
    print(f"💡 总结:")
    print(f"   1. JSON解析方法：快速但依赖模型输出格式")
    print(f"   2. 采样估计方法：更稳定但计算时间较长")
    print(f"   3. 建议根据应用场景选择合适的方法")


if __name__ == "__main__":
    main()
