# NLI缓存系统使用说明

## 概述

新的NLI缓存系统使用结构化的CSV格式保存NLI相似度计算结果，便于后续分析复用和管理。

## 文件结构

```
cache/
├── nli_results_cache.csv              # 主缓存文件
├── nli_results_cache_20250131_143022.csv  # 带时间戳的备份文件
├── nli_results_cache_20250131_153045.csv  # 阶段性保存的缓存
└── nli_cache.pkl                      # 旧格式的pickle缓存（兼容性）

data/Uq_Evaluation_20250731/
├── uq_methods_complete_analysis.csv   # 主分析结果
├── twitter_uncertainty_pivot.csv      # Twitter不确定性汇总
├── twitter_similarity_pivot.csv       # Twitter相似度汇总
├── twitter_validation_accuracy_pivot.csv  # Twitter validation准确率汇总
├── commit_uncertainty_pivot.csv       # Commit不确定性汇总
└── commit_similarity_pivot.csv        # Commit相似度汇总
```

## CSV缓存文件格式

`nli_results_cache.csv` 包含以下字段：

- `text1`: 第一个文本
- `text2`: 第二个文本  
- `model_name`: 使用的NLI模型名称
- `similarity_score`: 计算得到的相似度分数
- `text1_hash`: text1的MD5哈希值（用于快速查找）
- `text2_hash`: text2的MD5哈希值
- `timestamp`: 计算时间戳

## 缓存管理工具

使用 `nli_cache_manager.py` 来管理和查看缓存：

### 查看缓存统计信息
```bash
python nli_cache_manager.py stats
```

### 搜索缓存条目
```bash
# 按模型搜索
python nli_cache_manager.py search --model microsoft/deberta-large-mnli

# 按相似度范围搜索
python nli_cache_manager.py search --min-sim 0.8 --max-sim 1.0

# 限制结果数量
python nli_cache_manager.py search --model microsoft/deberta-large-mnli --limit 20
```

### 导出缓存子集
```bash
# 导出特定模型的缓存
python nli_cache_manager.py export high_similarity_cache.csv --model microsoft/deberta-large-mnli --min-sim 0.9

# 导出低相似度的条目
python nli_cache_manager.py export low_similarity_cache.csv --max-sim 0.3
```

### 查询特定文本对的相似度
```bash
python nli_cache_manager.py query "positive sentiment" "negative sentiment" microsoft/deberta-large-mnli
```

### 合并多个缓存文件
```bash
python nli_cache_manager.py merge cache/nli_results_cache_*.csv --output merged_cache.csv
```

### 清理缓存
```bash
# 去重并保留最新的1000条记录
python nli_cache_manager.py cleanup --keep-latest 1000

# 只去重，不限制条目数量
python nli_cache_manager.py cleanup
```

## 使用场景

### 1. 分析NLI计算结果
```python
import pandas as pd

# 加载缓存数据
df = pd.read_csv('cache/nli_results_cache.csv')

# 分析相似度分布
print(df['similarity_score'].describe())

# 查看不同模型的结果差异
model_stats = df.groupby('model_name')['similarity_score'].describe()
print(model_stats)

# 查找高相似度的文本对
high_sim = df[df['similarity_score'] > 0.9]
print(high_sim[['text1', 'text2', 'similarity_score']])
```

### 2. 复用已计算的相似度
```python
from nli_cache_manager import get_similarity_for_texts

# 查询缓存中的相似度
similarity = get_similarity_for_texts(
    "This is positive", 
    "This is good", 
    "microsoft/deberta-large-mnli"
)

if similarity is not None:
    print(f"Cached similarity: {similarity}")
else:
    print("Need to compute similarity")
```

### 3. 节约计算资源
- 运行分析前，可以合并之前的缓存文件
- 中断的分析可以从缓存中恢复，避免重复计算
- 可以在不同实验间共享NLI计算结果

## 注意事项

1. **缓存键的生成**: 使用文本的MD5哈希值作为键，确保相同文本对的一致性
2. **阶段性保存**: 每处理100个groups会自动保存带时间戳的缓存备份
3. **兼容性**: 保持与旧pickle格式缓存的兼容性
4. **文件大小**: CSV格式可能比pickle文件大，但更便于查看和分析
5. **去重**: 缓存管理工具会自动处理重复条目

## 性能优化建议

1. 定期清理缓存以保持合理的文件大小
2. 使用export功能创建特定用途的缓存子集
3. 在大规模分析前，先检查缓存统计信息评估覆盖率
4. 利用时间戳备份来追踪计算进度