#!/usr/bin/env python3
"""
完成剩余response生成的脚本
确保每个input text都有30条response，避免重复已有的数据
"""

import os
import sys
import yaml
import pandas as pd
import uuid
import logging
from typing import Dict, List, Any, Optional
from pymongo import MongoClient
from llm_response_generator import LLMResponseGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RemainingResponseGenerator:
    def __init__(self, config_path: str = "config.yaml"):
        """初始化生成器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 连接MongoDB
        mongo_config = self.config['output']['mongo']
        self.client = MongoClient(mongo_config['host'], mongo_config['port'])
        self.db = self.client[mongo_config['database']]
        self.collection = self.db[mongo_config['collection']]
        
        # 初始化LLM生成器
        self.llm_generator = LLMResponseGenerator(config_path)
        
    def get_insufficient_inputs(self, task_name: str, target_count: int = 30) -> List[Dict[str, Any]]:
        """获取response数量不足的input_text"""
        pipeline = [
            {'$match': {'task_name': task_name}},
            {'$group': {
                '_id': '$input_text',
                'count': {'$sum': 1},
                'sample_doc': {'$first': '$$ROOT'}
            }},
            {'$match': {'count': {'$lt': target_count}}},
            {'$sort': {'count': 1}}
        ]
        
        results = list(self.collection.aggregate(pipeline))
        insufficient_inputs = []
        
        for result in results:
            insufficient_inputs.append({
                'input_text': result['_id'],
                'current_count': result['count'],
                'needed_count': target_count - result['count'],
                'sample_doc': result['sample_doc']
            })
        
        return insufficient_inputs
    
    def get_unused_inputs(self, task_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """获取未使用的input_text（从原始数据中选择）"""
        task_config = self.config['tasks'][task_name]
        data_file = task_config['data_file']
        
        # 读取原始数据
        if task_name == 'counterfactual_qa':
            df = pd.read_csv(data_file)
            text_field = task_config['text_field']  # 'Prompt'
            id_field = task_config['id_field']      # 'Category'
        else:
            df = pd.read_csv(data_file)
            text_field = task_config['text_field']
            id_field = task_config['id_field']
        
        # 获取已使用的input_text
        used_inputs = set()
        for doc in self.collection.find({'task_name': task_name}, {'input_text': 1}):
            used_inputs.add(doc['input_text'])
        
        # 找到未使用的输入
        unused_inputs = []
        for _, row in df.iterrows():
            input_text = row[text_field]
            if input_text not in used_inputs:
                unused_inputs.append({
                    'input_text': input_text,
                    'row_data': row.to_dict()
                })
                if len(unused_inputs) >= limit:
                    break
        
        return unused_inputs
    
    def complete_insufficient_responses(self, task_name: str):
        """补充response数量不足的输入"""
        logger.info(f"开始补充 {task_name} 任务的不足response...")
        
        insufficient_inputs = self.get_insufficient_inputs(task_name)
        if not insufficient_inputs:
            logger.info(f"{task_name} 任务没有需要补充的输入")
            return
        
        logger.info(f"找到 {len(insufficient_inputs)} 个需要补充response的输入")
        
        for input_info in insufficient_inputs:
            logger.info(f"补充输入: {input_info['input_text'][:50]}...")
            logger.info(f"当前有 {input_info['current_count']} 条，需要补充 {input_info['needed_count']} 条")
            
            # 计算需要多少个prompt和每个prompt多少次尝试
            needed = input_info['needed_count']
            prompts_needed = min(5, (needed + 5) // 6)  # 最多5个prompt
            attempts_per_prompt = (needed + prompts_needed - 1) // prompts_needed
            
            logger.info(f"将使用 {prompts_needed} 个prompt，每个prompt尝试 {attempts_per_prompt} 次")
            
            # 生成补充的response
            self._generate_responses_for_input(
                task_name, 
                input_info['input_text'],
                input_info['sample_doc'],
                prompts_needed,
                attempts_per_prompt
            )
    
    def generate_new_responses(self, task_name: str, num_inputs: int = 5):
        """为新的输入生成response"""
        logger.info(f"开始为 {task_name} 任务生成新的response...")
        
        unused_inputs = self.get_unused_inputs(task_name, num_inputs)
        if not unused_inputs:
            logger.info(f"{task_name} 任务没有未使用的输入")
            return
        
        logger.info(f"找到 {len(unused_inputs)} 个未使用的输入")
        
        for input_info in unused_inputs:
            logger.info(f"为新输入生成response: {input_info['input_text'][:50]}...")
            
            # 每个输入生成30条response (5个prompt × 6次尝试)
            self._generate_responses_for_new_input(
                task_name,
                input_info['input_text'],
                input_info['row_data'],
                5,  # 5个prompt
                6   # 每个prompt 6次尝试
            )
    
    def _generate_responses_for_input(self, task_name: str, input_text: str, 
                                    sample_doc: Dict[str, Any], num_prompts: int, 
                                    attempts_per_prompt: int):
        """为特定输入生成response（补充模式）"""
        # 这里调用现有的LLM生成器逻辑
        # 由于代码较复杂，我们可以复用现有的生成逻辑
        pass
    
    def _generate_responses_for_new_input(self, task_name: str, input_text: str,
                                        row_data: Dict[str, Any], num_prompts: int,
                                        attempts_per_prompt: int):
        """为新输入生成response"""
        # 这里调用现有的LLM生成器逻辑
        pass

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="完成剩余response生成")
    parser.add_argument("--task", choices=['sentiment_analysis', 'explorative_coding', 'counterfactual_qa'],
                       help="指定要处理的任务")
    parser.add_argument("--mode", choices=['complete', 'new', 'both'], default='both',
                       help="处理模式：complete=补充不足的，new=生成新的，both=两者都做")
    parser.add_argument("--config", default="config.yaml", help="配置文件路径")
    
    args = parser.parse_args()
    
    generator = RemainingResponseGenerator(args.config)
    
    tasks = [args.task] if args.task else ['sentiment_analysis', 'explorative_coding', 'counterfactual_qa']
    
    for task in tasks:
        logger.info(f"\n{'='*50}")
        logger.info(f"处理任务: {task}")
        logger.info(f"{'='*50}")
        
        if args.mode in ['complete', 'both']:
            generator.complete_insufficient_responses(task)
        
        if args.mode in ['new', 'both']:
            generator.generate_new_responses(task)

if __name__ == "__main__":
    main()
