#!/usr/bin/env python3
"""
专门测试 gpt-oss:20b 和 llama3.3:latest 模型的NLI能力
"""

import sys
import os
import logging
import time
from typing import List, Dict, Any, <PERSON><PERSON>

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.nli_calculator import OllamaNLICalculator, get_available_ollama_models
from core.nli_shared import get_nli_calculator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def get_simple_test_sentences() -> List[Tuple[str, str]]:
    """获取简单的测试句子对"""
    return [
        (
            "The cat is sleeping on the mat",
            "A feline is resting on a rug"
        ),
        (
            "It is raining heavily outside",
            "The weather is sunny and clear"
        ),
        (
            "The student passed the exam with high marks",
            "The student failed the test completely"
        ),
        (
            "The book is on the table",
            "There is a novel somewhere in the room"
        ),
        (
            "All birds can fly",
            "Penguins are birds but cannot fly"
        )
    ]


def test_single_model(model_name: str, sentence_pairs: List[Tuple[str, str]]) -> List[Dict[str, Any]]:
    """测试单个Ollama模型"""
    print(f"\n🤖 测试模型: {model_name}")
    print("="*50)
    
    try:
        # 初始化计算器
        calc = OllamaNLICalculator(model_name=model_name, verbose=False)
        
        results = []
        
        for i, (text1, text2) in enumerate(sentence_pairs, 1):
            print(f"\n📝 测试 {i}/{len(sentence_pairs)}")
            print(f"   句子1: {text1}")
            print(f"   句子2: {text2}")
            
            # 计算NLI分数
            start_time = time.time()
            try:
                result = calc.compute_nli_scores(text1, text2)
                elapsed_time = time.time() - start_time
                
                print(f"   结果: E={result.entailment:.3f}, N={result.neutral:.3f}, C={result.contradiction:.3f} ({elapsed_time:.2f}s)")
                
                # 判断主要预测
                scores = [result.entailment, result.neutral, result.contradiction]
                labels = ['ENTAILMENT', 'NEUTRAL', 'CONTRADICTION']
                predicted_label = labels[scores.index(max(scores))]
                print(f"   预测: {predicted_label}")
                
                results.append({
                    'model': model_name,
                    'text1': text1,
                    'text2': text2,
                    'entailment': result.entailment,
                    'neutral': result.neutral,
                    'contradiction': result.contradiction,
                    'time': elapsed_time,
                    'prediction': predicted_label,
                    'success': True
                })
                
            except Exception as e:
                print(f"   ❌ 错误: {e}")
                results.append({
                    'model': model_name,
                    'text1': text1,
                    'text2': text2,
                    'entailment': 0.33,
                    'neutral': 0.34,
                    'contradiction': 0.33,
                    'time': 0,
                    'prediction': 'ERROR',
                    'success': False,
                    'error': str(e)
                })
        
        return results
        
    except Exception as e:
        log.error(f"初始化模型 {model_name} 失败: {e}")
        return []


def compare_with_deberta(sentence_pairs: List[Tuple[str, str]]) -> List[Dict[str, Any]]:
    """与DeBERTa模型对比"""
    print(f"\n🎯 DeBERTa基线测试")
    print("="*50)
    
    try:
        deberta_calc = get_nli_calculator("microsoft/deberta-large-mnli")
        
        results = []
        
        for i, (text1, text2) in enumerate(sentence_pairs, 1):
            print(f"\n📝 测试 {i}/{len(sentence_pairs)}")
            
            start_time = time.time()
            result = deberta_calc.compute_nli_scores_cached(text1, text2)
            elapsed_time = time.time() - start_time
            
            scores = [result.entailment, result.neutral, result.contradiction]
            labels = ['ENTAILMENT', 'NEUTRAL', 'CONTRADICTION']
            predicted_label = labels[scores.index(max(scores))]
            
            print(f"   结果: E={result.entailment:.3f}, N={result.neutral:.3f}, C={result.contradiction:.3f} ({elapsed_time:.2f}s)")
            print(f"   预测: {predicted_label}")
            
            results.append({
                'model': 'deberta-large-mnli',
                'text1': text1,
                'text2': text2,
                'entailment': result.entailment,
                'neutral': result.neutral,
                'contradiction': result.contradiction,
                'time': elapsed_time,
                'prediction': predicted_label,
                'success': True
            })
        
        return results
        
    except Exception as e:
        log.error(f"DeBERTa测试失败: {e}")
        return []


def analyze_model_comparison(all_results: List[Dict[str, Any]]):
    """分析模型对比结果"""
    print(f"\n📊 模型对比分析")
    print("="*60)
    
    # 按模型分组
    models = {}
    for result in all_results:
        model_name = result['model']
        if model_name not in models:
            models[model_name] = []
        models[model_name].append(result)
    
    # 统计每个模型的表现
    print(f"{'模型':<20} {'成功率':<8} {'平均时间':<10} {'预测分布'}")
    print("-" * 70)
    
    for model_name, results in models.items():
        successful_results = [r for r in results if r['success']]
        success_rate = len(successful_results) / len(results)
        
        if successful_results:
            avg_time = sum(r['time'] for r in successful_results) / len(successful_results)
            
            # 统计预测分布
            predictions = [r['prediction'] for r in successful_results]
            pred_counts = {pred: predictions.count(pred) for pred in ['ENTAILMENT', 'NEUTRAL', 'CONTRADICTION']}
            pred_dist = f"E:{pred_counts['ENTAILMENT']} N:{pred_counts['NEUTRAL']} C:{pred_counts['CONTRADICTION']}"
        else:
            avg_time = 0
            pred_dist = "N/A"
        
        print(f"{model_name:<20} {success_rate:.1%}    {avg_time:<10.2f} {pred_dist}")
    
    # 一致性分析
    if len(models) > 1:
        print(f"\n🎯 预测一致性分析:")
        
        # 获取所有模型的预测
        model_names = list(models.keys())
        sentence_count = len(next(iter(models.values())))
        
        consistent_predictions = 0
        for i in range(sentence_count):
            predictions = []
            for model_name in model_names:
                if i < len(models[model_name]) and models[model_name][i]['success']:
                    predictions.append(models[model_name][i]['prediction'])
            
            if len(predictions) == len(model_names) and len(set(predictions)) == 1:
                consistent_predictions += 1
        
        consistency_rate = consistent_predictions / sentence_count
        print(f"   所有模型一致的预测: {consistent_predictions}/{sentence_count} ({consistency_rate:.1%})")


def main():
    """主函数"""
    print("🚀 Ollama NLI模型专项测试")
    print("🎯 目标模型: gpt-oss:20b, llama3.3:latest")
    print("="*60)
    
    # 检查目标模型可用性
    available_models = get_available_ollama_models()
    target_models = ["gpt-oss:20b", "llama3.3:latest"]
    available_targets = [model for model in target_models if model in available_models]
    
    if not available_targets:
        print(f"❌ 目标模型都不可用")
        print(f"📋 可用模型: {available_models}")
        return
    
    print(f"✅ 可用目标模型: {available_targets}")
    
    # 获取测试数据
    sentence_pairs = get_simple_test_sentences()
    print(f"📝 准备了 {len(sentence_pairs)} 个测试句子对")
    
    # 测试所有可用的目标模型
    all_results = []
    
    for model in available_targets:
        results = test_single_model(model, sentence_pairs)
        all_results.extend(results)
    
    # 测试DeBERTa基线
    deberta_results = compare_with_deberta(sentence_pairs)
    all_results.extend(deberta_results)
    
    # 分析结果
    if all_results:
        analyze_model_comparison(all_results)
        
        print(f"\n✅ 测试完成！")
        print(f"💡 建议:")
        print(f"   1. 观察不同模型的预测一致性")
        print(f"   2. 比较计算时间差异")
        print(f"   3. 评估与DeBERTa基线的差异")
    else:
        print(f"❌ 测试失败，没有获得有效结果")


if __name__ == "__main__":
    main()
