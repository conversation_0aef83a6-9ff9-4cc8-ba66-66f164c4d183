#!/usr/bin/env python3
"""
Setup script for LLM Uncertainty Quantification Framework
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="llm-uncertainty-quantification",
    version="1.0.0",
    author="LLM-UQ Team",
    author_email="<EMAIL>",
    description="A comprehensive framework for Large Language Model uncertainty quantification",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/llm-uncertainty-1",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Researchers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "jupyter>=1.0.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "plotly>=5.0.0",
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
        ],
        "gpu": [
            "torch>=2.0.0",  # Will install appropriate CUDA version
        ],
    },
    entry_points={
        "console_scripts": [
            "llm-uq-generate=generate_llm_responses:main",
            "llm-uq-analyze=run_uq_analysis:main",
            "llm-uq-export=export_test_results:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.txt", "*.md"],
        "prompts": ["**/*.txt"],
        "configs": ["*.yaml"],
    },
)
