"""
Comprehensive LLM Response Storage Format Implementation
支持多种任务类型的LLM响应存储格式
"""

import json
import csv
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib


@dataclass
class TokenUsage:
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_estimate: Optional[float] = None


@dataclass
class EvaluationMetrics:
    accuracy: Optional[float] = None
    f1_score: Optional[float] = None
    bleu_score: Optional[float] = None
    rouge_score: Optional[float] = None


@dataclass
class HumanEvaluation:
    annotator: Optional[str] = None
    score: Optional[float] = None
    feedback: Optional[str] = None


@dataclass
class LLMResponseRecord:
    """LLM响应记录的标准格式"""
    
    # Metadata
    task_id: str
    task_name: str  # faculty1000_llmcoding, twitter_sentiment, commit_analysis, etc.
    task_type: str  # classification, generation, reasoning, qa, code_generation
    t_type: str     #  type:1, 2, 3
    v_type: str     # validation type: 
    dataset_source: str
    
    # Message context
    message_id: str
    message_sequence: int
    original_message: str
    message_metadata: Dict[str, Any]
    
    # Prompt details
    prompt_type: str
    prompt_template: str
    prompt_text: str
    prompt_version: str = "1.0"
    prompt_parameters: Dict[str, Any] = None
    
    # LLM interaction
    model_name: str
    model_version: str
    temperature: float
    max_tokens: int
    query_index: int
    total_queries: int
    
    # Response
    response_text: str
    response_cleaned: str
    response_type: str
    confidence_score: Optional[float] = None
    response_metadata: Dict[str, Any] = None
    
    # Evaluation
    has_reference: bool
    reference_answer: Optional[str] = None
    evaluation_metrics: Optional[EvaluationMetrics] = None
    human_eval: Optional[HumanEvaluation] = None
    
    # Token usage
    token_usage: TokenUsage
    
    # Debug info
    raw_response: Optional[str] = None
    request_id: Optional[str] = None
    latency_ms: Optional[int] = None
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.prompt_parameters is None:
            self.prompt_parameters = {}
        if self.response_metadata is None:
            self.response_metadata = {}
        if self.evaluation_metrics is None:
            self.evaluation_metrics = EvaluationMetrics()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = self.to_dict()
        # 处理datetime序列化
        data['created_at'] = data['created_at'].isoformat()
        data['updated_at'] = data['updated_at'].isoformat()
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMResponseRecord':
        """从字典创建实例"""
        # 处理datetime反序列化
        if isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 处理嵌套对象
        if 'token_usage' in data and isinstance(data['token_usage'], dict):
            data['token_usage'] = TokenUsage(**data['token_usage'])
        if 'evaluation_metrics' in data and data['evaluation_metrics']:
            data['evaluation_metrics'] = EvaluationMetrics(**data['evaluation_metrics'])
        if 'human_eval' in data and data['human_eval']:
            data['human_eval'] = HumanEvaluation(**data['human_eval'])
            
        return cls(**data)
    
    def get_csv_headers(self) -> List[str]:
        """获取CSV表头"""
        return [
            'task_id', 'task_name', 'task_type', 't_type', 'v_type',
            'message_id', 'message_sequence', 'original_message',
            'prompt_type', 'prompt_template', 'prompt_text', 'prompt_version',
            'model_name', 'model_version', 'temperature', 'max_tokens',
            'query_index', 'total_queries',
            'response_text', 'response_cleaned', 'response_type', 'confidence_score',
            'has_reference', 'reference_answer',
            'accuracy', 'f1_score', 'bleu_score', 'rouge_score',
            'prompt_tokens', 'completion_tokens', 'total_tokens', 'cost_estimate',
            'latency_ms', 'created_at', 'updated_at'
        ]
    
    def to_csv_row(self) -> List[str]:
        """转换为CSV行"""
        return [
            self.task_id, self.task_name, self.task_type, self.t_type, self.v_type,
            self.message_id, str(self.message_sequence), self.original_message,
            self.prompt_type, self.prompt_template, self.prompt_text, self.prompt_version,
            self.model_name, self.model_version, str(self.temperature), str(self.max_tokens),
            str(self.query_index), str(self.total_queries),
            self.response_text, self.response_cleaned, self.response_type, 
            str(self.confidence_score) if self.confidence_score else '',
            str(self.has_reference), self.reference_answer or '',
            str(self.evaluation_metrics.accuracy) if self.evaluation_metrics.accuracy else '',
            str(self.evaluation_metrics.f1_score) if self.evaluation_metrics.f1_score else '',
            str(self.evaluation_metrics.bleu_score) if self.evaluation_metrics.bleu_score else '',
            str(self.evaluation_metrics.rouge_score) if self.evaluation_metrics.rouge_score else '',
            str(self.token_usage.prompt_tokens), str(self.token_usage.completion_tokens),
            str(self.token_usage.total_tokens), str(self.token_usage.cost_estimate or ''),
            str(self.latency_ms) if self.latency_ms else '',
            self.created_at.isoformat(), self.updated_at.isoformat()
        ]


class LLMResponseStorage:
    """LLM响应存储管理器"""
    
    def __init__(self, storage_path: str = "./data/llm_responses"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
    
    def save_record(self, record: LLMResponseRecord, format_type: str = "json"):
        """保存单个记录"""
        if format_type == "json":
            file_path = self.storage_path / f"{record.task_id}_{record.message_id}_{record.query_index}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(record.to_json())
        elif format_type == "csv":
            file_path = self.storage_path / f"{record.task_name}_responses.csv"
            is_new_file = not file_path.exists()
            
            with open(file_path, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                if is_new_file:
                    writer.writerow(record.get_csv_headers())
                writer.writerow(record.to_csv_row())
    
    def load_record(self, file_path: str) -> LLMResponseRecord:
        """加载单个记录"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.endswith('.json'):
                data = json.load(f)
                return LLMResponseRecord.from_dict(data)
            else:
                raise ValueError("Only JSON format supported for loading")
    
    def batch_save(self, records: List[LLMResponseRecord], format_type: str = "csv"):
        """批量保存记录"""
        if format_type == "csv":
            # 按任务分组保存
            task_groups = {}
            for record in records:
                if record.task_name not in task_groups:
                    task_groups[record.task_name] = []
                task_groups[record.task_name].append(record)
            
            for task_name, task_records in task_groups.items():
                file_path = self.storage_path / f"{task_name}_batch_responses.csv"
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    if task_records:
                        writer.writerow(task_records[0].get_csv_headers())
                        for record in task_records:
                            writer.writerow(record.to_csv_row())


# 使用示例
if __name__ == "__main__":
    # 创建示例记录
    record = LLMResponseRecord(
        task_id="faculty1000_llmcoding_001",
        task_name="faculty1000_llmcoding",
        task_type="code_generation",
        t_type="programming",
        v_type="human_eval",
        dataset_source="faculty1000",
        message_id="paper_12345",
        message_sequence=1,
        original_message="Implement a binary search tree in Python",
        message_metadata={"author": "user123", "timestamp": "2025-07-31T09:30:00Z"},
        prompt_type="code_generation",
        prompt_template="programming_task",
        prompt_text="Please implement a binary search tree...",
        model_name="qwen-32b",
        model_version="2025-07-31",
        temperature=0.7,
        max_tokens=2048,
        query_index=1,
        total_queries=5,
        response_text="class BSTNode:\n    def __init__(self, val):\n        self.val = val",
        response_cleaned="class BSTNode:\n    def __init__(self, val):\n        self.val = val",
        response_type="code",
        confidence_score=0.95,
        has_reference=True,
        reference_answer="标准二叉搜索树实现",
        token_usage=TokenUsage(prompt_tokens=156, completion_tokens=342, total_tokens=498, cost_estimate=0.002),
        latency_ms=1200
    )
    
    # 保存记录
    storage = LLMResponseStorage()
    storage.save_record(record, format_type="json")
    storage.save_record(record, format_type="csv")
    
    print("示例记录已创建并保存")