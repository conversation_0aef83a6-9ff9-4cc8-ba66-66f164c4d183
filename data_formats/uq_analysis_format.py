"""
UQ Analysis Results Storage Format Implementation
不确定性量化分析结果存储格式
"""

import json
import csv
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib


@dataclass
class TokenUsage:
    prompt_tokens: int
    completion_tokens: int


@dataclass
class GenerationParameters:
    temperature: float
    max_tokens: int
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0


@dataclass
class SampleResponse:
    response_id: str
    response_sequence: int
    response_text: str
    response_hash: str
    generation_time: datetime
    token_usage: TokenUsage
    
    def __post_init__(self):
        if not self.response_hash:
            self.response_hash = hashlib.md5(self.response_text.encode()).hexdigest()[:8]


@dataclass
class ConfidenceInterval:
    lower: float
    upper: float


@dataclass
class UncertaintyDistribution:
    type: str  # gaussian, beta, categorical, etc.
    parameters: Dict[str, float]
    samples: List[float]


@dataclass
class UQMethodResult:
    method_name: str
    method_type: str  # entropy_based, distance_based, ensemble, graph_based
    method_version: str
    uncertainty_score: float
    uncertainty_type: str  # aleatoric, epistemic
    confidence_interval: ConfidenceInterval
    distribution: Optional[UncertaintyDistribution] = None
    additional_metrics: Dict[str, float] = None
    method_specific_results: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_metrics is None:
            self.additional_metrics = {}
        if self.method_specific_results is None:
            self.method_specific_results = {}


@dataclass
class DisagreementMetrics:
    pairwise_distance: float
    variance: float
    entropy: float
    

@dataclass
class AggregatedResults:
    consensus_score: float
    disagreement_metrics: DisagreementMetrics
    most_certain: List[str]
    least_certain: List[str]


@dataclass
class EvaluationMetrics:
    ground_truth: Optional[str] = None
    prediction_consensus: Optional[str] = None
    consensus_accuracy: Optional[float] = None
    individual_accuracies: Dict[str, float] = None
    expected_calibration_error: Optional[float] = None
    maximum_calibration_error: Optional[float] = None
    
    def __post_init__(self):
        if self.individual_accuracies is None:
            self.individual_accuracies = {}


@dataclass
class VisualizationData:
    uncertainty_distribution: Dict[str, List[float]] = None
    response_clusters: Dict[str, List[float]] = None
    method_comparison: Dict[str, List[float]] = None
    
    def __post_init__(self):
        if self.uncertainty_distribution is None:
            self.uncertainty_distribution = {"bins": [], "counts": []}
        if self.response_clusters is None:
            self.response_clusters = {"cluster_labels": [], "embeddings_2d": []}
        if self.method_comparison is None:
            self.method_comparison = {"scores": {}, "rankings": []}


@dataclass
class UQAnalysisResult:
    """UQ分析结果的标准格式"""
    
    # 分析元数据
    analysis_id: str
    task_type: str
    task_name: str
    message_id: str
    message_sequence: int
    original_message: str
    analysis_timestamp: datetime
    analyst_version: str
    
    # LLM配置
    llm_name: str
    llm_version: str
    generation_parameters: GenerationParameters
    
    # Prompt配置
    prompt_type: str
    prompt_text: str
    prompt_hash: str
    prompt_template_version: str
    
    # 样本收集
    sample_size: int
    sample_responses: List[SampleResponse]
    sampling_method: str
    sampling_parameters: Dict[str, Any]
    
    # UQ方法结果
    uq_methods: List[UQMethodResult]
    aggregated_results: AggregatedResults
    
    # 评估结果
    evaluation: EvaluationMetrics
    
    # 可视化数据
    visualization_data: VisualizationData
    
    # 处理信息
    processing_time_ms: int
    memory_usage_mb: float
    error_log: Optional[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.sampling_parameters is None:
            self.sampling_parameters = {}
        if self.warnings is None:
            self.warnings = []
        if not self.prompt_hash:
            self.prompt_hash = hashlib.md5(self.prompt_text.encode()).hexdigest()[:8]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = self.to_dict()
        # 处理datetime序列化
        data['analysis_timestamp'] = data['analysis_timestamp'].isoformat()
        for response in data['sample_responses']:
            response['generation_time'] = response['generation_time'].isoformat()
        return json.dumps(data, indent=2, ensure_ascii=False, cls=NumpyEncoder)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UQAnalysisResult':
        """从字典创建实例"""
        # 处理datetime反序列化
        data['analysis_timestamp'] = datetime.fromisoformat(data['analysis_timestamp'])
        for response in data['sample_responses']:
            response['generation_time'] = datetime.fromisoformat(response['generation_time'])
            response['token_usage'] = TokenUsage(**response['token_usage'])
        
        # 处理嵌套对象
        data['generation_parameters'] = GenerationParameters(**data['generation_parameters'])
        data['sample_responses'] = [SampleResponse(**sr) for sr in data['sample_responses']]
        data['uq_methods'] = [UQMethodResult(**um) for um in data['uq_methods']]
        data['aggregated_results'] = AggregatedResults(
            consensus_score=data['aggregated_results']['consensus_score'],
            disagreement_metrics=DisagreementMetrics(**data['aggregated_results']['disagreement_metrics']),
            most_certain=data['aggregated_results']['most_certain'],
            least_certain=data['aggregated_results']['least_certain']
        )
        data['evaluation'] = EvaluationMetrics(**data['evaluation'])
        data['visualization_data'] = VisualizationData(**data['visualization_data'])
        
        return cls(**data)
    
    def get_csv_headers(self) -> List[str]:
        """获取CSV表头"""
        return [
            'analysis_id', 'task_type', 'task_name', 'message_id', 'message_sequence',
            'original_message', 'llm_name', 'llm_version', 'temperature', 'max_tokens',
            'prompt_type', 'prompt_text', 'prompt_hash', 'prompt_template_version',
            'sample_size', 'sampling_method', 'processing_time_ms', 'memory_usage_mb',
            'consensus_score', 'pairwise_distance', 'variance', 'entropy',
            'ground_truth', 'prediction_consensus', 'consensus_accuracy',
            'expected_calibration_error', 'maximum_calibration_error'
        ]
    
    def to_csv_row(self) -> List[str]:
        """转换为CSV行（聚合数据）"""
        return [
            self.analysis_id, self.task_type, self.task_name, self.message_id,
            str(self.message_sequence), self.original_message, self.llm_name,
            self.llm_version, str(self.generation_parameters.temperature),
            str(self.generation_parameters.max_tokens), self.prompt_type,
            self.prompt_text, self.prompt_hash, self.prompt_template_version,
            str(self.sample_size), self.sampling_method, str(self.processing_time_ms),
            str(self.memory_usage_mb), str(self.aggregated_results.consensus_score),
            str(self.aggregated_results.disagreement_metrics.pairwise_distance),
            str(self.aggregated_results.disagreement_metrics.variance),
            str(self.aggregated_results.disagreement_metrics.entropy),
            str(self.evaluation.ground_truth or ''),
            str(self.evaluation.prediction_consensus or ''),
            str(self.evaluation.consensus_accuracy or ''),
            str(self.evaluation.expected_calibration_error or ''),
            str(self.evaluation.maximum_calibration_error or '')
        ]


class NumpyEncoder(json.JSONEncoder):
    """处理numpy数据类型的JSON编码器"""
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)


class UQAnalysisStorage:
    """UQ分析结果存储管理器"""
    
    def __init__(self, storage_path: str = "./data/uq_analysis"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
    
    def save_analysis(self, analysis: UQAnalysisResult, format_type: str = "json"):
        """保存分析结果"""
        if format_type == "json":
            file_path = self.storage_path / f"{analysis.analysis_id}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(analysis.to_json())
        elif format_type == "csv":
            file_path = self.storage_path / f"{analysis.task_name}_uq_results.csv"
            is_new_file = not file_path.exists()
            
            with open(file_path, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                if is_new_file:
                    writer.writerow(analysis.get_csv_headers())
                writer.writerow(analysis.to_csv_row())
    
    def load_analysis(self, file_path: str) -> UQAnalysisResult:
        """加载分析结果"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.endswith('.json'):
                data = json.load(f)
                return UQAnalysisResult.from_dict(data)
            else:
                raise ValueError("Only JSON format supported for loading")
    
    def save_method_details(self, analysis: UQAnalysisResult):
        """保存详细的UQ方法结果"""
        # 为每个方法保存详细结果
        for method_result in analysis.uq_methods:
            method_file = self.storage_path / f"{analysis.analysis_id}_{method_result.method_name}.json"
            with open(method_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(method_result), f, indent=2, cls=NumpyEncoder)
    
    def batch_save(self, analyses: List[UQAnalysisResult], format_type: str = "csv"):
        """批量保存分析结果"""
        if format_type == "csv":
            # 按任务类型分组保存
            task_groups = {}
            for analysis in analyses:
                if analysis.task_type not in task_groups:
                    task_groups[analysis.task_type] = []
                task_groups[analysis.task_type].append(analysis)
            
            for task_type, task_analyses in task_groups.items():
                file_path = self.storage_path / f"{task_type}_batch_uq_results.csv"
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    if task_analyses:
                        writer.writerow(task_analyses[0].get_csv_headers())
                        for analysis in task_analyses:
                            writer.writerow(analysis.to_csv_row())


# 工厂函数用于创建不同类型的UQ分析结果
def create_uq_analysis_result(
    analysis_id: str,
    task_type: str,
    task_name: str,
    message_id: str,
    message_sequence: int,
    original_message: str,
    llm_name: str,
    prompt_type: str,
    prompt_text: str,
    sample_responses: List[Dict],
    uq_methods: List[Dict],
    **kwargs
) -> UQAnalysisResult:
    """创建UQ分析结果的工厂函数"""
    
    # 转换样本响应
    sample_responses_obj = [
        SampleResponse(
            response_id=sr.get('response_id', f"resp_{i}"),
            response_sequence=sr.get('response_sequence', i),
            response_text=sr['response_text'],
            response_hash=sr.get('response_hash', ''),
            generation_time=datetime.fromisoformat(sr['generation_time']) if isinstance(sr['generation_time'], str) else sr['generation_time'],
            token_usage=TokenUsage(**sr['token_usage'])
        )
        for i, sr in enumerate(sample_responses)
    ]
    
    # 转换UQ方法结果
    uq_methods_obj = []
    for method_data in uq_methods:
        uq_methods_obj.append(UQMethodResult(
            method_name=method_data['method_name'],
            method_type=method_data['method_type'],
            method_version=method_data.get('method_version', '1.0'),
            uncertainty_score=method_data['uncertainty_score'],
            uncertainty_type=method_data['uncertainty_type'],
            confidence_interval=ConfidenceInterval(**method_data['confidence_interval']),
            additional_metrics=method_data.get('additional_metrics', {}),
            method_specific_results=method_data.get('method_specific_results', {})
        ))
    
    return UQAnalysisResult(
        analysis_id=analysis_id,
        task_type=task_type,
        task_name=task_name,
        message_id=message_id,
        message_sequence=message_sequence,
        original_message=original_message,
        analysis_timestamp=datetime.utcnow(),
        analyst_version=kwargs.get('analyst_version', '1.0'),
        llm_name=llm_name,
        llm_version=kwargs.get('llm_version', 'latest'),
        generation_parameters=GenerationParameters(**kwargs.get('generation_parameters', {})),
        prompt_type=prompt_type,
        prompt_text=prompt_text,
        prompt_template_version=kwargs.get('prompt_template_version', '1.0'),
        sample_size=len(sample_responses),
        sample_responses=sample_responses_obj,
        sampling_method=kwargs.get('sampling_method', 'repeated_sampling'),
        sampling_parameters=kwargs.get('sampling_parameters', {}),
        uq_methods=uq_methods_obj,
        aggregated_results=AggregatedResults(
            consensus_score=kwargs.get('consensus_score', 0.0),
            disagreement_metrics=DisagreementMetrics(**kwargs.get('disagreement_metrics', {})) if 'disagreement_metrics' in kwargs else DisagreementMetrics(0.0, 0.0, 0.0),
            most_certain=kwargs.get('most_certain', []),
            least_certain=kwargs.get('least_certain', [])
        ),
        evaluation=EvaluationMetrics(**kwargs.get('evaluation', {})),
        visualization_data=VisualizationData(**kwargs.get('visualization_data', {})),
        processing_time_ms=kwargs.get('processing_time_ms', 0),
        memory_usage_mb=kwargs.get('memory_usage_mb', 0.0),
        error_log=kwargs.get('error_log'),
        warnings=kwargs.get('warnings', [])
    )


# 使用示例
if __name__ == "__main__":
    # 创建示例分析结果
    sample_responses = [
        {
            'response_id': 'resp_001',
            'response_sequence': 1,
            'response_text': 'Positive',
            'generation_time': datetime.utcnow(),
            'token_usage': {'prompt_tokens': 45, 'completion_tokens': 2}
        },
        {
            'response_id': 'resp_002',
            'response_sequence': 2,
            'response_text': 'Positive',
            'generation_time': datetime.utcnow(),
            'token_usage': {'prompt_tokens': 45, 'completion_tokens': 2}
        }
    ]
    
    uq_methods = [
        {
            'method_name': 'semantic_entropy',
            'method_type': 'entropy_based',
            'uncertainty_score': 0.15,
            'uncertainty_type': 'epistemic',
            'confidence_interval': {'lower': 0.12, 'upper': 0.18},
            'additional_metrics': {'entropy': 0.15, 'mutual_information': 0.05}
        },
        {
            'method_name': 'num_sets',
            'method_type': 'distance_based',
            'uncertainty_score': 0.22,
            'uncertainty_type': 'aleatoric',
            'confidence_interval': {'lower': 0.18, 'upper': 0.26},
            'additional_metrics': {'jaccard_index': 0.85}
        }
    ]
    
    analysis = create_uq_analysis_result(
        analysis_id="uq_twitter_sentiment_001",
        task_type="sentiment_analysis",
        task_name="twitter_sentiment",
        message_id="tweet_12345",
        message_sequence=1,
        original_message="I love this new product!",
        llm_name="qwen-32b",
        prompt_type="sentiment_classification",
        prompt_text="Classify the sentiment of this tweet...",
        sample_responses=sample_responses,
        uq_methods=uq_methods,
        consensus_score=0.89,
        disagreement_metrics={'pairwise_distance': 0.05, 'variance': 0.03, 'entropy': 0.12},
        processing_time_ms=2500,
        memory_usage_mb=125.5
    )
    
    # 保存分析结果
    storage = UQAnalysisStorage()
    storage.save_analysis(analysis, format_type="json")
    storage.save_analysis(analysis, format_type="csv")
    
    print("示例UQ分析结果已创建并保存")