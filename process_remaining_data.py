#!/usr/bin/env python3
"""
处理剩余未处理的input_text数据
确保每个input_text都有30条response（5个prompt × 6次尝试）
"""

import os
import sys
import yaml
import pandas as pd
import uuid
import logging
from typing import Dict, List, Any, Set
from pymongo import MongoClient
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RemainingDataProcessor:
    def __init__(self, config_path: str = "config.yaml"):
        """初始化处理器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 连接MongoDB
        mongo_config = self.config['output']['mongo']
        self.client = MongoClient(mongo_config['host'], mongo_config['port'])
        self.db = self.client[mongo_config['database']]
        self.collection = self.db[mongo_config['collection']]
        
    def get_processed_inputs(self, task_name: str) -> Set[str]:
        """获取已经处理过的input_text"""
        processed_inputs = set()
        
        # 查询该任务的所有已处理input_text
        cursor = self.collection.find(
            {'task_name': task_name}, 
            {'input_text': 1}
        )
        
        for doc in cursor:
            processed_inputs.add(doc['input_text'])
        
        logger.info(f"{task_name}: 找到 {len(processed_inputs)} 个已处理的input_text")
        return processed_inputs
    
    def get_remaining_inputs(self, task_name: str) -> List[Dict[str, Any]]:
        """获取剩余未处理的input_text"""
        task_config = self.config['tasks'][task_name]
        data_file = task_config['data_file']
        text_field = task_config['text_field']
        
        # 读取原始数据
        try:
            df = pd.read_csv(data_file)
            logger.info(f"{task_name}: 原始数据文件包含 {len(df)} 条记录")
        except Exception as e:
            logger.error(f"无法读取数据文件 {data_file}: {e}")
            return []
        
        # 获取已处理的input_text
        processed_inputs = self.get_processed_inputs(task_name)
        
        # 找到未处理的数据
        remaining_data = []
        for _, row in df.iterrows():
            input_text = row[text_field]
            if input_text not in processed_inputs:
                remaining_data.append(row.to_dict())
        
        logger.info(f"{task_name}: 找到 {len(remaining_data)} 个未处理的input_text")
        return remaining_data
    
    def create_filtered_data_file(self, task_name: str, remaining_data: List[Dict[str, Any]]) -> str:
        """创建只包含剩余数据的临时文件"""
        if not remaining_data:
            return None
        
        # 创建DataFrame
        df = pd.DataFrame(remaining_data)
        
        # 保存临时文件
        temp_file = f"temp_remaining_{task_name}.csv"
        df.to_csv(temp_file, index=False)
        
        logger.info(f"{task_name}: 创建临时数据文件 {temp_file}，包含 {len(df)} 条记录")
        return temp_file
    
    def create_task_config(self, task_name: str, temp_data_file: str) -> str:
        """创建只处理指定任务的临时配置文件"""
        # 复制配置
        new_config = self.config.copy()
        
        # 只启用指定任务
        for task in new_config['tasks']:
            if task == task_name:
                new_config['tasks'][task]['enabled'] = True
                new_config['tasks'][task]['data_file'] = temp_data_file
            else:
                new_config['tasks'][task]['enabled'] = False
        
        # 保存临时配置文件
        temp_config_file = f"temp_config_{task_name}.yaml"
        with open(temp_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"{task_name}: 创建临时配置文件 {temp_config_file}")
        return temp_config_file
    
    def process_task(self, task_name: str):
        """处理指定任务的剩余数据"""
        logger.info(f"\n{'='*60}")
        logger.info(f"开始处理任务: {task_name}")
        logger.info(f"{'='*60}")
        
        # 检查任务是否启用
        task_config = self.config['tasks'].get(task_name, {})
        if not task_config.get('enabled', False):
            logger.info(f"任务 {task_name} 未启用，跳过")
            return
        
        # 获取剩余未处理的数据
        remaining_data = self.get_remaining_inputs(task_name)
        
        if not remaining_data:
            logger.info(f"任务 {task_name} 没有剩余数据需要处理")
            return
        
        # 创建临时数据文件
        temp_data_file = self.create_filtered_data_file(task_name, remaining_data)
        if not temp_data_file:
            logger.error(f"无法创建临时数据文件")
            return
        
        # 创建临时配置文件
        temp_config_file = self.create_task_config(task_name, temp_data_file)
        
        try:
            # 使用现有的生成器处理数据
            from llm_response_generator import LLMResponseGenerator
            
            logger.info(f"开始生成 {task_name} 的剩余response...")
            generator = LLMResponseGenerator(temp_config_file)
            
            # 运行生成（不恢复进度，创建新的运行）
            run_id = generator.run(resume=False, test_mode=False)
            
            logger.info(f"任务 {task_name} 完成！运行ID: {run_id}")
            
        except Exception as e:
            logger.error(f"处理任务 {task_name} 时出错: {e}")
            
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_data_file):
                    os.remove(temp_data_file)
                    logger.info(f"删除临时数据文件: {temp_data_file}")
                if os.path.exists(temp_config_file):
                    os.remove(temp_config_file)
                    logger.info(f"删除临时配置文件: {temp_config_file}")
            except Exception as e:
                logger.warning(f"清理临时文件时出错: {e}")
    
    def show_status(self):
        """显示当前状态"""
        logger.info("\n" + "="*60)
        logger.info("当前数据处理状态")
        logger.info("="*60)
        
        tasks = ['sentiment_analysis', 'explorative_coding', 'counterfactual_qa']
        
        for task_name in tasks:
            task_config = self.config['tasks'].get(task_name, {})
            if not task_config.get('enabled', False):
                logger.info(f"\n{task_name.upper()}: 任务未启用")
                continue
                
            # 读取原始数据数量
            try:
                data_file = task_config['data_file']
                df = pd.read_csv(data_file)
                total_inputs = len(df)
            except:
                total_inputs = 0
            
            # 获取已处理数量
            processed_inputs = self.get_processed_inputs(task_name)
            processed_count = len(processed_inputs)
            remaining_count = total_inputs - processed_count
            
            logger.info(f"\n{task_name.upper()}:")
            logger.info(f"  原始数据: {total_inputs} 条")
            logger.info(f"  已处理: {processed_count} 条")
            logger.info(f"  剩余: {remaining_count} 条")
            
            if remaining_count > 0:
                expected_responses = remaining_count * 30  # 每个input 30条response
                logger.info(f"  预计生成response: {expected_responses} 条")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="处理剩余未处理的数据")
    parser.add_argument("--task", 
                       choices=['sentiment_analysis', 'explorative_coding', 'counterfactual_qa'],
                       help="指定要处理的任务")
    parser.add_argument("--status-only", action="store_true", 
                       help="只显示状态，不执行处理")
    parser.add_argument("--config", default="config.yaml", 
                       help="配置文件路径")
    
    args = parser.parse_args()
    
    processor = RemainingDataProcessor(args.config)
    
    if args.status_only:
        processor.show_status()
        return
    
    # 显示初始状态
    processor.show_status()
    
    # 处理任务
    if args.task:
        processor.process_task(args.task)
    else:
        # 处理所有启用的任务
        tasks = ['sentiment_analysis', 'explorative_coding', 'counterfactual_qa']
        for task in tasks:
            processor.process_task(task)
    
    # 显示最终状态
    logger.info("\n" + "="*60)
    logger.info("处理完成，最终状态:")
    logger.info("="*60)
    processor.show_status()

if __name__ == "__main__":
    main()
