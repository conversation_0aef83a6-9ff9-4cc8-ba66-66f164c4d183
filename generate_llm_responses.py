import os
import json
import csv
import random
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any
import pandas as pd
from openai import OpenAI
from pymongo import MongoClient
import glob

class LLMResponseGenerator:
    def __init__(self, api_key=None, base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"):
        """初始化LLM响应生成器"""
        self.client = OpenAI(
            api_key=api_key or os.getenv("DASHSCOPE_API_KEY", 'null'),
            base_url=base_url,
        )
        
        # MongoDB配置
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["LLM-UQ"]
        self.collection = self.db["response"]
        
        # 创建索引
        self.create_indexes()
        
        # 加载prompt模板
        self.prompt_templates = self.load_prompt_templates()
        
    def create_indexes(self):
        """创建MongoDB索引"""
        self.collection.create_index([("run_id", 1)])
        self.collection.create_index([("task_id", 1)])
        self.collection.create_index([("dataset_source", 1)])
        self.collection.create_index([("execution_timestamp", -1)])
    
    def load_prompt_templates(self) -> Dict[str, str]:
        """加载prompt模板"""
        templates = {}
        
        # 加载Twitter情感分析prompt模板
        template_files = {
            "only_answer": "prompts/twitter_sentiment.txt",
            "answer_then_reason": "prompts/twitter_sentiment_reason.txt", 
            "reasoning_then_answer": "prompts/twitter_sentiment_reason_first.txt"
        }
        
        for variant, file_path in template_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    templates[variant] = f.read().strip()
            except FileNotFoundError:
                print(f"Warning: {file_path} not found")
        
        return templates
    
    def sample_semeval_data(self, sample_size: int = 500) -> List[Dict[str, Any]]:
        """从SemEval数据集采样数据"""
        print(f"Sampling {sample_size} records from SemEval dataset...")
        
        # 读取CSV文件
        df = pd.read_csv('data/SemEval2017-task4-test.subtask-A.english.csv')
        
        # 随机采样
        sampled_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        # 转换为字典列表
        sampled_data = []
        for _, row in sampled_df.iterrows():
            sampled_data.append({
                'id': str(row['id']),
                'text': row['text'],
                'label': row['label']
            })
        
        # 保存采样数据到CSV
        sampled_df.to_csv('sampled_semeval.csv', index=False)
        print(f"Saved {len(sampled_data)} SemEval samples to sampled_semeval.csv")
        
        return sampled_data
    
    def sample_commits_data(self, sample_size: int = 500) -> List[Dict[str, Any]]:
        """从PyTorch commits数据集采样数据"""
        print(f"Sampling {sample_size} records from PyTorch commits dataset...")
        
        # 获取所有commit文件
        commit_files = glob.glob('data/pytorch_commits/commit_*.json')
        
        # 随机选择文件
        selected_files = random.sample(commit_files, min(sample_size, len(commit_files)))
        
        sampled_data = []
        for file_path in selected_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    commit_data = json.load(f)
                
                # 提取需要的信息
                sampled_data.append({
                    'sha': commit_data.get('sha', ''),
                    'message': commit_data.get('commit', {}).get('message', ''),
                    'date': commit_data.get('commit', {}).get('author', {}).get('date', ''),
                    'author': commit_data.get('commit', {}).get('author', {}).get('name', '')
                })
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
        
        # 保存采样数据到CSV
        with open('sampled_commits.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(sampled_data)
        
        print(f"Saved {len(sampled_data)} commit samples to sampled_commits.csv")
        return sampled_data
    
    def call_llm(self, prompt: str, model: str = "qwen3-14b") -> Dict[str, Any]:
        """调用LLM API"""
        try:
            completion = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                logprobs=True,
                extra_body={"enable_thinking": False},
            )
            
            if completion.choices:
                return {
                    'content': completion.choices[0].message.content,
                    'logprobs': completion.choices[0].logprobs.content if hasattr(completion.choices[0], 'logprobs') else None,
                    'model': model
                }
            else:
                return {'content': '', 'logprobs': None, 'model': model}
                
        except Exception as e:
            print(f"Error calling LLM: {e}")
            return {'content': '', 'logprobs': None, 'model': model}
    
    def parse_response(self, raw_response: str, prompt_variant: str) -> Dict[str, Any]:
        """解析LLM响应"""
        parsed = {
            'raw_answer': raw_response,
            'parsed_answer': None,
            'parsed_reason': None
        }
        
        if prompt_variant == "only_answer":
            # 只返回答案，直接使用原始响应
            parsed['parsed_answer'] = raw_response.strip()
            
        elif prompt_variant == "answer_then_reason":
            # 格式: [Label]: <label> [Reasoning]: <reasoning>
            try:
                if '[Label]:' in raw_response and '[Reasoning]:' in raw_response:
                    label_part = raw_response.split('[Reasoning]:')[0]
                    reason_part = raw_response.split('[Reasoning]:')[1]
                    
                    label = label_part.split('[Label]:')[1].strip()
                    reason = reason_part.strip()
                    
                    parsed['parsed_answer'] = label
                    parsed['parsed_reason'] = reason
                else:
                    parsed['parsed_answer'] = raw_response.strip()
            except:
                parsed['parsed_answer'] = raw_response.strip()
                
        elif prompt_variant == "reasoning_then_answer":
            # 格式: [Reasoning]: <reasoning> [Label]: <label>
            try:
                if '[Reasoning]:' in raw_response and '[Label]:' in raw_response:
                    reason_part = raw_response.split('[Label]:')[0]
                    label_part = raw_response.split('[Label]:')[1]
                    
                    reason = reason_part.split('[Reasoning]:')[1].strip()
                    label = label_part.strip()
                    
                    parsed['parsed_answer'] = label
                    parsed['parsed_reason'] = reason
                else:
                    parsed['parsed_answer'] = raw_response.strip()
            except:
                parsed['parsed_answer'] = raw_response.strip()
        
        return parsed
    
    def generate_task_id(self, dataset_source: str, input_id: str, prompt_variant: str) -> str:
        """生成任务ID"""
        return f"task_{dataset_source}_{input_id}_{prompt_variant}"
    
    def save_to_mongodb(self, data: Dict[str, Any]):
        """保存数据到MongoDB"""
        try:
            result = self.collection.insert_one(data)
            return result.inserted_id
        except Exception as e:
            print(f"Error saving to MongoDB: {e}")
            return None
    
    def process_semeval_data(self, data: List[Dict[str, Any]], prompt_variants: List[str] = None):
        """处理SemEval数据"""
        if prompt_variants is None:
            prompt_variants = ["only_answer", "answer_then_reason", "reasoning_then_answer"]
        
        run_id = str(uuid.uuid4())
        task_attempt_total = 0
        
        for item in data:
            input_text = item['text']
            reference_answer = item['label']
            
            for prompt_variant in prompt_variants:
                if prompt_variant not in self.prompt_templates:
                    print(f"Warning: Prompt template for {prompt_variant} not found")
                    continue
                
                task_attempt_total += 1
                task_id = self.generate_task_id("twitter_sentiment", item['id'], prompt_variant)
                
                # 填充prompt模板
                prompt_template = self.prompt_templates[prompt_variant]
                final_prompt = prompt_template.replace("{tweet}", input_text)
                
                # 调用LLM
                print(f"Processing SemEval item {item['id']} with {prompt_variant}...")
                llm_response = self.call_llm(final_prompt)
                
                # 解析响应
                parsed_response = self.parse_response(llm_response['content'], prompt_variant)
                
                # 准备MongoDB文档
                document = {
                    "run_id": run_id,
                    "task_id": task_id,
                    "dataset_source": "twitter_sentiment",
                    "task_category": "sentiment_analysis",
                    "input_text": input_text,
                    "reference_answer": reference_answer,
                    "model_identifier": llm_response['model'],
                    "prompt_variant": prompt_variant,
                    "prompt_template": prompt_template,
                    "final_prompt": final_prompt,
                    "generation_config": {
                        "temperature": 0.7,
                        "top_p": 0.95,
                        "enable_thinking": False
                    },
                    "task_attempt_prompt": 1,
                    "task_attempt_total": task_attempt_total,
                    "raw_response": llm_response['content'],
                    "response_logprobs": llm_response['logprobs'],
                    "raw_answer": parsed_response['raw_answer'],
                    "parsed_answer": parsed_response['parsed_answer'],
                    "parsed_reason": parsed_response['parsed_reason'],
                    "execution_timestamp": datetime.now(timezone.utc)
                }
                
                # 保存到MongoDB
                inserted_id = self.save_to_mongodb(document)
                if inserted_id:
                    print(f"Saved to MongoDB with ID: {inserted_id}")
    
    def process_commits_data(self, data: List[Dict[str, Any]], prompt_variants: List[str] = None):
        """处理Commits数据"""
        if prompt_variants is None:
            prompt_variants = ["only_answer", "answer_then_reason", "reasoning_then_answer"]
        
        run_id = str(uuid.uuid4())
        task_attempt_total = 0
        
        for item in data:
            input_text = item['message']
            reference_answer = None  # Commits没有预定义的答案
            
            for prompt_variant in prompt_variants:
                if prompt_variant not in self.prompt_templates:
                    print(f"Warning: Prompt template for {prompt_variant} not found")
                    continue
                
                task_attempt_total += 1
                task_id = self.generate_task_id("pytorch_commits", item['sha'], prompt_variant)
                
                # 填充prompt模板
                prompt_template = self.prompt_templates[prompt_variant]
                final_prompt = prompt_template.replace("{tweet}", input_text)
                
                # 调用LLM
                print(f"Processing commit {item['sha'][:8]} with {prompt_variant}...")
                llm_response = self.call_llm(final_prompt)
                
                # 解析响应
                parsed_response = self.parse_response(llm_response['content'], prompt_variant)
                
                # 准备MongoDB文档
                document = {
                    "run_id": run_id,
                    "task_id": task_id,
                    "dataset_source": "pytorch_commits",
                    "task_category": "open_explorative_coding",
                    "input_text": input_text,
                    "reference_answer": reference_answer,
                    "model_identifier": llm_response['model'],
                    "prompt_variant": prompt_variant,
                    "prompt_template": prompt_template,
                    "final_prompt": final_prompt,
                    "generation_config": {
                        "temperature": 0.7,
                        "top_p": 0.95,
                        "enable_thinking": False
                    },
                    "task_attempt_prompt": 1,
                    "task_attempt_total": task_attempt_total,
                    "raw_response": llm_response['content'],
                    "response_logprobs": llm_response['logprobs'],
                    "raw_answer": parsed_response['raw_answer'],
                    "parsed_answer": parsed_response['parsed_answer'],
                    "parsed_reason": parsed_response['parsed_reason'],
                    "execution_timestamp": datetime.now(timezone.utc)
                }
                
                # 保存到MongoDB
                inserted_id = self.save_to_mongodb(document)
                if inserted_id:
                    print(f"Saved to MongoDB with ID: {inserted_id}")
    
    def run(self, semeval_sample_size: int = 500, commits_sample_size: int = 500):
        """运行完整的生成流程"""
        print("Starting LLM response generation...")
        
        # 1. 采样数据
        semeval_data = self.sample_semeval_data(semeval_sample_size)
        commits_data = self.sample_commits_data(commits_sample_size)
        
        # 2. 处理SemEval数据
        print("\nProcessing SemEval data...")
        self.process_semeval_data(semeval_data)
        
        # 3. 处理Commits数据
        print("\nProcessing Commits data...")
        self.process_commits_data(commits_data)
        
        print("\nLLM response generation completed!")

def main():
    """主函数"""
    # 创建生成器实例
    generator = LLMResponseGenerator()
    
    # 运行生成流程
    generator.run()

if __name__ == "__main__":
    main()
