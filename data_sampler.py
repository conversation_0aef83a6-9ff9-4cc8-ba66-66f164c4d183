import os
import json
import csv
import random
import pandas as pd
import glob
from typing import List, Dict, Any
from pathlib import Path
from pymongo import MongoClient

class DataSampler:
    """数据采样器，负责从不同数据源采样数据"""
    
    def __init__(self):
        """初始化数据采样器"""
        pass
    
    def sample_semeval_data(self, sample_size: int = 500) -> List[Dict[str, Any]]:
        """从SemEval数据集采样数据"""
        print(f"Sampling {sample_size} records from SemEval dataset...")
        
        # 读取CSV文件
        df = pd.read_csv('data/SemEval2017-task4-test.subtask-A.english.csv')
        
        # 随机采样
        sampled_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        # 转换为字典列表
        sampled_data = []
        for _, row in sampled_df.iterrows():
            sampled_data.append({
                'id': str(row['id']),
                'text': row['text'],
                'label': row['label']
            })
        
        # 保存采样数据到CSV
        sampled_df.to_csv('sampled_semeval.csv', index=False)
        print(f"Saved {len(sampled_data)} SemEval samples to sampled_semeval.csv")
        
        return sampled_data
    
    def sample_commits_data(self, sample_size: int = 500) -> List[Dict[str, Any]]:
        """从MongoDB中的PyTorch commits数据集采样数据"""
        print(f"Sampling {sample_size} records from MongoDB PyTorch commits dataset...")
        
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['pytorch-sha']
        collection = db['pytorch-commits']
        
        # 获取总文档数
        total_count = collection.count_documents({})
        print(f"Total commits in database: {total_count}")
        
        # 使用聚合管道随机采样
        pipeline = [
            {"$sample": {"size": sample_size}}
        ]
        
        sampled_data = []
        for doc in collection.aggregate(pipeline):
            # 提取需要的信息
            sampled_data.append({
                'sha': doc.get('sha', ''),
                'message': doc.get('commit', {}).get('message', ''),
                'date': doc.get('commit', {}).get('author', {}).get('date', ''),
                'author': doc.get('commit', {}).get('author', {}).get('name', '')
            })
        
        # 关闭数据库连接
        client.close()
        
        # 保存采样数据到CSV
        with open('sampled_commits.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(sampled_data)
        
        print(f"Saved {len(sampled_data)} commit samples to sampled_commits.csv")
        return sampled_data

    def sample_additional_commits(self, existing_csv: str = 'sampled_commits.csv', additional_size: int = 200,
                                   output_csv: str = 'sampled_commits_additional.csv', merged_output: str | None = None) -> List[Dict[str, Any]]:
        """从MongoDB再采样 additional_size 条不在 existing_csv 中的 commit。

        参数:
            existing_csv: 已有采样CSV (包含 sha 列)
            additional_size: 需要新增的数量
            output_csv: 仅新增样本输出文件
            merged_output: 若提供, 生成合并后的文件 (existing + additional 去重)
        """
        import pandas as pd
        from pymongo import MongoClient

        print(f"[INFO] Sampling additional {additional_size} commits excluding {existing_csv}...")
        existing_shas = set()
        if Path(existing_csv).exists():
            try:
                df_exist = pd.read_csv(existing_csv)
                if 'sha' not in df_exist.columns:
                    raise ValueError(f"{existing_csv} 缺少 sha 列")
                existing_shas = set(df_exist['sha'].dropna().astype(str).tolist())
            except Exception as e:
                print(f"[WARN] 读取 {existing_csv} 失败: {e}")
        print(f"[INFO] 已有样本数: {len(existing_shas)}")

        client = MongoClient('localhost', 27017)
        coll = client['pytorch-sha']['pytorch-commits']

        total = coll.count_documents({})
        print(f"[INFO] Mongo 全量 commit 数: {total}")

        # 查询剩余可用数量
        remaining_count = coll.count_documents({ 'sha': { '$nin': list(existing_shas) } }) if existing_shas else total
        print(f"[INFO] 剩余可用数量: {remaining_count}")
        sample_n = min(additional_size, remaining_count)
        if sample_n == 0:
            print("[INFO] 没有可用的新增 commit 可采样.")
            return []
        if sample_n < additional_size:
            print(f"[WARN] 仅能采样 {sample_n} 条 (请求 {additional_size})")

        # 使用聚合管道随机采样剩余
        pipeline = [
            { '$match': { 'sha': { '$nin': list(existing_shas) } }} if existing_shas else { '$match': {} },
            { '$sample': { 'size': sample_n } }
        ]
        new_rows: List[Dict[str, Any]] = []
        for doc in coll.aggregate(pipeline):
            new_rows.append({
                'sha': doc.get('sha', ''),
                'message': doc.get('commit', {}).get('message', ''),
                'date': doc.get('commit', {}).get('author', {}).get('date', ''),
                'author': doc.get('commit', {}).get('author', {}).get('name', ''),
            })
        client.close()

        # 输出新增文件
        with open(output_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(new_rows)
        print(f"[INFO] 写入新增样本 {len(new_rows)} 条 -> {output_csv}")

        # 合并输出
        if merged_output:
            merged = list(new_rows)
            if existing_shas and Path(existing_csv).exists():
                merged_df = pd.read_csv(existing_csv)
                merged = merged_df.to_dict('records') + [r for r in new_rows if r['sha'] not in existing_shas]
            # 去重 (保持顺序: 先旧后新)
            seen = set()
            dedup = []
            for r in merged:
                s = r.get('sha')
                if s and s not in seen:
                    seen.add(s)
                    dedup.append(r)
            with open(merged_output, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
                writer.writeheader()
                writer.writerows(dedup)
            print(f"[INFO] 合并文件生成 {merged_output}, 总计 {len(dedup)} 条")

        return new_rows
    
    def run_sampling(self, semeval_sample_size: int = 500, commits_sample_size: int = 500):
        """运行数据采样流程"""
        print("Starting data sampling...")
        
        # 采样SemEval数据
        semeval_data = self.sample_semeval_data(semeval_sample_size)
        
        # 采样Commits数据
        commits_data = self.sample_commits_data(commits_sample_size)
        
        print(f"\nSampling completed!")
        print(f"- SemEval: {len(semeval_data)} samples")
        print(f"- Commits: {len(commits_data)} samples")
        
        return semeval_data, commits_data

def main():
    """主函数 - 仅运行数据采样"""
    sampler = DataSampler()
    sampler.run_sampling()

if __name__ == "__main__":
    main()
