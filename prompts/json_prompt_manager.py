import os
import json
import random
from typing import List, Dict, Any, Optional

class JSONPromptManager:
    """JSON格式的Prompt模板管理器"""
    
    def __init__(self, config_file: str = "prompts/prompts_config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
        
    def load_config(self):
        """加载JSON配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            print(f"Warning: Prompt config file {self.config_file} not found")
            self.config = {}
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON config file: {e}")
            self.config = {}
    
    def get_task_prompts(self, task_name: str) -> List[Dict[str, Any]]:
        """获取指定任务的所有prompt"""
        tasks = self.config.get('tasks', {})
        if task_name not in tasks:
            return []
        return tasks[task_name].get('prompts', [])
    
    def get_task_config(self, task_name: str) -> Dict[str, Any]:
        """获取任务配置信息"""
        tasks = self.config.get('tasks', {})
        return tasks.get(task_name, {})
    
    def get_prompt_by_id(self, task_name: str, prompt_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取特定prompt"""
        prompts = self.get_task_prompts(task_name)
        for prompt in prompts:
            if prompt.get('id') == prompt_id:
                return prompt
        return None
    
    def get_random_prompts(self, task_name: str, count: int = 5, seed: int = None) -> List[Dict[str, Any]]:
        """随机选择指定数量的prompts，支持固定种子确保可重现性"""
        prompts = self.get_task_prompts(task_name)
        if len(prompts) <= count:
            return prompts

        # 如果提供了种子，使用固定种子确保可重现性
        if seed is not None:
            random.seed(seed)

        return random.sample(prompts, count)
    
    def format_prompt(self, prompt_dict: Dict[str, Any], **kwargs) -> str:
        """格式化prompt模板，替换占位符"""
        template = prompt_dict.get('template', '')
        return template.format(**kwargs)
    
    def get_template_variables(self, task_name: str) -> List[str]:
        """获取任务的模板变量列表"""
        task_config = self.get_task_config(task_name)
        return task_config.get('template_variables', [])
    
    def get_output_format(self, task_name: str) -> str:
        """获取任务的输出格式"""
        task_config = self.get_task_config(task_name)
        return task_config.get('output_format', '')
    
    def validate_task_config(self, task_name: str) -> bool:
        """验证任务配置是否完整"""
        task_config = self.get_task_config(task_name)
        if not task_config:
            return False
        
        required_fields = ['task_name', 'description', 'template_variables', 'prompts']
        for field in required_fields:
            if field not in task_config:
                print(f"Missing required field '{field}' in task '{task_name}'")
                return False
        
        prompts = task_config.get('prompts', [])
        if not prompts:
            print(f"No prompts found for task '{task_name}'")
            return False
        
        return True
    
    def get_prompts_by_style(self, task_name: str, style: str) -> List[Dict[str, Any]]:
        """根据风格筛选prompts"""
        prompts = self.get_task_prompts(task_name)
        return [p for p in prompts if p.get('style') == style]
    
    def get_prompts_with_reasoning(self, task_name: str, with_reasoning: bool = True) -> List[Dict[str, Any]]:
        """根据是否包含推理筛选prompts"""
        prompts = self.get_task_prompts(task_name)
        return [p for p in prompts if p.get('reasoning', False) == with_reasoning]
    
    def list_available_tasks(self) -> List[str]:
        """列出所有可用的任务"""
        return list(self.config.get('tasks', {}).keys())
    
    def get_task_summary(self, task_name: str) -> Dict[str, Any]:
        """获取任务摘要信息"""
        task_config = self.get_task_config(task_name)
        if not task_config:
            return {}
        
        prompts = self.get_task_prompts(task_name)
        styles = list(set(p.get('style', 'unknown') for p in prompts))
        
        return {
            'task_name': task_name,
            'description': task_config.get('description', ''),
            'prompt_count': len(prompts),
            'template_variables': task_config.get('template_variables', []),
            'output_format': task_config.get('output_format', ''),
            'available_styles': styles,
            'reasoning_prompts': len([p for p in prompts if p.get('reasoning', False)]),
            'non_reasoning_prompts': len([p for p in prompts if not p.get('reasoning', False)])
        }
