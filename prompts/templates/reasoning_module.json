{"template": "Here is a commit from {{repo_name}}.\nPlease analyze this commit and determine which module it belongs to.\n\nCommit SHA: {{sha}}\nAuthor: {{author}}\nDate: {{date}}\nMessage: {{message}}\n\nFirst, provide your reasoning based on the commit information, then identify the module.\nFormat your response as:\nReasoning: [your reasoning based on file changes, commit message, and context]\nModule: [module name]", "variations": [], "semantic_variants": []}