"""
Prompt templates for semantic uncertainty analysis.

This module provides centralized access to all prompts used in the system.
"""

import os

# Prompt directory
PROMPT_DIR = os.path.dirname(__file__)

def load_prompt(prompt_name: str) -> str:
    """Load a prompt template from file."""
    prompt_path = os.path.join(PROMPT_DIR, f"{prompt_name}.txt")
    with open(prompt_path, 'r', encoding='utf-8') as f:
        return f.read().strip()

# Available prompts
PROMPTS = {
    'nli_classification': load_prompt('nli_classification'),
    'question_answering': load_prompt('question_answering'),
}

__all__ = ['PROMPTS', 'load_prompt']