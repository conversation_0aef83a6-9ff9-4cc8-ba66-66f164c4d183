from typing import List, Dict, Any, Optional
from jinja2 import Template
import json
from pathlib import Path

class PromptManager:
    """管理多种prompt模板和变体"""
    
    def __init__(self, templates_dir: str = "prompts/templates"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """加载所有prompt模板"""
        for file in self.templates_dir.glob("*.json"):
            with open(file) as f:
                template_config = json.load(f)
                self.templates[file.stem] = template_config
    
    def create_variations(self, template_name: str, data: Dict[str, Any]) -> List[str]:
        """基于模板和数据创建prompt变体"""
        if template_name not in self.templates:
            raise ValueError(f"Template {template_name} not found")
        
        template_config = self.templates[template_name]
        variations = []
        
        base_template = Template(template_config['template'])
        variations.append(base_template.render(**data))
        
        # 处理参数变体
        for variation in template_config.get('variations', []):
            combined_data = {**data, **variation}
            prompt = base_template.render(**combined_data)
            variations.append(prompt)
        
        # 处理语义等价变体
        for semantic_var in template_config.get('semantic_variants', []):
            template = Template(semantic_var['template'])
            prompt = template.render(**data)
            variations.append(prompt)
        
        return variations
    
    def add_template(self, name: str, template: str, variations: List[Dict] = None):
        """添加新的prompt模板"""
        config = {
            "template": template,
            "variations": variations or [],
            "semantic_variants": []
        }
        
        with open(self.templates_dir / f"{name}.json", 'w') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.templates[name] = config
    
    def list_templates(self) -> List[str]:
        """列出所有可用模板"""
        return list(self.templates.keys())
    
    def get_template_config(self, template_name: str) -> Dict[str, Any]:
        """获取模板配置"""
        return self.templates.get(template_name, {})