You are an expert in Natural Language Inference (NLI). Given two statements, determine their relationship:

Premise: "{premise}"
Hypothesis: "{hypothesis}"

Classify the relationship as one of:
1. ENTAILMENT: The hypothesis is definitely true given the premise
2. CONTRADICTION: The hypothesis is definitely false given the premise  
3. NEUTRAL: The hypothesis might be true or false given the premise

Think step by step:
1. What does the premise state?
2. What does the hypothesis claim?
3. Does the premise guarantee the truth/falsity of the hypothesis?

Provide your response as JSON with probabilities for each category that sum to 1.0:
{
    "entailment": 0.0-1.0,
    "contradiction": 0.0-1.0,
    "neutral": 0.0-1.0
}

Be precise and conservative in your assessment.